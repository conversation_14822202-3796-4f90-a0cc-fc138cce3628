<?php

class M_data_table extends CI_Model
{
  public function getRecords($requestData)
  {
      $tahun = $requestData['tahun']; // Ambil tahun dari $requestData
      $satker = $requestData['pem'];
              
      $iduser = $this->session->userdata('id_user');
      $idgroup = $this->session->userdata('id_user_group');
      $kode_eselon = $this->session->userdata('kode_eselon_1');

      $a = "";
      $b = "";
      $c = "";
      $d = "";
      $aa = "";
      $bb = "";
      $cc = "";
      $dd = "";

      if ($idgroup == 3) {

        $a = " and b.id_user=$iduser";
        $b = " and c.id_user=$iduser";
        $c = " and d.id_user=$iduser";
        $d = " and e.id_user=$iduser";
    } elseif ($idgroup == 4) {

        $a = " and b.kode_eselon_1='".$kode_eselon."'";
        $b = " and c.kode_eselon_1='".$kode_eselon."'";
        $c = " and d.kode_eselon_1='".$kode_eselon."'";
        $d = " and e.kode_eselon_1='".$kode_eselon."'";

    }

    if (!empty($requestData['eselon'])) {
        $aa = " and b.kode_eselon_1='".$kode_eselon."'";
        $bb = " and c.kode_eselon_1='".$kode_eselon."'";
        $cc = " and d.kode_eselon_1='".$kode_eselon."'";
        $dd = " and e.kode_eselon_1='".$kode_eselon."'";

    }

    if (!empty($requestData['pem'])) {
        $aa = " and b.pemberi_konsesi='".$satker."'";
        $bb = " and c.pemberi_konsesi='".$satker."'";
        $cc = " and d.pemberi_konsesi='".$satker."'";
        $dd = " and e.pemberi_konsesi='".$satker."'";
    }

    $wh1 = $a . $aa;
    $wh2 = $b . $bb;
    $wh3 = $c . $cc;
    $wh4 = $d . $dd;

      $this->load->database();

      if($tahun == 2022){
      $this->db->select("
          a.alias,
          tr.tahun,
          COALESCE(SUM(total_pemerintah.total_pemerintah), 0) AS total_pemerintah,
          COALESCE(SUM(total_mitra.total_mitra), 0) AS total_mitra,
          COALESCE(SUM(total_kewajiban.total_nilai_buku), 0) AS total_nilai_buku,
          COALESCE(SUM(total_kewajiban_gov.total_nilai_buku), 0) AS total_nilai_buku_gov
      ");
      $this->db->from('(SELECT 2022 AS tahun) tr');
      $this->db->join('ref_jenis_pengusahaan a', '1=1', 'left');
      $this->db->join("
          (
              SELECT 
                  c.id_jenis_pengusahaan,
                  SUM(
                      COALESCE(c.nilai_tanah_smst1, 0) +
                      COALESCE(c.nilai_tanah_tw3, 0) +
                      COALESCE(c.nilai_tanah_unaudited, 0) +
                      COALESCE(c.nilai_tanah_audited, 0) +
                      COALESCE(c.nilai_kdp_smst1, 0) +
                      COALESCE(c.nilai_kdp_tw3, 0) +
                      COALESCE(c.nilai_kdp_unaudited, 0) +
                      COALESCE(c.nilai_kdp_audited, 0) +
                      COALESCE(c.nilai_nontanah_smst1, 0) +
                      COALESCE(c.nilai_nontanah_tw3, 0) +
                      COALESCE(c.nilai_nontanah_unaudited, 0) +
                      COALESCE(c.nilai_nontanah_audited, 0)
                  ) - SUM(
                      COALESCE(c.nilai_penyusutan_smst1, 0) +
                      COALESCE(c.nilai_penyusutan_tw3, 0) +
                      COALESCE(c.nilai_penyusutan_unaudited, 0) +
                      COALESCE(c.nilai_penyusutan_audited, 0)
                  ) AS total_pemerintah
              FROM 
                  v_saldo_awal_lp c
              WHERE 
                  c.tahun = $tahun $wh2
              GROUP BY 
                  c.id_jenis_pengusahaan
          ) total_pemerintah", 'a.id_jenis_pengusahaan = total_pemerintah.id_jenis_pengusahaan', 'left'
      );
      
      // Join untuk total_mitra
      $this->db->join("
          (
              SELECT 
                  b.id_jenis_pengusahaan, 
                  SUM(
                      COALESCE(b.nilai_tanah_smst1, 0) + 
                      COALESCE(b.nilai_tanah_tw3, 0) + 
                      COALESCE(b.nilai_tanah_smst2, 0) + 
                      COALESCE(b.nilai_tanah_audited, 0) +
                      COALESCE(b.nilai_kdp_smst1, 0) + 
                      COALESCE(b.nilai_kdp_tw3, 0) + 
                      COALESCE(b.nilai_kdp_smst2, 0) + 
                      COALESCE(b.nilai_kdp_audited, 0) +
                      COALESCE(b.nilai_nontanah_smst1, 0) + 
                      COALESCE(b.nilai_nontanah_tw3, 0) + 
                      COALESCE(b.nilai_nontanah_smst2, 0) + 
                      COALESCE(b.nilai_nontanah_audited, 0)
                  ) - SUM(
                      COALESCE(b.nilai_akumulasi_penyusutan_smst1, 0) + 
                      COALESCE(b.nilai_akumulasi_penyusutan_tw3, 0) + 
                      COALESCE(b.nilai_akumulasi_penyusutan_smst2, 0) + 
                      COALESCE(b.nilai_akumulasi_penyusutan_audited, 0)
                  ) AS total_mitra
              FROM 
                  v_saldo_awal b
              WHERE 
                  b.tahun = $tahun $wh1
              GROUP BY 
                  b.id_jenis_pengusahaan
          ) total_mitra", 'a.id_jenis_pengusahaan = total_mitra.id_jenis_pengusahaan', 'left'
      );

      // Join untuk total_kewajiban
      $this->db->join("
          (
              SELECT 
                  d.id_jenis_pengusahaan, 
                  SUM(d.total_nilai_buku) AS total_nilai_buku
              FROM 
                  v_saldo_awal_kewajiban d
              WHERE 
                  d.tahun = $tahun $wh3
              GROUP BY 
                  d.id_jenis_pengusahaan
          ) total_kewajiban", 'a.id_jenis_pengusahaan = total_kewajiban.id_jenis_pengusahaan', 'left'
      );

      // Join untuk total_kewajiban_gov
      $this->db->join("
          (
              SELECT 
                  e.id_jenis_pengusahaan, 
                  SUM(e.total_nilai_buku) AS total_nilai_buku
              FROM 
                  v_saldo_awal_kewajiban_gov e
              WHERE 
                  e.tahun = $tahun $wh4
              GROUP BY 
                  e.id_jenis_pengusahaan
          ) total_kewajiban_gov", 'a.id_jenis_pengusahaan = total_kewajiban_gov.id_jenis_pengusahaan', 'left'
      );
      $this->db->group_by('a.alias, tr.tahun');
    }else{
      $this->db->select("a.alias, 
      tr.tahun, 
      COALESCE(b.total_aset, 0) AS mitra_smst1,
      COALESCE(b.mitra_tw3, 0) AS mitra_tw3,
      COALESCE(b.mitra_unaudited, 0) AS mitra_unaudited,
      COALESCE(b.mitra_audited, 0) AS mitra_audited,
      COALESCE(c.pemerintah_smst1, 0) AS pemerintah_smst1 ,
      COALESCE(c.pemerintah_tw3, 0) AS pemerintah_tw3 ,
      COALESCE(c.pemerintah_unaudited, 0) AS pemerintah_unaudited ,
      COALESCE(c.pemerintah_audited, 0) AS pemerintah_audited ,
      SUM(d.nb_tw1_2) AS nb_tw1_2,
      SUM(d.nb_tw3) AS nb_tw3,
      SUM(d.nb_tw4) AS nb_tw4,
      SUM(d.nb_audited) AS nb_audited,
      COALESCE(e.nbgp_tw1_2, 0) as nbgp_tw1_2,
      COALESCE(e.nbgp_tw3, 0) AS nbgp_tw3,
      COALESCE(e.nbgp_tw4, 0) AS nbgp_tw4,
      COALESCE(e.nbgp_audited, 0) AS nbgp_audited  
      ");
      $this->db->from("(WITH RECURSIVE Tahun AS (
        SELECT 2023 AS tahun
        UNION ALL
        SELECT tahun + 1
        FROM Tahun
        WHERE tahun <= YEAR(CURDATE())
    )
    SELECT tahun FROM Tahun) tr");
      $this->db->join('ref_jenis_pengusahaan a', '1=1', 'left');
  
      $this->db->join("(SELECT
      b.id_jenis_pengusahaan,
      b.tahun,
      SUM(
          COALESCE(b.nilai_tanah_smst1, 0) + 
          COALESCE(b.nilai_kdp_smst1, 0) + 
          COALESCE(b.nilai_nontanah_smst1, 0) + 
          COALESCE(b.sa_tanah_smst1, 0) + 
          COALESCE(b.sa_kdp_smst1, 0) + 
          COALESCE(b.sa_nontanah_smst1, 0) + 
          COALESCE(b.sa_tanah_tw3, 0) + 
          COALESCE(b.sa_kdp_tw3, 0) + 
          COALESCE(b.sa_nontanah_tw3, 0) + 
          COALESCE(b.sa_tanah, 0) + 
          COALESCE(b.sa_kdp, 0) + 
          COALESCE(b.sa_nontanah, 0) + 
          COALESCE(b.sa_tanah_smst2, 0) + 
          COALESCE(b.sa_kdp_smst2, 0) + 
          COALESCE(b.sa_nontanah_smst2, 0)
      ) - SUM(
          COALESCE(b.nilai_akumulasi_penyusutan_smst1, 0) + 
          COALESCE(b.sa_akumulasi_smst1, 0) + 
          COALESCE(b.sa_akumulasi_smst2, 0) + 
          COALESCE(b.sa_akumulasi_tw3, 0) + 
          COALESCE(b.sa_akumulasi, 0)
      ) AS total_aset,
       SUM(
      COALESCE(b.nilai_tanah_smst1, 0) + 
      COALESCE(b.nilai_kdp_smst1, 0) + 
      COALESCE(b.nilai_nontanah_smst1, 0) + 
      COALESCE(b.nilai_tanah_tw3, 0) + 
      COALESCE(b.nilai_kdp_tw3, 0) + 
      COALESCE(b.nilai_nontanah_tw3, 0) + 
      COALESCE(b.sa_tanah_smst1, 0) + 
      COALESCE(b.sa_kdp_smst1, 0) + 
      COALESCE(b.sa_nontanah_smst1, 0) + 
      COALESCE(b.sa_tanah_tw3, 0) + 
      COALESCE(b.sa_kdp_tw3, 0) + 
      COALESCE(b.sa_nontanah_tw3, 0) + 
      COALESCE(b.sa_tanah, 0) + 
      COALESCE(b.sa_kdp, 0) + 
      COALESCE(b.sa_nontanah, 0) + 
      COALESCE(b.sa_tanah_smst2, 0) + 
      COALESCE(b.sa_kdp_smst2, 0) + 
      COALESCE(b.sa_nontanah_smst2, 0)
  ) - SUM(
      COALESCE(b.nilai_akumulasi_penyusutan_smst1, 0) + 
      COALESCE(b.nilai_akumulasi_penyusutan_tw3, 0) + 
      COALESCE(b.sa_akumulasi_smst1, 0) + 
      COALESCE(b.sa_akumulasi_smst2, 0) + 
      COALESCE(b.sa_akumulasi_tw3, 0) + 
      COALESCE(b.sa_akumulasi, 0)
  ) AS mitra_tw3,
  SUM(
      COALESCE(b.nilai_tanah_smst1, 0) + 
      COALESCE(b.nilai_kdp_smst1, 0) + 
      COALESCE(b.nilai_nontanah_smst1, 0) + 
      COALESCE(b.nilai_tanah_tw3, 0) + 
      COALESCE(b.nilai_kdp_tw3, 0) + 
      COALESCE(b.nilai_nontanah_tw3, 0) +
      COALESCE(b.nilai_tanah_smst2 , 0) + 
      COALESCE(b.nilai_kdp_smst2 , 0) + 
      COALESCE(b.nilai_nontanah_smst2 , 0) +
      COALESCE(b.sa_tanah_smst1, 0) + 
      COALESCE(b.sa_kdp_smst1, 0) + 
      COALESCE(b.sa_nontanah_smst1, 0) + 
      COALESCE(b.sa_tanah_tw3, 0) + 
      COALESCE(b.sa_kdp_tw3, 0) + 
      COALESCE(b.sa_nontanah_tw3, 0) + 
      COALESCE(b.sa_tanah, 0) + 
      COALESCE(b.sa_kdp, 0) + 
      COALESCE(b.sa_nontanah, 0) + 
      COALESCE(b.sa_tanah_smst2, 0) + 
      COALESCE(b.sa_kdp_smst2, 0) + 
      COALESCE(b.sa_nontanah_smst2, 0)
  ) - SUM(
      COALESCE(b.nilai_akumulasi_penyusutan_smst1, 0) + 
      COALESCE(b.nilai_akumulasi_penyusutan_tw3, 0) + 
       COALESCE(b.nilai_akumulasi_penyusutan_smst2 , 0) + 
      COALESCE(b.sa_akumulasi_smst1, 0) + 
      COALESCE(b.sa_akumulasi_smst2, 0) + 
      COALESCE(b.sa_akumulasi_tw3, 0) + 
      COALESCE(b.sa_akumulasi, 0)
  ) AS mitra_unaudited,
  SUM(
      COALESCE(b.nilai_tanah_smst1, 0) + 
      COALESCE(b.nilai_kdp_smst1, 0) + 
      COALESCE(b.nilai_nontanah_smst1, 0) + 
      COALESCE(b.nilai_tanah_tw3, 0) + 
      COALESCE(b.nilai_kdp_tw3, 0) + 
      COALESCE(b.nilai_nontanah_tw3, 0) +
      COALESCE(b.nilai_tanah_smst2 , 0) + 
      COALESCE(b.nilai_kdp_smst2 , 0) + 
      COALESCE(b.nilai_nontanah_smst2 , 0) +
      COALESCE(b.nilai_tanah_audited , 0) + 
      COALESCE(b.nilai_kdp_audited , 0) + 
      COALESCE(b.nilai_nontanah_audited , 0) +
      COALESCE(b.sa_tanah_smst1, 0) + 
      COALESCE(b.sa_kdp_smst1, 0) + 
      COALESCE(b.sa_nontanah_smst1, 0) + 
      COALESCE(b.sa_tanah_tw3, 0) + 
      COALESCE(b.sa_kdp_tw3, 0) + 
      COALESCE(b.sa_nontanah_tw3, 0) + 
      COALESCE(b.sa_tanah, 0) + 
      COALESCE(b.sa_kdp, 0) + 
      COALESCE(b.sa_nontanah, 0) + 
      COALESCE(b.sa_tanah_smst2, 0) + 
      COALESCE(b.sa_kdp_smst2, 0) + 
      COALESCE(b.sa_nontanah_smst2, 0)
  ) - SUM(
      COALESCE(b.nilai_akumulasi_penyusutan_smst1, 0) + 
      COALESCE(b.nilai_akumulasi_penyusutan_tw3, 0) + 
       COALESCE(b.nilai_akumulasi_penyusutan_smst2 , 0) +
        COALESCE(b.nilai_akumulasi_penyusutan_audited , 0) + 
      COALESCE(b.sa_akumulasi_smst1, 0) + 
      COALESCE(b.sa_akumulasi_smst2, 0) + 
      COALESCE(b.sa_akumulasi_tw3, 0) + 
      COALESCE(b.sa_akumulasi, 0)
  ) AS mitra_audited
  FROM v_perolehan_mutasi b
  WHERE b.tahun = $tahun $wh1
  GROUP BY b.id_jenis_pengusahaan,tahun)  b", 'a.id_jenis_pengusahaan = b.id_jenis_pengusahaan AND tr.tahun = b.tahun', 'left');

      $this->db->join("(SELECT
      c.id_jenis_pengusahaan,
      c.tahun,
      SUM(
          COALESCE(c.nilai_tanah_smst1, 0) + 
          COALESCE(c.nilai_kdp_smst1, 0) + 
          COALESCE(c.nilai_nontanah_smst1, 0) + 
          COALESCE(c.sa_tanah_smst1, 0) + 
          COALESCE(c.sa_kdp_smst1, 0) + 
          COALESCE(c.sa_nontanah_smst1, 0) + 
          COALESCE(c.sa_tanah_tw3, 0) + 
          COALESCE(c.sa_kdp_tw3, 0) + 
          COALESCE(c.sa_nontanah_tw3, 0) + 
          COALESCE(c.sa_tanah, 0) + 
          COALESCE(c.sa_kdp, 0) + 
          COALESCE(c.sa_nontanah, 0) + 
          COALESCE(c.sa_tanah_unaudited, 0) + 
          COALESCE(c.sa_kdp_unaudited, 0) + 
          COALESCE(c.sa_nontanah_unaudited, 0)
      ) - SUM(
          COALESCE(c.nilai_penyusutan_smst1, 0) + 
          COALESCE(c.sa_penyusutan_smst1, 0) + 
          COALESCE(c.sa_penyusutan_unaudited, 0) + 
          COALESCE(c.sa_penyusutan_tw3, 0) + 
          COALESCE(c.sa_penyusutan, 0)
      ) AS pemerintah_smst1,
       SUM(
      COALESCE(c.nilai_tanah_smst1, 0) + 
      COALESCE(c.nilai_kdp_smst1, 0) + 
      COALESCE(c.nilai_nontanah_smst1, 0) + 
      COALESCE(c.nilai_tanah_tw3, 0) + 
      COALESCE(c.nilai_kdp_tw3, 0) + 
      COALESCE(c.nilai_nontanah_tw3, 0) + 
      COALESCE(c.sa_tanah_smst1, 0) + 
      COALESCE(c.sa_kdp_smst1, 0) + 
      COALESCE(c.sa_nontanah_smst1, 0) + 
      COALESCE(c.sa_tanah_tw3, 0) + 
      COALESCE(c.sa_kdp_tw3, 0) + 
      COALESCE(c.sa_nontanah_tw3, 0) + 
      COALESCE(c.sa_tanah, 0) + 
      COALESCE(c.sa_kdp, 0) + 
      COALESCE(c.sa_nontanah, 0) + 
      COALESCE(c.sa_tanah_unaudited, 0) + 
      COALESCE(c.sa_kdp_unaudited, 0) + 
      COALESCE(c.sa_nontanah_unaudited, 0)
  ) - SUM(
      COALESCE(c.nilai_penyusutan_smst1, 0) + 
      COALESCE(c.nilai_penyusutan_tw3, 0) + 
      COALESCE(c.sa_penyusutan_smst1, 0) + 
      COALESCE(c.sa_penyusutan_unaudited, 0) + 
      COALESCE(c.sa_penyusutan_tw3, 0) + 
      COALESCE(c.sa_penyusutan, 0)
  ) AS pemerintah_tw3,
  SUM(
      COALESCE(c.nilai_tanah_smst1, 0) + 
      COALESCE(c.nilai_kdp_smst1, 0) + 
      COALESCE(c.nilai_nontanah_smst1, 0) + 
      COALESCE(c.nilai_tanah_tw3, 0) + 
      COALESCE(c.nilai_kdp_tw3, 0) + 
      COALESCE(c.nilai_nontanah_tw3, 0) +
      COALESCE(c.nilai_tanah_unaudited , 0) + 
      COALESCE(c.nilai_kdp_unaudited , 0) + 
      COALESCE(c.nilai_nontanah_unaudited , 0) +
      COALESCE(c.sa_tanah_smst1, 0) + 
      COALESCE(c.sa_kdp_smst1, 0) + 
      COALESCE(c.sa_nontanah_smst1, 0) + 
      COALESCE(c.sa_tanah_tw3, 0) + 
      COALESCE(c.sa_kdp_tw3, 0) + 
      COALESCE(c.sa_nontanah_tw3, 0) + 
      COALESCE(c.sa_tanah, 0) + 
      COALESCE(c.sa_kdp, 0) + 
      COALESCE(c.sa_nontanah, 0) + 
      COALESCE(c.sa_tanah_unaudited, 0) + 
      COALESCE(c.sa_kdp_unaudited, 0) + 
      COALESCE(c.sa_nontanah_unaudited, 0)
  ) - SUM(
      COALESCE(c.nilai_penyusutan_smst1, 0) + 
      COALESCE(c.nilai_penyusutan_tw3, 0) + 
       COALESCE(c.nilai_penyusutan_unaudited , 0) + 
      COALESCE(c.sa_penyusutan_smst1, 0) + 
      COALESCE(c.sa_penyusutan_unaudited, 0) + 
      COALESCE(c.sa_penyusutan_tw3, 0) + 
      COALESCE(c.sa_penyusutan, 0)
  ) AS pemerintah_unaudited,
  SUM(
      COALESCE(c.nilai_tanah_smst1, 0) + 
      COALESCE(c.nilai_kdp_smst1, 0) + 
      COALESCE(c.nilai_nontanah_smst1, 0) + 
      COALESCE(c.nilai_tanah_tw3, 0) + 
      COALESCE(c.nilai_kdp_tw3, 0) + 
      COALESCE(c.nilai_nontanah_tw3, 0) +
      COALESCE(c.nilai_tanah_unaudited , 0) + 
      COALESCE(c.nilai_kdp_unaudited , 0) + 
      COALESCE(c.nilai_nontanah_unaudited , 0) +
      COALESCE(c.nilai_tanah_audited , 0) + 
      COALESCE(c.nilai_kdp_audited , 0) + 
      COALESCE(c.nilai_nontanah_audited , 0) +
      COALESCE(c.sa_tanah_smst1, 0) + 
      COALESCE(c.sa_kdp_smst1, 0) + 
      COALESCE(c.sa_nontanah_smst1, 0) + 
      COALESCE(c.sa_tanah_tw3, 0) + 
      COALESCE(c.sa_kdp_tw3, 0) + 
      COALESCE(c.sa_nontanah_tw3, 0) + 
      COALESCE(c.sa_tanah, 0) + 
      COALESCE(c.sa_kdp, 0) + 
      COALESCE(c.sa_nontanah, 0) + 
      COALESCE(c.sa_tanah_unaudited, 0) + 
      COALESCE(c.sa_kdp_unaudited, 0) + 
      COALESCE(c.sa_nontanah_unaudited, 0)
  ) - SUM(
      COALESCE(c.nilai_penyusutan_smst1, 0) + 
      COALESCE(c.nilai_penyusutan_tw3, 0) + 
       COALESCE(c.nilai_penyusutan_unaudited , 0) +
        COALESCE(c.nilai_penyusutan_audited , 0) + 
      COALESCE(c.sa_penyusutan_smst1, 0) + 
      COALESCE(c.sa_penyusutan_unaudited, 0) + 
      COALESCE(c.sa_penyusutan_tw3, 0) + 
      COALESCE(c.sa_penyusutan, 0)
  ) AS pemerintah_audited
  FROM v_perolehan_mutasi_lp c
  WHERE c.tahun = $tahun $wh2
  GROUP BY c.id_jenis_pengusahaan,tahun)  c", 'a.id_jenis_pengusahaan = c.id_jenis_pengusahaan AND tr.tahun = c.tahun', 'left');
   
    //   $this->db->join('v_perolehan_mutasi_kewajiban d', 'a.id_jenis_pengusahaan = d.id_jenis_pengusahaan AND tr.tahun = d.tahun' . $wh3, 'left');

      $this->db->join("(select
      d.id_jenis_pengusahaan,
      d.tahun,
     SUM(d.nilai_buku_kewajiban_tw1_2) AS nb_tw1_2,
      SUM(d.nilai_buku_kewajiban_tw3) AS nb_tw3,
      SUM(d.nilai_buku_kewajiban_tw4) AS nb_tw4,
      SUM(d.nilai_buku_kewajiban_audited) AS nb_audited
       from v_perolehan_mutasi_kewajiban d
      where d.tahun=$tahun $wh3
      GROUP BY d.id_jenis_pengusahaan,tahun) d",'a.id_jenis_pengusahaan = d.id_jenis_pengusahaan AND tr.tahun = d.tahun','left');

      $this->db->join("(select
      e.id_jenis_pengusahaan,
      e.tahun,
      SUM(e.nilai_buku_kewajiban_tw1_2) AS nbgp_tw1_2,
      SUM(e.nilai_buku_kewajiban_tw3) AS nbgp_tw3,
      SUM(e.nilai_buku_kewajiban_tw4) AS nbgp_tw4,
      SUM(e.nilai_buku_kewajiban_audited) AS nbgp_audited 
       from v_perolehan_mutasi_kewajiban_gov e
      where e.tahun=$tahun $wh4
      GROUP BY e.id_jenis_pengusahaan,tahun) e",'a.id_jenis_pengusahaan = e.id_jenis_pengusahaan AND tr.tahun = e.tahun','left');
      $this->db->where('tr.tahun', $tahun);
      $this->db->group_by(' a.alias, 
      b.tahun,
      b.total_aset,
      b.mitra_tw3,
      b.mitra_unaudited,
      b.mitra_audited,
      c.pemerintah_smst1,
      c.pemerintah_tw3,
      c.pemerintah_unaudited,
      c.pemerintah_audited,
      d.nb_tw1_2,
      d.nb_tw3,
      d.nb_tw4,
      d.nb_audited,
      e.nbgp_tw1_2,
      e.nbgp_tw3,
      e.nbgp_tw4,
      e.nbgp_audited,
      '
    );
    }

    

      $query = $this->db->get();
      $result = $query->result();
    //  echo $this->db->last_query();
      
      return array(
          'draw' => $requestData['draw'],
          'recordsTotal' => count($result),
          'recordsFiltered' => count($result),
          'data' => $result
      );
  }

}
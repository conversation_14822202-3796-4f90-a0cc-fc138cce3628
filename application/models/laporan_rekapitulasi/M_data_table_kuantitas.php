<?php

class M_data_table extends CI_Model
{
  public function getRecords($requestData)
  {
      $tahun = $requestData['tahun']; // Ambil tahun dari $requestData
      $satker = $requestData['pem'];
              
      $iduser = $this->session->userdata('id_user');
      $idgroup = $this->session->userdata('id_user_group');
      $kode_eselon = $this->session->userdata('kode_eselon_1');

      $a = "";
      $b = "";
      $c = "";
      $d = "";

      if ($idgroup == 3) {
          $a = " AND b.id_user = $iduser";
          $b = " AND c.id_user = $iduser";
          $c = " AND d.id_user = $iduser";
          $d = " AND e.id_user = $iduser";
      } elseif ($idgroup == 4) {
          $a = " AND b.kode_eselon_1 = '$kode_eselon'";
          $b = " AND c.kode_eselon_1 = '$kode_eselon'";
          $c = " AND d.kode_eselon_1 = '$kode_eselon'";
          $d = " AND e.kode_eselon_1 = '$kode_eselon'";
      }

      if (!empty($requestData['eselon'])) {
          $a = " AND b.kode_eselon_1 = '$kode_eselon'";
          $b = " AND c.kode_eselon_1 = '$kode_eselon'";
          $c = " AND d.kode_eselon_1 = '$kode_eselon'";
          $d = " AND e.kode_eselon_1 = '$kode_eselon'";
      }

      if (!empty($requestData['pem'])) {
          $a = " AND b.pemberi_konsesi = '$satker'";
          $b = " AND c.pemberi_konsesi = '$satker'";
          $c = " AND d.pemberi_konsesi = '$satker'";
          $d = " AND e.pemberi_konsesi = '$satker'";
      }

      $this->load->database();
      $this->db->select("
      a.alias,
      tr.tahun,
       SUM(
          COALESCE(b.nilai_tanah_smst1, 0) + 
          COALESCE(b.nilai_kdp_smst1, 0) + 
          COALESCE(b.nilai_nontanah_smst1, 0) +
          COALESCE(b.sa_tanah_smst1 , 0) +
          COALESCE(b.sa_kdp_smst1  , 0) +
          COALESCE(b.sa_nontanah_smst1  , 0)
      ) - SUM(
          COALESCE(b.nilai_akumulasi_penyusutan_smst1, 0) +
          COALESCE(b.sa_akumulasi_smst1  , 0) 
      )  as mitra_smst1,
      SUM(
          COALESCE(b.nilai_tanah_smst1, 0) + 
          COALESCE(b.nilai_kdp_smst1, 0) + 
          COALESCE(b.nilai_nontanah_smst1, 0) +
          COALESCE(b.nilai_tanah_tw3 , 0) + 
          COALESCE(b.nilai_kdp_tw3, 0) + 
          COALESCE(b.nilai_nontanah_tw3, 0) +
          COALESCE(b.sa_tanah_smst1 , 0) +
          COALESCE(b.sa_kdp_smst1  , 0) +
          COALESCE(b.sa_nontanah_smst1  , 0)+
          COALESCE(b.sa_tanah_tw3 , 0) +
          COALESCE(b.sa_kdp_tw3 , 0) +
          COALESCE(b.sa_nontanah_tw3 , 0)
      ) - SUM(
          COALESCE(b.nilai_akumulasi_penyusutan_smst1, 0) +
          COALESCE(b.nilai_akumulasi_penyusutan_tw3, 0) +
          COALESCE(b.sa_akumulasi_smst1  , 0) +
          COALESCE(b.sa_akumulasi_tw3 , 0) 
      ) as mitra_tw3,
      SUM(
          COALESCE(b.nilai_tanah_smst1, 0) + 
          COALESCE(b.nilai_kdp_smst1, 0) + 
          COALESCE(b.nilai_nontanah_smst1, 0) +
          COALESCE(b.nilai_tanah_tw3 , 0) + 
          COALESCE(b.nilai_kdp_tw3, 0) + 
          COALESCE(b.nilai_nontanah_tw3, 0) +
          COALESCE(b.nilai_tanah_smst2 , 0) + 
          COALESCE(b.nilai_kdp_smst2, 0) + 
          COALESCE(b.nilai_nontanah_smst2, 0) +
          COALESCE(b.sa_tanah_smst1 , 0) +
          COALESCE(b.sa_kdp_smst1  , 0) +
          COALESCE(b.sa_nontanah_smst1  , 0)+
          COALESCE(b.sa_tanah_tw3 , 0) +
          COALESCE(b.sa_kdp_tw3 , 0) +
          COALESCE(b.sa_nontanah_tw3 , 0)+
          COALESCE(b.sa_tanah_smst2 , 0) +
          COALESCE(b.sa_kdp_smst2 , 0) +
          COALESCE(b.sa_nontanah_smst2 , 0)
      ) - SUM(
          COALESCE(b.nilai_akumulasi_penyusutan_smst1, 0) +
          COALESCE(b.nilai_akumulasi_penyusutan_tw3, 0) +
          COALESCE(b.nilai_akumulasi_penyusutan_smst2, 0) +
          COALESCE(b.sa_akumulasi_smst1  , 0) +
          COALESCE(b.sa_akumulasi_tw3 , 0) +
          COALESCE(b.sa_akumulasi_smst2 , 0) 
      ) as mitra_unaudited,
      SUM(
          COALESCE(b.nilai_tanah_smst1, 0) + 
          COALESCE(b.nilai_kdp_smst1, 0) + 
          COALESCE(b.nilai_nontanah_smst1, 0) +
          COALESCE(b.nilai_tanah_tw3 , 0) + 
          COALESCE(b.nilai_kdp_tw3, 0) + 
          COALESCE(b.nilai_nontanah_tw3, 0) +
          COALESCE(b.nilai_tanah_smst2 , 0) + 
          COALESCE(b.nilai_kdp_smst2, 0) + 
          COALESCE(b.nilai_nontanah_smst2, 0) +
          COALESCE(b.nilai_tanah_audited , 0) + 
          COALESCE(b.nilai_kdp_audited, 0) + 
          COALESCE(b.nilai_nontanah_audited, 0) +
          COALESCE(b.sa_tanah_smst1 , 0) +
          COALESCE(b.sa_kdp_smst1  , 0) +
          COALESCE(b.sa_nontanah_smst1  , 0)+
          COALESCE(b.sa_tanah_tw3 , 0) +
          COALESCE(b.sa_kdp_tw3 , 0) +
          COALESCE(b.sa_nontanah_tw3 , 0)+
          COALESCE(b.sa_tanah_smst2 , 0) +
          COALESCE(b.sa_kdp_smst2 , 0) +
          COALESCE(b.sa_nontanah_smst2 , 0)+
          COALESCE(b.sa_tanah , 0) +
          COALESCE(b.sa_kdp , 0) +
          COALESCE(b.sa_nontanah , 0)
      ) - SUM(
          COALESCE(b.nilai_akumulasi_penyusutan_smst1, 0) +
          COALESCE(b.nilai_akumulasi_penyusutan_tw3, 0) +
          COALESCE(b.nilai_akumulasi_penyusutan_smst2, 0) +
          COALESCE(b.nilai_akumulasi_penyusutan_audited, 0) +
           COALESCE(b.sa_akumulasi_smst1  , 0) +
          COALESCE(b.sa_akumulasi_tw3 , 0) +
          COALESCE(b.sa_akumulasi_smst2 , 0)+ 
          COALESCE(b.sa_akumulasi , 0) 
      ) as mitra_audited,
       SUM(
          COALESCE(c.nilai_tanah_smst1, 0) + 
          COALESCE(c.nilai_kdp_smst1, 0) + 
          COALESCE(c.nilai_nontanah_smst1, 0) +
          COALESCE(c.sa_tanah_smst1 , 0) +
          COALESCE(c.sa_kdp_smst1 , 0) +
          COALESCE(c.sa_nontanah_smst1 , 0)
      ) - SUM(
          COALESCE(c.nilai_penyusutan_smst1, 0) +
          COALESCE(c.sa_penyusutan_smst1 , 0) 
      )  as pemerintah_smst1,
      SUM(
          COALESCE(c.nilai_tanah_smst1, 0) + 
          COALESCE(c.nilai_kdp_smst1, 0) + 
          COALESCE(c.nilai_nontanah_smst1, 0) +
          COALESCE(c.nilai_tanah_tw3 , 0) + 
          COALESCE(c.nilai_kdp_tw3, 0) + 
          COALESCE(c.nilai_nontanah_tw3, 0) +
          COALESCE(c.sa_tanah_smst1 , 0) +
          COALESCE(c.sa_kdp_smst1 , 0) +
          COALESCE(c.sa_nontanah_smst1 , 0)+
          COALESCE(c.sa_tanah_tw3 , 0) +
          COALESCE(c.sa_kdp_tw3 , 0) +
          COALESCE(c.sa_nontanah_tw3 , 0)
      ) - SUM(
          COALESCE(c.nilai_penyusutan_smst1, 0) +
          COALESCE(c.nilai_penyusutan_tw3, 0) +
          COALESCE(c.sa_penyusutan_smst1 , 0) +
          COALESCE(c.sa_penyusutan_tw3 , 0) 
      ) as pemerintah_tw3,
      SUM(
          COALESCE(c.nilai_tanah_smst1, 0) + 
          COALESCE(c.nilai_kdp_smst1, 0) + 
          COALESCE(c.nilai_nontanah_smst1, 0) +
          COALESCE(c.nilai_tanah_tw3 , 0) + 
          COALESCE(c.nilai_kdp_tw3, 0) + 
          COALESCE(c.nilai_nontanah_tw3, 0) +
          COALESCE(c.nilai_tanah_unaudited , 0) + 
          COALESCE(c.nilai_kdp_unaudited, 0) + 
          COALESCE(c.nilai_nontanah_unaudited, 0) +
          COALESCE(c.sa_tanah_smst1 , 0) +
          COALESCE(c.sa_kdp_smst1 , 0) +
          COALESCE(c.sa_nontanah_smst1 , 0)+
          COALESCE(c.sa_tanah_tw3 , 0) +
          COALESCE(c.sa_kdp_tw3 , 0) +
          COALESCE(c.sa_nontanah_tw3 , 0)+
          COALESCE(c.sa_tanah_unaudited , 0) +
          COALESCE(c.sa_kdp_unaudited , 0) +
          COALESCE(c.sa_nontanah_unaudited , 0)
      ) - SUM(
          COALESCE(c.nilai_penyusutan_smst1, 0) +
          COALESCE(c.nilai_penyusutan_tw3, 0) +
          COALESCE(c.nilai_penyusutan_unaudited, 0) +
           COALESCE(c.sa_penyusutan_smst1 , 0) +
          COALESCE(c.sa_penyusutan_tw3 , 0) +
          COALESCE(c.sa_penyusutan_unaudited , 0) 
      ) as pemerintah_unaudited,
      SUM(
          COALESCE(c.nilai_tanah_smst1, 0) + 
          COALESCE(c.nilai_kdp_smst1, 0) + 
          COALESCE(c.nilai_nontanah_smst1, 0) +
          COALESCE(c.nilai_tanah_tw3 , 0) + 
          COALESCE(c.nilai_kdp_tw3, 0) + 
          COALESCE(c.nilai_nontanah_tw3, 0) +
          COALESCE(c.nilai_tanah_unaudited , 0) + 
          COALESCE(c.nilai_kdp_unaudited, 0) + 
          COALESCE(c.nilai_nontanah_unaudited, 0) +
          COALESCE(c.nilai_tanah_audited , 0) + 
          COALESCE(c.nilai_kdp_audited, 0) + 
          COALESCE(c.nilai_nontanah_audited, 0) +
          COALESCE(c.sa_tanah_smst1 , 0) +
          COALESCE(c.sa_kdp_smst1 , 0) +
          COALESCE(c.sa_nontanah_smst1 , 0)+
          COALESCE(c.sa_tanah_tw3 , 0) +
          COALESCE(c.sa_kdp_tw3 , 0) +
          COALESCE(c.sa_nontanah_tw3 , 0)+
          COALESCE(c.sa_tanah_unaudited , 0) +
          COALESCE(c.sa_kdp_unaudited , 0) +
          COALESCE(c.sa_nontanah_unaudited , 0)+
          COALESCE(c.sa_tanah , 0) +
          COALESCE(c.sa_kdp , 0) +
          COALESCE(c.sa_nontanah , 0)
      ) - SUM(
          COALESCE(c.nilai_penyusutan_smst1, 0) +
          COALESCE(c.nilai_penyusutan_tw3, 0) +
          COALESCE(c.nilai_penyusutan_unaudited, 0) +
          COALESCE(c.nilai_penyusutan_audited, 0) +
           COALESCE(c.sa_penyusutan_smst1 , 0) +
          COALESCE(c.sa_penyusutan_tw3 , 0) +
          COALESCE(c.sa_penyusutan_unaudited , 0) +
          COALESCE(c.sa_penyusutan , 0) 
      ) as pemerintah_audited,
      sum(d.nilai_buku_kewajiban_tw1_2) as nb_tw1_2,
      sum(d.nilai_buku_kewajiban_tw3) as nb_tw3,
      sum(d.nilai_buku_kewajiban_tw4) as nb_tw4,
      sum(d.nilai_buku_kewajiban_audited) as nb_audited,
       sum(e.nilai_buku_kewajiban_tw1_2) as nbgp_tw1_2,
      sum(e.nilai_buku_kewajiban_tw3) as nbgp_tw3,
      sum(e.nilai_buku_kewajiban_tw4) as nbgp_tw4,
      sum(e.nilai_buku_kewajiban_audited) as nbgp_audited
      ");
      $this->db->from('(SELECT 2022 AS tahun) tr');
      $this->db->join('ref_jenis_pengusahaan a', '1=1', 'left');
  

      $this->db->join("v_perolehan_mutasi b","
      ON a.id_jenis_pengusahaan = b.id_jenis_pengusahaan
      AND tr.tahun = b.tahun",'left' );

      $this->db->join("v_perolehan_mutasi_lp c","
      ON a.id_jenis_pengusahaan = c.id_jenis_pengusahaan
      AND tr.tahun = c.tahun",'left' );
      
      $this->db->join("v_perolehan_mutasi_kewajiban d ","
      ON a.id_jenis_pengusahaan = d.id_jenis_pengusahaan
      AND tr.tahun = d.tahun",'left' );

      $this->db->join("v_perolehan_mutasi_kewajiban_gov e","
      ON a.id_jenis_pengusahaan = e.id_jenis_pengusahaan
      AND tr.tahun = e.tahun",'left' );

      $this->db->group_by('a.alias, tr.tahun');

      $query = $this->db->get();
      $result = $query->result();
      
      return array(
          'draw' => $requestData['draw'],
          'recordsTotal' => count($result),
          'recordsFiltered' => count($result),
          'data' => $result
      );
  }

}
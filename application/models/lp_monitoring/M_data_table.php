<?php
class M_data_table extends CI_Model
{
  public function getRecords($tab = '', $requestData = '')
  {
    $tab = 'v_perolehan_mutasi';
    $this->load->database();
    $iduser = $this->session->userdata('id_user');
    $idgroup = $this->session->userdata('id_user_group');
    $kode_eselon = $this->session->userdata('kode_eselon_1');
    $tahun = isset($requestData['tahun']) ? $requestData['tahun'] : '';

    if ($idgroup == 3) {
      $this->db->where('id_user', $iduser);
    } elseif ($idgroup == 4) {
      $this->db->where('kode_eselon_1', $kode_eselon);
    }

    if (!empty($tahun)) {
      $this->db->where('tahun', $tahun);
    }
    if (!empty($requestData['nama'])) {
      $this->db->where('nm_perjanjian', $requestData['nama']);
    }
    if (!empty($requestData['badan'])) {
      $this->db->where('id_badan_usaha', $requestData['badan']);
    }
    if (!empty($requestData['eselon'])) {
      $this->db->where('kode_eselon_1', $requestData['eselon']);
    }
    if (!empty($requestData['pem'])) {
      $this->db->where('pemberi_konsesi', $requestData['pem']);
    }
    if (!empty($requestData['pulau'])) {
      $this->db->where('id_pulau', $requestData['pulau']);
    }
    if (!empty($requestData['jenis'])) {
      $this->db->where('id_jenis_pengusahaan', $requestData['jenis']);
    }

    $this->db->select("
    'Monitoring mitra yang belum menyampaikan Laporan Keuangan' as jenis_file,
    'lk' as file,
    COUNT(DISTINCT pemberi_konsesi) as jumlah_satker,
    SUM(
        CASE 
            WHEN periode_pembukuan = 'Semester I' THEN 
                IF(file_lk IS NULL OR file_lk = '', 1, 0)
            WHEN periode_pembukuan = 'Triwulan III' THEN 
                IF(file_lk IS NULL OR file_lk = '', 1, 0) + 
                IF(file_lk_tw3 IS NULL OR file_lk_tw3 = '', 1, 0)
            WHEN periode_pembukuan = 'Unaudited' THEN 
                IF(file_lk IS NULL OR file_lk = '', 1, 0) + 
                IF(file_lk_tw3 IS NULL OR file_lk_tw3 = '', 1, 0) + 
                IF(file_lk_smst2 IS NULL OR file_lk_smst2 = '', 1, 0)
            WHEN periode_pembukuan = 'Audited' THEN 
                IF(file_lk IS NULL OR file_lk = '', 1, 0) + 
                IF(file_lk_tw3 IS NULL OR file_lk_tw3 = '', 1, 0) + 
                IF(file_lk_smst2 IS NULL OR file_lk_smst2 = '', 1, 0) + 
                IF(file_lk_audited IS NULL OR file_lk_audited = '', 1, 0)
        END
    ) AS jumlah_file_kosong,
    COUNT(CASE WHEN file_lk IS NULL OR file_lk = '' THEN 1 END) as jumlah_kosong_lk,
    COUNT(CASE WHEN file_lk_smst2 IS NULL OR file_lk_smst2 = '' THEN 1 END) as jumlah_kosong_lk_smst2,
    COUNT(CASE WHEN file_lk_tw3 IS NULL OR file_lk_tw3 = '' THEN 1 END) as jumlah_kosong_lk_tw3,
    COUNT(CASE WHEN file_lk_audited IS NULL OR file_lk_audited = '' THEN 1 END) as jumlah_kosong_lk_audited
    ");
    $this->db->from($tab);
    $this->db->where('tahun', $tahun);

    $this->db->group_start();
    $tableFields = $this->db->list_fields($tab);
    $searchValue = isset($requestData['search']['value']) ? $requestData['search']['value'] : '';

    foreach ($tableFields as $field) {
      $this->db->or_like("CONVERT($field, CHAR)", $searchValue);
    }
    $this->db->group_end();

    $query1 = $this->db->get();

    $this->db->select("
    'Monitoring mitra yang belum menyampaikan BA rekon' AS jenis_file,
    'ba' AS file,
    COUNT(DISTINCT pemberi_konsesi) AS jumlah_satker,
    COUNT(CASE WHEN file_ba_rekonsiliasi IS NULL OR file_ba_rekonsiliasi = '' THEN 1 END) as jumlah_kosong_lk,
    COUNT(CASE WHEN file_ba_rekonsiliasi_tw3 IS NULL OR file_ba_rekonsiliasi_tw3 = '' THEN 1 END) as jumlah_kosong_lk_tw3,
    SUM(
        CASE 
            WHEN periode_pembukuan = 'Semester I' THEN 
                IF(file_ba_rekonsiliasi IS NULL OR file_ba_rekonsiliasi = '', 1, 0)
            WHEN periode_pembukuan = 'Triwulan III' THEN 
                IF(file_ba_rekonsiliasi IS NULL OR file_ba_rekonsiliasi = '', 1, 0) + 
                IF(file_ba_rekonsiliasi_tw3 IS NULL OR file_ba_rekonsiliasi_tw3 = '', 1, 0)
            WHEN periode_pembukuan = 'Unaudited' THEN 
                IF(file_ba_rekonsiliasi IS NULL OR file_ba_rekonsiliasi = '', 1, 0) + 
                IF(file_ba_rekonsiliasi_tw3 IS NULL OR file_ba_rekonsiliasi_tw3 = '', 1, 0) + 
                IF(file_ba_rekonsiliasi_smst2 IS NULL OR file_ba_rekonsiliasi_smst2 = '', 1, 0)
            WHEN periode_pembukuan = 'Audited' THEN 
                IF(file_ba_rekonsiliasi IS NULL OR file_ba_rekonsiliasi = '', 1, 0) + 
                IF(file_ba_rekonsiliasi_tw3 IS NULL OR file_ba_rekonsiliasi_tw3 = '', 1, 0) + 
                IF(file_ba_rekonsiliasi_smst2 IS NULL OR file_ba_rekonsiliasi_smst2 = '', 1, 0) + 
                IF(file_ba_rekonsiliasi_audited IS NULL OR file_ba_rekonsiliasi_audited = '', 1, 0)
        END
    ) AS jumlah_file_kosong
    ");
    $this->db->from($tab);
    $this->db->where('tahun', $tahun);

    $this->db->group_start();
    $searchValue = isset($requestData['search']['value']) ? $requestData['search']['value'] : '';

    foreach ($tableFields as $field) {
      $this->db->or_like("CAST($field AS CHAR)", $searchValue);
    }
    $this->db->group_end();

    $query2 = $this->db->get();

    $result = array_merge($query1->result_array(), $query2->result_array());

    return array(
      'draw' => isset($requestData['draw']) ? $requestData['draw'] : 1,
      'recordsTotal' => count($result),
      'recordsFiltered' => count($result),
      'data' => $result
    );
  }
}
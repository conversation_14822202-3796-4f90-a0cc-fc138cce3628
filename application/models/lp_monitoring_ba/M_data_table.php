<?php
class M_data_table extends CI_Model
{


  public function getRecords($tab, $requestData)
  {
    $this->load->database();
    $this->db->from($tab);
    $iduser = $this->session->userdata('id_user');
    $idgroup = $this->session->userdata('id_user_group');
    $kode_eselon = $this->session->userdata('kode_eselon_1');
    if ($idgroup == 3) {
      $this->db->where('id_user', $iduser);

    } else if ($idgroup == 4) {
      $this->db->where('kode_eselon_1', $kode_eselon);

    }
    if (!empty($requestData['tahun'])) {
      $this->db->where('tahun', $requestData['tahun']);
    }
    if (!empty($requestData['nama'])) {
      // echo $requestData['nama'];
      $this->db->where('nm_perjanjian', $requestData['nama']);
    }
    // echo $requestData['badan'];
    // echo "Asd";
    if (!empty($requestData['badan'])) {

      $this->db->where('id_badan_usaha', $requestData['badan']);
    }
    if (!empty($requestData['eselon'])) {
      $this->db->where('kode_eselon_1', $requestData['eselon']);
    }
    if (!empty($requestData['pem'])) {
      $this->db->where('pemberi_konsesi', $requestData['pem']);
    }
    if (!empty($requestData['pulau'])) {
      $this->db->where('id_pulau', $requestData['pulau']);
    }
    if (!empty($requestData['jenis'])) {
      // echo $requestData['jenis'];
      $this->db->where('id_jenis_pengusahaan', $requestData['jenis']);
    }
    $tableFields = $this->db->list_fields($tab);

    if (!empty($requestData['search']['value'])) {
      $searchValue = $requestData['search']['value'];
      $this->db->group_start();

      foreach ($tableFields as $field) {
        $this->db->or_like("CONVERT($field, CHAR)", $requestData['search']['value']);
      }

      $this->db->group_end();
    }

    $totalRecords = $this->db->count_all_results('', false);

    if (!empty($requestData['order'])) {
      $orderByColumn = $tableFields[$requestData['order'][0]['column']];
      $orderDir = $requestData['order'][0]['dir'];
      $this->db->order_by($orderByColumn, $orderDir);
    }

    // $start = $requestData['start'];
    // $length = $requestData['length'];
    // $this->db->limit($length, $start);

    $query = $this->db->get();
    $result = $query->result();
    $datax = array_filter($result, function ($dt) {
      if ($dt->periode_pembukuan === 'Semester I') {
        return $dt->kategori === 'Semester I';
      } elseif ($dt->periode_pembukuan === 'Triwulan III') {
        return $dt->kategori === 'Semester I' || $dt->kategori === 'Triwulan III';
      } elseif ($dt->periode_pembukuan === 'Unaudited') {
        return $dt->kategori === 'Semester I' || $dt->kategori === 'Triwulan III' || $dt->kategori === 'Unaudited';
      } elseif ($dt->periode_pembukuan === 'Audited') {
        return $dt->kategori === 'Semester I' || $dt->kategori === 'Triwulan III' || $dt->kategori === 'Unaudited' || $dt->kategori === 'Audited';
      }
      return false;
    });
    $totalRecords = count($datax);
    $start = $requestData['start'];
    $length = $requestData['length'];
    $datax = array_slice($datax, $start, $length);
    return array(
      'draw' => $requestData['draw'],
      'recordsTotal' => $totalRecords,
      'recordsFiltered' => $totalRecords,
      'data' => array_values($datax)
    );
  }
}
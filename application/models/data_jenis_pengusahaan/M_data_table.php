<?php
class M_data_table extends CI_Model
{


  public function getRecords($tab, $requestData)
  {
    $this->load->database();
    $this->db->from($tab);
    if (!empty($requestData['tahun'])) {
      $this->db->where('tahun_anggaran', $requestData['tahun']);
    }
    if (!empty($requestData['semester'])) {
      $this->db->where('semester', $requestData['semester']);
    }

    $tableFields = $this->db->list_fields($tab);

    if (!empty($requestData['search']['value'])) {
      $searchValue = $requestData['search']['value'];
      $this->db->group_start();

      foreach ($tableFields as $field) {
        $this->db->or_like("CONVERT($field, CHAR)", $requestData['search']['value']);
      }

      $this->db->group_end();
    }

    $totalRecords = $this->db->count_all_results('', false);

    if (!empty($requestData['order'])) {
      $orderByColumn = $tableFields[$requestData['order'][0]['column']];
      $orderDir = $requestData['order'][0]['dir'];
      $this->db->order_by($orderByColumn, $orderDir);
    }

    $start = $requestData['start'];
    $length = $requestData['length'];
    $this->db->limit($length, $start);

    $query = $this->db->get();
    $result = $query->result();

    return array(
      'draw' => $requestData['draw'],
      'recordsTotal' => $totalRecords,
      'recordsFiltered' => $totalRecords,
      'data' => $result
    );
  }
}
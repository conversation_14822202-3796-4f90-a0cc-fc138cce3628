<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes with
| underscores in the controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'welcome';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

# Login
$route['login'] = 'login/login';
$route['login/(:any)'] = 'login/login/$1';

# Perjanjian Konsesi Jasa
$route['perjanjian_konsesijasa'] = 'perjanjian_konsesijasa/Perjanjian_konsesijasa/page';
$route['perjanjian_konsesijasa/(:any)'] = 'perjanjian_konsesijasa/Perjanjian_konsesijasa/$1';

# SLO (Surat Layak Operasi)
$route['slo'] = 'slo/Slo/page';
$route['slo/tampildata/(.+)'] = 'slo/Slo/tampildata/$1';
$route['slo/(:any)'] = 'slo/Slo/$1';

# Saldo Awal
$route['saldo_awal'] = 'saldo_awal/Saldo_awal/page';
$route['saldo_awal/tampildata/(.+)'] = 'saldo_awal/Saldo_awal/tampildata/$1';
$route['saldo_awal/(:any)'] = 'saldo_awal/Saldo_awal/$1';

# Saldo Awal LP
$route['saldo_awal_lp'] = 'saldo_awal_lp/Saldo_awal_lp/page';
$route['saldo_awal_lp/tampildata/(.+)'] = 'saldo_awal_lp/Saldo_awal_lp/tampildata/$1';
$route['saldo_awal_lp/(:any)'] = 'saldo_awal_lp/Saldo_awal_lp/$1';

# Perolehan Mutasi LP
$route['perolehan_mutasi_lp'] = 'perolehan_mutasi_lp/Perolehan_mutasi_lp/page';
$route['perolehan_mutasi_lp/tampildata/(.+)'] = 'perolehan_mutasi_lp/Perolehan_mutasi_lp/tampildata/$1';
$route['perolehan_mutasi_lp/(:any)'] = 'perolehan_mutasi_lp/Perolehan_mutasi_lp/$1';

# Perolehan Mutasi Kuantitas
$route['perolehan_mutasi_kuantitas'] = 'perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas/page';
$route['perolehan_mutasi_kuantitas/tampildata/(.+)'] = 'perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas/tampildata/$1';
$route['perolehan_mutasi_kuantitas/(:any)'] = 'perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas/$1';

# Saldo Awal Kuantitas
$route['saldo_awal_kuantitas'] = 'saldo_awal_kuantitas/Saldo_awal_kuantitas/page';
$route['saldo_awal_kuantitas/tampildata/(.+)'] = 'saldo_awal_kuantitas/Saldo_awal_kuantitas/tampildata/$1';
$route['saldo_awal_kuantitas/(:any)'] = 'saldo_awal_kuantitas/Saldo_awal_kuantitas/$1';

# Saldo Awal Kuantitas Gov
$route['saldo_awal_kuantitas_gov'] = 'saldo_awal_kuantitas_gov/Saldo_awal_kuantitas_gov/page';
$route['saldo_awal_kuantitas_gov/tampildata/(.+)'] = 'saldo_awal_kuantitas_gov/Saldo_awal_kuantitas_gov/tampildata/$1';
$route['saldo_awal_kuantitas_gov/(:any)'] = 'saldo_awal_kuantitas_gov/Saldo_awal_kuantitas_gov/$1';

# Saldo Awal Kewajiban
$route['saldo_awal_kewajiban'] = 'saldo_awal_kewajiban/Saldo_awal_kewajiban/page';
$route['saldo_awal_kewajiban/tampildata/(.+)'] = 'saldo_awal_kewajiban/Saldo_awal_kewajiban/tampildata/$1';
$route['saldo_awal_kewajiban/(:any)'] = 'saldo_awal_kewajiban/Saldo_awal_kewajiban/$1';

# Perolehan Mutasi Kuantitas Gov
$route['perolehan_mutasi_kuantitas_gov'] = 'perolehan_mutasi_kuantitas_gov/Perolehan_mutasi_kuantitas_gov/page';
$route['perolehan_mutasi_kuantitas_gov/tampildata/(.+)'] = 'perolehan_mutasi_kuantitas_gov/Perolehan_mutasi_kuantitas_gov/tampildata/$1';
$route['perolehan_mutasi_kuantitas_gov/(:any)'] = 'perolehan_mutasi_kuantitas_gov/Perolehan_mutasi_kuantitas_gov/$1';

# Perolehan Mutasi Kewajiban
$route['perolehan_mutasi_kewajiban'] = 'perolehan_mutasi_kewajiban/Perolehan_mutasi_kewajiban/index';
$route['perolehan_mutasi_kewajiban/tampildata/(.+)'] = 'perolehan_mutasi_kewajiban/Perolehan_mutasi_kewajiban/tampildata/$1';
$route['perolehan_mutasi_kewajiban/(:any)'] = 'perolehan_mutasi_kewajiban/Perolehan_mutasi_kewajiban/$1';

# Perolehan Mutasi Kewajiban Gov
$route['perolehan_mutasi_kewajiban_gov'] = 'perolehan_mutasi_kewajiban_gov/Perolehan_mutasi_kewajiban_gov/index';
$route['perolehan_mutasi_kewajiban_gov/tampildata/(.+)'] = 'perolehan_mutasi_kewajiban_gov/Perolehan_mutasi_kewajiban_gov/tampildata/$1';
$route['perolehan_mutasi_kewajiban_gov/(:any)'] = 'perolehan_mutasi_kewajiban_gov/Perolehan_mutasi_kewajiban_gov/$1';

# Amortisasi
$route['amortisasi'] = 'amortisasi/Amortisasi/index';
$route['amortisasi/tampildata/(.+)'] = 'amortisasi/Amortisasi/tampildata/$1';
$route['amortisasi/(:any)'] = 'amortisasi/Amortisasi/$1';

# Data Users
$route['data_users'] = 'data_users/Data_users/index';
$route['data_users/tampildata/(.+)'] = 'data_users/Data_users/tampildata/$1';
$route['data_users/(:any)'] = 'data_users/Data_users/$1';

# Data Pulau
$route['data_pulau'] = 'data_pulau/Data_pulau/index';
$route['data_pulau/tampildata/(.+)'] = 'data_pulau/Data_pulau/tampildata/$1';
$route['data_pulau/(:any)'] = 'data_pulau/Data_pulau/$1';

# Data Aset Mitra
$route['data_aset_mitra'] = 'data_aset_mitra/Data_aset_mitra/index';
$route['data_aset_mitra/tampildata/(.+)'] = 'data_aset_mitra/Data_aset_mitra/tampildata/$1';
$route['data_aset_mitra/(:any)'] = 'data_aset_mitra/Data_aset_mitra/$1';

# Data Aset Pemerintah
$route['data_aset_pemerintah'] = 'data_aset_pemerintah/Data_aset_pemerintah/index';
$route['data_aset_pemerintah/tampildata/(.+)'] = 'data_aset_pemerintah/Data_aset_pemerintah/tampildata/$1';
$route['data_aset_pemerintah/(:any)'] = 'data_aset_pemerintah/Data_aset_pemerintah/$1';

# Data Badan Usaha
$route['data_badan_usaha'] = 'data_badan_usaha/Data_badan_usaha/index';
$route['data_badan_usaha/tampildata/(.+)'] = 'data_badan_usaha/Data_badan_usaha/tampildata/$1';
$route['data_badan_usaha/(:any)'] = 'data_badan_usaha/Data_badan_usaha/$1';

# Data Eselon 1
$route['data_eselon_1'] = 'data_eselon_1/Data_eselon_1/index';
$route['data_eselon_1/tampildata/(.+)'] = 'data_eselon_1/Data_eselon_1/tampildata/$1';
$route['data_eselon_1/(:any)'] = 'data_eselon_1/Data_eselon_1/$1';

# Data Satker
$route['data_satker'] = 'data_satker/Data_satker/index';
$route['data_satker/tampildata/(.+)'] = 'data_satker/Data_satker/tampildata/$1';
$route['data_satker/(:any)'] = 'data_satker/Data_satker/$1';

# Rincian Mutasi
$route['rincian_mutasi'] = 'rincian_mutasi/Rincian_mutasi/index';
$route['rincian_mutasi/ssp/(.+)'] = 'rincian_mutasi/Rincian_mutasi/ssp/$1';
$route['rincian_mutasi/ssp'] = 'rincian_mutasi/Rincian_mutasi/ssp';
$route['rincian_mutasi/tampildata/(.+)'] = 'rincian_mutasi/Rincian_mutasi/tampildata/$1';
$route['rincian_mutasi/(:any)'] = 'rincian_mutasi/Rincian_mutasi/$1';

# Laporan
$route['laporan'] = 'laporan/Laporan/index';
$route['laporan/ssp/(.+)'] = 'laporan/Laporan/ssp/$1';
$route['laporan/ssp'] = 'laporan/Laporan/ssp';
$route['laporan/tampildata/(.+)'] = 'laporan/Laporan/tampildata/$1';
$route['laporan/(:any)'] = 'laporan/Laporan/$1';

# LP Monitoring
$route['lp_monitoring'] = 'lp_monitoring/Lp_monitoring/index';
$route['lp_monitoring/ssp/(.+)'] = 'lp_monitoring/Lp_monitoring/ssp/$1';
$route['lp_monitoring/tampildata/(.+)'] = 'lp_monitoring/Lp_monitoring/tampildata/$1';
$route['lp_monitoring/(:any)'] = 'lp_monitoring/Lp_monitoring/$1';

# LP Monitoring LK
$route['lp_monitoring_lk'] = 'lp_monitoring_lk/Lp_monitoring_lk/index';
$route['lp_monitoring_lk/ssp/(.+)'] = 'lp_monitoring_lk/Lp_monitoring_lk/ssp/$1';
$route['lp_monitoring_lk/ssp'] = 'lp_monitoring_lk/Lp_monitoring_lk/ssp';
$route['lp_monitoring_lk/tampildata/(.+)'] = 'lp_monitoring_lk/Lp_monitoring_lk/tampildata/$1';
$route['lp_monitoring_lk/(:any)'] = 'lp_monitoring_lk/Lp_monitoring_lk/$1';

# LP Monitoring BA
$route['lp_monitoring_ba'] = 'lp_monitoring_ba/Lp_monitoring_ba/index';
$route['lp_monitoring_ba/ssp/(.+)'] = 'lp_monitoring_ba/Lp_monitoring_ba/ssp/$1';
$route['lp_monitoring_ba/ssp'] = 'lp_monitoring_ba/Lp_monitoring_ba/ssp';
$route['lp_monitoring_ba/tampildata/(.+)'] = 'lp_monitoring_ba/Lp_monitoring_ba/tampildata/$1';
$route['lp_monitoring_ba/(:any)'] = 'lp_monitoring_ba/Lp_monitoring_ba/$1';

# Data Users
$route['data_users'] = 'data_users/Data_users/index';
$route['data_users/ssp/(.+)'] = 'data_users/Data_users/ssp/$1';
$route['data_users/ssp'] = 'data_users/Data_users/ssp';
$route['data_users/tampildata/(.+)'] = 'data_users/Data_users/tampildata/$1';
$route['data_users/(:any)'] = 'data_users/Data_users/$1';

# Data Pulau
$route['data_pulau'] = 'data_pulau/Data_pulau/index';
$route['data_pulau/ssp/(.+)'] = 'data_pulau/Data_pulau/ssp/$1';
$route['data_pulau/ssp'] = 'data_pulau/Data_pulau/ssp';
$route['data_pulau/tampildata/(.+)'] = 'data_pulau/Data_pulau/tampildata/$1';
$route['data_pulau/(:any)'] = 'data_pulau/Data_pulau/$1';

# Data Aset Mitra
$route['data_aset_mitra'] = 'data_aset_mitra/Data_aset_mitra/index';
$route['data_aset_mitra/ssp/(.+)'] = 'data_aset_mitra/Data_aset_mitra/ssp/$1';
$route['data_aset_mitra/ssp'] = 'data_aset_mitra/Data_aset_mitra/ssp';
$route['data_aset_mitra/tampildata/(.+)'] = 'data_aset_mitra/Data_aset_mitra/tampildata/$1';
$route['data_aset_mitra/(:any)'] = 'data_aset_mitra/Data_aset_mitra/$1';

# Data Aset Pemerintah
$route['data_aset_pemerintah'] = 'data_aset_pemerintah/Data_aset_pemerintah/index';
$route['data_aset_pemerintah/ssp/(.+)'] = 'data_aset_pemerintah/Data_aset_pemerintah/ssp/$1';
$route['data_aset_pemerintah/ssp'] = 'data_aset_pemerintah/Data_aset_pemerintah/ssp';
$route['data_aset_pemerintah/tampildata/(.+)'] = 'data_aset_pemerintah/Data_aset_pemerintah/tampildata/$1';
$route['data_aset_pemerintah/(:any)'] = 'data_aset_pemerintah/Data_aset_pemerintah/$1';

# Data Badan Usaha
$route['data_badan_usaha'] = 'data_badan_usaha/Data_badan_usaha/index';
$route['data_badan_usaha/ssp/(.+)'] = 'data_badan_usaha/Data_badan_usaha/ssp/$1';
$route['data_badan_usaha/ssp'] = 'data_badan_usaha/Data_badan_usaha/ssp';
$route['data_badan_usaha/tampildata/(.+)'] = 'data_badan_usaha/Data_badan_usaha/tampildata/$1';
$route['data_badan_usaha/(:any)'] = 'data_badan_usaha/Data_badan_usaha/$1';

# Data Eselon 1
$route['data_eselon_1'] = 'data_eselon_1/Data_eselon_1/index';
$route['data_eselon_1/ssp/(.+)'] = 'data_eselon_1/Data_eselon_1/ssp/$1';
$route['data_eselon_1/ssp'] = 'data_eselon_1/Data_eselon_1/ssp';
$route['data_eselon_1/tampildata/(.+)'] = 'data_eselon_1/Data_eselon_1/tampildata/$1';
$route['data_eselon_1/(:any)'] = 'data_eselon_1/Data_eselon_1/$1';

# Saldo Awal Kewajiban Gov
$route['saldo_awal_kewajiban_gov'] = 'saldo_awal_kewajiban_gov/Saldo_awal_kewajiban_gov/index';
$route['saldo_awal_kewajiban_gov/tampildata/(.+)'] = 'saldo_awal_kewajiban_gov/Saldo_awal_kewajiban_gov/tampildata/$1';
$route['saldo_awal_kewajiban_gov/(:any)'] = 'saldo_awal_kewajiban_gov/Saldo_awal_kewajiban_gov/$1';

# Perolehan Mutasi Kewajiban Gov
$route['perolehan_mutasi_kewajiban_gov'] = 'perolehan_mutasi_kewajiban_gov/Perolehan_mutasi_kewajiban_gov/index';
$route['perolehan_mutasi_kewajiban_gov/tampildata/(.+)'] = 'perolehan_mutasi_kewajiban_gov/Perolehan_mutasi_kewajiban_gov/tampildata/$1';
$route['perolehan_mutasi_kewajiban_gov/(:any)'] = 'perolehan_mutasi_kewajiban_gov/Perolehan_mutasi_kewajiban_gov/$1';

# Tarif
$route['tarif'] = 'tarif/Tarif';
$route['tarif/tampildata/(.+)'] = 'tarif/Tarif/tampildata/$1';
$route['tarif/(:any)'] = 'tarif/Tarif/$1';

# Perolehan Mutasi
$route['perolehan_mutasi'] = 'perolehan_mutasi/Perolehan_mutasi';
$route['perolehan_mutasi/tampildata/(.+)'] = 'perolehan_mutasi/Perolehan_mutasi/tampildata/$1';
$route['perolehan_mutasi/(:any)'] = 'perolehan_mutasi/Perolehan_mutasi/$1';

# AJAX Content Loading
$route['ajax_content/(:any)'] = 'ajax_content/$1';

# Test Dynamic Content
$route['test-dynamic'] = 'ajax_content/load_view/test_dynamic_content';

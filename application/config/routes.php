<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes with
| underscores in the controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'welcome';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

# Login
$route['login'] = 'login/login';
$route['login/(:any)'] = 'login/login/$1';

# Perjanjian Konsesi Jasa
$route['perjanjian_konsesijasa'] = 'perjanjian_konsesijasa/Perjanjian_konsesijasa/page';
$route['perjanjian_konsesijasa/(:any)'] = 'perjanjian_konsesijasa/Perjanjian_konsesijasa/$1';

# SLO (Surat Layak Operasi)
$route['slo'] = 'slo/Slo/page';
$route['slo/tampildata/(.+)'] = 'slo/Slo/tampildata/$1';
$route['slo/(:any)'] = 'slo/Slo/$1';

# Saldo Awal
$route['saldo_awal'] = 'saldo_awal/Saldo_awal/page';
$route['saldo_awal/tampildata/(.+)'] = 'saldo_awal/Saldo_awal/tampildata/$1';
$route['saldo_awal/(:any)'] = 'saldo_awal/Saldo_awal/$1';

# Saldo Awal LP
$route['saldo_awal_lp'] = 'saldo_awal_lp/Saldo_awal_lp/page';
$route['saldo_awal_lp/tampildata/(.+)'] = 'saldo_awal_lp/Saldo_awal_lp/tampildata/$1';
$route['saldo_awal_lp/(:any)'] = 'saldo_awal_lp/Saldo_awal_lp/$1';

# Perolehan Mutasi LP
$route['perolehan_mutasi_lp'] = 'perolehan_mutasi_lp/Perolehan_mutasi_lp/page';
$route['perolehan_mutasi_lp/tampildata/(.+)'] = 'perolehan_mutasi_lp/Perolehan_mutasi_lp/tampildata/$1';
$route['perolehan_mutasi_lp/(:any)'] = 'perolehan_mutasi_lp/Perolehan_mutasi_lp/$1';

# Perolehan Mutasi Kuantitas
$route['perolehan_mutasi_kuantitas'] = 'perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas/page';
$route['perolehan_mutasi_kuantitas/tampildata/(.+)'] = 'perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas/tampildata/$1';
$route['perolehan_mutasi_kuantitas/(:any)'] = 'perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas/$1';

# Tarif (SK Tarif)
$route['tarif'] = 'tarif/Tarif';
$route['tarif/tampildata/(.+)'] = 'tarif/Tarif/tampildata/$1';
$route['tarif/(:any)'] = 'tarif/Tarif/$1';

# Perolehan Mutasi
$route['perolehan_mutasi'] = 'perolehan_mutasi/Perolehan_mutasi';
$route['perolehan_mutasi/tampildata/(.+)'] = 'perolehan_mutasi/Perolehan_mutasi/tampildata/$1';
$route['perolehan_mutasi/(:any)'] = 'perolehan_mutasi/Perolehan_mutasi/$1';

# Saldo Awal LP
$route['saldo_awal_lp'] = 'saldo_awal_lp/Saldo_awal_lp';
$route['saldo_awal_lp/tampildata/(.+)'] = 'saldo_awal_lp/Saldo_awal_lp/tampildata/$1';
$route['saldo_awal_lp/(:any)'] = 'saldo_awal_lp/Saldo_awal_lp/$1';

# Perolehan Mutasi LP
$route['perolehan_mutasi_lp'] = 'perolehan_mutasi_lp/Perolehan_mutasi_lp';
$route['perolehan_mutasi_lp/tampildata/(.+)'] = 'perolehan_mutasi_lp/Perolehan_mutasi_lp/tampildata/$1';
$route['perolehan_mutasi_lp/(:any)'] = 'perolehan_mutasi_lp/Perolehan_mutasi_lp/$1';

# Perolehan Mutasi Kuantitas
$route['perolehan_mutasi_kuantitas'] = 'perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas';
$route['perolehan_mutasi_kuantitas/tampildata/(.+)'] = 'perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas/tampildata/$1';
$route['perolehan_mutasi_kuantitas/(:any)'] = 'perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas/$1';

# Saldo Awal Kuantitas
$route['saldo_awal_kuantitas'] = 'saldo_awal_kuantitas/Saldo_awal_kuantitas';
$route['saldo_awal_kuantitas/tampildata/(.+)'] = 'saldo_awal_kuantitas/Saldo_awal_kuantitas/tampildata/$1';
$route['saldo_awal_kuantitas/(:any)'] = 'saldo_awal_kuantitas/Saldo_awal_kuantitas/$1';

# AJAX Content Loading
$route['ajax_content/(:any)'] = 'ajax_content/$1';

# Test Dynamic Content
$route['test-dynamic'] = 'ajax_content/load_view/test_dynamic_content';

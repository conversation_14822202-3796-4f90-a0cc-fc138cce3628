<?php
// To use reCAPTCHA, you need to sign up for an API key pair for your site.
// link: http://www.google.com/recaptcha/admin
$config['recaptcha_site_key'] = '6LckVhIeAAAAAIQpb68oNxlCH5aHtmhguRYY-pgU';
$config['recaptcha_secret_key'] = '6LckVhIeAAAAAOBbrca9eTn9k9-QkPyW7us9TXGI';
// reCAPTCHA supported 40+ languages listed here:
// https://developers.google.com/recaptcha/docs/language
$config['recaptcha_lang'] = 'id';
/* End of file recaptcha.php */
/* Location: ./application/config/recaptcha.php */

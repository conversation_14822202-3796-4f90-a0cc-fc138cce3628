<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Security Headers Hook
 * 
 * Implements security headers to protect against common web vulnerabilities
 * 
 * @package    KONJAS
 * @subpackage Hooks
 * @category   Security
 * <AUTHOR> Team
 */
class Security_headers {
    
    /**
     * Set security headers
     * 
     * This method sets various security headers to protect against:
     * - MIME type sniffing attacks
     * - Clickjacking attacks
     * - XSS attacks
     * - Man-in-the-middle attacks (HSTS)
     * - Content injection attacks (CSP)
     */
    public function set_headers() {
        // Prevent MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // Prevent clickjacking
        header('X-Frame-Options: DENY');
        
        // XSS Protection
        header('X-XSS-Protection: 1; mode=block');
        
        // HSTS (HTTP Strict Transport Security) - only if using HTTPS
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        }
        
        // Content Security Policy
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://code.jquery.com; " .
               "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; " .
               "img-src 'self' data: https:; " .
               "font-src 'self' https://fonts.gstatic.com; " .
               "connect-src 'self'; " .
               "frame-ancestors 'none'; " .
               "base-uri 'self'; " .
               "form-action 'self'";
        
        header("Content-Security-Policy: {$csp}");
        
        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Permissions Policy (formerly Feature Policy)
        header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
        
        // Remove server information
        header_remove('X-Powered-By');
        header_remove('Server');
        
        // Cache control for sensitive pages
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($uri, 'login') !== false || strpos($uri, 'admin') !== false) {
            header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
            header('Pragma: no-cache');
            header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');
        }
    }
    
    /**
     * Log security events
     */
    public function log_security_event($event_type, $details) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event_type' => $event_type,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'details' => $details
        ];
        
        log_message('error', 'SECURITY_EVENT: ' . json_encode($log_entry));
    }
}

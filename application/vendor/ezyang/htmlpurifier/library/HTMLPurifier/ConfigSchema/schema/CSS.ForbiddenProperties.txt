CSS.ForbiddenProperties
TYPE: lookup
VERSION: 4.2.0
DEFAULT: array()
--DESCRIPTION--
<p>
    This is the logical inverse of %CSS.AllowedProperties, and it will
    override that directive or any other directive.  If possible,
    %CSS.AllowedProperties is recommended over this directive,
    because it can sometimes be difficult to tell whether or not you've
    forbidden all of the CSS properties you truly would like to disallow.
</p>
--# vim: et sw=4 sts=4

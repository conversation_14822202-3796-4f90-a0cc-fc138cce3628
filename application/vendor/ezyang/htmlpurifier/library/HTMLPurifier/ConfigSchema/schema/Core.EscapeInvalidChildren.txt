Core.EscapeInvalidChildren
TYPE: bool
DEFAULT: false
--DESCRIPTION--
<p><strong>Warning:</strong> this configuration option is no longer does anything as of 4.6.0.</p>

<p>When true, a child is found that is not allowed in the context of the
parent element will be transformed into text as if it were ASCII. When
false, that element and all internal tags will be dropped, though text will
be preserved.  There is no option for dropping the element but preserving
child nodes.</p>
--# vim: et sw=4 sts=4

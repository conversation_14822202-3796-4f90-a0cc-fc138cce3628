URI.Base
TYPE: string/null
VERSION: 2.1.0
DEFAULT: NULL
--DESCRIPTION--

<p>
    The base URI is the URI of the document this purified HTML will be
    inserted into.  This information is important if HTML Purifier needs
    to calculate absolute URIs from relative URIs, such as when %URI.MakeAbsolute
    is on.  You may use a non-absolute URI for this value, but behavior
    may vary (%URI.MakeAbsolute deals nicely with both absolute and
    relative paths, but forwards-compatibility is not guaranteed).
    <strong>Warning:</strong> If set, the scheme on this URI
    overrides the one specified by %URI.DefaultScheme.
</p>
--# vim: et sw=4 sts=4

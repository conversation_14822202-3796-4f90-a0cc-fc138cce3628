<?php

/**
 * This file is part of cocur/slugify.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cocur\Slugify;

/**
 * SlugifyInterface
 *
 * @package   org.cocur.slugify
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR>
 * @copyright 2012-2014 F<PERSON><PERSON>
 * @license   http://www.opensource.org/licenses/MIT The MIT License
 */
interface SlugifyInterface
{
    /**
     * Return a URL safe version of a string.
     *
     * @param string            $string
     * @param string|array|null $options
     *
     * @return string
     *
     * @api
     */
    public function slugify($string, $options = null);
}

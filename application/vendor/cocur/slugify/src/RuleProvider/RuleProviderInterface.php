<?php

/**
 * This file is part of cocur/slugify.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cocur\Slugify\RuleProvider;

/**
 * RuleProviderInterface
 *
 * @package   Cocur\Slugify\RuleProvider
 * <AUTHOR>
 * @copyright 2015 <PERSON><PERSON><PERSON>
 */
interface RuleProviderInterface
{
    /**
     * @param $ruleset
     *
     * @return array
     */
    public function getRules($ruleset);
}

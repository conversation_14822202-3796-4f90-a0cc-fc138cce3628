<?php

/**
 * This file is part of cocur/slugify.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Cocur\Slugify\Bridge\Symfony;

use Symfony\Component\HttpKernel\Bundle\Bundle;

/**
 * CocurSlugifyBundle
 *
 * @package    cocur/slugify
 * @subpackage bridge
 * <AUTHOR> <<EMAIL>>
 * @copyright  2012-2014 F<PERSON><PERSON>
 * @license    http://www.opensource.org/licenses/MIT The MIT License
 */
class CocurSlugifyBundle extends Bundle
{
    public function getContainerExtension()
    {
        return new CocurSlugifyExtension();
    }
}

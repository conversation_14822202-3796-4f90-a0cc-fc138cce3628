{"active": true, "name": "Instantiator", "slug": "instantiator", "docsSlug": "doctrine-instantiator", "codePath": "/src", "versions": [{"name": "1.4", "branchName": "master", "slug": "latest", "upcoming": true}, {"name": "1.3", "branchName": "1.3.x", "slug": "1.3", "aliases": ["current", "stable"], "maintained": true, "current": true}, {"name": "1.2", "branchName": "1.2.x", "slug": "1.2"}, {"name": "1.1", "branchName": "1.1.x", "slug": "1.1"}, {"name": "1.0", "branchName": "1.0.x", "slug": "1.0"}]}
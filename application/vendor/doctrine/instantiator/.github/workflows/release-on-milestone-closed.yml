name: "Automatic Releases"

on:
  milestone:
    types:
      - "closed"

jobs:
  release:
    name: "Git tag, release & create merge-up PR"
    runs-on: "ubuntu-20.04"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"

      - name: "Release"
        uses: "laminas/automatic-releases@v1"
        with:
          command-name: "laminas:automatic-releases:release"
        env:
          "GITHUB_TOKEN": ${{ secrets.GITHUB_TOKEN }}
          "SIGNING_SECRET_KEY": ${{ secrets.SIGNING_SECRET_KEY }}
          "GIT_AUTHOR_NAME": ${{ secrets.GIT_AUTHOR_NAME }}
          "GIT_AUTHOR_EMAIL": ${{ secrets.GIT_AUTHOR_EMAIL }}

      - name: "Create Merge-Up Pull Request"
        uses: "laminas/automatic-releases@v1"
        with:
          command-name: "laminas:automatic-releases:create-merge-up-pull-request"
        env:
          "GITHUB_TOKEN": ${{ secrets.GITHUB_TOKEN }}
          "SIGNING_SECRET_KEY": ${{ secrets.SIGNING_SECRET_KEY }}
          "GIT_AUTHOR_NAME": ${{ secrets.GIT_AUTHOR_NAME }}
          "GIT_AUTHOR_EMAIL": ${{ secrets.GIT_AUTHOR_EMAIL }}

      - name: "Create new milestones"
        uses: "laminas/automatic-releases@v1"
        with:
          command-name: "laminas:automatic-releases:create-milestones"
        env:
          "GITHUB_TOKEN": ${{ secrets.GITHUB_TOKEN }}
          "SIGNING_SECRET_KEY": ${{ secrets.SIGNING_SECRET_KEY }}
          "GIT_AUTHOR_NAME": ${{ secrets.GIT_AUTHOR_NAME }}
          "GIT_AUTHOR_EMAIL": ${{ secrets.GIT_AUTHOR_EMAIL }}

<?php

// autoload_namespaces.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'org\\bovigo\\vfs' => array($vendorDir . '/mikey179/vfsstream/src/main/php'),
    'deploydog\\Slavedriver' => array($vendorDir . '/deploy-dog/slavedriver/src'),
    'Unirest\\' => array($vendorDir . '/mashape/unirest-php/src'),
    'Requests' => array($vendorDir . '/rmccue/requests/library'),
    'Phossa2\\Event\\' => array($vendorDir . '/phossa2/event/src'),
    'HTMLPurifier' => array($vendorDir . '/ezyang/htmlpurifier/library'),
);

<?php return array(
    'root' => array(
        'name' => 'codeigniter/framework',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '6df0fa048fb126aa9c880c93e8e0dae1783c3d78',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'cocur/slugify' => array(
            'pretty_version' => 'v4.0.0',
            'version' => '4.0.0.0',
            'reference' => '3f1ffc300f164f23abe8b64ffb3f92d35cec8307',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cocur/slugify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'codeigniter/framework' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '6df0fa048fb126aa9c880c93e8e0dae1783c3d78',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'deploy-dog/slavedriver' => array(
            'pretty_version' => 'v1.1.0',
            'version' => '1.1.0.0',
            'reference' => '4ff405d5e9e58ab1f4be522fa47daec64b10102d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../deploy-dog/slavedriver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'diversen/gps-from-exif' => array(
            'pretty_version' => '1.0.7',
            'version' => '*******',
            'reference' => 'dc5310a1beb52b22783749837f76aaa33c5a5d88',
            'type' => 'library',
            'install_path' => __DIR__ . '/../diversen/gps-from-exif',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/instantiator' => array(
            'pretty_version' => '1.4.0',
            'version' => '1.4.0.0',
            'reference' => 'd56bf6102915de5702778fe20f2de3b2fe570b5b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/instantiator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v2.3.1',
            'version' => '2.3.1.0',
            'reference' => '65b2d8ee1f10915efb3b55597da3404f096acba2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.13.0',
            'version' => '4.13.0.0',
            'reference' => '08e27c97e4c6ed02f37c5b2b20488046c8d90d75',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v5.3.0',
            'version' => '5.3.0.0',
            'reference' => '3c2d70f2e64e2922345e89f2ceae47d2463faae1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'gasparesganga/php-shapefile' => array(
            'pretty_version' => 'v3.4.0',
            'version' => '3.4.0.0',
            'reference' => '3c7e4cb80edb40ff184e3971458577a04845f8da',
            'type' => 'library',
            'install_path' => __DIR__ . '/../gasparesganga/php-shapefile',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ghunti/highcharts-php' => array(
            'pretty_version' => 'v3.0.3',
            'version' => '3.0.3.0',
            'reference' => '548b321478510ec1559432a9baabbfba1a1e559a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ghunti/highcharts-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hisamu/php-xbase' => array(
            'pretty_version' => '1.4.0',
            'version' => '1.4.0.0',
            'reference' => 'cdbe9d41292ead86d7f52d2f6af54779a5b04d57',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hisamu/php-xbase',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'inok/dbf' => array(
            'pretty_version' => '1.0.7',
            'version' => '*******',
            'reference' => '96122f8b93c2a017aea2745f8f72f6b77ffeac4e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../inok/dbf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maennchen/zipstream-php' => array(
            'pretty_version' => '2.1.0',
            'version' => '*******',
            'reference' => 'c4c5803cc1f93df3d2448478ef79394a5981cc58',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maennchen/zipstream-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'malkusch/lock' => array(
            'pretty_version' => 'v2.2',
            'version' => '*******',
            'reference' => 'c3ee9d7e530c9b90e9634adaeb3a1f78af704f0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../malkusch/lock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '2.0.3',
            'version' => '*******',
            'reference' => '6f724d7e04606fd8adaa4e3bb381c3e9db09c946',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '2.1.3',
            'version' => '*******',
            'reference' => '174395a901b5ba0925f1d790fa91bab531074b61',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mashape/unirest-php' => array(
            'pretty_version' => 'v3.0.4',
            'version' => '3.0.4.0',
            'reference' => '842c0f242dfaaf85f16b72e217bf7f7c19ab12cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mashape/unirest-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mikey179/vfsstream' => array(
            'pretty_version' => 'v1.1.0',
            'version' => '1.1.0.0',
            'reference' => 'fc0fe8f4d0b527254a2dc45f0c265567c881d07e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mikey179/vfsstream',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.10.2',
            'version' => '1.10.2.0',
            'reference' => '776f831124e9c62e1a2c601ecc52e776d8bb7220',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => true,
            'replaced' => array(
                0 => '1.10.2',
            ),
        ),
        'myclabs/php-enum' => array(
            'pretty_version' => '1.7.7',
            'version' => '1.7.7.0',
            'reference' => 'd178027d1e679832db9f38248fcc7200647dc2b7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/php-enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phayes/geophp' => array(
            'pretty_version' => '1.2',
            'version' => '1.2.0.0',
            'reference' => '015404e85b602e0df1f91441f8db0f9e98f7e567',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phayes/geophp',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phossa/phossa-event' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'phossa2/event' => array(
            'pretty_version' => '2.1.6',
            'version' => '2.1.6.0',
            'reference' => '3eef33c05438f9a8fd812c1dc24ccfaba4060ad2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phossa2/event',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phossa2/shared' => array(
            'pretty_version' => '2.0.28',
            'version' => '2.0.28.0',
            'reference' => '8fe3a590555e522523f51778b5384c3af86e669b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phossa2/shared',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-extended/php-export-csv' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'b1d09d4f75e9b8fcf1844e9c885372abdb8dff37',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-extended/php-export-csv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-extended/php-export-interface' => array(
            'pretty_version' => '3.1.22',
            'version' => '3.1.22.0',
            'reference' => 'c94f96d8c28392a30c3df8b419635bea1c217aba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-extended/php-export-interface',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-extended/php-export-object' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => 'ba864220a838fa767eda9869471bd907927f1364',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-extended/php-export-object',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-extended/polyfill-php80-stringable' => array(
            'pretty_version' => '1.0.23',
            'version' => '1.0.23.0',
            'reference' => '062ec7e74a1e687f0d90e0183c4e00a0a4eac557',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-extended/polyfill-php80-stringable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpdocumentor/reflection-common' => array(
            'pretty_version' => '2.2.0',
            'version' => '*******',
            'reference' => '1d01c49d4ed62f25aa84a747ad35d5a16924662b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-common',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpdocumentor/reflection-docblock' => array(
            'pretty_version' => '5.2.2',
            'version' => '5.2.2.0',
            'reference' => '069a785b2141f5bcf49f3e353548dc1cce6df556',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-docblock',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpdocumentor/type-resolver' => array(
            'pretty_version' => '1.4.0',
            'version' => '1.4.0.0',
            'reference' => '6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/type-resolver',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '1.18.0',
            'version' => '1.18.0.0',
            'reference' => '418cd304e8e6b417ea79c3b29126a25dc4b1170c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpspec/prophecy' => array(
            'pretty_version' => 'v1.10.3',
            'version' => '1.10.3.0',
            'reference' => '451c3cd1418cf640de218914901e51b064abb093',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpspec/prophecy',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '4.0.8',
            'version' => '4.0.8.0',
            'reference' => 'ef7b2f56815df854e66ceaee8ebe9393ae36a40d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '1.4.5',
            'version' => '1.4.5.0',
            'reference' => '730b01bc3e867237eaac355e06a36b85dd93a8b4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'reference' => '31f8b717e51d9a2afca6c9f046f5d69fc27c8686',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '1.0.9',
            'version' => '1.0.9.0',
            'reference' => '3dcf38ca72b158baf0bc245e9184d3fdffa9c46f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-token-stream' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => '791198a2c6254db10131eecfe8c06670700904db',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-token-stream',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '5.7.27',
            'version' => '5.7.27.0',
            'reference' => 'b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit-mock-objects' => array(
            'pretty_version' => '3.4.4',
            'version' => '3.4.4.0',
            'reference' => 'a23b761686d50a560cc56233b9ecf49597cc9118',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit-mock-objects',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/event-manager-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '2dfb5f6c5eff0e91e20e913f8c5452ed95b86621',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '12ac7fcd07e5b077433f5f2bee95b3a771bf61be',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => 'f6561bf28d520154e4b0ec72be95418abe6d9363',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '408d5eafb83c57f6365a3ca330ff23aa4a5fa39b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rmccue/requests' => array(
            'pretty_version' => 'v1.8.1',
            'version' => '1.8.1.0',
            'reference' => '82e6936366eac3af4d836c18b9d8c31028fe4cd5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rmccue/requests',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '1de8cd5c010cb153fcd68b8d0f64606f523f7619',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '1.2.4',
            'version' => '1.2.4.0',
            'reference' => '2b7424b55f5047b47ac6e5ccb20b2aea4011d9be',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '1.4.3',
            'version' => '1.4.3.0',
            'reference' => '7f066a26a962dbe58ddea9f72a4e82874a3975a4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => '5795ffe5dc5b02460c3e34222fee8cbe245d8fac',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '1.1.1',
            'version' => '1.1.1.0',
            'reference' => 'bc37d50fea7d017d3d340f230811c9f1d7280af4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '1311872ac850040a79c3c058bea3e22d0f09cbb7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => '2c3ba150cbec723aa057506e73a8d33bdb286c9a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/resource-operations' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'ce990bb21759f94aeafd30209e8cfcdfa8bc3f52',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/resource-operations',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '99732be0ddb3361e16ad77b68ba41efc8e979019',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.23.0',
            'version' => '1.23.0.0',
            'reference' => '46cd95797e9df938fdd2b03693b5fca5e64b01ce',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.23.0',
            'version' => '1.23.0.0',
            'reference' => '2df51500adbaebdc4c38dea4c89a2e131c45c8a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v4.4.25',
            'version' => '4.4.25.0',
            'reference' => '81cdac5536925c1c4b7b50aabc9ff6330b9eb5fc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'vyuldashev/xml-to-array' => array(
            'pretty_version' => 'v1.0.1',
            'version' => '1.0.1.0',
            'reference' => 'ae1713b167093716b8e8d56bfb318befac4a8c4b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vyuldashev/xml-to-array',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.10.0',
            'version' => '1.10.0.0',
            'reference' => '6964c76c7804814a842473e0c8fd15bab0f18e25',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
    ),
);

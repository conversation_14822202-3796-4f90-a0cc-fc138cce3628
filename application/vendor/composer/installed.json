{"packages": [{"name": "cocur/slugify", "version": "v4.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/cocur/slugify.git", "reference": "3f1ffc300f164f23abe8b64ffb3f92d35cec8307"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cocur/slugify/zipball/3f1ffc300f164f23abe8b64ffb3f92d35cec8307", "reference": "3f1ffc300f164f23abe8b64ffb3f92d35cec8307", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=7.0"}, "conflict": {"symfony/config": "<3.4 || >=4,<4.3", "symfony/dependency-injection": "<3.4 || >=4,<4.3", "symfony/http-kernel": "<3.4 || >=4,<4.3", "twig/twig": "<2.12.1"}, "require-dev": {"laravel/framework": "~5.1", "latte/latte": "~2.2", "league/container": "^2.2.0", "mikey179/vfsstream": "~1.6.8", "mockery/mockery": "^1.3", "nette/di": "~2.4", "phpunit/phpunit": "^5.7.27", "pimple/pimple": "~1.1", "plumphp/plum": "~0.1", "symfony/config": "^3.4 || ^4.3 || ^5.0", "symfony/dependency-injection": "^3.4 || ^4.3 || ^5.0", "symfony/http-kernel": "^3.4 || ^4.3 || ^5.0", "twig/twig": "^2.12.1 || ~3.0", "zendframework/zend-modulemanager": "~2.2", "zendframework/zend-servicemanager": "~2.2", "zendframework/zend-view": "~2.2"}, "time": "2019-12-14T13:04:14+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Cocur\\Slugify\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://florian.ec"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Converts a string into a slug.", "keywords": ["slug", "slugify"], "install-path": "../cocur/slugify"}, {"name": "deploy-dog/slavedriver", "version": "v1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/deploy-dog/slavedriver.git", "reference": "4ff405d5e9e58ab1f4be522fa47daec64b10102d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/deploy-dog/slavedriver/zipball/4ff405d5e9e58ab1f4be522fa47daec64b10102d", "reference": "4ff405d5e9e58ab1f4be522fa47daec64b10102d", "shasum": ""}, "require": {"cocur/slugify": "*", "dragonmantank/cron-expression": "^2.0.0", "malkusch/lock": "*", "phossa2/event": "^2.1.0", "php": "^7.0", "psr/log": "^1.0", "psr/simple-cache": "^1.0"}, "require-dev": {"phpunit/phpunit": "^6.1 || ^5.7"}, "suggest": {"matthiasmullie/scrapbook": "Caching adapters compatible with Slavedriver.", "monolog/monolog": "Compatible loggers."}, "time": "2021-04-26T14:21:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"deploydog\\Slavedriver": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://deploy.dog", "role": "Project Lead"}], "description": " A PHP based cron job runner. Manage, monitor, log and react to problems with your scheduled tasks.", "homepage": "https://github.com/deploy-dog/slavedriver", "keywords": ["cron", "cronjob", "cronjobs", "crontab", "job", "job runner", "jobs", "scheduled job", "scheduled jobs", "scheduled task", "scheduled tasks", "task", "task runner"], "install-path": "../deploy-dog/slavedriver"}, {"name": "diversen/gps-from-exif", "version": "1.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/diversen/gps-from-exif.git", "reference": "dc5310a1beb52b22783749837f76aaa33c5a5d88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/diversen/gps-from-exif/zipball/dc5310a1beb52b22783749837f76aaa33c5a5d88", "reference": "dc5310a1beb52b22783749837f76aaa33c5a5d88", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-07-19T12:05:29+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"diversen\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Get GPS from exif", "homepage": "https://github.com/diversen/gps-from-exif", "keywords": ["exif", "gps", "image"], "install-path": "../diversen/gps-from-exif"}, {"name": "doctrine/instantiator", "version": "1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/d56bf6102915de5702778fe20f2de3b2fe570b5b", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13 || 1.0.0-alpha2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "time": "2020-11-10T18:47:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "install-path": "../doctrine/instantiator"}, {"name": "dragonmantank/cron-expression", "version": "v2.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/65b2d8ee1f10915efb3b55597da3404f096acba2", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2", "shasum": ""}, "require": {"php": "^7.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7.0|^8.0|^9.0"}, "time": "2020-10-13T00:52:37+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "install-path": "../dragonmantank/cron-expression"}, {"name": "ezyang/htmlpurifier", "version": "v4.13.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "dev-master#72de02a7b80c6bb8864ef9bf66d41d2f58f826bd"}, "time": "2020-06-29T00:56:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"], "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "install-path": "../ezyang/htmlpurifier"}, {"name": "firebase/php-jwt", "version": "v5.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "3c2d70f2e64e2922345e89f2ceae47d2463faae1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/3c2d70f2e64e2922345e89f2ceae47d2463faae1", "reference": "3c2d70f2e64e2922345e89f2ceae47d2463faae1", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "time": "2021-05-20T17:37:02+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "install-path": "../firebase/php-jwt"}, {"name": "gasparesganga/php-shapefile", "version": "v3.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/gasparesganga/php-shapefile.git", "reference": "3c7e4cb80edb40ff184e3971458577a04845f8da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/gasparesganga/php-shapefile/zipball/3c7e4cb80edb40ff184e3971458577a04845f8da", "reference": "3c7e4cb80edb40ff184e3971458577a04845f8da", "shasum": ""}, "require": {"php": ">=5.4.0"}, "time": "2021-01-23T08:10:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Shapefile\\": "src/Shapefile/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://gasparesganga.com/labs/php-shapefile/", "role": "Developer"}], "description": "PHP library to read and write ESRI Shapefiles, compatible with WKT and GeoJSON", "homepage": "https://gasparesganga.com/labs/php-shapefile/", "keywords": ["ESRI", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dbf", "g<PERSON><PERSON><PERSON>", "shape", "shp", "wkt"], "install-path": "../gasparesganga/php-shapefile"}, {"name": "ghunti/highcharts-php", "version": "v3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ghunti/HighchartsPHP.git", "reference": "548b321478510ec1559432a9baabbfba1a1e559a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ghunti/HighchartsPHP/zipball/548b321478510ec1559432a9baabbfba1a1e559a", "reference": "548b321478510ec1559432a9baabbfba1a1e559a", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-01-24T11:57:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Ghunti\\HighchartsPHP\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://goncaloqueiros.net", "role": "Developer"}], "description": "A php wrapper for highcharts and highstock javascript libraries", "homepage": "http://goncaloqueiros.net/highcharts.php", "keywords": ["charts", "highcharts", "highstock", "javascript", "php"], "support": {"email": "<EMAIL>", "issues": "https://github.com/ghunti/HighchartsPHP/issues", "source": "https://github.com/ghunti/HighchartsPHP"}, "install-path": "../ghunti/highcharts-php"}, {"name": "hisamu/php-xbase", "version": "1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/luads/php-xbase.git", "reference": "cdbe9d41292ead86d7f52d2f6af54779a5b04d57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/luads/php-xbase/zipball/cdbe9d41292ead86d7f52d2f6af54779a5b04d57", "reference": "cdbe9d41292ead86d7f52d2f6af54779a5b04d57", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^7.0||^8.0||^9.0"}, "time": "2021-03-15T21:04:31+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"XBase\\": "src/XBase"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Luã de <PERSON>uza", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple parser for *.dbf, *.fpt files using PHP", "homepage": "https://github.com/luads/php-xbase", "keywords": ["dbase", "dbf", "foxpro"], "install-path": "../hisamu/php-xbase"}, {"name": "inok/dbf", "version": "1.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/nchizhov/inok-dbf.git", "reference": "96122f8b93c2a017aea2745f8f72f6b77ffeac4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nchizhov/inok-dbf/zipball/96122f8b93c2a017aea2745f8f72f6b77ffeac4e", "reference": "96122f8b93c2a017aea2745f8f72f6b77ffeac4e", "shasum": ""}, "require": {"ext-calendar": "*", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "php": ">=5.6"}, "time": "2020-03-15T08:24:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Inok\\Dbf\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.kgd.in", "role": "System Administrator"}], "description": "Package for reading DBASE-files (FoxPro) with/without MEMO-fields", "homepage": "http://blog.kgd.in", "keywords": ["dbase", "dbf", "foxpro", "memo"], "install-path": "../inok/dbf"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "time": "2020-05-30T13:11:16+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "install-path": "../maennchen/zipstream-php"}, {"name": "malkusch/lock", "version": "v2.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-lock/lock.git", "reference": "c3ee9d7e530c9b90e9634adaeb3a1f78af704f0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-lock/lock/zipball/c3ee9d7e530c9b90e9634adaeb3a1f78af704f0d", "reference": "c3ee9d7e530c9b90e9634adaeb3a1f78af704f0d", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "psr/log": "^1"}, "require-dev": {"eloquent/liberator": "^2.0", "ext-memcached": "*", "ext-pcntl": "*", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-pdo_sqlite": "*", "ext-sysvsem": "*", "friendsofphp/php-cs-fixer": "^2.16", "johnkary/phpunit-speedtrap": "^3.0", "mikey179/vfsstream": "^1.6.7", "php-mock/php-mock-phpunit": "^2.1", "phpstan/phpstan": "^0.12.58", "phpunit/phpunit": "^9.4", "predis/predis": "^1.1", "spatie/async": "^1.5", "squizlabs/php_codesniffer": "^3.3"}, "suggest": {"ext-igbinary": "To use this library with PHP Redis igbinary serializer enabled.", "ext-lzf": "To use this library with PHP Redis lzf compression enabled.", "ext-pnctl": "Enables locking with flock without busy waiting in CLI scripts.", "ext-redis": "To use this library with the PHP Redis extension.", "ext-sysvsem": "Enables locking using semaphores.", "predis/predis": "To use this library with predis."}, "time": "2021-03-07T19:43:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"malkusch\\lock\\": "classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["WTFPL"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://markus.malkusch.de", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Mutex library for exclusive code execution.", "homepage": "https://github.com/malkusch/lock", "keywords": ["advisory-locks", "cas", "flock", "lock", "locking", "memcache", "mutex", "mysql", "postgresql", "redis", "redlock", "semaphore"], "install-path": "../malkusch/lock"}, {"name": "markbaker/complex", "version": "2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "6f724d7e04606fd8adaa4e3bb381c3e9db09c946"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/6f724d7e04606fd8adaa4e3bb381c3e9db09c946", "reference": "6f724d7e04606fd8adaa4e3bb381c3e9db09c946", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "squizlabs/php_codesniffer": "^3.4"}, "time": "2021-06-02T09:44:11+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "install-path": "../markbaker/complex"}, {"name": "markbaker/matrix", "version": "2.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "174395a901b5ba0925f1d790fa91bab531074b61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/174395a901b5ba0925f1d790fa91bab531074b61", "reference": "174395a901b5ba0925f1d790fa91bab531074b61", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "time": "2021-05-25T15:42:17+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/Functions/adjoint.php", "classes/src/Functions/antidiagonal.php", "classes/src/Functions/cofactors.php", "classes/src/Functions/determinant.php", "classes/src/Functions/diagonal.php", "classes/src/Functions/identity.php", "classes/src/Functions/inverse.php", "classes/src/Functions/minors.php", "classes/src/Functions/trace.php", "classes/src/Functions/transpose.php", "classes/src/Operations/add.php", "classes/src/Operations/directsum.php", "classes/src/Operations/subtract.php", "classes/src/Operations/multiply.php", "classes/src/Operations/divideby.php", "classes/src/Operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "install-path": "../markbaker/matrix"}, {"name": "mashape/unirest-php", "version": "v3.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Mashape/unirest-php.git", "reference": "842c0f242dfaaf85f16b72e217bf7f7c19ab12cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Mashape/unirest-php/zipball/842c0f242dfaaf85f16b72e217bf7f7c19ab12cb", "reference": "842c0f242dfaaf85f16b72e217bf7f7c19ab12cb", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.4.0"}, "require-dev": {"codeclimate/php-test-reporter": "0.1.*", "phpunit/phpunit": "~4.4"}, "suggest": {"ext-json": "Allows using JSON Bodies for sending and parsing requests"}, "time": "2016-08-11T17:49:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Unirest\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Unirest PHP", "homepage": "https://github.com/Mashape/unirest-php", "keywords": ["client", "curl", "http", "https", "rest"], "install-path": "../mashape/unirest-php"}, {"name": "mikey179/vfsstream", "version": "v1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/bovigo/vfsStream.git", "reference": "fc0fe8f4d0b527254a2dc45f0c265567c881d07e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bovigo/vfsStream/zipball/fc0fe8f4d0b527254a2dc45f0c265567c881d07e", "reference": "fc0fe8f4d0b527254a2dc45f0c265567c881d07e", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2012-08-25T12:49:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"org\\bovigo\\vfs": "src/main/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD"], "homepage": "http://vfs.bovigo.org/", "install-path": "../mikey179/vfsstream"}, {"name": "myclabs/deep-copy", "version": "1.10.2", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/776f831124e9c62e1a2c601ecc52e776d8bb7220", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "time": "2020-11-13T09:40:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "install-path": "../myclabs/deep-copy"}, {"name": "myclabs/php-enum", "version": "1.7.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/d178027d1e679832db9f38248fcc7200647dc2b7", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}, "time": "2020-11-14T18:14:52+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "install-path": "../myclabs/php-enum"}, {"name": "phayes/geophp", "version": "1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phayes/geoPHP.git", "reference": "015404e85b602e0df1f91441f8db0f9e98f7e567"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phayes/geoPHP/zipball/015404e85b602e0df1f91441f8db0f9e98f7e567", "reference": "015404e85b602e0df1f91441f8db0f9e98f7e567", "shasum": ""}, "require-dev": {"phpunit/phpunit": "4.1.*"}, "time": "2014-12-02T06:11:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["geoPHP.inc"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2 or New-BSD"], "authors": [{"name": "<PERSON>"}], "description": "GeoPHP is a open-source native PHP library for doing geometry operations. It is written entirely in PHP and can therefore run on shared hosts. It can read and write a wide variety of formats: WKT (including EWKT), WKB (including EWKB), GeoJSON, KML, GPX, GeoRSS). It works with all Simple-Feature geometries (Point, LineString, Polygon, GeometryCollection etc.) and can be used to get centroids, bounding-boxes, area, and a wide variety of other useful information.", "homepage": "https://github.com/phayes/geoPHP", "install-path": "../phayes/geophp"}, {"name": "phossa2/event", "version": "2.1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phossa2/event.git", "reference": "3eef33c05438f9a8fd812c1dc24ccfaba4060ad2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phossa2/event/zipball/3eef33c05438f9a8fd812c1dc24ccfaba4060ad2", "reference": "3eef33c05438f9a8fd812c1dc24ccfaba4060ad2", "shasum": ""}, "require": {"phossa2/shared": "^2.0.21", "php": ">=5.4.0"}, "provide": {"psr/event-manager-implementation": "^1.0"}, "replace": {"phossa/phossa-event": "*"}, "require-dev": {"phpunit/phpunit": "4.*"}, "time": "2016-08-26T01:39:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "installation-source": "dist", "autoload": {"psr-0": {"Phossa2\\Event\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/phossa2"}], "description": "PSR-14 event manager implementation libraray for PHP", "homepage": "https://github.com/phossa2/event", "keywords": ["event", "event manager", "phossa", "psr", "psr-14"], "abandoned": true, "install-path": "../phossa2/event"}, {"name": "phossa2/shared", "version": "2.0.28", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/phossa2/shared.git", "reference": "8fe3a590555e522523f51778b5384c3af86e669b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phossa2/shared/zipball/8fe3a590555e522523f51778b5384c3af86e669b", "reference": "8fe3a590555e522523f51778b5384c3af86e669b", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "4.*", "squizlabs/php_codesniffer": "2.*"}, "time": "2017-01-08T06:32:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Phossa2\\Shared\\": "src/Shared/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/phossa2"}], "description": "The shared library for other phossa2 libraries", "homepage": "https://github.com/phossa2/shared", "keywords": ["framework", "library", "phossa", "phossa2", "php"], "install-path": "../phossa2/shared"}, {"name": "php-extended/php-export-csv", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://gitlab.com/php-extended/php-export-csv.git", "reference": "b1d09d4f75e9b8fcf1844e9c885372abdb8dff37"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/php-extended%2Fphp-export-csv/repository/archive.zip?sha=b1d09d4f75e9b8fcf1844e9c885372abdb8dff37", "reference": "b1d09d4f75e9b8fcf1844e9c885372abdb8dff37", "shasum": ""}, "require": {"php": ">=7.0", "php-extended/php-export-object": "^3"}, "time": "2020-02-22T19:21:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpExtended\\Export\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://gitlab.com/Anastaszor", "role": "developer"}], "description": "A library to export raw data into csv format", "homepage": "https://gitlab.com/php-extended/php-export-csv", "keywords": ["csv", "data", "export", "php"], "install-path": "../php-extended/php-export-csv"}, {"name": "php-extended/php-export-interface", "version": "3.1.22", "version_normalized": "********", "source": {"type": "git", "url": "https://gitlab.com/php-extended/php-export-interface.git", "reference": "c94f96d8c28392a30c3df8b419635bea1c217aba"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/php-extended%2Fphp-export-interface/repository/archive.zip?sha=c94f96d8c28392a30c3df8b419635bea1c217aba", "reference": "c94f96d8c28392a30c3df8b419635bea1c217aba", "shasum": ""}, "require": {"php": ">=7.1", "php-extended/polyfill-php80-stringable": ">=1"}, "time": "2021-06-06T13:57:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpExtended\\Export\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://gitlab.com/Anastaszor", "role": "developer"}], "description": "A library to specify how to export data into files", "homepage": "https://gitlab.com/php-extended/php-export-interface", "keywords": ["data", "export", "format", "php"], "install-path": "../php-extended/php-export-interface"}, {"name": "php-extended/php-export-object", "version": "3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://gitlab.com/php-extended/php-export-object.git", "reference": "ba864220a838fa767eda9869471bd907927f1364"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/php-extended%2Fphp-export-object/repository/archive.zip?sha=ba864220a838fa767eda9869471bd907927f1364", "reference": "ba864220a838fa767eda9869471bd907927f1364", "shasum": ""}, "require": {"php": ">=7.0", "php-extended/php-export-interface": "^3"}, "time": "2020-02-22T20:50:46+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpExtended\\Export\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://gitlab.com/Anastaszor", "role": "developer"}], "description": "A library that implements the php-extended/php-export-interface", "homepage": "https://gitlab.com/php-extended/php-export-object", "keywords": ["abstract", "data", "export", "object", "php"], "install-path": "../php-extended/php-export-object"}, {"name": "php-extended/polyfill-php80-stringable", "version": "1.0.23", "version_normalized": "********", "source": {"type": "git", "url": "https://gitlab.com/php-extended/polyfill-php80-stringable.git", "reference": "062ec7e74a1e687f0d90e0183c4e00a0a4eac557"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/php-extended%2Fpolyfill-php80-stringable/repository/archive.zip?sha=062ec7e74a1e687f0d90e0183c4e00a0a4eac557", "reference": "062ec7e74a1e687f0d90e0183c4e00a0a4eac557", "shasum": ""}, "require": {"php": ">=7.0"}, "time": "2021-06-06T13:46:44+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://gitlab.com/Anastaszor", "role": "developer"}], "description": "A polyfill that adds the stringable interface added in php8.0 to previous versions of php", "homepage": "https://gitlab.com/php-extended/polyfill-php80-stringable", "keywords": ["php", "php8", "polyfill", "stringable"], "install-path": "../php-extended/polyfill-php80-stringable"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2020-06-27T09:03:43+00:00", "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "install-path": "../phpdocumentor/reflection-common"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.2.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "069a785b2141f5bcf49f3e353548dc1cce6df556"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/069a785b2141f5bcf49f3e353548dc1cce6df556", "reference": "069a785b2141f5bcf49f3e353548dc1cce6df556", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2"}, "time": "2020-09-03T19:13:55+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "install-path": "../phpdocumentor/reflection-docblock"}, {"name": "phpdocumentor/type-resolver", "version": "1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*"}, "time": "2020-09-17T18:55:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "install-path": "../phpdocumentor/type-resolver"}, {"name": "phpoffice/phpspreadsheet", "version": "1.18.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "418cd304e8e6b417ea79c3b29126a25dc4b1170c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/418cd304e8e6b417ea79c3b29126a25dc4b1170c", "reference": "418cd304e8e6b417ea79c3b29126a25dc4b1170c", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.13", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^2.0", "markbaker/matrix": "^2.0", "php": "^7.2 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "dompdf/dompdf": "^1.0", "friendsofphp/php-cs-fixer": "^2.18", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^0.12.82", "phpstan/phpstan-phpunit": "^0.12.18", "phpunit/phpunit": "^8.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "time": "2021-05-31T18:21:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "install-path": "../phpoffice/phpspreadsheet"}, {"name": "phpspec/prophecy", "version": "v1.10.3", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "451c3cd1418cf640de218914901e51b064abb093"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/451c3cd1418cf640de218914901e51b064abb093", "reference": "451c3cd1418cf640de218914901e51b064abb093", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.2.3|^2.0|^3.0|^4.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0|^4.0"}, "require-dev": {"phpspec/phpspec": "^2.5 || ^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "time": "2020-03-05T15:02:03+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "install-path": "../phpspec/prophecy"}, {"name": "phpunit/php-code-coverage", "version": "4.0.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "^1.3", "phpunit/php-text-template": "^1.2", "phpunit/php-token-stream": "^1.4.2 || ^2.0", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^1.3.2 || ^2.0", "sebastian/version": "^1.0 || ^2.0"}, "require-dev": {"ext-xdebug": "^2.1.4", "phpunit/phpunit": "^5.7"}, "suggest": {"ext-xdebug": "^2.5.1"}, "time": "2017-04-02T07:44:40+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "install-path": "../phpunit/php-code-coverage"}, {"name": "phpunit/php-file-iterator", "version": "1.4.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/730b01bc3e867237eaac355e06a36b85dd93a8b4", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "time": "2017-11-27T13:52:08+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "install-path": "../phpunit/php-file-iterator"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "time": "2015-06-21T13:50:34+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "install-path": "../phpunit/php-text-template"}, {"name": "phpunit/php-timer", "version": "1.0.9", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "time": "2017-02-26T11:10:40+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "install-path": "../phpunit/php-timer"}, {"name": "phpunit/php-token-stream", "version": "2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "791198a2c6254db10131eecfe8c06670700904db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/791198a2c6254db10131eecfe8c06670700904db", "reference": "791198a2c6254db10131eecfe8c06670700904db", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.2.4"}, "time": "2017-11-27T05:48:46+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "abandoned": true, "install-path": "../phpunit/php-token-stream"}, {"name": "phpunit/phpunit", "version": "5.7.27", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit/zipball/b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "~1.3", "php": "^5.6 || ^7.0", "phpspec/prophecy": "^1.6.2", "phpunit/php-code-coverage": "^4.0.4", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "^3.2", "sebastian/comparator": "^1.2.4", "sebastian/diff": "^1.4.3", "sebastian/environment": "^1.3.4 || ^2.0", "sebastian/exporter": "~2.0", "sebastian/global-state": "^1.1", "sebastian/object-enumerator": "~2.0", "sebastian/resource-operations": "~1.0", "sebastian/version": "^1.0.6|^2.0.1", "symfony/yaml": "~2.1|~3.0|~4.0"}, "conflict": {"phpdocumentor/reflection-docblock": "3.0.2"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-xdebug": "*", "phpunit/php-invoker": "~1.1"}, "time": "2018-02-01T05:50:59+00:00", "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.7.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "install-path": "../phpunit/phpunit"}, {"name": "phpunit/phpunit-mock-objects", "version": "3.4.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/a23b761686d50a560cc56233b9ecf49597cc9118", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.6 || ^7.0", "phpunit/php-text-template": "^1.2", "sebastian/exporter": "^1.2 || ^2.0"}, "conflict": {"phpunit/phpunit": "<5.4.0"}, "require-dev": {"phpunit/phpunit": "^5.4"}, "suggest": {"ext-soap": "*"}, "time": "2017-06-30T09:13:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "abandoned": true, "install-path": "../phpunit/phpunit-mock-objects"}, {"name": "psr/http-client", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "time": "2020-06-29T06:28:15+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "time": "2019-04-30T12:38:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T14:39:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "install-path": "../psr/log"}, {"name": "psr/simple-cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2017-10-23T01:57:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "install-path": "../psr/simple-cache"}, {"name": "rmccue/requests", "version": "v1.8.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/WordPress/Requests.git", "reference": "82e6936366eac3af4d836c18b9d8c31028fe4cd5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/Requests/zipball/82e6936366eac3af4d836c18b9d8c31028fe4cd5", "reference": "82e6936366eac3af4d836c18b9d8c31028fe4cd5", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "php-parallel-lint/php-console-highlighter": "^0.5.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5", "requests/test-server": "dev-master", "squizlabs/php_codesniffer": "^3.5", "wp-coding-standards/wpcs": "^2.0"}, "time": "2021-06-04T09:56:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Requests": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "homepage": "http://ryanmccue.info"}], "description": "A HTTP library written in PHP, for human beings.", "homepage": "http://github.com/WordPress/Requests", "keywords": ["curl", "fsockopen", "http", "idna", "ipv6", "iri", "sockets"], "install-path": "../rmccue/requests"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/1de8cd5c010cb153fcd68b8d0f64606f523f7619", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "time": "2020-11-30T08:15:22+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "install-path": "../sebastian/code-unit-reverse-lookup"}, {"name": "sebastian/comparator", "version": "1.2.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2 || ~2.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "time": "2017-01-29T09:50:25+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "install-path": "../sebastian/comparator"}, {"name": "sebastian/diff", "version": "1.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/7f066a26a962dbe58ddea9f72a4e82874a3975a4", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "time": "2017-05-22T07:24:03+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "install-path": "../sebastian/diff"}, {"name": "sebastian/environment", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.0"}, "time": "2016-11-26T07:53:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "install-path": "../sebastian/environment"}, {"name": "sebastian/exporter", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~2.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "time": "2016-11-19T08:54:04+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "install-path": "../sebastian/exporter"}, {"name": "sebastian/global-state", "version": "1.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "time": "2015-10-12T03:26:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "install-path": "../sebastian/global-state"}, {"name": "sebastian/object-enumerator", "version": "2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/1311872ac850040a79c3c058bea3e22d0f09cbb7", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7", "shasum": ""}, "require": {"php": ">=5.6", "sebastian/recursion-context": "~2.0"}, "require-dev": {"phpunit/phpunit": "~5"}, "time": "2017-02-18T15:18:39+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "install-path": "../sebastian/object-enumerator"}, {"name": "sebastian/recursion-context", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/2c3ba150cbec723aa057506e73a8d33bdb286c9a", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "time": "2016-11-19T07:33:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "install-path": "../sebastian/recursion-context"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "time": "2015-07-28T20:34:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "install-path": "../sebastian/resource-operations"}, {"name": "sebastian/version", "version": "2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "time": "2016-10-03T07:35:21+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "install-path": "../sebastian/version"}, {"name": "symfony/polyfill-ctype", "version": "v1.23.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/46cd95797e9df938fdd2b03693b5fca5e64b01ce", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2021-02-19T12:13:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-mbstring", "version": "v1.23.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "2df51500adbaebdc4c38dea4c89a2e131c45c8a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/2df51500adbaebdc4c38dea4c89a2e131c45c8a1", "reference": "2df51500adbaebdc4c38dea4c89a2e131c45c8a1", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2021-05-27T09:27:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/yaml", "version": "v4.4.25", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "81cdac5536925c1c4b7b50aabc9ff6330b9eb5fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/81cdac5536925c1c4b7b50aabc9ff6330b9eb5fc", "reference": "81cdac5536925c1c4b7b50aabc9ff6330b9eb5fc", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "time": "2021-05-26T17:39:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "install-path": "../symfony/yaml"}, {"name": "vyuldashev/xml-to-array", "version": "v1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/vyuldashev/xml-to-array.git", "reference": "ae1713b167093716b8e8d56bfb318befac4a8c4b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vyuldashev/xml-to-array/zipball/ae1713b167093716b8e8d56bfb318befac4a8c4b", "reference": "ae1713b167093716b8e8d56bfb318befac4a8c4b", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"larapack/dd": "^1.1", "phpunit/phpunit": "^6.4", "spatie/array-to-xml": "^2.7"}, "time": "2018-04-22T15:20:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Vyuldashev\\XmlToArray\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Convert xml to an array", "install-path": "../vyuldashev/xml-to-array"}, {"name": "webmozart/assert", "version": "1.10.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/6964c76c7804814a842473e0c8fd15bab0f18e25", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "time": "2021-03-09T10:59:23+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "install-path": "../webmozart/assert"}], "dev": true, "dev-package-names": ["doctrine/instantiator", "mikey179/vfsstream", "myclabs/deep-copy", "phpdocumentor/reflection-common", "phpdocumentor/reflection-docblock", "phpdocumentor/type-resolver", "phpspec/prophecy", "phpunit/php-code-coverage", "phpunit/php-file-iterator", "phpunit/php-text-template", "phpunit/php-timer", "phpunit/php-token-stream", "phpunit/phpunit", "phpunit/phpunit-mock-objects", "sebastian/code-unit-reverse-lookup", "sebastian/comparator", "sebastian/diff", "sebastian/environment", "sebastian/exporter", "sebastian/global-state", "sebastian/object-enumerator", "sebastian/recursion-context", "sebastian/resource-operations", "sebastian/version", "symfony/polyfill-ctype", "symfony/yaml", "webmozart/assert"]}
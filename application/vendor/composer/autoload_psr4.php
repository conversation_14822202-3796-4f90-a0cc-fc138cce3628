<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'phpDocumentor\\Reflection\\' => array($vendorDir . '/phpdocumentor/reflection-common/src', $vendorDir . '/phpdocumentor/reflection-docblock/src', $vendorDir . '/phpdocumentor/type-resolver/src'),
    'malkusch\\lock\\' => array($vendorDir . '/malkusch/lock/classes'),
    'diversen\\' => array($vendorDir . '/diversen/gps-from-exif'),
    'ZipStream\\' => array($vendorDir . '/maennchen/zipstream-php/src'),
    'XBase\\' => array($vendorDir . '/hisamu/php-xbase/src/XBase'),
    'Webmozart\\Assert\\' => array($vendorDir . '/webmozart/assert/src'),
    'Vyuldashev\\XmlToArray\\' => array($vendorDir . '/vyuldashev/xml-to-array/src'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Component\\Yaml\\' => array($vendorDir . '/symfony/yaml'),
    'Shapefile\\' => array($vendorDir . '/gasparesganga/php-shapefile/src/Shapefile'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Prophecy\\' => array($vendorDir . '/phpspec/prophecy/src/Prophecy'),
    'PhpOffice\\PhpSpreadsheet\\' => array($vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet'),
    'PhpExtended\\Export\\' => array($vendorDir . '/php-extended/php-export-csv/src', $vendorDir . '/php-extended/php-export-interface/src', $vendorDir . '/php-extended/php-export-object/src'),
    'Phossa2\\Shared\\' => array($vendorDir . '/phossa2/shared/src/Shared'),
    'MyCLabs\\Enum\\' => array($vendorDir . '/myclabs/php-enum/src'),
    'Matrix\\' => array($vendorDir . '/markbaker/matrix/classes/src'),
    'Inok\\Dbf\\' => array($vendorDir . '/inok/dbf/src'),
    'Ghunti\\HighchartsPHP\\' => array($vendorDir . '/ghunti/highcharts-php/src'),
    'Firebase\\JWT\\' => array($vendorDir . '/firebase/php-jwt/src'),
    'Doctrine\\Instantiator\\' => array($vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'Cron\\' => array($vendorDir . '/dragonmantank/cron-expression/src/Cron'),
    'Complex\\' => array($vendorDir . '/markbaker/complex/classes/src'),
    'Cocur\\Slugify\\' => array($vendorDir . '/cocur/slugify/src'),
);

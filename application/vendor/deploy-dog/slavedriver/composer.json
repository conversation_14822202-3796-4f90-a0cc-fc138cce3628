{"name": "deploy-dog/slavedriver", "description": " A PHP based cron job runner. Manage, monitor, log and react to problems with your scheduled tasks.", "license": "MIT", "autoload": {"psr-0": {"deploydog\\Slavedriver": "src"}}, "require": {"php": "^7.0", "psr/simple-cache": "^1.0", "psr/log": "^1.0", "dragonmantank/cron-expression": "^2.0.0", "malkusch/lock": "*", "cocur/slugify": "*", "phossa2/event": "^2.1.0"}, "require-dev": {"phpunit/phpunit": "^6.1 || ^5.7"}, "suggest": {"matthiasmullie/scrapbook": "Caching adapters compatible with Slavedriver.", "monolog/monolog": "Compatible loggers."}, "keywords": ["cron", "cronjob", "cronjobs", "crontab", "task", "task runner", "job", "jobs", "job runner", "scheduled task", "scheduled tasks", "scheduled job", "scheduled jobs"], "homepage": "https://github.com/deploy-dog/slavedriver", "authors": [{"name": "<PERSON>", "homepage": "http://deploy.dog", "role": "Project Lead"}]}
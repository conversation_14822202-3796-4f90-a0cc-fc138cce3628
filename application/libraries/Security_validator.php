<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Security Validator Library
 * 
 * Provides comprehensive input validation and output sanitization
 * 
 * @package    KONJAS
 * @subpackage Libraries
 * @category   Security
 * <AUTHOR> Team
 */
class Security_validator {
    
    protected $CI;
    
    /**
     * Validation rules
     */
    private $validation_rules = [
        'username' => '/^[a-zA-Z0-9_]{3,20}$/',
        'email' => '/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
        'numeric' => '/^[0-9]+$/',
        'alphanumeric' => '/^[a-zA-Z0-9]+$/',
        'alpha' => '/^[a-zA-Z]+$/',
        'phone' => '/^[\+]?[0-9\-\(\)\s]+$/',
        'date' => '/^\d{4}-\d{2}-\d{2}$/',
        'time' => '/^\d{2}:\d{2}:\d{2}$/',
        'url' => '/^https?:\/\/[^\s\/$.?#].[^\s]*$/i'
    ];
    
    /**
     * Dangerous patterns to detect
     */
    private $dangerous_patterns = [
        'sql_injection' => [
            '/union\s+select/i',
            '/or\s+1\s*=\s*1/i',
            '/drop\s+table/i',
            '/exec\s*\(/i',
            '/insert\s+into/i',
            '/delete\s+from/i',
            '/update\s+.*set/i'
        ],
        'xss' => [
            '/<script/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe/i',
            '/<object/i',
            '/<embed/i'
        ],
        'path_traversal' => [
            '/\.\.\//i',
            '/\.\.\\\\/i',
            '/etc\/passwd/i',
            '/windows\/system32/i'
        ],
        'command_injection' => [
            '/;\s*(rm|del|format)/i',
            '/\|\s*(nc|netcat)/i',
            '/`.*`/i',
            '/\$\(.*\)/i'
        ]
    ];
    
    public function __construct() {
        $this->CI =& get_instance();
        $this->CI->load->helper('security');
    }
    
    /**
     * Validate input against predefined rules
     * 
     * @param string $input The input to validate
     * @param string $type The validation type
     * @return bool True if valid, false otherwise
     */
    public function validate_input($input, $type) {
        // Check for null or empty input
        if (is_null($input) || $input === '') {
            return false;
        }
        
        // Check for dangerous patterns first
        if ($this->detect_attack($input)) {
            $this->log_security_violation('DANGEROUS_PATTERN_DETECTED', $input);
            return false;
        }
        
        // Apply specific validation rules
        switch($type) {
            case 'username':
                return preg_match($this->validation_rules['username'], $input);
            case 'email':
                return filter_var($input, FILTER_VALIDATE_EMAIL) !== false;
            case 'numeric':
                return is_numeric($input);
            case 'alphanumeric':
                return ctype_alnum($input);
            case 'alpha':
                return ctype_alpha($input);
            case 'phone':
                return preg_match($this->validation_rules['phone'], $input);
            case 'date':
                return preg_match($this->validation_rules['date'], $input) && 
                       strtotime($input) !== false;
            case 'time':
                return preg_match($this->validation_rules['time'], $input);
            case 'url':
                return filter_var($input, FILTER_VALIDATE_URL) !== false;
            case 'safe_string':
                return $this->validate_safe_string($input);
            default:
                return false;
        }
    }
    
    /**
     * Validate safe string (no dangerous characters)
     */
    private function validate_safe_string($input) {
        // Allow alphanumeric, spaces, and common punctuation
        return preg_match('/^[a-zA-Z0-9\s\.\,\-\_\(\)]+$/', $input);
    }
    
    /**
     * Sanitize output for safe display
     * 
     * @param string $output The output to sanitize
     * @param string $context The context (html, attribute, js, css)
     * @return string Sanitized output
     */
    public function sanitize_output($output, $context = 'html') {
        if (is_null($output)) {
            return '';
        }
        
        switch($context) {
            case 'html':
                return htmlspecialchars($output, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            case 'attribute':
                return htmlspecialchars($output, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            case 'js':
                return json_encode($output, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
            case 'css':
                return preg_replace('/[^a-zA-Z0-9\-\_\s]/', '', $output);
            case 'url':
                return urlencode($output);
            default:
                return htmlspecialchars($output, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        }
    }
    
    /**
     * Detect potential attacks in input
     * 
     * @param string $input The input to check
     * @return string|false Attack type if detected, false otherwise
     */
    public function detect_attack($input) {
        foreach ($this->dangerous_patterns as $attack_type => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $input)) {
                    return $attack_type;
                }
            }
        }
        return false;
    }
    
    /**
     * Validate file upload
     * 
     * @param array $file $_FILES array element
     * @return bool True if valid, false otherwise
     */
    public function validate_file_upload($file) {
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return false;
        }
        
        // Check file size (max 10MB)
        $max_size = 10 * 1024 * 1024;
        if ($file['size'] > $max_size) {
            return false;
        }
        
        // Check file extension
        $allowed_extensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'gif'];
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowed_extensions)) {
            return false;
        }
        
        // Check MIME type
        $allowed_mimes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'image/jpeg',
            'image/png',
            'image/gif'
        ];
        
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mime_type, $allowed_mimes)) {
            return false;
        }
        
        // Check for embedded scripts in images
        if (strpos($mime_type, 'image/') === 0) {
            $content = file_get_contents($file['tmp_name']);
            if (preg_match('/<script|javascript:|on\w+=/i', $content)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Sanitize filename
     * 
     * @param string $filename The filename to sanitize
     * @return string Sanitized filename
     */
    public function sanitize_filename($filename) {
        // Remove path information
        $filename = basename($filename);
        
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9\-\_\.]/', '_', $filename);
        
        // Prevent double extensions
        $filename = preg_replace('/\.+/', '.', $filename);
        
        // Limit length
        if (strlen($filename) > 100) {
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $name = substr(pathinfo($filename, PATHINFO_FILENAME), 0, 95);
            $filename = $name . '.' . $extension;
        }
        
        return $filename;
    }
    
    /**
     * Log security violations
     */
    private function log_security_violation($type, $input) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'type' => $type,
            'input' => substr($input, 0, 200),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        log_message('error', 'SECURITY_VIOLATION: ' . json_encode($log_entry));
    }
    
    /**
     * Generate secure random token
     * 
     * @param int $length Token length
     * @return string Random token
     */
    public function generate_secure_token($length = 32) {
        if (function_exists('random_bytes')) {
            return bin2hex(random_bytes($length / 2));
        } elseif (function_exists('openssl_random_pseudo_bytes')) {
            return bin2hex(openssl_random_pseudo_bytes($length / 2));
        } else {
            // Fallback (less secure)
            return substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', $length)), 0, $length);
        }
    }
}

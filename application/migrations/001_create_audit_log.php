<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Migration to create audit log table for security monitoring
 */
class Migration_Create_audit_log extends CI_Migration {

    public function up() {
        // Create audit_log table
        $this->dbforge->add_field(array(
            'id' => array(
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => TRUE,
                'auto_increment' => TRUE
            ),
            'timestamp' => array(
                'type' => 'DATETIME',
                'null' => FALSE
            ),
            'user_id' => array(
                'type' => 'INT',
                'constraint' => 11,
                'null' => TRUE
            ),
            'action' => array(
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => FALSE
            ),
            'resource' => array(
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => FALSE
            ),
            'ip_address' => array(
                'type' => 'VARCHAR',
                'constraint' => 45,
                'null' => FALSE
            ),
            'user_agent' => array(
                'type' => 'TEXT',
                'null' => TRUE
            ),
            'session_id' => array(
                'type' => 'VARCHAR',
                'constraint' => 128,
                'null' => TRUE
            ),
            'details' => array(
                'type' => 'TEXT',
                'null' => TRUE
            )
        ));
        
        $this->dbforge->add_key('id', TRUE);
        $this->dbforge->add_key('timestamp');
        $this->dbforge->add_key('user_id');
        $this->dbforge->add_key('action');
        
        $this->dbforge->create_table('audit_log');
        
        // Create security_events table
        $this->dbforge->add_field(array(
            'id' => array(
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => TRUE,
                'auto_increment' => TRUE
            ),
            'timestamp' => array(
                'type' => 'DATETIME',
                'null' => FALSE
            ),
            'event_type' => array(
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => FALSE
            ),
            'severity' => array(
                'type' => 'ENUM',
                'constraint' => array('LOW', 'MEDIUM', 'HIGH', 'CRITICAL'),
                'default' => 'MEDIUM'
            ),
            'ip_address' => array(
                'type' => 'VARCHAR',
                'constraint' => 45,
                'null' => FALSE
            ),
            'user_agent' => array(
                'type' => 'TEXT',
                'null' => TRUE
            ),
            'details' => array(
                'type' => 'TEXT',
                'null' => TRUE
            ),
            'resolved' => array(
                'type' => 'BOOLEAN',
                'default' => FALSE
            )
        ));
        
        $this->dbforge->add_key('id', TRUE);
        $this->dbforge->add_key('timestamp');
        $this->dbforge->add_key('event_type');
        $this->dbforge->add_key('severity');
        
        $this->dbforge->create_table('security_events');
    }

    public function down() {
        $this->dbforge->drop_table('audit_log');
        $this->dbforge->drop_table('security_events');
    }
}

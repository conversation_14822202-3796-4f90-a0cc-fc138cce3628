<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Test_ajax extends CI_Controller {

    public function __construct() {
        parent::__construct();
    }

    public function test_internal_request() {
        $url = base_url('data_users/index');
        
        echo "Testing internal request to: " . $url . "<br>";
        
        // Get current session cookie
        $session_cookie = '';
        if (isset($_COOKIE[session_name()])) {
            $session_cookie = session_name() . '=' . $_COOKIE[session_name()];
        }
        
        echo "Session cookie: " . $session_cookie . "<br>";
        
        // Create context for file_get_contents
        $context_options = [
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'user_agent' => 'KONJAS Internal Request',
                'header' => [
                    'Connection: close'
                ]
            ]
        ];
        
        // Add session cookie if available
        if ($session_cookie) {
            $context_options['http']['header'][] = 'Cookie: ' . $session_cookie;
        }
        
        $context = stream_context_create($context_options);
        
        echo "Making request...<br>";
        
        // Make the request using file_get_contents
        $content = @file_get_contents($url, false, $context);
        
        if ($content === false) {
            $error = error_get_last();
            echo 'Failed to load content<br>';
            echo 'Error: ' . ($error ? $error['message'] : 'Unknown error') . '<br>';
        } else {
            echo 'Success! Content length: ' . strlen($content) . ' bytes<br>';
            echo 'First 200 characters: ' . substr($content, 0, 200) . '...<br>';
        }
    }
}
?>

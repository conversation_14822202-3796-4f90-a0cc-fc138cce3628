<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Tarif extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url

		$this->load->model('tarif/M_data_table','m_data_table');

		$this->load->library(array('session', 'template'));
		$this->load->database();
		if ($this->session->userdata('active') != 1) {
			redirect('home');
		}

	}

	public function index()
	{
		$js_file = $this->load->view('tarif/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => '<PERSON>janjian <PERSON>',
			"jv_script" => $js_file
		);

		$this->load->view('tarif/v_data', $data);
	}
	function cleanAndConvertToFloat($input)
	{
		if ($input == '') {
			return NULL;
		} else {
			// Hapus tanda Rp dan spasi
			$cleaned = str_replace(['Rp', ' '], '', $input);

			// Hapus semua tanda desimal (titik) jika ada
			$cleaned = str_replace('.', '', $cleaned);

			// Konversi ke float
			return (float) $cleaned;
		}
	}

	function insert_data()
	{

		$id = $this->input->post('id');
		$nm_pengusahaan = $this->input->post('nm_perjanjian');

		// die();
		$masa = $this->input->post('masa');

		$no_dok = $this->input->post('no_dok');
		$tgl_op = $this->input->post('tgl_op');
		$nilai = $this->input->post('nilai');
		$id_detail = $this->input->post('id_detail');
		$filename = $this->input->post('filenames');
		$tahun_layout = $this->input->post('tahun_layout');


		$data = array(
			'id_perjanjian_pengusahaan' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
			'masa_manfaat' => ($masa == '') ? NULL : $masa,
			'tahun' => ($tahun_layout == '') ? NULL : $tahun_layout,
			'created_by' => $this->session->userdata('id_user'),
			'created_at' => date('Y/m/d H:i:s'),
			'updated_at' => date('Y/m/d H:i:s'),
		);
		if ($id == '') {
			unset($data['updated_at']);
			$x = $this->db->insert('tarif', $data);
			if ($x) {
				$id = $this->db->insert_id();
				$z = "insert";
			}
		} else {
			unset($data['created_at']);
			$this->db->where('id_tarif', $id);
			$x = $this->db->update('tarif', $data);
			$z = "update";
		}
		$this->db->where('id_tarif', $id);
		$this->db->where_not_in('id_tarif_detail', $id_detail);
		$this->db->delete('tarif_detail');

		for ($xx = 0; $xx <= count($no_dok) - 1; $xx++) {
			if (isset($id_detail[$xx]) && $id_detail[$xx] !== '') {
				// $this->db->where('id_tarif', $id);
				// $this->db->where('id_tarif_detail <>', $id_detail[$xx]);


			}
			$namf = '';
			if ($no_dok[$xx] !== '') {
				if (is_uploaded_file($_FILES['file_tarif']['tmp_name'][$xx])) {

					$sourcePath = $_FILES['file_tarif']['tmp_name'][$xx];
					$namf = $_FILES['file_tarif']['name'][$xx];
					$rep = str_replace(" ", "_", $namf);
					$fil = date('Ymd') . date("his") . "~" . $rep;
					$targetPath = FCPATH . "assets/file_tarif/" . $fil;
					move_uploaded_file($sourcePath, $targetPath);
				} else {
					if (isset($id_detail[$xx]) && $id_detail[$xx] !== '') {
						$fil = $filename[$xx];
					} else {
						$fil = NULL;
					}
				}
				$a = array();

				$b = array(
					'id_tarif' => $id,
					'no_dok_tarif' => ($no_dok[$xx] == '') ? NULL : $no_dok[$xx],
					'tgl_tarif' => ($tgl_op[$xx] == '') ? NULL : $tgl_op[$xx],
					'file' => $fil,
				);
				$data = array_merge($b, $a);
				if (isset($id_detail[$xx]) && $id_detail[$xx] !== '') {
					$this->db->where('id_tarif_detail', $id_detail[$xx]);
					$this->db->update('tarif_detail', $data);

				} else {
					$this->db->insert('tarif_detail', $data);

				}
			}
		}
		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('v_tarif', $requestData);

		echo json_encode($data);
	}



	function tampildata($table = '', $colum = '', $val_colum = '', $combo = '')
	{
		$tab = explode("~", $table)[0];
	
		if ($combo != '') {
			$nama = explode("~", $table)[1];
			$kode = explode("~", $table)[2];
			$this->db->select("$nama as nama");
			$this->db->select("$kode as kode");
		}
	
		if ($colum == '') {
			$data = $this->db->get($tab)->result_array();
		} else {
			$dt = array();
			$columns = explode("~", $colum);
			$values = explode("~", $val_colum);
	
			// Hapus prefix 'idx' dari val_colum jika ada
			for ($x = 0; $x < count($values); $x++) {
				$values[$x] = str_replace('idx', '', $values[$x]); // Menghapus 'idx'
			}
	
			for ($x = 0; $x < count($columns); $x++) {
				$col = $columns[$x];
				$val = str_replace('_', ' ', $values[$x]);
				$dt[$col] = $val;
			}
	
			$data = $this->db->get_where($tab, $dt)->result_array();
		}
	
		echo json_encode($data);
	}

	// function tampildata($table = '', $colum = '', $val_colum = '', $combo = '')
	// {
	// 	// $table='data_kegiatan';
	// 	$tab = explode("~", $table)[0];

	// 	if ($combo != '') {
	// 		$nama = explode("~", $table)[1];
	// 		$kode = explode("~", $table)[2];
	// 		$this->db->select("$nama as nama");
	// 		$this->db->select("$kode as kode");
	// 	}
	// 	if ($colum == '') {
	// 		$data = $this->db->get($tab)->result_array();
	// 	} else {

	// 		$data = $this->db->get_where($tab, array($colum => str_replace('_', ' ', $val_colum)))->result_array();
	// 	}
	// 	echo json_encode($data);
	// }
	function delete_data($table, $colum, $id)
	{
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			$this->db->where($colum, $id);
			$this->db->delete('tarif_detail');
			echo 0;
		} else {
			echo 1;
		}
	}
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
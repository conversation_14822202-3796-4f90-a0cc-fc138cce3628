<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Amortisasi extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url

		$this->load->model('amortisasi/M_data_table', 'm_data_table');

		$this->load->library(array('session', 'template'));
		$this->load->database();

		// Allow tampildata and ssp methods without session check for AJAX calls
		$method = $this->router->fetch_method();
		if (!in_array($method, ['tampildata', 'ssp']) && $this->session->userdata('active') != 1) {
			redirect(base_url('login'));
		}

	}

	public function index()
	{
		$js_file = $this->load->view('amortisasi/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => 'Amortisasi',
			"jv_script" => $js_file
		);

		$this->load->view('amortisasi/v_data', $data);
		// $this->upgrade_data();
	}

	public function upgrade_data()
	{
		$currentYear = date('Y');

		$this->db->where('tahun <', $currentYear);
		$query = $this->db->get('amortisasi');

		if ($query->num_rows() > 0) {
			$dataToUpdate = array('status_data' => 'old');

			$this->db->where('tahun <', $currentYear);
			$this->db->update('amortisasi', $dataToUpdate);
		}
	}
	function cleanAndConvertToFloat($input)
	{
		if ($input == '') {
			return NULL;
		} else {
			// Hapus tanda Rp dan spasi
			$cleaned = str_replace(['Rp', ' '], '', $input);

			// Hapus semua tanda desimal (titik) jika ada
			 $cleaned = str_replace('.', '', $cleaned);
			 $cleaned = str_replace(',', '', $cleaned);

			// Konversi ke float
			return (float) $cleaned;
		}
	}
	function cleanAndConvertToDecimal($input)
	{
		if ($input == '') {
			return NULL;
		} else {

			// Hapus semua tanda desimal (titik) jika ada
			 $cleaned = str_replace(',', '.', $input);

			// Konversi ke float
			return $cleaned;
		}
	}

	function insert_data()
	{
		$id = $this->input->post('id');
		$nm_pengusahaan = $this->input->post('nm_perjanjian');
		$periode_perolehan = $this->input->post('p_perolehan');
		$periode_pembukuan = $this->input->post('p_pembukuan');
		$tahun = $this->input->post('tahun_layout');
		$nilai_buku_kewajiban = $this->input->post('nilai_buku_kewajiban');
		$id_slo = $this->input->post('id_slo');
		$nilai_slo_tw1_2 = $this->input->post('nilai_slo_tw1_2');
		$nilai_amortisasi_tw1_2 = $this->input->post('nilai_amortisasi_tw1_2');
		$nilai_slo_tw3 = $this->input->post('nilai_slo_tw3');
		$nilai_amortisasi_tw3 = $this->input->post('nilai_amortisasi_tw3');
		$nilai_slo_tw4 = $this->input->post('nilai_slo_tw4');
		$nilai_amortisasi_tw4 = $this->input->post('nilai_amortisasi_tw4');
		$nilai_slo_audited = $this->input->post('nilai_slo_audited');
		$nilai_amortisasi_audited = $this->input->post('nilai_amortisasi_audited');
		//  echo $nilai_amortisasi_tw1_2;
		//  echo "<br>";
		//  echo $nilai_amortisasi_tw3;
		//  echo "<br>";

		//  echo $nilai_amortisasi_tw4;
		//  echo "<br>";

		//  echo $nilai_amortisasi_audited;

		$filename_lk = $this->input->post('filenames_lk');
		$filename_kertas = $this->input->post('filenames_kertas');
		$data = array(
			'id_perjanjian_pengusahaan' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
			'periode_perolehan' => ($periode_perolehan == '') ? NULL : $periode_perolehan,
			'periode_pembukuan' => ($periode_pembukuan == '') ? NULL : $periode_pembukuan,
			'tahun' => ($tahun == '') ? NULL : $tahun,
			'tahun_perolehan' => ($tahun == '') ? NULL : $tahun,
			'tahun_pembukuan' => ($tahun == '') ? NULL : $tahun,
			'id_slo' => ($id_slo == '') ? NULL : $id_slo,
			'nilai_buku_kewajiban' => $this->cleanAndConvertToFloat($nilai_buku_kewajiban),
			'nilai_slo_tw1_2' => $this->cleanAndConvertToFloat($nilai_slo_tw1_2),
			'nilai_amortisasi_tw1_2' => $this->cleanAndConvertToFloat($nilai_amortisasi_tw1_2),
			'nilai_slo_tw3' => $this->cleanAndConvertToFloat($nilai_slo_tw3),
			'nilai_amortisasi_tw3' => $this->cleanAndConvertToFloat($nilai_amortisasi_tw3),
			'nilai_slo_tw4' => $this->cleanAndConvertToFloat($nilai_slo_tw4),
			'nilai_amortisasi_tw4' => $this->cleanAndConvertToFloat($nilai_amortisasi_tw4),
			'nilai_slo_audited' => $this->cleanAndConvertToFloat($nilai_slo_audited),
			'nilai_amortisasi_audited' => $this->cleanAndConvertToFloat($nilai_amortisasi_audited),
			'created_by' => $this->session->userdata('id_user'),
			'created_at' => date("Y-m-d"),
		);
		//  print_r($data);
		// die();

		if (isset($_FILES['file_lk']) && is_uploaded_file($_FILES['file_lk']['tmp_name'])) {
			$sourcePath = $_FILES['file_lk']['tmp_name'];
			$namf = $_FILES['file_lk']['name'];
			$fileExtension = strtolower(pathinfo($namf, PATHINFO_EXTENSION)); // Mengecek ekstensi file
		
			// Gunakan finfo_file() untuk validasi MIME type
			$finfo = finfo_open(FILEINFO_MIME_TYPE);
			$fileType = finfo_file($finfo, $sourcePath);
			finfo_close($finfo);
		
			// Daftar MIME type yang diperbolehkan
			$allowedMimeTypes = ['application/pdf'];
		
			// Daftar ekstensi yang diperbolehkan
			$allowedExtensions = ['pdf'];
		
			// Validasi file berdasarkan MIME type & ekstensi
			if (in_array($fileType, $allowedMimeTypes) && in_array($fileExtension, $allowedExtensions)) {
				// Sanitasi nama file untuk keamanan
				$safeFileName = preg_replace("/[^a-zA-Z0-9\._-]/", "_", $namf);
		
				// Buat nama file unik dengan timestamp
				$fil_lk = date('Ymd_His') . "~" . $safeFileName;
				$targetPath = FCPATH . "assets/file_amortisasi/" . $fil_lk;
		
				// Pindahkan file jika valid
				if (move_uploaded_file($sourcePath, $targetPath)) {
					// echo "File berhasil diunggah!";
				} else {
					echo "Gagal mengunggah file!";
					die();
				}
			} else {
				echo "1_File yang diunggah harus berupa PDF!";
				die();
			}
		} else {
			$fil_lk = $filename_lk; // Jika tidak ada file yang diunggah, gunakan file lama
		}
		
		
		
		if (isset($_FILES['file_kertas']) && is_uploaded_file($_FILES['file_kertas']['tmp_name'])) {
			$sourcePath = $_FILES['file_kertas']['tmp_name'];
			$namf = $_FILES['file_kertas']['name'];
			$fileExtension = strtolower(pathinfo($namf, PATHINFO_EXTENSION)); // Mengecek ekstensi file
		
			// Gunakan finfo_file() untuk validasi MIME type
			$finfo = finfo_open(FILEINFO_MIME_TYPE);
			$fileType = finfo_file($finfo, $sourcePath);
			finfo_close($finfo);
		
			// Daftar MIME type yang diperbolehkan (sesuaikan jika perlu)
			$allowedMimeTypes = ['application/pdf'];
		
			// Daftar ekstensi yang diperbolehkan
			$allowedExtensions = ['pdf'];
		
			// Validasi file berdasarkan MIME type & ekstensi
			if (in_array($fileType, $allowedMimeTypes) && in_array($fileExtension, $allowedExtensions)) {
				// Sanitasi nama file untuk keamanan
				$safeFileName = preg_replace("/[^a-zA-Z0-9\._-]/", "_", $namf);
		
				// Buat nama file unik dengan timestamp
				$fil_kertas = date('Ymd_His') . "~" . $safeFileName;
				$targetPath = FCPATH . "assets/file_amortisasi/" . $fil_kertas;
		
				// Pindahkan file jika valid
				if (move_uploaded_file($sourcePath, $targetPath)) {
					// echo "File berhasil diunggah!";
				} else {
					echo "Gagal mengunggah file!";
					die();
				}
			} else {
				echo "1_File yang diunggah harus berupa PDF!";
				die();
			}
		} else {
			$fil_kertas = $filename_kertas; // Jika tidak ada file yang diunggah, gunakan file lama
		}
		$data_file = array(
			'file_lk' => $fil_lk,
			'file_kertas_kerja_retrospektif' => $fil_kertas,
		);
		$data = array_merge($data, $data_file);
		if ($id == '') {
			$x = $this->db->insert('amortisasi', $data);
			if ($x) {
				$id = $this->db->insert_id();
				$z = "insert";
			}
		} else {
			$this->db->where('id_amortisasi', $id);
			$x = $this->db->update('amortisasi', $data);
			$z = "update";
		}


		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('v_amortisasi', $requestData);

		echo json_encode($data);
	}




	function tampildata()
	{
		// Get all URI segments
		$segments = $this->uri->segment_array();

		// For URL: /amortisasi/tampildata/table/colum/val_colum/combo
		// Segments: [1]=amortisasi, [2]=tampildata, [3]=table, [4]=colum, [5]=val_colum, [6]=combo
		$table = isset($segments[3]) ? urldecode($segments[3]) : '';
		$colum = isset($segments[4]) ? urldecode($segments[4]) : '';
		$val_colum = isset($segments[5]) ? urldecode($segments[5]) : '';
		$combo = isset($segments[6]) ? urldecode($segments[6]) : '';

		// Debug: log the parameters
		log_message('debug', 'Amortisasi tampildata called with: table=' . $table . ', colum=' . $colum . ', val_colum=' . $val_colum . ', combo=' . $combo);

		if (empty($table)) {
			echo json_encode(['error' => 'Table parameter is required']);
			return;
		}

		$tab = explode("~", $table)[0];

		// Check if table exists
		if (!$this->db->table_exists($tab)) {
			echo json_encode(['error' => 'Table does not exist: ' . $tab]);
			return;
		}

		if ($combo == 'combobox') {
			$table_parts = explode("~", $table);
			if (count($table_parts) >= 3) {
				$nama = $table_parts[1];
				$kode = $table_parts[2];
				$this->db->select("$nama as nama");
				$this->db->select("$kode as kode");
			}
		}

		if (empty($colum) || $colum == '1') {
			$data = $this->db->get($tab)->result_array();
		} else {
			$dt = array();
			$columns = explode("~", $colum);
			$values = explode("~", $val_colum);

			// Hapus prefix 'idx' dari val_colum jika ada
			for ($x = 0; $x < count($values); $x++) {
				$values[$x] = str_replace('idx', '', $values[$x]); // Menghapus 'idx'
			}

			for ($x = 0; $x < count($columns); $x++) {
				if (isset($values[$x]) && !empty($values[$x]) && $values[$x] != '1') {
					$col = $columns[$x];
					$val = str_replace('_', ' ', $values[$x]);
					$dt[$col] = $val;
				}
			}

			if (!empty($dt)) {
				$data = $this->db->get_where($tab, $dt)->result_array();
			} else {
				$data = $this->db->get($tab)->result_array();
			}
		}

		echo json_encode($data);
	}
	function delete_data($table, $colum, $id)
	{
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Data_badan_usaha extends CI_Controller
{

	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url

		$this->load->model('data_badan_usaha/M_data_table');

		$this->load->library(array('session', 'template'));
		$this->load->database();
		if ($this->session->userdata('active') != 1) {
			redirect('home');
		}

	}

	public function index()
	{
		$js_file = $this->load->view('data_badan_usaha/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => '',
			"jv_script" => $js_file
		);

		$this->load->view('data_badan_usaha/v_data', $data);
	}


	function insert_data()
	{
		$id = $this->input->post('id');
		$nama = $this->input->post('nama');
		$nohp = $this->input->post('nohp');
		$alamat = $this->input->post('alamat');


		$data = array(
			'nm_mitra' => $nama,
			'alamat' => $alamat,
			'no_telepon' => $nohp,
		);
		if ($id == '') {
			$x = $this->db->insert('ref_badan_usaha', $data);
			$z = "insert";
		} else {
			$this->db->where('id_badan_usaha', $id);
			$x = $this->db->update('ref_badan_usaha', $data);
			$z = "update";
		}
		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('ref_badan_usaha', $requestData);

		echo json_encode($data);
	}




	function tampildata($table = '', $colum = '', $val_colum = '', $combo = '')
	{
		// $table='data_kegiatan';
		$tab = explode("~", $table)[0];

		if ($combo != '') {
			$nama = explode("~", $table)[1];
			$kode = explode("~", $table)[2];
			$this->db->select("$nama as nama");
			$this->db->select("$kode as kode");
		}
		if ($colum == '') {
			$data = $this->db->get($tab)->result_array();
		} else {

			$data = $this->db->get_where($tab, array($colum => str_replace('_', ' ', $val_colum)))->result_array();
		}
		echo json_encode($data);
	}
	function delete_data($table, $colum, $id)
	{
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
<?php
defined('BASEPATH') or exit('No direct script access allowed');

class perolehan_mutasi extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url

		$this->load->model('perolehan_mutasi/M_data_table','m_data_table');

		$this->load->library('session');
		$this->load->database();
		if ($this->session->userdata('active') != 1) {
			redirect('home');
		}

	}

	public function index()
	{
		

		$js_file = $this->load->view('perolehan_mutasi/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => 'Perjanjian Konsesi Jasa',
			"jv_script" => $js_file
		);

		$this->load->view('perolehan_mutasi/v_data', $data);
	}
	function cleanAndConvertToFloat($input)
	{
		if ($input == '') {
			return NULL;
		} else {
			$cleaned = str_replace('Rp ', '', $input);
			$cleaned = str_replace(' ', '', $cleaned);
			$cleaned = str_replace('.', '', $cleaned);
			$cleaned = str_replace(',', '.', $cleaned);
			return $cleaned;
		}

	}

	function insert_data()
	{
		$id = $this->input->post('id');
		$nm_pengusahaan = $this->input->post('nm_perjanjian');
		$id_slo = $this->input->post('id_slo');
		$id_tarif = $this->input->post('id_tarif');
		$kode_satker = $this->input->post('satker');
		$periode_perolehan = $this->input->post('p_perolehan');
		$periode_pembukuan = $this->input->post('p_pembukuan');
		$nilai_tanah_smst1 = $this->input->post('nilai_tanah_smst1');
		$nilai_non_tanah_smst1 = $this->input->post('nilai_non_tanah_smst1');
		$nilai_kdp_smst1 = $this->input->post('nilai_kdp_smst1');
		$nilai_ak_penyusutan_smst1 = $this->input->post('nilai_ak_penyusutan_smst1');
		$nilai_kewajiban_konjas_smst1 = $this->input->post('nilai_kewajiban_konjas_smst1');
		$nilai_am_kewajiban_konjas_smst1 = $this->input->post('nilai_am_kewajiban_konjas_smst1');
		$nilai_tanah_tw3 = $this->input->post('nilai_tanah_tw3');
		$nilai_non_tanah_tw3 = $this->input->post('nilai_non_tanah_tw3');
		$nilai_kdp_tw3 = $this->input->post('nilai_kdp_tw3');
		$nilai_ak_penyusutan_tw3 = $this->input->post('nilai_ak_penyusutan_tw3');
		$nilai_kewajiban_konjas_tw3 = $this->input->post('nilai_kewajiban_konjas_tw3');
		$nilai_am_kewajiban_konjas_tw3 = $this->input->post('nilai_am_kewajiban_konjas_tw3');
		$nilai_tanah_smst2 = $this->input->post('nilai_tanah_smst2');
		$nilai_non_tanah_smst2 = $this->input->post('nilai_non_tanah_smst2');
		$nilai_kdp_smst2 = $this->input->post('nilai_kdp_smst2');
		$nilai_ak_penyusutan_smst2 = $this->input->post('nilai_ak_penyusutan_smst2');
		$nilai_kewajiban_konjas_smst2 = $this->input->post('nilai_kewajiban_konjas_smst2');
		$nilai_am_kewajiban_konjas_smst2 = $this->input->post('nilai_am_kewajiban_konjas_smst2');
		$nilai_tanah_audited = $this->input->post('nilai_tanah_audited');
		$nilai_non_tanah_audited = $this->input->post('nilai_non_tanah_audited');
		$nilai_kdp_audited = $this->input->post('nilai_kdp_audited');
		$nilai_ak_penyusutan_audited = $this->input->post('nilai_ak_penyusutan_audited');
		$nilai_kewajiban_konjas_audited = $this->input->post('nilai_kewajiban_konjas_audited');
		$nilai_am_kewajiban_konjas_audited = $this->input->post('nilai_am_kewajiban_konjas_audited');
		$filename_lk = $this->input->post('filenames_lk');
		$filename_kertas = $this->input->post('filenames_kertas');
		$filename_lk_smst2 = $this->input->post('filenames_lk_smst2');
		$filename_kertas_smst2 = $this->input->post('filenames_kertas_smst2');
		$filename_lk_tw3 = $this->input->post('filenames_lk_tw3');
		$filename_kertas_tw3 = $this->input->post('filenames_kertas_tw3');
		$filename_lk_audited = $this->input->post('filenames_lk_audited');
		$filename_kertas_audited = $this->input->post('filenames_kertas_audited');

		$tahun = $this->input->post('tahun_layout');
		$smt = $this->input->post('sisa_manfaat_tarif');
		$sms = $this->input->post('sisa_manfaat_slo');
		$barang = $this->input->post('barang');
		$nup = $this->input->post('nup');
		if($tahun > 2023){
		$cek =$this->db->get_where('v_perolehan_mutasi',array("id_perjanjian_pengusahaan"=>$nm_pengusahaan,"tahun"=>$tahun-1))->row();
		$sa_tanah= $cek->sa_tanah;
		$sa_nontanah= $cek->sa_nontanah;
		$sa_kdp= $cek->sa_kdp;
		$sa_akumulasi= $cek->sa_akumulasi;

		$nilai_awal_tanah_smst1=$cek->nilai_tanah_smst1;
		$nilai_awal_kdp_smst1=$cek->nilai_kdp_smst1;
		$nilai_awal_nontanah_smst1=$cek->nilai_nontanah_smst1;
		$nilai_awal_akumulasi_penyusutan_smst1=$cek->nilai_akumulasi_penyusutan_smst1;
	
		$nilai_awal_tanah_tw3=$cek->nilai_tanah_tw3;
		$nilai_awal_kdp_tw3=$cek->nilai_kdp_tw3;
		$nilai_awal_nontanah_tw3=$cek->nilai_nontanah_tw3;
		$nilai_awal_akumulasi_penyusutan_tw3=$cek->nilai_akumulasi_penyusutan_tw3;

		$nilai_awal_tanah_smst2=$cek->nilai_tanah_smst2;
		$nilai_awal_kdp_smst2=$cek->nilai_kdp_smst2;
		$nilai_awal_nontanah_smst2=$cek->nilai_nontanah_smst2;
		$nilai_awal_akumulasi_penyusutan_smst2=$cek->nilai_akumulasi_penyusutan_smst2;

		$nilai_awal_tanah_audited=$cek->nilai_tanah_audited;
		$nilai_awal_kdp_audited=$cek->nilai_kdp_audited;
		$nilai_awal_nontanah_audited=$cek->nilai_nontanah_audited;
		$nilai_awal_akumulasi_penyusutan_audited=$cek->nilai_akumulasi_penyusutan_audited;

		$total_tanah = $sa_tanah + $nilai_awal_tanah_smst1 + $nilai_awal_tanah_tw3 + $nilai_awal_tanah_smst2 + $nilai_awal_tanah_audited;

		// Menghitung total untuk KDP
		$total_kdp = $sa_kdp + $nilai_awal_kdp_smst1 + $nilai_awal_kdp_tw3 + $nilai_awal_kdp_smst2 + $nilai_awal_kdp_audited;

		// Menghitung total untuk Non-Tanah
		$total_nontanah = $sa_nontanah + $nilai_awal_nontanah_smst1 + $nilai_awal_nontanah_tw3 + $nilai_awal_nontanah_smst2 + $nilai_awal_nontanah_audited;

		// Menghitung total untuk Akumulasi Penyusutan
		$total_akumulasi_penyusutan = $sa_akumulasi + $nilai_awal_akumulasi_penyusutan_smst1 + $nilai_awal_akumulasi_penyusutan_tw3 + $nilai_awal_akumulasi_penyusutan_smst2 + $nilai_awal_akumulasi_penyusutan_audited;



		$data_awal=array('id_perjanjian_pengusahaan' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
						'tahun'=>$tahun,
						'nilai_tanah_audited' => $this->cleanAndConvertToFloat($total_tanah),
						'nilai_nontanah_audited' => $this->cleanAndConvertToFloat($total_nontanah),
						'nilai_kdp_audited' => $this->cleanAndConvertToFloat($total_kdp),
						'nilai_akumulasi_penyusutan_audited' => $this->cleanAndConvertToFloat($total_akumulasi_penyusutan),					
		);
		}

		$data = array(
			'id_perjanjian_pengusahaan' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
			'id_slo' => ($id_slo == '') ? NULL : $id_slo,
			'id_tarif' => ($id_tarif == '') ? NULL : $id_tarif,
			'kode_satker' => ($kode_satker == '') ? NULL : $kode_satker,
			'kode_barang' => ($barang == '') ? NULL : $barang,
			'nup' => ($nup == '') ? NULL : $nup,
			'periode_perolehan' => ($periode_perolehan == '') ? NULL : $periode_perolehan,
			'periode_pembukuan' => ($periode_pembukuan == '') ? NULL : $periode_pembukuan,
			'nilai_tanah_smst1' => $this->cleanAndConvertToFloat($nilai_tanah_smst1),
			'nilai_nontanah_smst1' => $this->cleanAndConvertToFloat($nilai_non_tanah_smst1),
			'nilai_kdp_smst1' => $this->cleanAndConvertToFloat($nilai_kdp_smst1),
			'nilai_akumulasi_penyusutan_smst1' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_smst1),
			'nilai_kewajiban_konjas_smst1' => $this->cleanAndConvertToFloat($nilai_kewajiban_konjas_smst1),
			'nilai_amortasi_kewajiban_konjas_smst1' => $this->cleanAndConvertToFloat($nilai_am_kewajiban_konjas_smst1),
			'nilai_tanah_tw3' => $this->cleanAndConvertToFloat($nilai_tanah_tw3),
			'nilai_nontanah_tw3' => $this->cleanAndConvertToFloat($nilai_non_tanah_tw3),
			'nilai_kdp_tw3' => $this->cleanAndConvertToFloat($nilai_kdp_tw3),
			'nilai_akumulasi_penyusutan_tw3' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_tw3),
			'nilai_kewajiban_konjas_tw3' => $this->cleanAndConvertToFloat($nilai_kewajiban_konjas_tw3),
			'nilai_amortasi_kewajiban_konjas_tw3' => $this->cleanAndConvertToFloat($nilai_am_kewajiban_konjas_tw3),
			'nilai_tanah_smst2' => $this->cleanAndConvertToFloat($nilai_tanah_smst2),
			'nilai_nontanah_smst2' => $this->cleanAndConvertToFloat($nilai_non_tanah_smst2),
			'nilai_kdp_smst2' => $this->cleanAndConvertToFloat($nilai_kdp_smst2),
			'nilai_akumulasi_penyusutan_smst2' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_smst2),
			'nilai_kewajiban_konjas_smst2' => $this->cleanAndConvertToFloat($nilai_kewajiban_konjas_smst2),
			'nilai_amortasi_kewajiban_konjas_smst2' => $this->cleanAndConvertToFloat($nilai_am_kewajiban_konjas_smst2),
			'nilai_tanah_audited' => $this->cleanAndConvertToFloat($nilai_tanah_audited),
			'nilai_nontanah_audited' => $this->cleanAndConvertToFloat($nilai_non_tanah_audited),
			'nilai_kdp_audited' => $this->cleanAndConvertToFloat($nilai_kdp_audited),
			'nilai_akumulasi_penyusutan_audited' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_audited),
			'nilai_kewajiban_konjas_audited' => $this->cleanAndConvertToFloat($nilai_kewajiban_konjas_audited),
			'nilai_amortasi_kewajiban_konjas_audited' => $this->cleanAndConvertToFloat($nilai_am_kewajiban_konjas_audited),
			'tahun' => ($tahun == '') ? NULL : $tahun,
			'tahun_perolehan' => ($tahun == '') ? NULL : $tahun,
			'tahun_pembukuan' => ($tahun == '') ? NULL : $tahun,
			'sisa_manfaat_slo' => ($smt == '') ? NULL : $smt,
			'sisa_manfaat_tarif' => ($sms == '') ? NULL : $sms,
			'created_by' => $this->session->userdata('id_user'),
		);

		function uploadFile($fileKey, $defaultFilename) {
			if (isset($_FILES[$fileKey]) && is_uploaded_file($_FILES[$fileKey]['tmp_name'])) {
				$sourcePath = $_FILES[$fileKey]['tmp_name'];
				$namf = $_FILES[$fileKey]['name'];
				$fileExtension = strtolower(pathinfo($namf, PATHINFO_EXTENSION));
		
				// Mengecek MIME type
				$finfo = finfo_open(FILEINFO_MIME_TYPE);
				$fileType = finfo_file($finfo, $sourcePath);
				finfo_close($finfo);
		
				// Daftar ekstensi dan MIME type yang diperbolehkan
				$allowedMimeTypes = ['application/pdf'];
				$allowedExtensions = ['pdf'];
		
				if (in_array($fileType, $allowedMimeTypes) && in_array($fileExtension, $allowedExtensions)) {
					// Sanitasi nama file
					$safeFileName = preg_replace("/[^a-zA-Z0-9\._-]/", "_", $namf);
					$newFileName = date('Ymd_His') . "~" . $safeFileName;
					$targetPath = FCPATH . "assets/file_perolehan_mutasi/" . $newFileName;
		
					if (move_uploaded_file($sourcePath, $targetPath)) {
						return $newFileName;
					} else {
						echo "1_Gagal mengunggah file!";
						die();
					}
				} else {
					echo "1_File yang diunggah harus berupa PDF!";
					die();
				}
			} else {
				return $defaultFilename; // Gunakan file lama jika tidak ada file yang diunggah
			}
		}
		
		// Gunakan fungsi uploadFile untuk setiap file
		$fil_lk = uploadFile('file_lk', $filename_lk);
		$fil_kertas = uploadFile('file_kertas', $filename_kertas);
		$fil_lk_smst2 = uploadFile('file_lk_smst2', $filename_lk_smst2);
		$fil_kertas_smst2 = uploadFile('file_kertas_smst2', $filename_kertas_smst2);
		$fil_lk_tw3 = uploadFile('file_lk_tw3', $filename_lk_tw3);
		$fil_kertas_tw3 = uploadFile('file_kertas_tw3', $filename_kertas_tw3);
		$fil_lk_audited = uploadFile('file_lk_audited', $filename_lk_audited);
		$fil_kertas_audited = uploadFile('file_kertas_audited', $filename_kertas_audited);
		


		$data_file = array(
			'file_lk' => $fil_lk,
			'file_ba_rekonsiliasi' => $fil_kertas,
			'file_lk_smst2' => $fil_lk_smst2,
			'file_ba_rekonsiliasi_smst2' => $fil_kertas_smst2,
			'file_lk_tw3' => $fil_lk_tw3,
			'file_ba_rekonsiliasi_tw3' => $fil_kertas_tw3,
			'file_lk_audited' => $fil_lk_audited,
			'file_ba_rekonsiliasi_audited' => $fil_kertas_audited,
		);
		$data = array_merge($data, $data_file);
		if ($id == '') {
			if($tahun > 2023){
			$this->db->insert('saldo_awal', $data_awal);
			}
			$x = $this->db->insert('perolehan_mutasi', $data);
			// echo $this->db->last_query();
			$z = "insert";

		} else {
			if($tahun > 2023){
			$this->db->delete("saldo_awal", [
				"id_perjanjian_pengusahaan" => $nm_pengusahaan,
				"tahun" => $tahun
			]);
			$this->db->insert('saldo_awal', $data_awal);
			}
			$this->db->where('id_perolehan_mutasi', $id);
			$x = $this->db->update('perolehan_mutasi', $data);
			$z = "update";
		}


		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('v_perolehan_mutasi', $requestData);

		echo json_encode($data);
	}




	function tampildata($table = '', $colum = '', $val_colum = '', $combo = '')
	{
		$tab = explode("~", $table)[0];
	
		if ($combo != '') {
			$nama = explode("~", $table)[1];
			$kode = explode("~", $table)[2];
			$this->db->select("$nama as nama");
			$this->db->select("$kode as kode");
		}
	
		if ($colum == '') {
			$data = $this->db->get($tab)->result_array();
		} else {
			$dt = array();
			$columns = explode("~", $colum);
			$values = explode("~", $val_colum);
	
			// Hapus prefix 'idx' dari val_colum jika ada
			for ($x = 0; $x < count($values); $x++) {
				$values[$x] = str_replace('idx', '', $values[$x]); // Menghapus 'idx'
			}
	
			for ($x = 0; $x < count($columns); $x++) {
				$col = $columns[$x];
				$val = str_replace('_', ' ', $values[$x]);
				$dt[$col] = $val;
			}
	
			$data = $this->db->get_where($tab, $dt)->result_array();
		}
	
		echo json_encode($data);
	}

	function delete_saldo_awal($table, $colum, $id)
{
    // Ambil satu baris data berdasarkan kolom dan ID
    $res = $this->db->where($colum, $id)->get($table)->row();

    // Periksa apakah data ditemukan
    if ($res) {
        // Hapus data di tabel saldo_awal
        $this->db->delete("saldo_awal", [
            "id_perjanjian_pengusahaan" => $res->id_perjanjian_pengusahaan,
            "tahun" => $res->tahun
        ]);
    } else {
        // Log atau tangani kasus jika data tidak ditemukan
        error_log("Data tidak ditemukan untuk penghapusan saldo_awal di tabel {$table}");
    }
}

function delete_data($table, $colum, $id)
{
    // Hapus data terkait di tabel saldo_awal
    $this->delete_saldo_awal($table, $colum, $id);

    // Hapus data di tabel utama
    $this->db->where($colum, $id);
    $res = $this->db->delete($table);

    // Tampilkan hasil
    if ($res) {
        echo 0; // Berhasil
    } else {
        echo 1; // Gagal
    }
}

	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
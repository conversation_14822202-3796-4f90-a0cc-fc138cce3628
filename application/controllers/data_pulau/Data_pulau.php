<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Data_pulau extends CI_Controller
{

	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url
		$this->load->model('data_pulau/M_data_table', 'm_data_table');
		$this->load->library(array('session', 'template'));
		$this->load->database();
		// if ($this->session->userdata('active') != 1) {
		// 	redirect('home');
		// }
		// Allow tampildata and ssp methods without session check for AJAX calls
		$method = $this->router->fetch_method();
		if (!in_array($method, ['tampildata', 'ssp']) && $this->session->userdata('active') != 1) {
			redirect(base_url('login'));
		}
	}

	public function index()
	{
		$js_file = $this->load->view('data_pulau/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => 'Data Pulau',
			"jv_script" => $js_file
		);

		$this->load->view('data_pulau/v_data', $data);
	}


	function insert_data()
	{
		$id = $this->input->post('id');
		$nama = $this->input->post('nama');


		$data = array(
			'nm_pulau' => $nama,
		);
		if ($id == '') {
			$x = $this->db->insert('ref_pulau', $data);
			$z = "insert";
		} else {
			$this->db->where('id_pulau', $id);
			$x = $this->db->update('ref_pulau', $data);
			$z = "update";
		}
		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('ref_pulau', $requestData);

		echo json_encode($data);
	}




	function tampildata()
	{
		// Get all URI segments
		$segments = $this->uri->segment_array();

		// For URL: /data_pulau/tampildata/table/colum/val_colum/combo
		// Segments: [1]=data_pulau, [2]=tampildata, [3]=table, [4]=colum, [5]=val_colum, [6]=combo
		$table = isset($segments[3]) ? urldecode($segments[3]) : '';
		$colum = isset($segments[4]) ? urldecode($segments[4]) : '';
		$val_colum = isset($segments[5]) ? urldecode($segments[5]) : '';
		$combo = isset($segments[6]) ? urldecode($segments[6]) : '';

		// Debug: log the parameters
		log_message('debug', 'Data_pulau tampildata called with: table=' . $table . ', colum=' . $colum . ', val_colum=' . $val_colum . ', combo=' . $combo);

		if (empty($table)) {
			echo json_encode(['error' => 'Table parameter is required']);
			return;
		}

		$tab = explode("~", $table)[0];

		// Check if table exists
		if (!$this->db->table_exists($tab)) {
			echo json_encode(['error' => 'Table does not exist: ' . $tab]);
			return;
		}

		if ($combo == 'combobox') {
			$table_parts = explode("~", $table);
			if (count($table_parts) >= 3) {
				$nama = $table_parts[1];
				$kode = $table_parts[2];
				$this->db->select("$nama as nama");
				$this->db->select("$kode as kode");
			}
		}

		if (empty($colum) || $colum == '1') {
			$data = $this->db->get($tab)->result_array();
		} else {
			if (!empty($val_colum) && $val_colum != '1') {
				$data = $this->db->get_where($tab, array($colum => str_replace('_', ' ', $val_colum)))->result_array();
			} else {
				$data = $this->db->get($tab)->result_array();
			}
		}

		echo json_encode($data);
	}
	function delete_data($table, $colum, $id)
	{
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
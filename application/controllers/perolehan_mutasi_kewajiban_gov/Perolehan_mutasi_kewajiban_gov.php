<?php
defined('BASEPATH') or exit('No direct script access allowed');

class perolehan_mutasi_kewajiban_gov extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url

		$this->load->model('perolehan_mutasi_kewajiban_gov/M_data_table');

		$this->load->library('session');
		$this->load->database();
		if ($this->session->userdata('active') != 1) {
			redirect('home');
		}

	}

	public function index()
	{
		$js_file = $this->load->view('perolehan_mutasi_kewajiban_gov/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => 'Perjanjian Konsesi Jasa',
			"jv_script" => $js_file
		);

		$this->load->view('perolehan_mutasi_kewajiban_gov/v_data', $data);
		// $this->upgrade_data();
	}

	public function upgrade_data()
	{
		$currentYear = date('Y');

		$this->db->where('tahun <', $currentYear);
		$query = $this->db->get('perolehan_mutasi_kewajiban_gov');

		if ($query->num_rows() > 0) {
			$dataToUpdate = array('status_data' => 'old');

			$this->db->where('tahun <', $currentYear);
			$this->db->update('perolehan_mutasi_kewajiban_gov', $dataToUpdate);
		}
	}
	function cleanAndConvertToFloat($input)
	{
		// Jika input kosong, kembalikan NULL
		if ($input == '') {
			return NULL;
		}
	
		// Hapus semua spasi (jika ada)
		$input = str_replace(' ', '', $input);
	
		// Ubah format desimal dan ribuan
		// Ganti koma dengan titik untuk desimal
		// Hapus titik-titik ribuan
		$cleaned = str_replace('.', '', $input);
		$cleaned = str_replace(',', '.', $cleaned);
	
		// Menggunakan bcmath untuk menjaga presisi
		return $cleaned; // Kembalikan sebagai string untuk operasi lebih lanjut
	}

	function insert_data()
	{
		// echo $this->input->post('nilai_buku_tw1_2');
		$id = $this->input->post('id');
		$nm_pengusahaan = $this->input->post('nm_perjanjian');
		$periode_perolehan = $this->input->post('p_perolehan');
		$periode_pembukuan = $this->input->post('p_pembukuan');
		$tahun = $this->input->post('tahun_layout');
		$nilai_buku_kewajiban = $this->input->post('nilai_buku_kewajiban');
		$nilai_mutasi_kewajiban_tw1_2 = $this->input->post('nilai_mutasi_kewajiban_tw1_2');
		$nilai_perolehan_tw1_2 = $this->input->post('nilai_perolehan_tw1_2');
		$nilai_buku_tw1_2 = $this->input->post('nilai_buku_tw1_2');
		$nilai_buku_tw3 = $this->input->post('nilai_buku_tw3');
		$nilai_buku_tw4 = $this->input->post('nilai_buku_tw4');
		$nilai_buku_audited = $this->input->post('nilai_buku_audited');
		$nilai_mutasi_kewajiban_tw3 = $this->input->post('nilai_mutasi_kewajiban_tw3');
		$nilai_perolehan_tw3 = $this->input->post('nilai_perolehan_tw3');
		$nilai_mutasi_kewajiban_tw4 = $this->input->post('nilai_mutasi_kewajiban_tw4');
		$nilai_perolehan_tw4 = $this->input->post('nilai_perolehan_tw4');
		$nilai_mutasi_kewajiban_audited = $this->input->post('nilai_mutasi_kewajiban_audited');
		$nilai_perolehan_audited = $this->input->post('nilai_perolehan_audited');
		$filename_lk = $this->input->post('filenames_lk');
		$filename_kertas = $this->input->post('filenames_kertas');
		$data_awal=[];
		if($tahun > 2022){

			
				// Prioritaskan nilai berdasarkan urutan
				if (!empty($nilai_buku_audited)) {
					$total = $nilai_buku_audited;
				} elseif (!empty($nilai_buku_tw4)) {
					$total = $nilai_buku_tw4;
				} elseif (!empty($nilai_buku_tw3)) {
					$total = $nilai_buku_tw3;
				} elseif (!empty($nilai_buku_tw1_2)) {
					$total = $nilai_buku_tw1_2;
				} else {
					$total = null; // Jika tidak ada data
				}
			
				// Buat array data awal
				$data_awal = array(
					'id_perjanjian_pengusahaan' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
					'tahun' => $tahun,
					'nilai_perolehan_audited' => $this->cleanAndConvertToFloat($total),
				);
			
		}
			

		$data = array(
			'id_perjanjian_pengusahaan' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
			'periode_perolehan' => ($periode_perolehan == '') ? NULL : $periode_perolehan,
			'periode_pembukuan' => ($periode_pembukuan == '') ? NULL : $periode_pembukuan,
			'tahun' => ($tahun == '') ? NULL : $tahun,
			'tahun_perolehan' => ($tahun == '') ? NULL : $tahun,
			'tahun_pembukuan' => ($tahun == '') ? NULL : $tahun,
			'nilai_buku_kewajiban' => $this->cleanAndConvertToFloat($nilai_buku_kewajiban),
			'nilai_mutasi_kewajiban_tw1_2' => $this->cleanAndConvertToFloat($nilai_mutasi_kewajiban_tw1_2),
			'nilai_perolehan_tw1_2' => $this->cleanAndConvertToFloat($nilai_perolehan_tw1_2),
			'nilai_buku_kewajiban_tw1_2' => $this->cleanAndConvertToFloat($nilai_buku_tw1_2),
			'nilai_buku_kewajiban_tw3' => $this->cleanAndConvertToFloat($nilai_buku_tw3),
			'nilai_buku_kewajiban_tw4' => $this->cleanAndConvertToFloat($nilai_buku_tw4),
			'nilai_buku_kewajiban_audited' => $this->cleanAndConvertToFloat($nilai_buku_audited),
			'nilai_mutasi_kewajiban_tw3' => $this->cleanAndConvertToFloat($nilai_mutasi_kewajiban_tw3),
			'nilai_perolehan_tw3' => $this->cleanAndConvertToFloat($nilai_perolehan_tw3),
			'nilai_mutasi_kewajiban_tw4' => $this->cleanAndConvertToFloat($nilai_mutasi_kewajiban_tw4),
			'nilai_perolehan_tw4' => $this->cleanAndConvertToFloat($nilai_perolehan_tw4),
			'nilai_mutasi_kewajiban_audited' => $this->cleanAndConvertToFloat($nilai_mutasi_kewajiban_audited),
			'nilai_perolehan_audited' => $this->cleanAndConvertToFloat($nilai_perolehan_audited),
			'created_by' => $this->session->userdata('id_user'),
			'created_at' => date("Y-m-d"),
		);
		// print_r("<pre>");
		// print_r($data);
		// print_r("</pre>");
		// die();
	
		if (isset($_FILES['file_kertas']) && is_uploaded_file($_FILES['file_kertas']['tmp_name'])) {
			$sourcePath = $_FILES['file_kertas']['tmp_name'];
			$namf = $_FILES['file_kertas']['name'];
			$fileExtension = strtolower(pathinfo($namf, PATHINFO_EXTENSION)); // Mengecek ekstensi file
		
			// Gunakan finfo_file() untuk validasi MIME type
			$finfo = finfo_open(FILEINFO_MIME_TYPE);
			$fileType = finfo_file($finfo, $sourcePath);
			finfo_close($finfo);
		
			// Daftar MIME type yang diperbolehkan (sesuaikan jika perlu)
			$allowedMimeTypes = ['application/pdf'];
		
			// Daftar ekstensi yang diperbolehkan
			$allowedExtensions = ['pdf'];
		
			// Validasi file berdasarkan MIME type & ekstensi
			if (in_array($fileType, $allowedMimeTypes) && in_array($fileExtension, $allowedExtensions)) {
				// Sanitasi nama file untuk keamanan
				$safeFileName = preg_replace("/[^a-zA-Z0-9\._-]/", "_", $namf);
		
				// Buat nama file unik dengan timestamp
				$fil_kertas = date('Ymd_His') . "~" . $safeFileName;
				$targetPath = FCPATH . "assets/file_perolehan_mutasi_kewajiban_gov/" . $fil_kertas;
		
				// Pindahkan file jika valid
				if (move_uploaded_file($sourcePath, $targetPath)) {
					// echo "File berhasil diunggah!";
				} else {
					echo "Gagal mengunggah file!";
					die();
				}
			} else {
				echo "1_File yang diunggah harus berupa PDF!";
				die();
			}
		} else {
			$fil_kertas = $filename_kertas; // Jika tidak ada file yang diunggah, gunakan file lama
		}
		$data_file = array(
			'file_kertas_kerja_retrospektif' => $fil_kertas,
		);
		$data = array_merge($data, $data_file);
		if ($id == '') {
			if($tahun > 2022){
				$this->db->insert('saldo_awal_kewajiban_gov', $data_awal);
				}
			$x = $this->db->insert('perolehan_mutasi_kewajiban_gov', $data);
			if ($x) {
				$id = $this->db->insert_id();
				$z = "insert";
			}
		} else {
			if($tahun > 2022){
				$this->db->delete("saldo_awal_kewajiban_gov", [
					"id_perjanjian_pengusahaan" => $nm_pengusahaan,
					"tahun" => $tahun
				]);
				$this->db->insert('saldo_awal_kewajiban_gov', $data_awal);
				}
			// unset($data['nilai_buku_kewajiban']);
			$this->db->where('id_perolehan_mutasi_kewajiban_gov', $id);
			$x = $this->db->update('perolehan_mutasi_kewajiban_gov', $data);
			$z = "update";
		}
	


		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
		
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('v_perolehan_mutasi_kewajiban_gov', $requestData);

		echo json_encode($data);
	}




	function tampildata($table = '', $colum = '', $val_colum = '', $combo = '')
{
    $tab = explode("~", $table)[0];

    if ($combo != '') {
        $nama = explode("~", $table)[1];
        $kode = explode("~", $table)[2];
        $this->db->select("$nama as nama");
        $this->db->select("$kode as kode");
    }

    if ($colum == '') {
        $data = $this->db->get($tab)->result_array();
    } else {
        $dt = array();
        $columns = explode("~", $colum);
        $values = explode("~", $val_colum);

        // Hapus prefix 'idx' dari val_colum jika ada
        for ($x = 0; $x < count($values); $x++) {
            $values[$x] = str_replace('idx', '', $values[$x]); // Menghapus 'idx'
        }

        for ($x = 0; $x < count($columns); $x++) {
            $col = $columns[$x];
            $val = str_replace('_', ' ', $values[$x]);
            $dt[$col] = $val;
        }

        $data = $this->db->get_where($tab, $dt)->result_array();
    }

    echo json_encode($data);
}
	function delete_saldo_awal($table, $colum, $id)
	{
		// Ambil satu baris data berdasarkan kolom dan ID
		$res = $this->db->where($colum, $id)->get($table)->row();
	
		// Periksa apakah data ditemukan
		if ($res) {
			// Hapus data di tabel saldo_awal
			$this->db->delete("saldo_awal_kewajiban_gov", [
				"id_perjanjian_pengusahaan" => $res->id_perjanjian_pengusahaan,
				"tahun" => $res->tahun
			]);
		} else {
			// Log atau tangani kasus jika data tidak ditemukan
			error_log("Data tidak ditemukan untuk penghapusan saldo_awal di tabel {$table}");
		}
	}
	function delete_data($table, $colum, $id)
	{
		$this->delete_saldo_awal($table, $colum, $id);
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
	
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Data_users extends CI_Controller
{

	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url
		$this->load->library(array('session', 'template'));
		$this->load->model('data_users/M_data_table', 'm_data_table');
		$this->load->database();

		// Allow tampildata and ssp methods without session check for AJAX calls
		$method = $this->router->fetch_method();
		if (!in_array($method, ['tampildata', 'ssp']) && $this->session->userdata('active') != 1) {
			redirect(base_url('login'));
		}
	}

	function page()
	{


		header("Access-Control-Allow-Origin: *");
		$data = array();

		$title = "";

		// $js_file2 = $this->load->view('data_users/js_file2', '', true);

		$js_file = $this->load->view('data_users/js_file', '', true);
		// if (!empty($this->session->users['kd_bujt'])) {
		//     $kd_prov_irmsv3 = $this->get_prov_irmsv3($this->session->users['kd_bujt']);
		// }
		$modal_tambah = $this->load->view('data_users/modal_tambah', '', true);

		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => $title,
			"jv_script" => $js_file,
			"tambah" => $modal_tambah,
		);



		$this->template->set('title', $title);
		$this->template->load('default_layout', 'contents', 'data_users/v_data', $data);

	}

	public function index()
	{

		// if ($tahaps == '') {
		//     $tap = $this->session->konfig_kd_tahapan;
		// } else {
		//     $tap = $tahaps;
		// }


		header("Access-Control-Allow-Origin: *");
		$data = array();

		$title = "Data Users";

		// $js_file2 = $this->load->view('data_users/js_file2', '', true);

		$js_file = $this->load->view('data_users/js_file', '', true);
		$modal_tambah = $this->load->view('data_users/modal_tambah', '', true);

		;
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => $title,
			"jv_script" => $js_file,
			"tambah" => $modal_tambah,
		);
		// print_r($data);
		// $this->load->view('index', $data);

		$this->load->view('data_users/v_data', $data);
		// $this->load->view('modal_tambah');


	}

	function insert_data()
	{
		$id = $this->input->post('id');
		$nm_user = $this->input->post('nama');
		$satker = $this->input->post('satker');
		$password = $this->input->post('password');
		$username = $this->input->post('username');
		$role = $this->input->post('role');
		$a = array();
		$c = array();
		if ($password != '') {
			$a = array(
				'password' => md5($password)
			);
		}

		$b = array(
			'fullname' => $nm_user,
			'id_user_group' => $role,
			'username' => $username,
			'userlogin' => $username,
		);
		if ($role == 3) {
			$c = array(
				'kode_satker' => $satker,
			);
		} elseif ($role == 4) {
			$c = array(
				'kode_eselon_1' => $satker,
			);
		}
		$data = array_merge($b, $a, $c);
		if ($id == '') {
			$x = $this->db->insert('user', $data);
			$z = "insert";
		} else {
			$this->db->where('id', $id);
			$x = $this->db->update('user', $data);
			$z = "update";
		}
		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}

	public function getDataTable($kategori, $tahun = '')
	{
		$this->load->model('m_data');
		$requestData = $_POST;

		$data = $this->m_data->getRecords($requestData, $kategori, $tahun);

		echo json_encode($data);
	}

	public function ssp()
	{


		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('v_user', $requestData);

		echo json_encode($data);
	}



	function tampildata()
	{
		// Get all URI segments
		$segments = $this->uri->segment_array();

		// For URL: /data_users/tampildata/table/colum/val_colum/combo
		// Segments: [1]=data_users, [2]=tampildata, [3]=table, [4]=colum, [5]=val_colum, [6]=combo
		$table = isset($segments[3]) ? urldecode($segments[3]) : '';
		$colum = isset($segments[4]) ? urldecode($segments[4]) : '';
		$val_colum = isset($segments[5]) ? urldecode($segments[5]) : '';
		$combo = isset($segments[6]) ? urldecode($segments[6]) : '';

		// Debug: log the parameters
		log_message('debug', 'Data_users tampildata called with: table=' . $table . ', colum=' . $colum . ', val_colum=' . $val_colum . ', combo=' . $combo);

		if (empty($table)) {
			echo json_encode(['error' => 'Table parameter is required']);
			return;
		}

		$tab = explode("~", $table)[0];

		// Check if table exists
		if (!$this->db->table_exists($tab)) {
			echo json_encode(['error' => 'Table does not exist: ' . $tab]);
			return;
		}

		if ($combo == 'combobox') {
			$table_parts = explode("~", $table);
			if (count($table_parts) >= 3) {
				$nama = $table_parts[1];
				$kode = $table_parts[2];
				$this->db->select("$nama as nama");
				$this->db->select("$kode as kode");
			}
		}

		if (empty($colum) || $colum == '1') {
			$data = $this->db->get($tab)->result_array();
		} else {
			if (!empty($val_colum) && $val_colum != '1') {
				$data = $this->db->get_where($tab, array($colum => str_replace('_', ' ', $val_colum)))->result_array();
			} else {
				$data = $this->db->get($tab)->result_array();
			}
		}

		echo json_encode($data);
	}
	function delete_data($table, $colum, $id)
	{
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
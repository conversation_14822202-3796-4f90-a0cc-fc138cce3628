<?php

defined('BASEPATH') or exit('No direct script access allowed');

require_once(APPPATH . "vendor/autoload.php");

use Firebase\JWT\JWT;

class Login extends CI_Controller
{

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct()
    {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        $this->load->library(array('form_validation', 'Recaptcha', 'template'));
        $this->load->database();
        if (!empty($_GET)) {
            // Jika tidak ada parameter GET, redirect ke halaman login
            redirect('login'); // Sesuaikan dengan URL halaman login Anda
        }
    }



    function check_connection()
    {
        echo json_encode(array("con_status" => "active"));
    }

    public function index()
    {

        $modal_tambah = $this->load->view('login/modal_tambah', '', true);
        $data = array(
            'captcha' => $this->recaptcha->getWidget(),
            'script_captcha' => $this->recaptcha->getScriptTag(),
            'modal_tambah' => $modal_tambah
        );
        $this->template->set('title', 'LOGIN');
        $js_file = $this->load->view('login/js_file', '', true); //asign view into variabel
        $this->template->set('jv_script', $js_file); //javascript file
        $this->template->load('guard_layout', 'contents', 'login/index', $data);
    }




    /**
     * Generate a secure password hash
     *
     * @param string $password The plain text password
     * @return string The secure password hash
     */
    private function secure_password_hash($password)
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    /**
     * Get configuration values from the database
     *
     * @return array Configuration values
     */
    private function get_config()
    {
        // Use Query Builder instead of direct SQL for better security
        $this->db->select('nilai');
        $this->db->from('aset_config');
        $this->db->where('tahun', date('Y')); // Use current year instead of hardcoded 2021

        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Get user data by username or email
     *
     * @param string $username The username or email to search for
     * @param string $field The field to search in (email or userlogin)
     * @return array User data
     */
    private function get_user($username, $field)
    {
        // Validate the field parameter to prevent SQL injection
        $allowed_fields = array('email', 'userlogin', 'username', 'id_user');

        if (!in_array($field, $allowed_fields)) {
            // If field is not in the allowed list, default to email
            $field = 'email';
        }

        // Build the query using CodeIgniter's Query Builder for safety
        $this->db->select('
            CAST(a.id_user AS INTEGER) as id_user,
            CAST(a.id_user_group AS INTEGER) as id_user_group,
            CAST(a.kd_satker AS INTEGER) as kd_satker,
            CAST(a.kd_bujt AS INTEGER) as kd_bujt,
            a.kd_prov,
            CAST(a.id_sub_user_group AS INTEGER) as id_sub_user_group,
            a.username,
            a.email,
            a.firstname,
            a.lastname,
            a.password,
            b.nama as roledesc,
            b.alias as role,
            a.is_approval,
            a.nama
        ');
        $this->db->from('aset_users a');
        $this->db->join('aset_user_group b', 'a.id_user_group = b.id_user_group AND a.id_sub_user_group = b.id_sub_user_group', 'left');
        $this->db->where("a.$field", $username);
        $this->db->distinct();

        $query = $this->db->get();
        return $query->result_array();
    }

    private function get_token($u, $n, $i)
    {

        $now = time();
        $payload = array(
            "user_id" => $u,
            "nama" => $n,
            "id_user_group" => $i,
            "exp" => time() + WGI_JWT_EXP_DELTA_SECONDS
        );

        $jwt = JWT::encode($payload, WGI_JWT_KEY);
        // $decoded = JWT::decode($jwt, WGI_JWT_KEY, array(WGI_JWT_ALG));

        return $jwt;

    }
    function aksi_login()
    {
        $username = $this->input->post('user');
        $password = $this->input->post('pass'); // Get plain password, don't hash it yet

        // Let's create a simple login that works directly with the database
        // First, let's check what tables are available
        $tables = $this->db->list_tables();

        // Find the user in any available user table
        $user_found = false;
        $cek = null;

        // Load security validator
        $this->load->library('security_validator');

        // Validate input
        if (!$this->security_validator->validate_input($username, 'username') &&
            !$this->security_validator->validate_input($username, 'email')) {
            echo json_encode(array("status" => "fail", "msg" => "Invalid username format"));
            return;
        }

        // Use Query Builder for better security - get user first, then verify password
        $this->db->select('*');
        $this->db->from('aset_users');
        $this->db->where('userlogin', $username);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            // Found user, now verify password
            $user_row = $query->row();
            $stored_password = $user_row->password;

            // Check if password is correct
            $password_valid = false;

            // Check if it's an old MD5 hash (32 characters, hexadecimal)
            if (strlen($stored_password) == 32 && ctype_xdigit($stored_password)) {
                // Legacy MD5 verification
                if (md5($password) === $stored_password) {
                    $password_valid = true;
                    // Upgrade to secure hash
                    $this->upgrade_password_hash($user_row->id_user ?? $user_row->id, $password);
                }
            } else {
                // Modern password verification
                $password_valid = password_verify($password, $stored_password);
            }

            if ($password_valid) {
                $user_found = true;
                $cek = $query;
            }
        } else {
            // Try with just username in aset_users
            $this->db->where('userlogin', $username);
            $query = $this->db->get('aset_users');

            if ($query->num_rows() > 0) {
                // Found user in aset_users
                $user_found = true;
                $cek = $query;
            } else {
                // Try v_user if it exists
                if (in_array('v_user', $tables)) {
                    $this->db->where('userlogin', $username);
                    $query = $this->db->get('v_user');

                    if ($query->num_rows() > 0) {
                        // Found user in v_user
                        $user_found = true;
                        $cek = $query;
                    }
                }
            }
        }

        if (!$user_found) {
            // User not found in any table
            echo json_encode(array("status" => "fail", "msg" => "User tidak terdaftar"));
            return;
        }

        // At this point, we found the user in one of the tables
        $user_row = $cek->row();

        // Get user data - handle different field names
        $ida = isset($user_row->id_user) ? $user_row->id_user : (isset($user_row->id) ? $user_row->id : null);
        $email = isset($user_row->email) ? $user_row->email : null;
        $username = isset($user_row->userlogin) ? $user_row->userlogin : (isset($user_row->username) ? $user_row->username : null);
        $id_user_group = isset($user_row->id_user_group) ? $user_row->id_user_group : null;
        $kode_satker = isset($user_row->kode_satker) ? $user_row->kode_satker : (isset($user_row->kd_satker) ? $user_row->kd_satker : null);
        $kode_eselon_1 = isset($user_row->kode_eselon_1) ? $user_row->kode_eselon_1 : null;

        // Determine fullname based on user group
        $fullname = null;
        if ($id_user_group == 3 && isset($user_row->nm_satker)) {
            $fullname = $user_row->nm_satker;
        } elseif ($id_user_group == 4 && isset($user_row->nm_unit_eselon_1)) {
            $fullname = $user_row->nm_unit_eselon_1;
        } else {
            if (isset($user_row->nm_role)) {
                $fullname = $user_row->nm_role;
            } elseif (isset($user_row->nama)) {
                $fullname = $user_row->nama;
            } elseif (isset($user_row->username)) {
                $fullname = $user_row->username;
            } else {
                $fullname = $username;
            }
        }

        // If we're here and using the MD5 query, we've already verified the password
        if ($query !== $cek) {
            // We need to verify the password
            $stored_password = isset($user_row->password) ? $user_row->password : null;

            if ($stored_password === null) {
                echo json_encode(array("status" => "fail", "msg" => "Kesalahan sistem: Password tidak ditemukan"));
                return;
            }

            // Check if the password is stored in the old MD5 format
            if (strlen($stored_password) == 32 && ctype_xdigit($stored_password)) {
                // This is an MD5 hash - verify using the old method
                if (md5($password) != $stored_password) {
                    echo json_encode(array("status" => "fail", "msg" => "Password salah"));
                    return;
                }
            } else {
                // Assume it's a modern password hash - verify using password_verify
                if (!password_verify($password, $stored_password)) {
                    echo json_encode(array("status" => "fail", "msg" => "Password salah"));
                    return;
                }
            }
        }

        // If we got here, password is correct
        // Create a session with all available data
        $data_session = array();

        // Add essential fields with fallbacks
        $data_session['id_user'] = $ida;
        $data_session['email'] = $email;
        $data_session['nama'] = $fullname;
        $data_session['username'] = $username;
        $data_session['active'] = '1';

        // Add optional fields if they exist
        if ($id_user_group !== null) {
            $data_session['id_user_group'] = $id_user_group;
            $data_session['id_user_group_real'] = $id_user_group;
        }

        if ($kode_satker !== null) {
            $data_session['kode_satker'] = $kode_satker;
        }

        if ($kode_eselon_1 !== null) {
            $data_session['kode_eselon_1'] = $kode_eselon_1;
        }

        // Set the session data
        $this->session->set_userdata($data_session);

        // Return success response
        echo json_encode(array(
            "status" => "sukses",
            "msg" => "Berhasil Login",
            "group" => $id_user_group
        ));
    }

    /**
     * Upgrade a user's password hash to the current secure format
     *
     * @param int $user_id The user ID
     * @param string $plain_password The plain text password
     * @return bool True if successful, false otherwise
     */
    private function upgrade_password_hash($user_id, $plain_password)
    {
        // Generate a new secure hash
        $new_hash = password_hash($plain_password, PASSWORD_DEFAULT);

        // First check if user exists in aset_users with id_user
        $this->db->where('id_user', $user_id);
        $user_exists = $this->db->get('aset_users')->num_rows() > 0;

        if ($user_exists) {
            // Update using id_user field
            $this->db->where('id_user', $user_id);
            return $this->db->update('aset_users', array('password' => $new_hash));
        } else {
            // Try with 'id' field
            $this->db->where('id', $user_id);
            return $this->db->update('aset_users', array('password' => $new_hash));
        }
    }

    public function create_login()
    {
        $this->load->database();
        $username = $this->input->post("email", TRUE);
        $password = $this->input->post("password", TRUE);
        $field = "";
        //  if(preg_match("/@/i", $username)){
        $field = 'email';
        // echo 'pake email'; die();
        //   }else{
        //     $field = 'userlogin';
        //   }
        //  // $url = WGI_APP_BASE_URL."login/get_token";
        //     $recaptcha = $this->input->post("captcha");
        //     $response = $this->recaptcha->verifyResponse($recaptcha);
        //     //  if (!isset($response['success']) || $response['success'] <> true) {
        //     //    echo json_encode(array("status" => "captcha_error"));
        //     //    return;
        //     // } else {
        // $password_hash=password_hash($password, PASSWORD_DEFAULT);
        // echo $password_hash;
        // die();
        $ruser = $this->get_user($username, $field);
        $jml_user = count($ruser);
        // echo $jml_user.' - '.$ruser[0]['is_approval']; die();


        if ($jml_user == 1 && $ruser[0]['is_approval'] == 1) {
            // echo $ruser[0]['password'];

            if (password_verify($password, $ruser[0]['password'])) {

                // echo 'masuk 1';

                $rconfig = $this->get_config();
                $arc = array();
                foreach ($rconfig as $row3) {
                    $dt3[] = $row3['nilai'];
                    $arc = $dt3;
                }

                $rc = array(
                    "tahun" => date("Y")
                    // "semester" => $arc[1]
                );

                $rd = array();
                foreach ($ruser as $row) {
                    $dt[] = $row['id_user'];
                    $dt[] = $row['username'];
                    $dt[] = $row['id_user_group'];

                    $rd = $dt;
                }

                $rs = array();
                foreach ($ruser as $row2) {
                    $dt2[0]['id_user'] = (int) $row2['id_user'];
                    $dt2[0]['id_user_group'] = (int) $row2['id_user_group'];
                    $dt2[0]['kd_satker'] = (int) $row2['kd_satker'];
                    $dt2[0]['kd_bujt'] = (int) $row2['kd_bujt'];
                    $dt2[0]['kd_prov'] = $row2['kd_prov'];
                    $dt2[0]['id_sub_user_group'] = (int) $row2['id_sub_user_group'];
                    $dt2[0]['username'] = $row2['username'];
                    $dt2[0]['email'] = $row2['email'];
                    $dt2[0]['firstname'] = '';
                    $dt2[0]['lastname'] = '';
                    $dt2[0]['roledesc'] = $row2['roledesc'];
                    $dt2[0]['role'] = $row2['role'];
                    $dt2[0]['is_approval'] = $row2['is_approval'];
                    $dt2[0]['nama'] = $row2['nama'];

                    $rs = $dt2;


                }

                $token = $this->get_token($rd[0], $rd[1], $rd[2]);


                $json_data = array(
                    "sukses" => "sukses",
                    "data" => $rs,
                    "token" => $token,
                    "config" => $rc
                );

                $data = json_decode(json_encode($json_data));
                // $child = "";
                if (isset($data->sukses)) {
                    $users = json_decode(json_encode($data->data), true);
                    $konfig = json_decode(json_encode($data->config), true);
                    $query3 = $this->db->get_where('v_user', array('id_user' => $users[0]["id_user"]));

                    $nmbujt = $query3->row_array();
                    $new_users = array(
                        "id_user" => (int) $users[0]["id_user"],
                        "id_user_group" => (int) $users[0]["id_user_group"],
                        //role
                        "id_user_group_real" => (int) $users[0]["id_user_group"],
                        //id_user_group
                        "id_sub_user_group" => (int) $users[0]["id_sub_user_group"],
                        // "id_sub_sub_user_group" => $nmsatker["id_sub_sub_user_group"],
                        "kd_satker" => (int) $users[0]["kd_satker"],
                        "kd_bujt" => (int) $users[0]["kd_bujt"],
                        "kd_prov" => $users[0]["kd_prov"],
                        //  "kd_camat" => $users[0]["kd_camat"],
                        //"kd_lurah" => $users[0]["kd_lurah"],
                        "username" => $users[0]["username"],
                        "email" => $users[0]["email"],
                        "firstname" => $users[0]["firstname"],
                        "nama" => $users[0]["nama"],
                        "roledesc" => $users[0]["roledesc"],
                        "role" => $users[0]["role"],
                        "lastname" => $users[0]["lastname"],
                        // "nm_bujt" => $nmbujt['nama_bujt'],
                        "kd_kabkot" => $query3->row()->kd_kabkot,
                        "kd_kec" => $query3->row()->kd_kec,
                        "kd_desa" => $query3->row()->kd_desa,
                        "nama_user_group" => $query3->row()->nama,
                        "is_approval" => $query3->row()->is_approval,
                        "userlogin" => $nmbujt['userlogin']
                    );

                    // print_r($new_users);

                    $this->session->set_userdata('users', $new_users);
                    $this->session->set_userdata('token', $data->token);



                    $konfig = json_decode(json_encode($data->config), true);
                    $this->session->set_userdata('konfig_tahun_ang', date('Y'));
                    // $this->session->set_userdata('konfig_tahapan', $konfig['semester']);

                    echo json_encode(
                        array(
                            "status" => "sukses",
                            "data" => $users
                        )
                    );
                } elseif (isset($data->error)) {
                    echo json_encode(array("status" => "gagal login"));
                }
            } else {
                echo json_encode(array("status" => "Password salah", "email" => $ruser[0]["email"]));

            }

        } elseif ($jml_user == 1 && $ruser[0]['is_approval'] == null) {
            echo json_encode(array("status" => "Akun anda belum di approval"));
        } else {
            echo json_encode(array("status" => "Email tidak terdaftar"));
        }
        // print_r($ruser);
        // die();
        //}

    }
    /**
     * Update data in a table
     *
     * @param string $table Table name
     * @param string $colum Column name for WHERE clause
     * @param mixed $id Value for WHERE clause
     * @param string $col Column name to update
     * @param mixed $val New value
     */
    function update_data($table, $colum, $id, $col, $val)
    {
        // Validate table name to prevent SQL injection
        $allowed_tables = array('aset_users', 'aset_config', 'aset_user_group');

        if (!in_array($table, $allowed_tables)) {
            echo 1; // Error
            return;
        }

        // Validate column names to prevent SQL injection
        $allowed_columns = array(
            'id_user', 'id', 'userlogin', 'username', 'email',
            'password', 'nama', 'is_approval', 'id_user_group',
            'id_sub_user_group', 'kd_satker', 'kode_satker',
            'kd_bujt', 'kd_prov', 'kode_eselon_1'
        );

        if (!in_array($colum, $allowed_columns) || !in_array($col, $allowed_columns)) {
            echo 1; // Error
            return;
        }

        // Convert 0 to NULL if needed
        if ($val == 0) {
            $val = NULL;
        }

        // Use Query Builder for safe updates
        $this->db->where($colum, $id);
        $res = $this->db->update($table, array($col => $val));

        if ($res) {
            echo 0; // Success
        } else {
            echo 1; // Error
        }
    }

    public function logout()
    {
        //echo "logout"; die();
        // $this->session->unset_userdata('konfig_tahun_ang');
        // $this->session->unset_userdata('konfig_tahapan');
        // $this->session->unset_userdata('konfig_status_sipro');
        // $this->session->unset_userdata('konfig_status_krisna');
        $this->session->unset_userdata('users');
        $this->session->unset_userdata('token');
        $this->session->sess_destroy();
        redirect('login', 'refresh');
    }


    function send_email()
    {
        // $email = $this->input->post("formData")["email"];
        // $email = '<EMAIL>';
        $email = $this->input->post("email");
        $kode = $this->input->post("kode");
        $nama = $this->input->post("nama");
        $this->load->library('phpmailer_lib');
        // PHPMailer object
        $mail = $this->phpmailer_lib->load();
        // SMTP configuration
        $mail->isSMTP();
        $mail->Host = 'smtp.gmail.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = 'rulxtcfmekgzqkrs';
        $mail->SMTPSecure = 'tls';
        $mail->Port = 587;

        $mail->setFrom('<EMAIL>', 'Admin Sistem Monitoring P3-TGAI');
        // $mail->addReplyTo('<EMAIL>', 'SmartFinance Official Admin');

        // email tujuan mu
        $mail->addAddress($email);


        // Email subject
        $mail->Subject = "Reset Password Akun Sistem Monitoring P3-TGAI";

        // Set email format to HTML
        $mail->isHTML(true);

        // Email body content / isi
        $message = "<html><head><head></head><body style='background:;'>
              <img src='" . base_url() . "/assets/themes/adminity/images/moon_logo.png' style='width:30%;'>
             <br><br>
             Yth.
             " . $nama . "<br>Ini adalah email konfirmasi reset password akun Sistem Monitoring P3-TGAI<br>
            Silakan klik link di bawah ini untuk melakukan reset password anda<br>
            <a href='" . base_url('login/lupa_password?id=') . $kode . "'>Klik Disini</a>";

        $mail->Body = $message;


        if ($mail->send()) {
            echo "0";
        } else {
            echo "1";
        }

    }

    function lupa_password()
    {
        $kode = $this->input->get('id');
        header("Access-Control-Allow-Origin: *");
        $modal_tambah = $this->load->view('login/modal_tambah', '', true);
        $data = array(
            'captcha' => $this->recaptcha->getWidget(),
            'script_captcha' => $this->recaptcha->getScriptTag(),
            'kode' => $kode
        );
        $this->template->set('title', 'Reset Password');
        $js_file = $this->load->view('login/js_file', '', true); //asign view into variabel
        $this->template->set('jv_script', $js_file); //javascript file
        $this->template->load('guard_layout', 'contents', 'login/form_lupa_password', $data);
    }

    function update_password()
    {
        $password = $this->input->post("password");
        $kode = $this->input->post("kode");
        $dt = $this->db->get_where('v_user', array('kode' => $kode));
        if ($dt->num_rows() < 1) {
            echo "1";
        } else {
            $email = $dt->row()->email;
            $this->db->where('email', $email);
            $upd = $this->db->update('aset_users', array('password' => password_hash($password, PASSWORD_BCRYPT)));
            if ($upd) {
                echo "0";
            } else {
                echo "1";
            }
        }
    }

    function tampildata($tab = '', $colum = '', $val_colum = '')
    {
        // Check if the requested table exists
        if (!$this->db->table_exists($tab)) {
            // If the requested table doesn't exist, try to use a fallback
            if ($tab == 'v_users' && $this->db->table_exists('v_user')) {
                $tab = 'v_user';
            } elseif ($tab == 'v_users' && $this->db->table_exists('aset_users')) {
                $tab = 'aset_users';
            } else {
                // Return empty array if table doesn't exist
                echo json_encode([]);
                return;
            }
        }

        $data = $this->db->get_where($tab, array($colum => str_replace('_', ' ', $val_colum)))->result_array();
        echo json_encode($data);
    }

    /**
     * Debug function to check database structure
     * Access via: /login/debug_db
     */
    public function debug_db()
    {
        // Only allow in development environment
        if (ENVIRONMENT !== 'development') {
            show_404();
            return;
        }

        $output = array();

        // List all tables
        $output['tables'] = $this->db->list_tables();

        // Check user tables structure
        if ($this->db->table_exists('aset_users')) {
            $output['aset_users_fields'] = $this->db->list_fields('aset_users');
            $output['aset_users_sample'] = $this->db->limit(1)->get('aset_users')->result_array();
        }

        if ($this->db->table_exists('v_user')) {
            $output['v_user_fields'] = $this->db->list_fields('v_user');
            $output['v_user_sample'] = $this->db->limit(1)->get('v_user')->result_array();
        }

        // Output as JSON
        header('Content-Type: application/json');
        echo json_encode($output, JSON_PRETTY_PRINT);
    }
}
<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Saldo_awal_kuantitas_gov extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url

		$this->load->model('saldo_awal_kuantitas_gov/M_data_table', 'm_data_table');

		$this->load->library(array('session', 'template'));
		$this->load->database();

		// Allow tampildata and ssp methods without session check for AJAX calls
		$method = $this->router->fetch_method();
		if (!in_array($method, ['tampildata', 'ssp']) && $this->session->userdata('active') != 1) {
			redirect(base_url('login'));
		}

	}

	public function index()
	{
		$js_file = $this->load->view('saldo_awal_kuantitas_gov/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => 'Saldo Awal Kuantitas Gov',
			"jv_script" => $js_file
		);

		$this->load->view('saldo_awal_kuantitas_gov/v_data', $data);
	}

	function page()
	{
		$data = array();
		$title = "Saldo Awal Kuantitas Gov";

		$js_file = $this->load->view('saldo_awal_kuantitas_gov/js_file', '', true);

		$data = array(
			"title" => $title,
			"jv_script" => $js_file,
		);

		// Check if this is an AJAX request
		if ($this->input->is_ajax_request()) {
			// For AJAX requests, return only the content
			$content = $this->load->view('saldo_awal_kuantitas_gov/v_data', $data, true);
			echo $content . $js_file;
		} else {
			// For regular requests, use the template
			$this->template->set('title', $title);
			$this->template->load('default_layout', 'contents', 'saldo_awal_kuantitas_gov/v_data', $data);
		}
	}
	function cleanAndConvertToFloat($input)
	{
		if ($input == '') {
			return NULL;
		} else {
			$cleaned = str_replace('Rp ', '', $input);
			$cleaned = str_replace(' ', '', $cleaned);
			$cleaned = str_replace('.', '', $cleaned);
			return $cleaned;
		}

	}

	function insert_data()
	{
		$id = $this->input->post('id');
		$nm_pengusahaan = $this->input->post('nm_perjanjian');
	
		$kode_satker = $this->input->post('satker');
		$periode_perolehan = $this->input->post('p_perolehan');
		$periode_pembukuan = $this->input->post('p_pembukuan');
		$kuantitas_tanah_smst1 = $this->input->post('kuantitas_tanah_smst1');
		$kuantitas_gedung_bangunan_smst1 = $this->input->post('kuantitas_gedung_bangunan_smst1');
		$kuantitas_kdp_smst1 = $this->input->post('kuantitas_kdp_smst1');
		$kuantitas_jij_smst1 = $this->input->post('kuantitas_jij_smst1');
		$kuantitas_peralatan_mesin_smst1 = $this->input->post('kuantitas_peralatan_mesin_smst1');
		$kuantitas_tanah_tw3 = $this->input->post('kuantitas_tanah_tw3');
		$kuantitas_gedung_bangunan_tw3 = $this->input->post('kuantitas_gedung_bangunan_tw3');
		$kuantitas_kdp_tw3 = $this->input->post('kuantitas_kdp_tw3');
		$kuantitas_jij_tw3 = $this->input->post('kuantitas_jij_tw3');
		$kuantitas_peralatan_mesin_tw3 = $this->input->post('kuantitas_peralatan_mesin_tw3');
		$kuantitas_tanah_smst2 = $this->input->post('kuantitas_tanah_smst2');
		$kuantitas_gedung_bangunan_smst2 = $this->input->post('kuantitas_gedung_bangunan_smst2');
		$kuantitas_kdp_smst2 = $this->input->post('kuantitas_kdp_smst2');
		$kuantitas_jij_smst2 = $this->input->post('kuantitas_jij_smst2');
		$kuantitas_peralatan_mesin_smst2 = $this->input->post('kuantitas_peralatan_mesin_smst2');
		$kuantitas_tanah_audited = $this->input->post('kuantitas_tanah_audited');
		$kuantitas_gedung_bangunan_audited = $this->input->post('kuantitas_gedung_bangunan_audited');
		$kuantitas_kdp_audited = $this->input->post('kuantitas_kdp_audited');
		$kuantitas_jij_audited = $this->input->post('kuantitas_jij_audited');
		$kuantitas_peralatan_mesin_audited = $this->input->post('kuantitas_peralatan_mesin_audited');
		$filename_lk = $this->input->post('filenames_lk');
		$filename_kertas = $this->input->post('filenames_kertas');
		$tahun = $this->input->post('tahun_layout');
		$smt = $this->input->post('sisa_manfaat_tarif');
		$sms = $this->input->post('sisa_manfaat_slo');
		$data = array(
			'id_perjanjian_pengusahaan' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
			'periode_perolehan' => ($periode_perolehan == '') ? NULL : $periode_perolehan,
			'periode_pembukuan' => ($periode_pembukuan == '') ? NULL : $periode_pembukuan,
			'kuantitas_tanah_smst1' => ($kuantitas_tanah_smst1 == '') ? NULL : $kuantitas_tanah_smst1,
			'kuantitas_gedung_bangunan_smst1' => $this->cleanAndConvertToFloat($kuantitas_gedung_bangunan_smst1),
			'kuantitas_kdp_smst1' => $this->cleanAndConvertToFloat($kuantitas_kdp_smst1),
			'kuantitas_jij_smst1' => $this->cleanAndConvertToFloat($kuantitas_jij_smst1),
			'kuantitas_peralatan_mesin_smst1' => $this->cleanAndConvertToFloat($kuantitas_peralatan_mesin_smst1),
			'kuantitas_tanah_tw3' => $this->cleanAndConvertToFloat($kuantitas_tanah_tw3),
			'kuantitas_gedung_bangunan_tw3' => $this->cleanAndConvertToFloat($kuantitas_gedung_bangunan_tw3),
			'kuantitas_kdp_tw3' => $this->cleanAndConvertToFloat($kuantitas_kdp_tw3),
			'kuantitas_jij_tw3' => $this->cleanAndConvertToFloat($kuantitas_jij_tw3),
			'kuantitas_peralatan_mesin_tw3' => $this->cleanAndConvertToFloat($kuantitas_peralatan_mesin_tw3),
			'kuantitas_tanah_smst2' => $this->cleanAndConvertToFloat($kuantitas_tanah_smst2),
			'kuantitas_gedung_bangunan_smst2' => $this->cleanAndConvertToFloat($kuantitas_gedung_bangunan_smst2),
			'kuantitas_kdp_smst2' => $this->cleanAndConvertToFloat($kuantitas_kdp_smst2),
			'kuantitas_jij_smst2' => $this->cleanAndConvertToFloat($kuantitas_jij_smst2),
			'kuantitas_peralatan_mesin_smst2' => $this->cleanAndConvertToFloat($kuantitas_peralatan_mesin_smst2),
			'kuantitas_tanah_audited' => $this->cleanAndConvertToFloat($kuantitas_tanah_audited),
			'kuantitas_gedung_bangunan_audited' => $this->cleanAndConvertToFloat($kuantitas_gedung_bangunan_audited),
			'kuantitas_kdp_audited' => $this->cleanAndConvertToFloat($kuantitas_kdp_audited),
			'kuantitas_jij_audited' => $this->cleanAndConvertToFloat($kuantitas_jij_audited),
			'kuantitas_peralatan_mesin_audited' => $this->cleanAndConvertToFloat($kuantitas_peralatan_mesin_audited),
			'tahun' => ($tahun == '') ? NULL : $tahun,
			'tahun_perolehan' => ($tahun == '') ? NULL : $tahun,
			'tahun_pembukuan' => ($tahun == '') ? NULL : $tahun,
			'sisa_manfaat_slo' => ($smt == '') ? NULL : $smt,
			'sisa_manfaat_tarif' => ($sms == '') ? NULL : $sms,
			'satuan_tanah_smst1' => $this->input->post('satuan_tanah_smst1') == '' ? NULL : $this->input->post('satuan_tanah_smst1'),
			'satuan_kdp_smst1' => $this->input->post('satuan_kdp_smst1') == '' ? NULL : $this->input->post('satuan_kdp_smst1'),
			'satuan_jij_smst1' => $this->input->post('satuan_jij_smst1') == '' ? NULL : $this->input->post('satuan_jij_smst1'),
			'satuan_gedung_bangunan_smst1' => $this->input->post('satuan_gedung_bangunan_smst1') == '' ? NULL : $this->input->post('satuan_gedung_bangunan_smst1'),
			'satuan_peralatan_mesin_smst1' => $this->input->post('satuan_peralatan_mesin_smst1') == '' ? NULL : $this->input->post('satuan_peralatan_mesin_smst1'),
			'satuan_tanah_tw3' => $this->input->post('satuan_tanah_tw3') == '' ? NULL : $this->input->post('satuan_tanah_tw3'),
			'satuan_kdp_tw3' => $this->input->post('satuan_kdp_tw3') == '' ? NULL : $this->input->post('satuan_kdp_tw3'),
			'satuan_jij_tw3' => $this->input->post('satuan_jij_tw3') == '' ? NULL : $this->input->post('satuan_jij_tw3'),
			'satuan_gedung_bangunan_tw3' => $this->input->post('satuan_gedung_bangunan_tw3') == '' ? NULL : $this->input->post('satuan_gedung_bangunan_tw3'),
			'satuan_peralatan_mesin_tw3' => $this->input->post('satuan_peralatan_mesin_tw3') == '' ? NULL : $this->input->post('satuan_peralatan_mesin_tw3'),
			'satuan_tanah_smst2' => $this->input->post('satuan_tanah_smst2') == '' ? NULL : $this->input->post('satuan_tanah_smst2'),
			'satuan_kdp_smst2' => $this->input->post('satuan_kdp_smst2') == '' ? NULL : $this->input->post('satuan_kdp_smst2'),
			'satuan_jij_smst2' => $this->input->post('satuan_jij_smst2') == '' ? NULL : $this->input->post('satuan_jij_smst2'),
			'satuan_gedung_bangunan_smst2' => $this->input->post('satuan_gedung_bangunan_smst2') == '' ? NULL : $this->input->post('satuan_gedung_bangunan_smst2'),
			'satuan_peralatan_mesin_smst2' => $this->input->post('satuan_peralatan_mesin_smst2') == '' ? NULL : $this->input->post('satuan_peralatan_mesin_smst2'),
			'satuan_tanah_audited' => $this->input->post('satuan_tanah_audited') == '' ? NULL : $this->input->post('satuan_tanah_audited'),
			'satuan_kdp_audited' => $this->input->post('satuan_kdp_audited') == '' ? NULL : $this->input->post('satuan_kdp_audited'),
			'satuan_jij_audited' => $this->input->post('satuan_jij_audited') == '' ? NULL : $this->input->post('satuan_jij_audited'),
			'satuan_gedung_bangunan_audited' => $this->input->post('satuan_gedung_bangunan_audited') == '' ? NULL : $this->input->post('satuan_gedung_bangunan_audited'),
			'satuan_peralatan_mesin_audited' => $this->input->post('satuan_peralatan_mesin_audited') == '' ? NULL : $this->input->post('satuan_peralatan_mesin_audited'),
			'created_by' => $this->session->userdata('id_user'),
		);

		if (isset($_FILES['file_lk']) && is_uploaded_file($_FILES['file_lk']['tmp_name'])) {
			$sourcePath = $_FILES['file_lk']['tmp_name'];
			$namf = $_FILES['file_lk']['name'];
			$fileExtension = strtolower(pathinfo($namf, PATHINFO_EXTENSION)); // Mengecek ekstensi file
		
			// Gunakan finfo_file() untuk validasi MIME type
			$finfo = finfo_open(FILEINFO_MIME_TYPE);
			$fileType = finfo_file($finfo, $sourcePath);
			finfo_close($finfo);
		
			// Daftar MIME type yang diperbolehkan
			$allowedMimeTypes = ['application/pdf'];
		
			// Daftar ekstensi yang diperbolehkan
			$allowedExtensions = ['pdf'];
		
			// Validasi file berdasarkan MIME type & ekstensi
			if (in_array($fileType, $allowedMimeTypes) && in_array($fileExtension, $allowedExtensions)) {
				// Sanitasi nama file untuk keamanan
				$safeFileName = preg_replace("/[^a-zA-Z0-9\._-]/", "_", $namf);
		
				// Buat nama file unik dengan timestamp
				$fil_lk = date('Ymd_His') . "~" . $safeFileName;
				$targetPath = FCPATH . "assets/file_saldo_awal_kuantitas_gov/" . $fil_lk;
		
				// Pindahkan file jika valid
				if (move_uploaded_file($sourcePath, $targetPath)) {
				} else {
					echo "Gagal mengunggah file!";
					die();
				}
			} else {
				echo "1_File yang diunggah harus berupa PDF!";
				die();
			}
		} else {
			$fil_lk = $filename_lk; // Jika tidak ada file yang diunggah, gunakan file lama
		}
		
		$data_file = array(
			'file_lk' => $fil_lk,
		);
		$data = array_merge($data, $data_file);
		if ($id == '') {
			$x = $this->db->insert('saldo_awal_kuantitas_gov', $data);
			if ($x) {
				$id = $this->db->insert_id();
				$z = "insert";
			}
		} else {
			$this->db->where('id_saldo_awal_kuantitas_gov', $id);
			$x = $this->db->update('saldo_awal_kuantitas_gov', $data);
			$z = "update";
		}


		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('v_saldo_awal_kuantitas_gov', $requestData);

		echo json_encode($data);
	}




	function tampildata()
	{
		// Get all URI segments
		$segments = $this->uri->segment_array();

		// For URL: /saldo_awal_kuantitas_gov/tampildata/table/colum/val_colum/combo
		// Segments: [1]=saldo_awal_kuantitas_gov, [2]=tampildata, [3]=table, [4]=colum, [5]=val_colum, [6]=combo
		$table = isset($segments[3]) ? urldecode($segments[3]) : '';
		$colum = isset($segments[4]) ? urldecode($segments[4]) : '';
		$val_colum = isset($segments[5]) ? urldecode($segments[5]) : '';
		$combo = isset($segments[6]) ? urldecode($segments[6]) : '';

		// Debug: log the parameters
		log_message('debug', 'Saldo_awal_kuantitas_gov tampildata called with: table=' . $table . ', colum=' . $colum . ', val_colum=' . $val_colum . ', combo=' . $combo);

		if (empty($table)) {
			echo json_encode(['error' => 'Table parameter is required']);
			return;
		}

		$tab = explode("~", $table)[0];

		// Check if table exists
		if (!$this->db->table_exists($tab)) {
			echo json_encode(['error' => 'Table does not exist: ' . $tab]);
			return;
		}

		if ($combo == 'combobox') {
			$table_parts = explode("~", $table);
			if (count($table_parts) >= 3) {
				$nama = $table_parts[1];
				$kode = $table_parts[2];
				$this->db->select("$nama as nama");
				$this->db->select("$kode as kode");
			}
		}

		if (empty($colum) || $colum == '1') {
			$data = $this->db->get($tab)->result_array();
		} else {
			$dt = array();
			$columns = explode("~", $colum);
			$values = explode("~", $val_colum);

			// Hapus prefix 'idx' dari val_colum jika ada
			for ($x = 0; $x < count($values); $x++) {
				$values[$x] = str_replace('idx', '', $values[$x]); // Menghapus 'idx'
			}

			for ($x = 0; $x < count($columns); $x++) {
				if (isset($values[$x]) && !empty($values[$x]) && $values[$x] != '1') {
					$col = $columns[$x];
					$val = str_replace('_', ' ', $values[$x]);
					$dt[$col] = $val;
				}
			}

			if (!empty($dt)) {
				$data = $this->db->get_where($tab, $dt)->result_array();
			} else {
				$data = $this->db->get($tab)->result_array();
			}
		}

		echo json_encode($data);
	}
	function delete_data($table, $colum, $id)
	{
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Saldo_awal_lp extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url

		$this->load->model('saldo_awal_lp/M_data_table', 'm_data_table');

		$this->load->library(array('session', 'template'));
		$this->load->database();

		// Allow tampildata and ssp methods without session check for AJAX calls
		$method = $this->router->fetch_method();
		if (!in_array($method, ['tampildata', 'ssp']) && $this->session->userdata('active') != 1) {
			redirect(base_url('login'));
		}

	}

	public function index()
	{
		$js_file = $this->load->view('saldo_awal_lp/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => 'Saldo Awal LP',
			"jv_script" => $js_file
		);

		$this->load->view('saldo_awal_lp/v_data', $data);
		// $this->upgrade_data();
	}

	function page()
	{
		$data = array();
		$title = "Saldo Awal LP";

		$js_file = $this->load->view('saldo_awal_lp/js_file', '', true);

		$data = array(
			"title" => $title,
			"jv_script" => $js_file,
		);

		// Check if this is an AJAX request
		if ($this->input->is_ajax_request()) {
			// For AJAX requests, return only the content
			$content = $this->load->view('saldo_awal_lp/v_data', $data, true);
			echo $content . $js_file;
		} else {
			// For regular requests, use the template
			$this->template->set('title', $title);
			$this->template->load('default_layout', 'contents', 'saldo_awal_lp/v_data', $data);
		}
	}

	public function upgrade_data()
	{
		$currentYear = date('Y');

		$this->db->where('tahun <', $currentYear);
		$query = $this->db->get('saldo_awal_lp');

		if ($query->num_rows() > 0) {
			$dataToUpdate = array('status_data' => 'old');

			$this->db->where('tahun <', $currentYear);
			$this->db->update('saldo_awal_lp', $dataToUpdate);
		}
	}
	function cleanAndConvertToFloat($input)
	{
		if ($input == '') {
			return NULL;
		} else {
			// Hapus tanda Rp dan spasi
			$cleaned = str_replace(['Rp', ' '], '', $input);

			// Hapus semua tanda desimal (titik) jika ada
			$cleaned = str_replace('.', '', $cleaned);

			// Konversi ke float
			return (float) $cleaned;
		}
	}

	function insert_data()
	{
		$id = $this->input->post('id');
		$nm_pengusahaan = $this->input->post('nm_perjanjian');
		$kode_satker =$this->session->userdata('kode_satker');
		$periode_perolehan = $this->input->post('p_perolehan');
		$periode_pembukuan = $this->input->post('p_pembukuan');
		$kuantitas = $this->input->post('kuantitas');
		$satuan = $this->input->post('satuan');
		$nilai_tanah_smst1 = $this->input->post('nilai_tanah_smst1');
		$nilai_non_tanah_smst1 = $this->input->post('nilai_non_tanah_smst1');
		$nilai_kdp_smst1 = $this->input->post('nilai_kdp_smst1');
		$total_aset_smst1 = $this->input->post('total_aset_smst1');
		$nilai_ak_penyusutan_smst1 = $this->input->post('nilai_ak_penyusutan_smst1');
		$nilai_buku_smst1 = $this->input->post('nilai_buku_smst1');
		$nilai_tanah_tw3 = $this->input->post('nilai_tanah_tw3');
		$nilai_non_tanah_tw3 = $this->input->post('nilai_non_tanah_tw3');
		$nilai_kdp_tw3 = $this->input->post('nilai_kdp_tw3');
		$total_aset_tw3 = $this->input->post('total_aset_tw3');
		$nilai_ak_penyusutan_tw3 = $this->input->post('nilai_ak_penyusutan_tw3');
		$nilai_buku_tw3 = $this->input->post('nilai_buku_tw3');
		$nilai_tanah_unaudited = $this->input->post('nilai_tanah_unaudited');
		$nilai_non_tanah_unaudited = $this->input->post('nilai_non_tanah_unaudited');
		$nilai_kdp_unaudited = $this->input->post('nilai_kdp_unaudited');

		$total_aset_unaudited = $this->input->post('total_aset_unaudited');
		$nilai_ak_penyusutan_unaudited = $this->input->post('nilai_ak_penyusutan_unaudited');
		$nilai_buku_unaudited = $this->input->post('nilai_buku_unaudited');
		$nilai_tanah_audited = $this->input->post('nilai_tanah_audited');
		$nilai_non_tanah_audited = $this->input->post('nilai_non_tanah_audited');
		$nilai_kdp_audited = $this->input->post('nilai_kdp_audited');

		$total_aset_audited = $this->input->post('total_aset_audited');
		$nilai_ak_penyusutan_audited = $this->input->post('nilai_ak_penyusutan_audited');
		$nilai_buku_audited = $this->input->post('nilai_buku_audited');
		$filename_lk = $this->input->post('filenames_lk');
		$filename_kertas = $this->input->post('filenames_kertas');
		$tgl_perolehan = $this->input->post('tgl_perolehan');
		$tahun = $this->input->post('tahun_layout');
		$barang = $this->input->post('barang');
		$nup = $this->input->post('nup');
		$data = array(
			'id_perjanjian_pengusahaan' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
			'kode_satker' => ($kode_satker == '') ? NULL : $kode_satker,
			'periode_perolehan' => ($periode_perolehan == '') ? NULL : $periode_perolehan,
			'periode_pembukuan' => ($periode_pembukuan == '') ? NULL : $periode_pembukuan,
			'kode_barang' => ($barang == '') ? NULL : $barang,
			'nup' => ($nup == '') ? NULL : $nup,
			'kuantitas' => ($kuantitas == '') ? NULL : $kuantitas,
			'satuan' => ($satuan == '') ? NULL : $satuan,
			'tgl_perolehan' => ($tgl_perolehan == '') ? NULL : $tgl_perolehan,
			'nilai_tanah_smst1' => $this->cleanAndConvertToFloat($nilai_tanah_smst1),
			'nilai_nontanah_smst1' => $this->cleanAndConvertToFloat($nilai_non_tanah_smst1),
			'nilai_kdp_smst1' => $this->cleanAndConvertToFloat($nilai_kdp_smst1),
			'nilai_total_smst1' => $this->cleanAndConvertToFloat($total_aset_smst1),
			'nilai_penyusutan_smst1' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_smst1),
			'nilai_buku_smst1' => $this->cleanAndConvertToFloat($nilai_buku_smst1),
			'nilai_tanah_tw3' => $this->cleanAndConvertToFloat($nilai_tanah_tw3),
			'nilai_nontanah_tw3' => $this->cleanAndConvertToFloat($nilai_non_tanah_tw3),
			'nilai_kdp_tw3' => $this->cleanAndConvertToFloat($nilai_kdp_tw3),
			'nilai_total_tw3' => $this->cleanAndConvertToFloat($total_aset_tw3),
			'nilai_penyusutan_tw3' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_tw3),
			'nilai_buku_tw3' => $this->cleanAndConvertToFloat($nilai_buku_tw3),
			'nilai_tanah_unaudited' => $this->cleanAndConvertToFloat($nilai_tanah_unaudited),
			'nilai_nontanah_unaudited' => $this->cleanAndConvertToFloat($nilai_non_tanah_unaudited),
			'nilai_kdp_unaudited' => $this->cleanAndConvertToFloat($nilai_kdp_unaudited),
			'nilai_total_unaudited' => $this->cleanAndConvertToFloat($total_aset_unaudited),
			'nilai_penyusutan_unaudited' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_unaudited),
			'nilai_buku_unaudited' => $this->cleanAndConvertToFloat($nilai_buku_unaudited),
			'nilai_tanah_audited' => $this->cleanAndConvertToFloat($nilai_tanah_audited),
			'nilai_nontanah_audited' => $this->cleanAndConvertToFloat($nilai_non_tanah_audited),
			'nilai_kdp_audited' => $this->cleanAndConvertToFloat($nilai_kdp_audited),
			'nilai_total_audited' => $this->cleanAndConvertToFloat($total_aset_audited),
			'nilai_penyusutan_audited' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_audited),
			'nilai_buku_audited' => $this->cleanAndConvertToFloat($nilai_buku_audited),
			'tahun' => ($tahun == '') ? NULL : $tahun,
			'tahun_perolehan' => ($tahun == '') ? NULL : $tahun,
			'tahun_pembukuan' => ($tahun == '') ? NULL : $tahun,
			'created_by' => $this->session->userdata('id_user'),
		);

		
		if (isset($_FILES['file_kertas']) && is_uploaded_file($_FILES['file_kertas']['tmp_name'])) {
			$sourcePath = $_FILES['file_kertas']['tmp_name'];
			$namf = $_FILES['file_kertas']['name'];
			$fileExtension = strtolower(pathinfo($namf, PATHINFO_EXTENSION)); // Mengecek ekstensi file
		
			// Gunakan finfo_file() untuk validasi MIME type
			$finfo = finfo_open(FILEINFO_MIME_TYPE);
			$fileType = finfo_file($finfo, $sourcePath);
			finfo_close($finfo);
		
			// Daftar MIME type yang diperbolehkan (sesuaikan jika perlu)
			$allowedMimeTypes = ['application/pdf'];
		
			// Daftar ekstensi yang diperbolehkan
			$allowedExtensions = ['pdf'];
		
			// Validasi file berdasarkan MIME type & ekstensi
			if (in_array($fileType, $allowedMimeTypes) && in_array($fileExtension, $allowedExtensions)) {
				// Sanitasi nama file untuk keamanan
				$safeFileName = preg_replace("/[^a-zA-Z0-9\._-]/", "_", $namf);
		
				// Buat nama file unik dengan timestamp
				$fil_kertas = date('Ymd_His') . "~" . $safeFileName;
				$targetPath = FCPATH . "assets/file_saldo_awal_lp/" . $fil_kertas;
		
				// Pindahkan file jika valid
				if (move_uploaded_file($sourcePath, $targetPath)) {
					// echo "File berhasil diunggah!";
				} else {
					echo "Gagal mengunggah file!";
					die();
				}
			} else {
				echo "1_File yang diunggah harus berupa PDF!";
				die();
			}
		} else {
			$fil_kertas = $filename_kertas; // Jika tidak ada file yang diunggah, gunakan file lama
		}
		$data_file = array(
			'file_kertas_kerja_retrospektif' => $fil_kertas,
		);
		$data = array_merge($data, $data_file);
		if ($id == '') {
			$x = $this->db->insert('saldo_awal_lp', $data);
			if ($x) {
				$id = $this->db->insert_id();
				$z = "insert";
			}
		} else {
			$this->db->where('id_saldo_awal_lp', $id);
			$x = $this->db->update('saldo_awal_lp', $data);
			$z = "update";
		}


		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('v_saldo_awal_lp', $requestData);

		echo json_encode($data);
	}




	function tampildata()
	{
		// Get all URI segments
		$segments = $this->uri->segment_array();

		// For URL: /saldo_awal_lp/tampildata/table/colum/val_colum/combo
		// Segments: [1]=saldo_awal_lp, [2]=tampildata, [3]=table, [4]=colum, [5]=val_colum, [6]=combo
		$table = isset($segments[3]) ? urldecode($segments[3]) : '';
		$colum = isset($segments[4]) ? urldecode($segments[4]) : '';
		$val_colum = isset($segments[5]) ? urldecode($segments[5]) : '';
		$combo = isset($segments[6]) ? urldecode($segments[6]) : '';

		// Debug: log the parameters
		log_message('debug', 'Saldo_awal_lp tampildata called with: table=' . $table . ', colum=' . $colum . ', val_colum=' . $val_colum . ', combo=' . $combo);

		if (empty($table)) {
			echo json_encode(['error' => 'Table parameter is required']);
			return;
		}

		$tab = explode("~", $table)[0];

		if ($combo == 'combobox') {
			$table_parts = explode("~", $table);
			if (count($table_parts) >= 3) {
				$nama = $table_parts[1];
				$kode = $table_parts[2];
				$this->db->select("$nama as nama");
				$this->db->select("$kode as kode");
			}
		}

		if (empty($colum) || $colum == '1') {
			$data = $this->db->get($tab)->result_array();
		} else {
			$dt = array();
			$columns = explode("~", $colum);
			$values = explode("~", $val_colum);

			// Hapus prefix 'idx' dari val_colum jika ada
			for ($x = 0; $x < count($values); $x++) {
				$values[$x] = str_replace('idx', '', $values[$x]); // Menghapus 'idx'
			}

			for ($x = 0; $x < count($columns); $x++) {
				if (isset($values[$x])) {
					$col = $columns[$x];
					$val = str_replace('_', ' ', $values[$x]);
					$dt[$col] = $val;
				}
			}

			$data = $this->db->get_where($tab, $dt)->result_array();
		}

		echo json_encode($data);
	}
	function delete_data($table, $colum, $id)
	{
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
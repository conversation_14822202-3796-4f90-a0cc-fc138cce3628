<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Saldo_awal_kewajiban extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url

		$this->load->model('saldo_awal_kewajiban/M_data_table', 'm_data_table');

		$this->load->library(array('session', 'template'));
		$this->load->database();

		// Allow tampildata and ssp methods without session check for AJAX calls
		$method = $this->router->fetch_method();
		if (!in_array($method, ['tampildata', 'ssp']) && $this->session->userdata('active') != 1) {
			redirect(base_url('login'));
		}

	}

	public function index()
	{
		$js_file = $this->load->view('saldo_awal_kewajiban/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => 'Saldo Awal Kewajiban',
			"jv_script" => $js_file
		);

		$this->load->view('saldo_awal_kewajiban/v_data', $data);
		// $this->upgrade_data();
	}

	function page()
	{
		$data = array();
		$title = "Saldo Awal Kewajiban";

		$js_file = $this->load->view('saldo_awal_kewajiban/js_file', '', true);

		$data = array(
			"title" => $title,
			"jv_script" => $js_file,
		);

		// Check if this is an AJAX request
		if ($this->input->is_ajax_request()) {
			// For AJAX requests, return only the content
			$content = $this->load->view('saldo_awal_kewajiban/v_data', $data, true);
			echo $content . $js_file;
		} else {
			// For regular requests, use the template
			$this->template->set('title', $title);
			$this->template->load('default_layout', 'contents', 'saldo_awal_kewajiban/v_data', $data);
		}
	}

	public function upgrade_data()
	{
		$currentYear = date('Y');

		$this->db->where('tahun <', $currentYear);
		$query = $this->db->get('saldo_awal_kewajiban');

		if ($query->num_rows() > 0) {
			$dataToUpdate = array('status_data' => 'old');

			$this->db->where('tahun <', $currentYear);
			$this->db->update('saldo_awal_kewajiban', $dataToUpdate);
		}
	}
	function cleanAndConvertToFloat($input)
	{
		// Jika input kosong, kembalikan NULL
		if ($input == '') {
			return NULL;
		}
	
		// Hapus semua spasi (jika ada)
		$input = str_replace(' ', '', $input);
	
		// Ubah format desimal dan ribuan
		// Ganti koma dengan titik untuk desimal
		// Hapus titik-titik ribuan
		$cleaned = str_replace('.', '', $input);
		$cleaned = str_replace(',', '.', $cleaned);
	
		// Menggunakan bcmath untuk menjaga presisi
		return $cleaned; // Kembalikan sebagai string untuk operasi lebih lanjut
	}

	function insert_data()
	{
		$id = $this->input->post('id');
		$nm_pengusahaan = $this->input->post('nm_perjanjian');
		$periode_perolehan = $this->input->post('p_perolehan');
		$periode_pembukuan = $this->input->post('p_pembukuan');
		$tahun = $this->input->post('tahun_layout');
		$nilai_buku_kewajiban = $this->input->post('nilai_buku_kewajiban');
		$nilai_mutasi_kewajiban_tw1_2 = $this->input->post('nilai_mutasi_kewajiban_tw1_2');
		$nilai_perolehan_tw1_2 = $this->input->post('nilai_perolehan_tw1_2');
		$nilai_tarif_tw1_2 = $this->input->post('nilai_tarif_tw1_2');
		$nilai_pendapatan_tw1_2 = $this->input->post('nilai_pendapatan_tw1_2');
		$nilai_mutasi_kewajiban_tw3 = $this->input->post('nilai_mutasi_kewajiban_tw3');
		$nilai_perolehan_tw3 = $this->input->post('nilai_perolehan_tw3');
		$nilai_tarif_tw3 = $this->input->post('nilai_tarif_tw3');
		$nilai_pendapatan_tw3 = $this->input->post('nilai_pendapatan_tw3');
		$nilai_mutasi_kewajiban_tw4 = $this->input->post('nilai_mutasi_kewajiban_tw4');
		$nilai_perolehan_tw4 = $this->input->post('nilai_perolehan_tw4');

		$nilai_tarif_tw4 = $this->input->post('nilai_tarif_tw4');
		$nilai_pendapatan_tw4 = $this->input->post('nilai_pendapatan_tw4');
		$nilai_mutasi_kewajiban_audited = $this->input->post('nilai_mutasi_kewajiban_audited');
		$nilai_perolehan_audited = $this->input->post('nilai_perolehan_audited');

		$nilai_tarif_audited = $this->input->post('nilai_tarif_audited');
		$nilai_pendapatan_audited = $this->input->post('nilai_pendapatan_audited');
		$nilai_tarif_tw3 = $this->input->post('nilai_tarif_tw3');
		$nilai_tarif_tw3 = $this->input->post('nilai_tarif_tw3');
		$filename_lk = $this->input->post('filenames_lk');
		$filename_kertas = $this->input->post('filenames_kertas');
		$data = array(
			'id_perjanjian_pengusahaan' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
			'periode_perolehan' => ($periode_perolehan == '') ? NULL : $periode_perolehan,
			'periode_pembukuan' => ($periode_pembukuan == '') ? NULL : $periode_pembukuan,
			'tahun' => ($tahun == '') ? NULL : $tahun,
			'tahun_perolehan' => ($tahun == '') ? NULL : $tahun,
			'tahun_pembukuan' => ($tahun == '') ? NULL : $tahun,
			'nilai_buku_kewajiban' => $this->cleanAndConvertToFloat($nilai_buku_kewajiban),
			'nilai_mutasi_kewajiban_tw1_2' => $this->cleanAndConvertToFloat($nilai_mutasi_kewajiban_tw1_2),
			'nilai_perolehan_tw1_2' => $this->cleanAndConvertToFloat($nilai_perolehan_tw1_2),
			'nilai_tarif_tw1_2' => $this->cleanAndConvertToFloat($nilai_tarif_tw1_2),
			'nilai_pendapatan_tw1_2' => $this->cleanAndConvertToFloat($nilai_pendapatan_tw1_2),
			'nilai_mutasi_kewajiban_tw3' => $this->cleanAndConvertToFloat($nilai_mutasi_kewajiban_tw3),
			'nilai_perolehan_tw3' => $this->cleanAndConvertToFloat($nilai_perolehan_tw3),

			'nilai_tarif_tw3' => $this->cleanAndConvertToFloat($nilai_tarif_tw3),
			'nilai_pendapatan_tw3' => $this->cleanAndConvertToFloat($nilai_pendapatan_tw3),
			'nilai_mutasi_kewajiban_tw4' => $this->cleanAndConvertToFloat($nilai_mutasi_kewajiban_tw4),
			'nilai_perolehan_tw4' => $this->cleanAndConvertToFloat($nilai_perolehan_tw4),

			'nilai_tarif_tw4' => $this->cleanAndConvertToFloat($nilai_tarif_tw4),
			'nilai_pendapatan_tw4' => $this->cleanAndConvertToFloat($nilai_pendapatan_tw4),
			'nilai_mutasi_kewajiban_audited' => $this->cleanAndConvertToFloat($nilai_mutasi_kewajiban_audited),
			'nilai_perolehan_audited' => $this->cleanAndConvertToFloat($nilai_perolehan_audited),

			'nilai_tarif_audited' => $this->cleanAndConvertToFloat($nilai_tarif_audited),
			'nilai_pendapatan_audited' => $this->cleanAndConvertToFloat($nilai_pendapatan_audited),
			'created_by' => $this->session->userdata('id_user'),
			'created_at' => date("Y-m-d"),
		);
		// print_r($data);
		if (isset($_FILES['file_lk']) && is_uploaded_file($_FILES['file_lk']['tmp_name'])) {
			$sourcePath = $_FILES['file_lk']['tmp_name'];
			$namf = $_FILES['file_lk']['name'];
			$fileExtension = strtolower(pathinfo($namf, PATHINFO_EXTENSION)); // Mengecek ekstensi file
		
			// Gunakan finfo_file() untuk validasi MIME type
			$finfo = finfo_open(FILEINFO_MIME_TYPE);
			$fileType = finfo_file($finfo, $sourcePath);
			finfo_close($finfo);
		
			// Daftar MIME type yang diperbolehkan
			$allowedMimeTypes = ['application/pdf'];
		
			// Daftar ekstensi yang diperbolehkan
			$allowedExtensions = ['pdf'];
		
			// Validasi file berdasarkan MIME type & ekstensi
			if (in_array($fileType, $allowedMimeTypes) && in_array($fileExtension, $allowedExtensions)) {
				// Sanitasi nama file untuk keamanan
				$safeFileName = preg_replace("/[^a-zA-Z0-9\._-]/", "_", $namf);
		
				// Buat nama file unik dengan timestamp
				$fil_lk = date('Ymd_His') . "~" . $safeFileName;
				$targetPath = FCPATH . "assets/file_saldo_awal_kewajiban/" . $fil_lk;
		
				// Pindahkan file jika valid
				if (move_uploaded_file($sourcePath, $targetPath)) {
					// echo "File berhasil diunggah!";
				} else {
					echo "Gagal mengunggah file!";
					die();
				}
			} else {
				echo "1_File yang diunggah harus berupa PDF!";
				die();
			}
		} else {
			$fil_lk = $filename_lk; // Jika tidak ada file yang diunggah, gunakan file lama
		}
		
		
		
		if (isset($_FILES['file_kertas']) && is_uploaded_file($_FILES['file_kertas']['tmp_name'])) {
			$sourcePath = $_FILES['file_kertas']['tmp_name'];
			$namf = $_FILES['file_kertas']['name'];
			$fileExtension = strtolower(pathinfo($namf, PATHINFO_EXTENSION)); // Mengecek ekstensi file
		
			// Gunakan finfo_file() untuk validasi MIME type
			$finfo = finfo_open(FILEINFO_MIME_TYPE);
			$fileType = finfo_file($finfo, $sourcePath);
			finfo_close($finfo);
		
			// Daftar MIME type yang diperbolehkan (sesuaikan jika perlu)
			$allowedMimeTypes = ['application/pdf'];
		
			// Daftar ekstensi yang diperbolehkan
			$allowedExtensions = ['pdf'];
		
			// Validasi file berdasarkan MIME type & ekstensi
			if (in_array($fileType, $allowedMimeTypes) && in_array($fileExtension, $allowedExtensions)) {
				// Sanitasi nama file untuk keamanan
				$safeFileName = preg_replace("/[^a-zA-Z0-9\._-]/", "_", $namf);
		
				// Buat nama file unik dengan timestamp
				$fil_kertas = date('Ymd_His') . "~" . $safeFileName;
				$targetPath = FCPATH . "assets/file_saldo_awal_kewajiban/" . $fil_kertas;
		
				// Pindahkan file jika valid
				if (move_uploaded_file($sourcePath, $targetPath)) {
					// echo "File berhasil diunggah!";
				} else {
					echo "Gagal mengunggah file!";
					die();
				}
			} else {
				echo "1_File yang diunggah harus berupa PDF!";
				die();
			}
		} else {
			$fil_kertas = $filename_kertas; // Jika tidak ada file yang diunggah, gunakan file lama
		}
		$data_file = array(
			'file_lk' => $fil_lk,
			'file_kertas_kerja_retrospektif' => $fil_kertas,
		);
		$data = array_merge($data, $data_file);
		if ($id == '') {
			$x = $this->db->insert('saldo_awal_kewajiban', $data);
			if ($x) {
				$id = $this->db->insert_id();
				$z = "insert";
			}
		} else {
			$this->db->where('id_saldo_awal_kewajiban', $id);
			$x = $this->db->update('saldo_awal_kewajiban', $data);
			$z = "update";
		}


		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('v_saldo_awal_kewajiban', $requestData);

		echo json_encode($data);
	}




	function tampildata()
	{
		// Get all URI segments
		$segments = $this->uri->segment_array();

		// For URL: /saldo_awal_kewajiban/tampildata/table/colum/val_colum/combo
		// Segments: [1]=saldo_awal_kewajiban, [2]=tampildata, [3]=table, [4]=colum, [5]=val_colum, [6]=combo
		$table = isset($segments[3]) ? urldecode($segments[3]) : '';
		$colum = isset($segments[4]) ? urldecode($segments[4]) : '';
		$val_colum = isset($segments[5]) ? urldecode($segments[5]) : '';
		$combo = isset($segments[6]) ? urldecode($segments[6]) : '';

		// Debug: log the parameters
		log_message('debug', 'Saldo_awal_kewajiban tampildata called with: table=' . $table . ', colum=' . $colum . ', val_colum=' . $val_colum . ', combo=' . $combo);

		if (empty($table)) {
			echo json_encode(['error' => 'Table parameter is required']);
			return;
		}

		$tab = explode("~", $table)[0];

		// Check if table exists
		if (!$this->db->table_exists($tab)) {
			echo json_encode(['error' => 'Table does not exist: ' . $tab]);
			return;
		}

		if ($combo == 'combobox') {
			$table_parts = explode("~", $table);
			if (count($table_parts) >= 3) {
				$nama = $table_parts[1];
				$kode = $table_parts[2];
				$this->db->select("$nama as nama");
				$this->db->select("$kode as kode");
			}
		}

		if (empty($colum) || $colum == '1') {
			$data = $this->db->get($tab)->result_array();
		} else {
			$dt = array();
			$columns = explode("~", $colum);
			$values = explode("~", $val_colum);

			// Hapus prefix 'idx' dari val_colum jika ada
			for ($x = 0; $x < count($values); $x++) {
				$values[$x] = str_replace('idx', '', $values[$x]); // Menghapus 'idx'
			}

			for ($x = 0; $x < count($columns); $x++) {
				if (isset($values[$x]) && !empty($values[$x]) && $values[$x] != '1') {
					$col = $columns[$x];
					$val = str_replace('_', ' ', $values[$x]);
					$dt[$col] = $val;
				}
			}

			if (!empty($dt)) {
				$data = $this->db->get_where($tab, $dt)->result_array();
			} else {
				$data = $this->db->get($tab)->result_array();
			}
		}

		echo json_encode($data);
	}
	function delete_data($table, $colum, $id)
	{
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
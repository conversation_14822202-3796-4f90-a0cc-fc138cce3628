<?php
defined('BASEPATH') or exit('No direct script access allowed');

class lp_monitoring extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url

		$this->load->model('lp_monitoring/M_data_table');

		$this->load->library('session');
		$this->load->database();
		if ($this->session->userdata('active') != 1) {
			redirect('home');
		}

	}

	public function index()
	{
		$js_file = $this->load->view('lp_monitoring/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => 'Perjanjian <PERSON>',
			"jv_script" => $js_file
		);

		$this->load->view('lp_monitoring/v_data', $data);
	}
	function cleanAndConvertToFloat($input)
	{
		if ($input == '') {
			return NULL;
		} else {
			$cleaned = str_replace('Rp ', '', $input);
			$cleaned = str_replace(' ', '', $cleaned);
			$cleaned = str_replace('.', '', $cleaned);
			return $cleaned;
		}

	}

	function insert_data()
	{
		$id = $this->input->post('id');
		$nm_pengusahaan = $this->input->post('nm_perjanjian');
		$id_slo = $this->input->post('id_slo');
		$id_tarif = $this->input->post('id_tarif');
		$kode_satker = $this->input->post('satker');
		$periode_perolehan = $this->input->post('p_perolehan');
		$periode_pembukuan = $this->input->post('p_pembukuan');
		$nilai_tanah_smst1 = $this->input->post('nilai_tanah_smst1');
		$nilai_non_tanah_smst1 = $this->input->post('nilai_non_tanah_smst1');
		$nilai_kdp_smst1 = $this->input->post('nilai_kdp_smst1');
		$nilai_ak_penyusutan_smst1 = $this->input->post('nilai_ak_penyusutan_smst1');
		$nilai_kewajiban_konjas_smst1 = $this->input->post('nilai_kewajiban_konjas_smst1');
		$nilai_am_kewajiban_konjas_smst1 = $this->input->post('nilai_am_kewajiban_konjas_smst1');
		$nilai_tanah_tw3 = $this->input->post('nilai_tanah_tw3');
		$nilai_non_tanah_tw3 = $this->input->post('nilai_non_tanah_tw3');
		$nilai_kdp_tw3 = $this->input->post('nilai_kdp_tw3');
		$nilai_ak_penyusutan_tw3 = $this->input->post('nilai_ak_penyusutan_tw3');
		$nilai_kewajiban_konjas_tw3 = $this->input->post('nilai_kewajiban_konjas_tw3');
		$nilai_am_kewajiban_konjas_tw3 = $this->input->post('nilai_am_kewajiban_konjas_tw3');
		$nilai_tanah_smst2 = $this->input->post('nilai_tanah_smst2');
		$nilai_non_tanah_smst2 = $this->input->post('nilai_non_tanah_smst2');
		$nilai_kdp_smst2 = $this->input->post('nilai_kdp_smst2');
		$nilai_ak_penyusutan_smst2 = $this->input->post('nilai_ak_penyusutan_smst2');
		$nilai_kewajiban_konjas_smst2 = $this->input->post('nilai_kewajiban_konjas_smst2');
		$nilai_am_kewajiban_konjas_smst2 = $this->input->post('nilai_am_kewajiban_konjas_smst2');
		$nilai_tanah_audited = $this->input->post('nilai_tanah_audited');
		$nilai_non_tanah_audited = $this->input->post('nilai_non_tanah_audited');
		$nilai_kdp_audited = $this->input->post('nilai_kdp_audited');
		$nilai_ak_penyusutan_audited = $this->input->post('nilai_ak_penyusutan_audited');
		$nilai_kewajiban_konjas_audited = $this->input->post('nilai_kewajiban_konjas_audited');
		$nilai_am_kewajiban_konjas_audited = $this->input->post('nilai_am_kewajiban_konjas_audited');
		$filename_lk = $this->input->post('filenames_lk');
		$filename_kertas = $this->input->post('filenames_kertas');
		$tahun = $this->input->post('tahun_layout');
		$smt = $this->input->post('sisa_manfaat_tarif');
		$sms = $this->input->post('sisa_manfaat_slo');

		$data = array(
			'id_perjanjian_pengusahaan' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
			'id_slo' => ($id_slo == '') ? NULL : $id_slo,
			'id_tarif' => ($id_tarif == '') ? NULL : $id_tarif,
			'kode_satker' => ($kode_satker == '') ? NULL : $kode_satker,
			'periode_perolehan' => ($periode_perolehan == '') ? NULL : $periode_perolehan,
			'periode_pembukuan' => ($periode_pembukuan == '') ? NULL : $periode_pembukuan,
			'nilai_tanah_smst1' => $this->cleanAndConvertToFloat($nilai_tanah_smst1),
			'nilai_nontanah_smst1' => $this->cleanAndConvertToFloat($nilai_non_tanah_smst1),
			'nilai_kdp_smst1' => $this->cleanAndConvertToFloat($nilai_kdp_smst1),
			'nilai_akumulasi_penyusutan_smst1' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_smst1),
			'nilai_kewajiban_konjas_smst1' => $this->cleanAndConvertToFloat($nilai_kewajiban_konjas_smst1),
			'nilai_amortasi_kewajiban_konjas_smst1' => $this->cleanAndConvertToFloat($nilai_am_kewajiban_konjas_smst1),
			'nilai_tanah_tw3' => $this->cleanAndConvertToFloat($nilai_tanah_tw3),
			'nilai_nontanah_tw3' => $this->cleanAndConvertToFloat($nilai_non_tanah_tw3),
			'nilai_kdp_tw3' => $this->cleanAndConvertToFloat($nilai_kdp_tw3),
			'nilai_akumulasi_penyusutan_tw3' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_tw3),
			'nilai_kewajiban_konjas_tw3' => $this->cleanAndConvertToFloat($nilai_kewajiban_konjas_tw3),
			'nilai_amortasi_kewajiban_konjas_tw3' => $this->cleanAndConvertToFloat($nilai_am_kewajiban_konjas_tw3),
			'nilai_tanah_smst2' => $this->cleanAndConvertToFloat($nilai_tanah_smst2),
			'nilai_nontanah_smst2' => $this->cleanAndConvertToFloat($nilai_non_tanah_smst2),
			'nilai_kdp_smst2' => $this->cleanAndConvertToFloat($nilai_kdp_smst2),
			'nilai_akumulasi_penyusutan_smst2' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_smst2),
			'nilai_kewajiban_konjas_smst2' => $this->cleanAndConvertToFloat($nilai_kewajiban_konjas_smst2),
			'nilai_amortasi_kewajiban_konjas_smst2' => $this->cleanAndConvertToFloat($nilai_am_kewajiban_konjas_smst2),
			'nilai_tanah_audited' => $this->cleanAndConvertToFloat($nilai_tanah_audited),
			'nilai_nontanah_audited' => $this->cleanAndConvertToFloat($nilai_non_tanah_audited),
			'nilai_kdp_audited' => $this->cleanAndConvertToFloat($nilai_kdp_audited),
			'nilai_akumulasi_penyusutan_audited' => $this->cleanAndConvertToFloat($nilai_ak_penyusutan_audited),
			'nilai_kewajiban_konjas_audited' => $this->cleanAndConvertToFloat($nilai_kewajiban_konjas_audited),
			'nilai_amortasi_kewajiban_konjas_audited' => $this->cleanAndConvertToFloat($nilai_am_kewajiban_konjas_audited),
			'tahun' => ($tahun == '') ? NULL : $tahun,
			'tahun_perolehan' => ($tahun == '') ? NULL : $tahun,
			'tahun_pembukuan' => ($tahun == '') ? NULL : $tahun,
			'sisa_manfaat_slo' => ($smt == '') ? NULL : $smt,
			'sisa_manfaat_tarif' => ($sms == '') ? NULL : $sms,
			'created_by' => $this->session->userdata('id_user'),
		);

		if (is_uploaded_file($_FILES['file_lk']['tmp_name'])) {
			$sourcePath = $_FILES['file_lk']['tmp_name'];
			$namf = $_FILES['file_lk']['name'];
			$rep = str_replace(" ", "_", $namf);
			$fil_lk = date('Ymd') . date("his") . "~" . $rep;
			$targetPath = FCPATH . "assets/file_perolehan_mutasi/" . $fil_lk;
			move_uploaded_file($sourcePath, $targetPath);
		} else {
			$fil_lk = $filename_lk;
		}
		if (is_uploaded_file($_FILES['file_kertas']['tmp_name'])) {
			$sourcePath = $_FILES['file_kertas']['tmp_name'];
			$namf = $_FILES['file_kertas']['name'];
			$rep = str_replace(" ", "_", $namf);
			$fil_kertas = date('Ymd') . date("his") . "~" . $rep;
			$targetPath = FCPATH . "assets/file_perolehan_mutasi/" . $fil_kertas;
			move_uploaded_file($sourcePath, $targetPath);
		} else {
			$fil_kertas = $filename_kertas;
		}
		$data_file = array(
			'file_lk' => $fil_lk,
			'file_ba_rekonsiliasi' => $fil_kertas,
		);
		$data = array_merge($data, $data_file);
		if ($id == '') {
			$x = $this->db->insert('lp_monitoring', $data);
			// echo $this->db->last_query();
			$z = "insert";

		} else {
			$this->db->where('id_perolehan_mutasi', $id);
			$x = $this->db->update('lp_monitoring', $data);
			$z = "update";
		}


		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('v_perolehan_mutasi', $requestData);

		echo json_encode($data);
	}




	function tampildata($table = '', $colum = '', $val_colum = '', $combo = '')
	{
		$tab = explode("~", $table)[0];
	
		if ($combo != '') {
			$nama = explode("~", $table)[1];
			$kode = explode("~", $table)[2];
			$this->db->select("$nama as nama");
			$this->db->select("$kode as kode");
		}
	
		if ($colum == '') {
			$data = $this->db->get($tab)->result_array();
		} else {
			$dt = array();
			$columns = explode("~", $colum);
			$values = explode("~", $val_colum);
	
			// Hapus prefix 'idx' dari val_colum jika ada
			for ($x = 0; $x < count($values); $x++) {
				$values[$x] = str_replace('idx', '', $values[$x]); // Menghapus 'idx'
			}
	
			for ($x = 0; $x < count($columns); $x++) {
				$col = $columns[$x];
				$val = str_replace('_', ' ', $values[$x]);
				$dt[$col] = $val;
			}
	
			$data = $this->db->get_where($tab, $dt)->result_array();
		}
	
		echo json_encode($data);
	}
	function delete_data($table, $colum, $id)
	{
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
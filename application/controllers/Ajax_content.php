<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Ajax_content extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        // Libraries and helpers are now autoloaded
        $this->load->database();
        
        // Check if user is logged in
        if ($this->session->userdata('active') != 1) {
            if ($this->input->is_ajax_request()) {
                echo json_encode(['error' => 'Session expired', 'redirect' => base_url('login')]);
                exit;
            } else {
                redirect(base_url('login'));
            }
        }
    }

    /**
     * Load content dynamically via AJAX - returns only content without layout
     *
     * @param string $controller The controller name
     * @param string $method The method name (default: index)
     */
    public function load_content($controller = '', $method = 'index')
    {
        // Validate input
        if (empty($controller)) {
            echo 'Controller not specified';
            return;
        }

        // Security: Only allow alphanumeric characters and underscores
        if (!preg_match('/^[a-zA-Z0-9_\/]+$/', $controller) || !preg_match('/^[a-zA-Z0-9_]+$/', $method)) {
            echo 'Invalid controller or method name';
            return;
        }

        try {
            // Map controller to correct path and method
            $controller_mappings = [
                'data_satker' => ['path' => 'data_satker/Data_satker', 'method' => 'index'],
                'data_users' => ['path' => 'data_users/Data_users', 'method' => 'index'],
                'data_eselon_1' => ['path' => 'data_eselon_1/Data_eselon_1', 'method' => 'index'],
                'data_badan_usaha' => ['path' => 'data_badan_usaha/Data_badan_usaha', 'method' => 'index'],
                'data_jenis_pengusahaan' => ['path' => 'data_jenis_pengusahaan/Data_jenis_pengusahaan', 'method' => 'index'],
                'data_aset_mitra' => ['path' => 'data_aset_mitra/Data_aset_mitra', 'method' => 'index'],
                'data_aset_pemerintah' => ['path' => 'data_aset_pemerintah/Data_aset_pemerintah', 'method' => 'index'],
                'data_satuan' => ['path' => 'data_satuan/Data_satuan', 'method' => 'index'],
                'data_pulau' => ['path' => 'data_pulau/Data_pulau', 'method' => 'index'],
                'data_skema_kompensasi' => ['path' => 'data_skema_kompensasi/Data_skema_kompensasi', 'method' => 'index'],
                'laporan' => ['path' => 'laporan/Laporan', 'method' => 'index'],
                'laporan_rekapitulasi' => ['path' => 'laporan_rekapitulasi/Laporan_rekapitulasi', 'method' => 'index'],
                'lp_monitoring' => ['path' => 'lp_monitoring/Lp_monitoring', 'method' => 'index'],
                'lp_monitoring_lk' => ['path' => 'lp_monitoring_lk/Lp_monitoring_lk', 'method' => 'index'],
                'lp_monitoring_ba' => ['path' => 'lp_monitoring_ba/Lp_monitoring_ba', 'method' => 'index'],
                'perjanjian_konsesijasa' => ['path' => 'perjanjian_konsesijasa/Perjanjian_konsesijasa', 'method' => 'page'],
                'slo' => ['path' => 'slo/Slo', 'method' => 'page'],
                'saldo_awal' => ['path' => 'saldo_awal/Saldo_awal', 'method' => 'page'],
                'saldo_awal_lp' => ['path' => 'saldo_awal_lp/Saldo_awal_lp', 'method' => 'page'],
                'perolehan_mutasi_lp' => ['path' => 'perolehan_mutasi_lp/Perolehan_mutasi_lp', 'method' => 'page'],
                'perolehan_mutasi_kuantitas' => ['path' => 'perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas', 'method' => 'page'],
                'saldo_awal_kuantitas' => ['path' => 'saldo_awal_kuantitas/Saldo_awal_kuantitas', 'method' => 'page'],
                'saldo_awal_kuantitas_gov' => ['path' => 'saldo_awal_kuantitas_gov/Saldo_awal_kuantitas_gov', 'method' => 'page'],
                'perolehan_mutasi_kuantitas_gov' => ['path' => 'perolehan_mutasi_kuantitas_gov/Perolehan_mutasi_kuantitas_gov', 'method' => 'page'],
                'saldo_awal_kewajiban' => ['path' => 'saldo_awal_kewajiban/Saldo_awal_kewajiban', 'method' => 'page'],
                'perolehan_mutasi_kewajiban' => ['path' => 'perolehan_mutasi_kewajiban/Perolehan_mutasi_kewajiban', 'method' => 'index'],
                'saldo_awal_kewajiban_gov' => ['path' => 'saldo_awal_kewajiban_gov/Saldo_awal_kewajiban_gov', 'method' => 'index'],
                'perolehan_mutasi_kewajiban_gov' => ['path' => 'perolehan_mutasi_kewajiban_gov/Perolehan_mutasi_kewajiban_gov', 'method' => 'index'],
                'amortisasi' => ['path' => 'amortisasi/Amortisasi', 'method' => 'index'],
                'rincian_mutasi' => ['path' => 'rincian_mutasi/Rincian_mutasi', 'method' => 'index'],
                'tarif' => ['path' => 'tarif/Tarif', 'method' => 'index'],
                'perolehan_mutasi' => ['path' => 'perolehan_mutasi/Perolehan_mutasi', 'method' => 'index'],
                'reff_users_group' => ['path' => 'reff_users_group/Reff_users_group', 'method' => 'index']
            ];

            // Get the correct controller path and method
            if (isset($controller_mappings[$controller])) {
                $controller_path = $controller_mappings[$controller]['path'];
                $method = $controller_mappings[$controller]['method'];
            } else {
                $controller_path = $controller;
            }

            // Build the URL for internal request
            $url = base_url($controller_path . '/' . $method);

            // Get current session cookie
            $session_cookie = '';
            if (isset($_COOKIE[session_name()])) {
                $session_cookie = session_name() . '=' . $_COOKIE[session_name()];
            }

            // Create context for file_get_contents
            $context_options = [
                'http' => [
                    'method' => 'GET',
                    'timeout' => 30,
                    'user_agent' => 'KONJAS Internal Request',
                    'header' => [
                        'Connection: close'
                    ]
                ]
            ];

            // Add session cookie if available
            if ($session_cookie) {
                $context_options['http']['header'][] = 'Cookie: ' . $session_cookie;
            }

            $context = stream_context_create($context_options);

            // Make the request using file_get_contents
            $content = @file_get_contents($url, false, $context);

            if ($content === false) {
                echo 'Failed to load content from: ' . $controller_path . '<br>URL: ' . $url;
                return;
            }

            // Return the content directly (not JSON)
            echo $content;

        } catch (Exception $e) {
            echo 'Error loading content: ' . $e->getMessage();
        }
    }

    /**
     * Load specific view content via AJAX
     * 
     * @param string $view_path The view file path
     */
    public function load_view($view_path = '')
    {
        if (empty($view_path)) {
            echo json_encode(['error' => 'View path not specified']);
            return;
        }

        // Security: Only allow alphanumeric characters, underscores, and slashes
        if (!preg_match('/^[a-zA-Z0-9_\/]+$/', $view_path)) {
            echo json_encode(['error' => 'Invalid view path']);
            return;
        }

        try {
            // Check if view exists
            $view_file = APPPATH . 'views/' . $view_path . '.php';
            if (!file_exists($view_file)) {
                echo json_encode(['error' => 'View not found: ' . $view_path]);
                return;
            }

            // Load view content
            $content = $this->load->view($view_path, [], true);
            
            echo json_encode([
                'success' => true,
                'content' => $content,
                'title' => ucfirst(str_replace('/', ' - ', $view_path))
            ]);

        } catch (Exception $e) {
            echo json_encode(['error' => 'Error loading view: ' . $e->getMessage()]);
        }
    }

    /**
     * Load perjanjian konsesi jasa content
     */
    public function perjanjian_konsesijasa()
    {
        $this->load_content('perjanjian_konsesijasa/Perjanjian_konsesijasa', 'page');
    }

    /**
     * Load SLO content
     */
    public function slo()
    {
        $this->load_content('slo/Slo', 'page');
    }

    /**
     * Load Saldo Awal content
     */
    public function saldo_awal()
    {
        $this->load_content('saldo_awal/Saldo_awal', 'page');
    }

    /**
     * Load Saldo Awal LP content
     */
    public function saldo_awal_lp()
    {
        $this->load_content('saldo_awal_lp/Saldo_awal_lp', 'page');
    }

    /**
     * Load Perolehan Mutasi LP content
     */
    public function perolehan_mutasi_lp()
    {
        $this->load_content('perolehan_mutasi_lp/Perolehan_mutasi_lp', 'page');
    }

    /**
     * Load Perolehan Mutasi Kuantitas content
     */
    public function perolehan_mutasi_kuantitas()
    {
        $this->load_content('perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas', 'page');
    }

    /**
     * Load Saldo Awal Kuantitas content
     */
    public function saldo_awal_kuantitas()
    {
        $this->load_content('saldo_awal_kuantitas/Saldo_awal_kuantitas', 'page');
    }

    /**
     * Load Saldo Awal Kuantitas Gov content
     */
    public function saldo_awal_kuantitas_gov()
    {
        $this->load_content('saldo_awal_kuantitas_gov/Saldo_awal_kuantitas_gov', 'page');
    }

    /**
     * Load Perolehan Mutasi Kuantitas Gov content
     */
    public function perolehan_mutasi_kuantitas_gov()
    {
        $this->load_content('perolehan_mutasi_kuantitas_gov/Perolehan_mutasi_kuantitas_gov', 'page');
    }

    /**
     * Load Saldo Awal Kewajiban content
     */
    public function saldo_awal_kewajiban()
    {
        $this->load_content('saldo_awal_kewajiban/Saldo_awal_kewajiban', 'page');
    }

    /**
     * Load Perolehan Mutasi Kewajiban content
     */
    public function perolehan_mutasi_kewajiban()
    {
        $this->load_content('perolehan_mutasi_kewajiban/Perolehan_mutasi_kewajiban', 'index');
    }

    /**
     * Load Amortisasi content
     */
    public function amortisasi()
    {
        $this->load_content('amortisasi/Amortisasi', 'index');
    }

    /**
     * Load Saldo Awal Kewajiban Gov content
     */
    public function saldo_awal_kewajiban_gov()
    {
        $this->load_content('saldo_awal_kewajiban_gov/Saldo_awal_kewajiban_gov', 'index');
    }

    /**
     * Load Perolehan Mutasi Kewajiban Gov content
     */
    public function perolehan_mutasi_kewajiban_gov()
    {
        $this->load_content('perolehan_mutasi_kewajiban_gov/Perolehan_mutasi_kewajiban_gov', 'index');
    }

    /**
     * Load Rincian Mutasi content
     */
    public function rincian_mutasi()
    {
        $this->load_content('rincian_mutasi/Rincian_mutasi', 'index');
    }

    /**
     * Load Laporan content
     */
    public function laporan()
    {
        $this->load_content('laporan/Laporan', 'index');
    }

    /**
     * Load Laporan Rekapitulasi content
     */
    public function laporan_rekapitulasi()
    {
        $this->load_content('laporan_rekapitulasi/Laporan_rekapitulasi', 'index');
    }

    /**
     * Load LP Monitoring content
     */
    public function lp_monitoring()
    {
        $this->load_content('lp_monitoring/Lp_monitoring', 'index');
    }

    /**
     * Load LP Monitoring LK content
     */
    public function lp_monitoring_lk()
    {
        $this->load_content('lp_monitoring_lk/Lp_monitoring_lk', 'index');
    }

    /**
     * Load LP Monitoring BA content
     */
    public function lp_monitoring_ba()
    {
        $this->load_content('lp_monitoring_ba/Lp_monitoring_ba', 'index');
    }

    /**
     * Load Data Users content
     */
    public function data_users()
    {
        $this->load_content('data_users/Data_users', 'index');
    }

    /**
     * Load Data Pulau content
     */
    public function data_pulau()
    {
        $this->load_content('data_pulau/Data_pulau', 'index');
    }

    /**
     * Load Data Aset Mitra content
     */
    public function data_aset_mitra()
    {
        $this->load_content('data_aset_mitra/Data_aset_mitra', 'index');
    }

    /**
     * Load Data Aset Pemerintah content
     */
    public function data_aset_pemerintah()
    {
        $this->load_content('data_aset_pemerintah/Data_aset_pemerintah', 'index');
    }

    /**
     * Load Data Badan Usaha content
     */
    public function data_badan_usaha()
    {
        $this->load_content('data_badan_usaha/Data_badan_usaha', 'index');
    }

    /**
     * Load Data Eselon 1 content
     */
    public function data_eselon_1()
    {
        $this->load_content('data_eselon_1/Data_eselon_1', 'index');
    }

    /**
     * Load Data Satker content
     */
    public function data_satker()
    {
        $this->load_content('data_satker/Data_satker', 'index');
    }

    /**
     * Load Data Jenis Pengusahaan content
     */
    public function data_jenis_pengusahaan()
    {
        $this->load_content('data_jenis_pengusahaan/Data_jenis_pengusahaan', 'index');
    }

    /**
     * Load Data Satuan content
     */
    public function data_satuan()
    {
        $this->load_content('data_satuan/Data_satuan', 'index');
    }

    /**
     * Load Data Skema Kompensasi content
     */
    public function data_skema_kompensasi()
    {
        $this->load_content('data_skema_kompensasi/Data_skema_kompensasi', 'index');
    }

    /**
     * Load dashboard content
     */
    public function dashboard()
    {
        $this->load_content('dashboard/Dashboard', 'index');
    }

    /**
     * Load any other module content
     * This method can be extended to handle other modules
     */
    public function module($controller = '', $method = 'index')
    {
        $this->load_content($controller, $method);
    }
}

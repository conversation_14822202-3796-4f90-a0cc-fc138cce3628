<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Ajax_content extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        $this->load->database();
        
        // Check if user is logged in
        if ($this->session->userdata('active') != 1) {
            if ($this->input->is_ajax_request()) {
                echo json_encode(['error' => 'Session expired', 'redirect' => base_url('login')]);
                exit;
            } else {
                redirect(base_url('login'));
            }
        }
    }

    /**
     * Load content dynamically via AJAX
     * 
     * @param string $controller The controller name
     * @param string $method The method name (default: index)
     */
    public function load_content($controller = '', $method = 'index')
    {
        // Validate input
        if (empty($controller)) {
            echo json_encode(['error' => 'Controller not specified']);
            return;
        }

        // Security: Only allow alphanumeric characters and underscores
        if (!preg_match('/^[a-zA-Z0-9_\/]+$/', $controller) || !preg_match('/^[a-zA-Z0-9_]+$/', $method)) {
            echo json_encode(['error' => 'Invalid controller or method name']);
            return;
        }

        try {
            // Build the controller path
            $controller_path = $controller;
            
            // Check if controller exists
            $file_path = APPPATH . 'controllers/' . $controller_path . '.php';
            if (!file_exists($file_path)) {
                echo json_encode(['error' => 'Controller not found: ' . $controller]);
                return;
            }

            // Load the controller and call the method
            $this->load->library('template');
            
            // Capture output
            ob_start();
            
            // Include the controller file
            include_once($file_path);
            
            // Get controller class name (last part of path)
            $controller_parts = explode('/', $controller_path);
            $controller_class = end($controller_parts);
            $controller_class = ucfirst($controller_class);
            
            // Check if class exists
            if (!class_exists($controller_class)) {
                ob_end_clean();
                echo json_encode(['error' => 'Controller class not found: ' . $controller_class]);
                return;
            }

            // Instantiate controller
            $controller_instance = new $controller_class();
            
            // Check if method exists
            if (!method_exists($controller_instance, $method)) {
                ob_end_clean();
                echo json_encode(['error' => 'Method not found: ' . $method]);
                return;
            }

            // Call the method
            $controller_instance->$method();
            
            $content = ob_get_clean();
            
            // Return JSON response
            echo json_encode([
                'success' => true,
                'content' => $content,
                'title' => isset($controller_instance->page_title) ? $controller_instance->page_title : ucfirst($controller)
            ]);

        } catch (Exception $e) {
            ob_end_clean();
            echo json_encode(['error' => 'Error loading content: ' . $e->getMessage()]);
        }
    }

    /**
     * Load specific view content via AJAX
     * 
     * @param string $view_path The view file path
     */
    public function load_view($view_path = '')
    {
        if (empty($view_path)) {
            echo json_encode(['error' => 'View path not specified']);
            return;
        }

        // Security: Only allow alphanumeric characters, underscores, and slashes
        if (!preg_match('/^[a-zA-Z0-9_\/]+$/', $view_path)) {
            echo json_encode(['error' => 'Invalid view path']);
            return;
        }

        try {
            // Check if view exists
            $view_file = APPPATH . 'views/' . $view_path . '.php';
            if (!file_exists($view_file)) {
                echo json_encode(['error' => 'View not found: ' . $view_path]);
                return;
            }

            // Load view content
            $content = $this->load->view($view_path, [], true);
            
            echo json_encode([
                'success' => true,
                'content' => $content,
                'title' => ucfirst(str_replace('/', ' - ', $view_path))
            ]);

        } catch (Exception $e) {
            echo json_encode(['error' => 'Error loading view: ' . $e->getMessage()]);
        }
    }

    /**
     * Load perjanjian konsesi jasa content
     */
    public function perjanjian_konsesijasa()
    {
        $this->load_content('perjanjian_konsesijasa/Perjanjian_konsesijasa', 'page');
    }

    /**
     * Load SLO content
     */
    public function slo()
    {
        $this->load_content('slo/Slo', 'page');
    }

    /**
     * Load Saldo Awal content
     */
    public function saldo_awal()
    {
        $this->load_content('saldo_awal/Saldo_awal', 'page');
    }

    /**
     * Load Saldo Awal LP content
     */
    public function saldo_awal_lp()
    {
        $this->load_content('saldo_awal_lp/Saldo_awal_lp', 'page');
    }

    /**
     * Load Perolehan Mutasi LP content
     */
    public function perolehan_mutasi_lp()
    {
        $this->load_content('perolehan_mutasi_lp/Perolehan_mutasi_lp', 'page');
    }

    /**
     * Load Perolehan Mutasi Kuantitas content
     */
    public function perolehan_mutasi_kuantitas()
    {
        $this->load_content('perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas', 'page');
    }

    /**
     * Load Saldo Awal Kuantitas content
     */
    public function saldo_awal_kuantitas()
    {
        $this->load_content('saldo_awal_kuantitas/Saldo_awal_kuantitas', 'page');
    }

    /**
     * Load Saldo Awal Kuantitas Gov content
     */
    public function saldo_awal_kuantitas_gov()
    {
        $this->load_content('saldo_awal_kuantitas_gov/Saldo_awal_kuantitas_gov', 'page');
    }

    /**
     * Load Perolehan Mutasi Kuantitas Gov content
     */
    public function perolehan_mutasi_kuantitas_gov()
    {
        $this->load_content('perolehan_mutasi_kuantitas_gov/Perolehan_mutasi_kuantitas_gov', 'page');
    }

    /**
     * Load Saldo Awal Kewajiban content
     */
    public function saldo_awal_kewajiban()
    {
        $this->load_content('saldo_awal_kewajiban/Saldo_awal_kewajiban', 'page');
    }

    /**
     * Load Perolehan Mutasi Kewajiban content
     */
    public function perolehan_mutasi_kewajiban()
    {
        $this->load_content('perolehan_mutasi_kewajiban/Perolehan_mutasi_kewajiban', 'index');
    }

    /**
     * Load Amortisasi content
     */
    public function amortisasi()
    {
        $this->load_content('amortisasi/Amortisasi', 'index');
    }

    /**
     * Load Saldo Awal Kewajiban Gov content
     */
    public function saldo_awal_kewajiban_gov()
    {
        $this->load_content('saldo_awal_kewajiban_gov/Saldo_awal_kewajiban_gov', 'index');
    }

    /**
     * Load Perolehan Mutasi Kewajiban Gov content
     */
    public function perolehan_mutasi_kewajiban_gov()
    {
        $this->load_content('perolehan_mutasi_kewajiban_gov/Perolehan_mutasi_kewajiban_gov', 'index');
    }

    /**
     * Load Rincian Mutasi content
     */
    public function rincian_mutasi()
    {
        $this->load_content('rincian_mutasi/Rincian_mutasi', 'index');
    }

    /**
     * Load Laporan content
     */
    public function laporan()
    {
        $this->load_content('laporan/Laporan', 'index');
    }

    /**
     * Load Laporan Rekapitulasi content
     */
    public function laporan_rekapitulasi()
    {
        $this->load_content('laporan_rekapitulasi/Laporan_rekapitulasi', 'index');
    }

    /**
     * Load LP Monitoring content
     */
    public function lp_monitoring()
    {
        $this->load_content('lp_monitoring/Lp_monitoring', 'index');
    }

    /**
     * Load LP Monitoring LK content
     */
    public function lp_monitoring_lk()
    {
        $this->load_content('lp_monitoring_lk/Lp_monitoring_lk', 'index');
    }

    /**
     * Load LP Monitoring BA content
     */
    public function lp_monitoring_ba()
    {
        $this->load_content('lp_monitoring_ba/Lp_monitoring_ba', 'index');
    }

    /**
     * Load Data Users content
     */
    public function data_users()
    {
        $this->load_content('data_users/Data_users', 'index');
    }

    /**
     * Load Data Pulau content
     */
    public function data_pulau()
    {
        $this->load_content('data_pulau/Data_pulau', 'index');
    }

    /**
     * Load dashboard content
     */
    public function dashboard()
    {
        $this->load_content('dashboard/Dashboard', 'index');
    }

    /**
     * Load any other module content
     * This method can be extended to handle other modules
     */
    public function module($controller = '', $method = 'index')
    {
        $this->load_content($controller, $method);
    }
}

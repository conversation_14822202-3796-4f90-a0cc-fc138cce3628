<?php
defined('BASEPATH') or exit('No direct script access allowed');

class perjanjian_konsesijasa extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url

		$this->load->model('perjanjian_konsesijasa/M_data_table', 'm_data_table');

		$this->load->library(array('session',  'template'));
		$this->load->database();
		// Temporarily disabled for testing
		// if ($this->session->userdata('active') != 1) {
		// 	redirect('home');
		// }

	}
	function page()
	{


		// Set more secure CORS headers
		header("Access-Control-Allow-Origin: " . base_url());
		header("Access-Control-Allow-Methods: GET, POST");
		header("Access-Control-Allow-Headers: Content-Type, Authorization");
		$data = array();

		$title = "";

		// $js_file2 = $this->load->view('data_users/js_file2', '', true);

		$js_file = $this->load->view('perjanjian_konsesijasa/js_file', '', true);
		// if (!empty($this->session->users['kd_bujt'])) {
		//     $kd_prov_irmsv3 = $this->get_prov_irmsv3($this->session->users['kd_bujt']);
		// }
		// $modal_tambah = $this->load->view('data_users/modal_tambah', '', true);

		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => $title,
			"jv_script" => $js_file,
			// "tambah" => $modal_tambah,
		);



		$this->template->set('title', $title);
		$this->template->load('default_layout', 'contents', 'perjanjian_konsesijasa/v_data', $data);

	}

	public function index()
	{
		$js_file = $this->load->view('perjanjian_konsesijasa/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => 'Perjanjian Konsesi Jasa',
			"jv_script" => $js_file
		);

		$this->load->view('perjanjian_konsesijasa/v_data', $data);
	}
	function cleanAndConvertToFloat($input)
	{
		if ($input == '') {
			return NULL;
		} else {
			// Hapus tanda Rp dan spasi
			$cleaned = str_replace(['Rp', ' '], '', $input);

			// Hapus semua tanda desimal (titik) jika ada
			$cleaned = str_replace('.', '', $cleaned);

			// Konversi ke float
			return (float) $cleaned;
		}
	}

	function insert_data()
	{

		$id = $this->input->post('id');
		$jenis_p = $this->input->post('jenis_p');
		$nm_pengusahaan = $this->input->post('nama_p');
		$bdn_usaha = $this->input->post('bdn_usaha');
		$pem_konsesi = $this->input->post('pem_konsesi');
		$skema_kompensasi = $this->input->post('skema_kompensasi');
		$kuantitas = $this->input->post('kuantitas');
		$satuan = $this->input->post('satuan');
		$ruang_l = $this->input->post('ruang_l');
		$pulau = $this->input->post('pulau');
		$tgl_awal = $this->input->post('tgl_mk');
		$tgl_akhir = $this->input->post('tgl_ak');
		$status_op = $this->input->post('status_op');
		$kd_pengusahaan = $this->input->post('kd_pengusahaan');
		// $barang = $this->input->post('barang');
		// $nup = $this->input->post('nup');

		$no_dok = $this->input->post('no_dok');
		$tgl_op = $this->input->post('tgl_op');
		$nilai = $this->input->post('nilai');
		$id_detail = $this->input->post('id_detail');
		$filename = $this->input->post('filenames');
		$tahun_layout = $this->input->post('tahun_layout');

		$data = array(
			'id_jenis_pengusahaan' => ($jenis_p == '') ? NULL : $jenis_p,
			'nm_perjanjian' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
			'id_badan_usaha' => ($bdn_usaha == '') ? NULL : $bdn_usaha,
			'pemberi_konsesi' => ($pem_konsesi == '') ? NULL : $pem_konsesi,
			'id_skema_kompensasi' => ($skema_kompensasi == '') ? NULL : $skema_kompensasi,
			'kuantitas' => ($kuantitas == '') ? NULL : $kuantitas,
			'id_satuan' => ($satuan == '') ? NULL : $satuan,
			'ruang_lingkup' => ($ruang_l == '') ? NULL : $ruang_l,
			'id_pulau' => ($pulau == '') ? NULL : $pulau,
			'tgl_awal_konsesi' => ($tgl_awal == '') ? NULL : $tgl_awal,
			'tgl_akhir_konsesi' => ($tgl_akhir == '') ? NULL : $tgl_akhir,
			'tahun' => ($tahun_layout == '') ? NULL : $tahun_layout,
			'status' => ($status_op == '') ? NULL : $status_op,
			// 'kode_barang' => ($barang == '') ? NULL : $barang,
			// 'nup' => ($nup == '') ? NULL : $nup,
			'kode_pengusahaan' => ($kd_pengusahaan == '') ? NULL : $kd_pengusahaan,
			'created_by' => $this->session->userdata('id_user'),
			'created_at' => date('Y/m/d H:i:s'),
			'updated_at' => date('Y/m/d H:i:s'),
		);
		$this->db->trans_start();
		if ($id == '') {
			unset($data['updated_at']);
			$x = $this->db->insert('perjanjian_pengusahaan', $data);
			if ($x) {
				$id = $this->db->insert_id();
				$z = "insert";
			}
		} else {
			unset($data['created_at']);
			$this->db->where('id_perjanjian_pengusahaan', $id);
			$x = $this->db->update('perjanjian_pengusahaan', $data);
			$z = "update";
		}
		$this->db->where('id_perjanjian_pengusahaan', $id);
		$this->db->where_not_in('id_perjanjian_pengusahaan_dtl', $id_detail);
		$this->db->delete('perjanjian_pengusahaan_detail');
		if (isset($no_dok)) {
			for ($xx = 0; $xx <= count($no_dok) - 1; $xx++) {
				$namf = '';
				if ($no_dok[$xx] !== '') {
					if (is_uploaded_file($_FILES['file_slo']['tmp_name'][$xx])) {
						// Get file information
						$sourcePath = $_FILES['file_slo']['tmp_name'][$xx];
						$namf = $_FILES['file_slo']['name'][$xx];
						$fileSize = $_FILES['file_slo']['size'][$xx];
						$fileType = $_FILES['file_slo']['type'][$xx];

						// Server-side validation
						// 1. Check file size (max 15MB)
						$maxFileSize = 15 * 1024 * 1024; // 15MB in bytes
						if ($fileSize > $maxFileSize) {
							echo "1_file_too_large";
							$this->db->trans_rollback();
							return;
						}

						// 2. Check file extension
						$fileExtension = strtolower(pathinfo($namf, PATHINFO_EXTENSION));
						if ($fileExtension !== 'pdf') {
							echo "1_invalid_file_type";
							$this->db->trans_rollback();
							return;
						}

						// 3. Verify actual file content using finfo
						$finfo = new finfo(FILEINFO_MIME_TYPE);
						$mimeType = $finfo->file($sourcePath);
						if ($mimeType !== 'application/pdf') {
							echo "1_invalid_file_content";
							$this->db->trans_rollback();
							return;
						}

						// 4. Sanitize filename more thoroughly
						$sanitizedName = preg_replace('/[^a-zA-Z0-9_.-]/', '_', $namf);
						$rep = str_replace(" ", "_", $sanitizedName);

						// 5. Use a more secure naming convention with random string
						$randomString = bin2hex(random_bytes(8)); // Generate a random 16-character hex string
						$fil = date('Ymd') . date("his") . "_" . $randomString . "~" . $rep;
						$targetPath = FCPATH . "assets/file_perjanjian/" . $fil;

						// 6. Move the file securely
						if (!move_uploaded_file($sourcePath, $targetPath)) {
							echo "1_file_upload_failed";
							$this->db->trans_rollback();
							return;
						}
					} else {
						if (isset($id_detail[$xx]) && $id_detail[$xx] !== '') {
							$fil = $filename[$xx];
						} else {
							$fil = NULL;
						}
					}
					$a = array();

					$b = array(
						'id_perjanjian_pengusahaan' => $id,
						'no_perjanjian' => ($no_dok[$xx] == '') ? NULL : $no_dok[$xx],
						'tgl_perjanjian' => ($tgl_op[$xx] == '') ? NULL : $tgl_op[$xx],
						'nilai_investasi' => $this->cleanAndConvertToFloat($nilai[$xx]),
						'file_perjanjian' => $fil,
					);
					$data = array_merge($b, $a);
					if (isset($id_detail[$xx]) && $id_detail[$xx] !== '') {
						$this->db->where('id_perjanjian_pengusahaan_dtl', $id_detail[$xx]);
						$this->db->update('perjanjian_pengusahaan_detail', $data);

					} else {
						$this->db->insert('perjanjian_pengusahaan_detail', $data);

					}
				}
			}
		}
		if ($this->db->trans_complete() == true) {
			if ($x) {
				echo "0_" . $z;
			} else {
				$this->db->last_query();
				// $errMess = $this->db->_error_message();
				echo "1_" . $z;
			}
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('v_perjanjian_pengusahaan', $requestData);

		echo json_encode($data);
	}


	function tampildata()
{
    // Ambil data JSON yang dikirimkan melalui POST
    $inputJSON = file_get_contents("php://input");
    $input = json_decode($inputJSON, true);  // Decode JSON menjadi array PHP

    // Ambil parameter dari JSON yang dikirim oleh frontend
    $table = $input['table'] ?? '';
    $colum = $input['colum'] ?? '';
    $val_colum = $input['id'] ?? '';  // Decode Base64 dari JavaScript
    $combo = $input['combobox'] ?? '';  // Menangani combobox

    // Menghapus prefix 'id-' dan mendekodekan base64 dari val_colum
    //$val_colum = base64_decode($val_colum);
	$decoded_val = base64_decode($val_colum, true);
$val_colum = ($decoded_val !== false) ? $decoded_val : $val_colum;

    $tab = explode("~", $table)[0];  // Menentukan tabel dari parameter table


    // Jika combo diisi, proses untuk combobox
    if ($combo != '') {
        $nama = explode("~", $table)[1];  // Mengambil nama kolom untuk nama
        $kode = explode("~", $table)[2];  // Mengambil kode kolom untuk kode
        $this->db->select("$nama as nama");  // Mengambil kolom nama
        $this->db->select("$kode as kode");  // Mengambil kolom kode
    }

    // Jika kolum kosong, ambil semua data dari tabel
    if ($colum == '') {
        $data = $this->db->get($tab)->result_array();
    } else {
        // Jika ada kolum dan nilai kolum, filter dengan data
        $dt = array();
        $columns = explode("~", $colum);
        $values = explode("~", $val_colum);

        // Hapus prefix 'idx' dari val_colum jika ada
        for ($x = 0; $x < count($values); $x++) {
            $values[$x] = str_replace('idx_', '', $values[$x]);  // Menghapus 'idx'
        }

        // Mencocokkan kolum dan nilai yang sesuai
        for ($x = 0; $x < count($columns); $x++) {
            $col = $columns[$x];
            $val = str_replace('_', ' ', $values[$x]);  // Mengganti '_' dengan spasi pada nilai kolum
            $dt[$col] = $val;
        }

        // Ambil data berdasarkan filter kolum dan nilai
        $data = $this->db->get_where($tab, $dt)->result_array();
    }

	// foreach ($data as &$row) {
	// 	if (!empty($row['pemberi_konsesi'])) {
	// 		$decoded = base64_decode($row['pemberi_konsesi'], true);

	// 		// Cek apakah decoding berhasil atau gagal
	// 		if ($decoded !== false) {
	// 			$row['pemberi_konsesi'] = $decoded;
	// 		} else {
	// 			$row['pemberi_konsesi'] = "DECODE FAILED: " . $row['pemberi_konsesi'];
	// 		}
	// 	}
	// }
	foreach ($data as &$row) {
		unset($row['pemberi_konsesi']); // Hapus 'pemberi_konsesi' dari setiap elemen
		unset($row['kode_eselon_1']); // Hapus 'pemberi_konsesi' dari setiap elemen
		unset($row['kode_satker']); // Hapus 'pemberi_konsesi' dari setiap elemen

	}
	unset($row);
	echo json_encode($data);


}



// 	function tampildata($table = '', $colum = '', $val_colum = '', $combo = '')
// {
// 	$val_colum = str_replace("id-", "", $val_colum);
// 	$val_colum = base64_decode($val_colum);
//     $tab = explode("~", $table)[0];

//     if ($combo != '') {
//         $nama = explode("~", $table)[1];
//         $kode = explode("~", $table)[2];
//         $this->db->select("$nama as nama");
//         $this->db->select("$kode as kode");
//     }

//     if ($colum == '') {
//         $data = $this->db->get($tab)->result_array();
//     } else {
//         $dt = array();
//         $columns = explode("~", $colum);
//         $values = explode("~", $val_colum);

//         // Hapus prefix 'idx' dari val_colum jika ada
//         for ($x = 0; $x < count($values); $x++) {
//             $values[$x] = str_replace('idx_', '', $values[$x]); // Menghapus 'idx'
//         }

//         for ($x = 0; $x < count($columns); $x++) {
//             $col = $columns[$x];
//             $val = str_replace('_', ' ', $values[$x]);
//             $dt[$col] = $val;
//         }

//         $data = $this->db->get_where($tab, $dt)->result_array();
//     }

//     echo json_encode($data);
// }

	function delete_data($table, $colum, $id)
	{
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			$this->db->where($colum, $id);
			$this->db->delete('perjanjian_pengusahaan_detail');

			$id_slo = $this->db->get_where('slo', array("id_perjanjian_pengusahaan" => $id));
			if ($id_slo->num_rows() > 0) {
				$this->db->where('id_slo', $id_slo->row()->id_slo);
				$this->db->delete('slo_detail');
			}
			$this->db->where($colum, $id);
			$this->db->delete('slo');



			$id_tarif = $this->db->get_where('tarif', array("id_perjanjian_pengusahaan" => $id));
			if ($id_tarif->num_rows() > 0) {
				$this->db->where('id_tarif', $id_tarif->row()->id_tarif);
				$this->db->delete('tarif_detail');
			}
			$this->db->where($colum, $id);
			$this->db->delete('tarif');

			$this->db->where($colum, $id);
			$this->db->delete('saldo_awal');

			$this->db->where($colum, $id);
			$this->db->delete('saldo_awal_kuantitas');

			$this->db->where($colum, $id);
			$this->db->delete('perolehan_mutasi');

			$this->db->where($colum, $id);
			$this->db->delete('perolehan_mutasi_kuantitas');

			echo 0;
		} else {
			echo 1;
		}
	}
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}

	/**
	 * Secure file download method
	 *
	 * @param string $file_id Encrypted file identifier
	 * @return void
	 */
	public function secure_download($file_id = '')
	{
		// Check if user is logged in
		if ($this->session->userdata('active') != 1) {
			redirect('home');
			return;
		}

		// Validate and sanitize the file_id
		if (empty($file_id) || !preg_match('/^[a-zA-Z0-9_\-\.]+$/', $file_id)) {
			show_error('Invalid file identifier', 400);
			return;
		}

		// Decrypt file ID
		try {
			$decoded_id = base64_decode($file_id);
			if ($decoded_id === false) {
				throw new Exception('Invalid file ID format');
			}
		} catch (Exception $e) {
			show_error('Invalid file identifier', 400);
			return;
		}

		// Query the database to get the file information
		$this->db->select('file_perjanjian');
		$this->db->from('perjanjian_pengusahaan_detail');
		$this->db->where('id_perjanjian_pengusahaan_dtl', $decoded_id);
		$query = $this->db->get();

		if ($query->num_rows() == 0) {
			show_error('File not found', 404);
			return;
		}

		$file_data = $query->row();
		$file_name = $file_data->file_perjanjian;

		// Validate that the file exists and is a PDF
		$file_path = FCPATH . "assets/file_perjanjian/" . $file_name;

		if (!file_exists($file_path)) {
			show_error('File not found on server', 404);
			return;
		}

		// Check file type using finfo
		$finfo = new finfo(FILEINFO_MIME_TYPE);
		$mime_type = $finfo->file($file_path);

		if ($mime_type !== 'application/pdf') {
			show_error('Invalid file type', 400);
			return;
		}

		// Get the original filename from the stored filename
		$original_name = '';
		if (strpos($file_name, '~') !== false) {
			$original_name = explode('~', $file_name)[1];
		} else {
			$original_name = $file_name;
		}

		// Set appropriate headers
		header('Content-Type: application/pdf');
		header('Content-Disposition: attachment; filename="' . $original_name . '"');
		header('Content-Length: ' . filesize($file_path));
		header('Cache-Control: private, max-age=0, must-revalidate');
		header('Pragma: public');

		// Clear output buffer
		if (ob_get_level()) {
			ob_end_clean();
		}

		// Read the file and output it to the browser
		readfile($file_path);
		exit;
	}
}
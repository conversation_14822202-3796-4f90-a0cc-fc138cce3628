<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Slo extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		date_default_timezone_set("Asia/Jakarta");
		$this->load->helper(array('url', 'file')); //load helper url

		$this->load->model('slo/M_data_table', 'm_data_table');

		$this->load->library(array('session', 'template'));
		$this->load->database();
		// Allow tampildata method without session check for AJAX calls
		$method = $this->router->fetch_method();
		if ($method !== 'tampildata' && $this->session->userdata('active') != 1) {
			redirect(base_url('login'));
		}

	}

	public function index()
	{
		$js_file = $this->load->view('slo/js_file', '', true);
		$data = array(
			/* "modal_filter" => $modal_filter, */
			"title" => 'SLO',
			"jv_script" => $js_file
		);

		$this->load->view('slo/v_data', $data);
	}

	function page()
	{
		$data = array();
		$title = "SLO (Surat Layak Operasi)";

		$js_file = $this->load->view('slo/js_file', '', true);

		$data = array(
			"title" => $title,
			"jv_script" => $js_file,
		);

		// Check if this is an AJAX request
		if ($this->input->is_ajax_request()) {
			// For AJAX requests, return only the content
			$content = $this->load->view('slo/v_data', $data, true);
			echo $content . $js_file;
		} else {
			// For regular requests, use the template
			$this->template->set('title', $title);
			$this->template->load('default_layout', 'contents', 'slo/v_data', $data);
		}
	}
	function cleanAndConvertToFloat($input)
	{
		if ($input == '') {
			return NULL;
		} else {
			// Hapus tanda Rp dan spasi
			$cleaned = str_replace(['Rp', ' '], '', $input);

			// Hapus semua tanda desimal (titik) jika ada
			$cleaned = str_replace('.', '', $cleaned);

			// Konversi ke float
			return (float) $cleaned;
		}
	}

	function insert_data()
	{
		$id = $this->input->post('id');
		$nm_pengusahaan = $this->input->post('nm_perjanjian');

		$masa = $this->input->post('masa');

		$no_dok = $this->input->post('no_dok');
		$tgl_op = $this->input->post('tgl_op');
		$nilai = $this->input->post('nilai');
		$id_detail = $this->input->post('id_detail');
		$filename = $this->input->post('filenames');
		$tahun_layout = $this->input->post('tahun_layout');


		$data = array(
			'id_perjanjian_pengusahaan' => ($nm_pengusahaan == '') ? NULL : $nm_pengusahaan,
			'masa_manfaat' => ($masa == '') ? NULL : $masa,
			'tahun' => ($tahun_layout == '') ? NULL : $tahun_layout,
			'created_by' => $this->session->userdata('id_user'),
			'created_at' => date('Y/m/d H:i:s'),
			'updated_at' => date('Y/m/d H:i:s'),
		);
		if ($id == '') {
			unset($data['updated_at']);

			$x = $this->db->insert('slo', $data);
			if ($x) {
				$id = $this->db->insert_id();
				$z = "insert";
			}
		} else {
			unset($data['created_at']);

			$this->db->where('id_slo', $id);
			$x = $this->db->update('slo', $data);
			$z = "update";
		}
		$this->db->where('id_slo', $id);
		$this->db->where_not_in('id_slo_detail', $id_detail);
		$this->db->delete('slo_detail');

		for ($xx = 0; $xx <= count($no_dok) - 1; $xx++) {
			if (isset($id_detail[$xx]) && $id_detail[$xx] !== '') {
				// $this->db->where('id_slo', $id);
				// $this->db->where('id_slo_detail <>', $id_detail[$xx]);


			}
			$namf = '';
			if ($no_dok[$xx] !== '') {
				if (is_uploaded_file($_FILES['file_slo']['tmp_name'][$xx])) {
					$sourcePath = $_FILES['file_slo']['tmp_name'][$xx];
					$namf = $_FILES['file_slo']['name'][$xx];
					$rep = str_replace(" ", "_", $namf);
					$fil = date('Ymd') . date("his") . "~" . $rep;
					$targetPath = FCPATH . "assets/file_slo/" . $fil;
					move_uploaded_file($sourcePath, $targetPath);
				} else {
					if (isset($id_detail[$xx]) && $id_detail[$xx] !== '') {
						$fil = $filename[$xx];
					} else {
						$fil = NULL;
					}
				}
				$a = array();

				$b = array(
					'id_slo' => $id,
					'no_dok_slo' => ($no_dok[$xx] == '') ? NULL : $no_dok[$xx],
					'tgl_slo' => ($tgl_op[$xx] == '') ? NULL : $tgl_op[$xx],
					'file' => $fil,
				);
				$data = array_merge($b, $a);
				if (isset($id_detail[$xx]) && $id_detail[$xx] !== '') {
					$this->db->where('id_slo_detail', $id_detail[$xx]);
					$this->db->update('slo_detail', $data);

				} else {
					$this->db->insert('slo_detail', $data);

				}
			}
		}
		if ($x) {
			echo "0_" . $z;
		} else {
			$this->db->last_query();
			// $errMess = $this->db->_error_message();
			echo "1_" . $z;
		}
	}




	public function ssp()
	{
		$requestData = $_POST;

		$data = $this->m_data_table->getRecords('v_slo', $requestData);

		echo json_encode($data);
	}



	function tampildata()
	{
		// Get all URI segments
		$segments = $this->uri->segment_array();

		// For URL: /slo/tampildata/table/colum/val_colum/combo
		// Segments: [1]=slo, [2]=tampildata, [3]=table, [4]=colum, [5]=val_colum, [6]=combo
		$table = isset($segments[3]) ? urldecode($segments[3]) : '';
		$colum = isset($segments[4]) ? urldecode($segments[4]) : '';
		$val_colum = isset($segments[5]) ? urldecode($segments[5]) : '';
		$combo = isset($segments[6]) ? urldecode($segments[6]) : '';

		// Debug: log the parameters
		log_message('debug', 'SLO tampildata called with: table=' . $table . ', colum=' . $colum . ', val_colum=' . $val_colum . ', combo=' . $combo);

		if (empty($table)) {
			echo json_encode(['error' => 'Table parameter is required']);
			return;
		}

		$tab = explode("~", $table)[0];

		if ($combo == 'combobox') {
			$table_parts = explode("~", $table);
			if (count($table_parts) >= 3) {
				$nama = $table_parts[1];
				$kode = $table_parts[2];
				$this->db->select("$nama as nama");
				$this->db->select("$kode as kode");
			}
		}

		if (empty($colum) || $colum == '1') {
			$data = $this->db->get($tab)->result_array();
		} else {
			$dt = array();
			$columns = explode("~", $colum);
			$values = explode("~", $val_colum);

			// Hapus prefix 'idx' dari val_colum jika ada
			for ($x = 0; $x < count($values); $x++) {
				$values[$x] = str_replace('idx', '', $values[$x]); // Menghapus 'idx'
			}

			for ($x = 0; $x < count($columns); $x++) {
				if (isset($values[$x])) {
					$col = $columns[$x];
					$val = str_replace('_', ' ', $values[$x]);
					$dt[$col] = $val;
				}
			}

			$data = $this->db->get_where($tab, $dt)->result_array();
		}

		echo json_encode($data);
	}

	// function tampildata($table = '', $colum = '', $val_colum = '', $combo = '')
	// {
	// 	// $table='data_kegiatan';
	// 	$tab = explode("~", $table)[0];

	// 	if ($combo != '') {
	// 		$nama = explode("~", $table)[1];
	// 		$kode = explode("~", $table)[2];
	// 		$this->db->select("$nama as nama");
	// 		$this->db->select("$kode as kode");
	// 	}
	// 	if ($colum == '') {
	// 		$data = $this->db->get($tab)->result_array();
	// 	} else {

	// 		$data = $this->db->get_where($tab, array($colum => str_replace('_', ' ', $val_colum)))->result_array();
	// 	}
	// 	echo json_encode($data);
	// }
	function delete_data($table, $colum, $id)
	{
		$this->db->where($colum, $id);
		$res = $this->db->delete($table);
		if ($res) {
			$this->db->where($colum, $id);
			$this->db->delete('slo_detail');
			echo 0;
		} else {
			echo 1;
		}
	}
	function update_data($table, $colum, $id, $col, $val)
	{
		$this->db->where($colum, $id);
		$res = $this->db->update($table, array($col => $val));
		if ($res) {
			echo 0;
		} else {
			echo 1;
		}
	}
}
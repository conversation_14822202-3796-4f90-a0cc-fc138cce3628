<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Reff_users_group extends CI_Controller
{

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct()
    {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url');
        $this->load->library(array('session', 'template'));
        if ($this->session->userdata('active') != 1) {
            redirect('home');
        }
        $this->thangusulan = 2023;
        $this->load->database();
        $this->load->helper('dtssp'); //datatable server side processing helper
        $this->thangwhere = " and tahun =2023";
        //  $this->thangwhere=" and tahun = 2021";
        $this->thang = "where tahun =2023";
        //$this->thang=" where tahun = 2021";
    }

    function get_alias()
    {
        $id = $this->input->get("id");
        $query = $this->db->get_where('aset_user_group', array('id_user_group' => $id, '1' => 1));
        $result = json_decode(json_encode($query->result()[0]));
        $xresult = json_encode($result);
        echo $xresult;
    }

    function get_url()
    {
        $id = $this->input->get("id");
        $query = $this->db->get_where('aset_module', array('kode_module' => $id, '1' => 1));
        $result = json_decode(json_encode($query->result()[0]));
        $xresult = json_encode($result);
        echo $xresult;
    }
    function search_parent_not_exists()
    {
        $data_parent = $this->db->query("select parent from aset_module");

        $data_kode_module = $this->db->query("select kode_module from aset_module" . $this->thang);
        //        echo "<pre>";
//        //print_r($data_parent->result());
//        print_r(json_decode(json_encode($data_parent->result()), true));
//        echo "</pre>";

        $array_parent = json_decode(json_encode($data_parent->result()), true);

        $array_kode_module = json_decode(json_encode($data_kode_module->result()), true);

        //print_r(!empty(array_intersect($array_parent, $array_kode_module)));
        // print_r(array_intersect($array_parent, $array_kode_module));

        //        echo "<pre>";
//        print_r($result);
//        echo "</pre>";
    }
    public function treeview()
    {
        $query = $this->db->get_where('aset_user_group', array('1' => 1));
        $this->db->group_by('id_sub_user_group');
        $array = json_decode(json_encode($query->result()), True);
        $tree = [];
        foreach ($array as $key => $value) {
            $parent = $value["id_sub_user_group"];
            if ($value["id_sub_user_group"] == 0 || $value["id_sub_user_group"] == "") {
                $array[$key]['id_sub_user_group'] = "#";
                $parent = "#";
            }
            array_push(
                $tree,
                array(
                    "id" => $value["id_user_group"],
                    "parent" => $parent,
                    "text" => $value["nama"],

                )
            );
        }
        echo json_encode($tree);
    }

    public function treeviewmodule($id_user_group)
    {

        $sql = "select DISTINCT a.nama_module,c.nama as nama_user,
                a.kode_module,convert(a.parent,char) as parent
                from aset_module a
                left join aset_group_modules b on (a.kode_module = b.kode_module and a.tahun=b.tahun)
        left join aset_user_group c on (c.id_user_group = b.id_user_group and c.tahun=b.tahun)
        WHERE c.id_user_group=$id_user_group ";
        $query = $this->db->query($sql);
        $array = json_decode(json_encode($query->result()), True);
        $tree = [];
        //print_r($array);
        foreach ($array as $key => $value) {
            if ($value["parent"] == NULL or $value["parent"] == '') {
                $array[$key]['parent'] = "#";
                $parent = "#";
                // echo (string)$parent;
            } else {
                $parent = $value["parent"];
            }
            array_push(
                $tree,
                array(
                    "id" => $value["kode_module"],
                    "parent" => (string) $parent,
                    "text" => $value["nama_module"]
                )
            );
        }
        echo json_encode($tree);
    }

    public function treeviewmoduleall()
    {
        $sql = "select * from aset_module ";
        $query = $this->db->query($sql);
        $array = json_decode(json_encode($query->result()), True);
        $tree = [];
        foreach ($array as $key => $value) {
            $parent = $value["parent"];
            if ($value["parent"] == null || $value["parent"] == 0) {
                $array[$key]['parent'] = "#";
                $parent = "#";
            }
            array_push(
                $tree,
                array(
                    "id" => $value["kode_module"],
                    "parent" => $parent,
                    "text" => $value["nama_module"]
                )
            );
        }
        echo json_encode($tree);
    }

    public function addform()
    {
        $param = $this->input->post('formData');
        // $sql = "select max(id_user_group) AS lastid from aset_user_group".$this->thang;
        $sql = "select max(id_user_group) AS lastid from aset_user_group";
        $query = $this->db->query($sql);
        $max_id_usergroup = $query->result()[0]->lastid;
        $param["id_user_group"] = $max_id_usergroup + 1;
        $res = $this->db->insert('aset_user_group', $param);
        if (!$res) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

    public function addformModule()
    {
        $param = $this->input->post('formData');


        //insert ke tabel module
        $id_user_group = $param['id_user_group'];
        unset($param['id_user_group']);
        //unset($param['parent']);

        $sql = "SELECT MAX(CAST(kode_module AS UNSIGNED)) AS lastid FROM aset_module ";
        $query = $this->db->query($sql);

        $maxmodule = $query->result()[0]->lastid;
        //print($maxmodule);
        //die();
        $param["kode_module"] = $maxmodule + 1;
        //$param["parent"] = NULL;
        if ($param["parent"] == "") {
            $param["parent"] = NULL;
        }

        // echo "<pre>";
        //     print_r($param);
        // echo "</pre>";
        // die();
        //$this->db->set('parent', NULL, false);
        // $this->db->set('thang', 2023, false);\
        $this->db->set('tahun', 2023);
        $res = $this->db->insert('aset_module', $param);

        $param_group_modules = array(
            "kode_module" => $param["kode_module"],
            "id_user_group" => (int) $id_user_group,
            "id_sub_user_group" => 0,
            "id_sub_sub_user_group" => 0,
            //   "thang" => 2023
        );
        //insert ke tabel grup_module

        //$this->db->set('thang', 2023, false);
        $this->db->set('tahun', 2023);
        $res1 = $this->db->insert('aset_group_modules', $param_group_modules);


        // if($res) {

        //  }

        if (!$res and !$res1) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

    function get_parent($kode_module)
    {
        $sql = "select parent,kode_module from aset_module where kode_module= '$kode_module'" . $this->thangwhere;

        $query = $this->db->query($sql);

        //print($query->result()[0]->parent);
        //print_r($query->result()[0]);
        //return json_decode($query->result()[0]);
        return json_decode(json_encode($query->result()[0]), True);
        //return $query->result()[0];
    }

    public function assign_modul()
    {
        $param = $this->input->post('formData');


        //insert ke tabel module

        $id_user_group = $param['id_user_group'];
        $parent = $this->get_parent($param['kode_module'])["parent"];
        $array_parent = [];
        //echo $parent;
        $i = 0;
        $data_parent = array();
        //die();
        while ($parent != "") {

            if ($i == 0) {
                //echo 1;
                $data_parent = $this->get_parent($param['kode_module']);
                //echo $data_parent["kode_module"];
            } else {
                //print_r(expression)
                $data_parent = $this->get_parent($data_parent["parent"]);
                //print_r($data_parent["kode_module"]);
                $parent = $data_parent["parent"];
            }


            $param_group_modules = array(
                "kode_module" => $data_parent["kode_module"],
                "id_user_group" => $id_user_group,
                "id_sub_user_group" => 0,
                "id_sub_sub_user_group" => 0
            );
            //insert ke tabel grup_module
            $this->db->set('tahun', 2023, false);
            $res1 = $this->db->insert('aset_group_modules', $param_group_modules);


            $i++;
        }

        if (!$res1) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

    function assign_modul2()
    {
        $param = $this->input->post('formData');
        $id_user_group = $param['id_user_group'];
        //echo $param["list_kode_module"][0];die();
        $array_kode_module = [];
        foreach ($param["list_kode_module"] as $value) {
            $parent = $this->get_parent($value)["parent"];
            $array_parent = [];
            $i = 0;
            $data_parent = array();

            while ($parent != "") {

                if ($i == 0) {
                    $data_parent = $this->get_parent($value);
                } else {
                    $data_parent = $this->get_parent($data_parent["parent"]);
                }



                $parent = $data_parent["parent"];
                array_push($array_kode_module, $data_parent["kode_module"]);
                $i++;
            }
            //echo 555; die();
        }
        if (empty($array_kode_module)) {
            array_push($array_kode_module, $param["list_kode_module"][0]);
        }
        $array_kode_module = array_unique($array_kode_module);
        //print_r($array_kode_module);
        //echo 7777;
        //die();
        foreach ($array_kode_module as $value) {
            $param_group_modules = array(
                "kode_module" => $value,
                "id_user_group" => $id_user_group,
                "id_sub_user_group" => 0,
                "id_sub_sub_user_group" => 0
            );
            //insert ke tabel grup_module
            $this->db->set('tahun', 2023, false);
            $res1 = $this->db->insert('aset_group_modules', $param_group_modules);
        }
    }

    //memeriksa apakah module sudah diinsert sebelumnya
    function check_kode_module_exist($id)
    {
        //$id=$this->input->get("id");
        $query = $this->db->get_where('aset_user_group', array('id_sub_user_group' => $id, '1' => 1));
        //echo $query->num_rows();
        return $query->num_rows();
    }

    public function editform()
    {
        $param = $this->input->post('formData');
        $this->db->where('id_user_group', $param['id_user_group']);
        $res = $this->db->update('aset_user_group', $param);


        if (!$res) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

    public function editformmodule()
    {
        $param = $this->input->post('formData');
        if ($param["parent"] == "") {
            $param["parent"] == null;
        }
        $this->db->where('kode_module', $param['kode_module']);
        $res = $this->db->update('aset_module', $param);


        if (!$res) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

    function check_children($id)
    {
        //$id=$this->input->get("id");
        $query = $this->db->get_where('aset_user_group', array('id_sub_user_group' => $id, '1' => 1));
        //echo $query->num_rows();
        return $query->num_rows();
    }

    function check_children_module($id)
    {
        //$id=$this->input->get("id");
        $query = $this->db->get_where('aset_module', array('parent' => $id, '1' => 1));
        //echo $id."<br/>";
        //echo $query->num_rows();
        return $query->num_rows();
    }

    function check_children_module_by_2_params($parent, $id_user_group)
    {
        //$id=$this->input->get("id");
        $sql = "select a.nama_module,c.nama as nama_user,
                a.kode_module, a.parent
                from aset_module a
                left join aset_group_modules b on (a.kode_module = b.kode_module and a.tahun=b.tahun)
        left join aset_user_group c on (c.id_user_group = b.id_user_group and c.tahun=b.tahun)
        WHERE
        c.id_user_group=$id_user_group and a.parent='$parent'";

        $query = $this->db->query($sql);

        return $query->num_rows();
    }

    public function deleteform()
    {

        $id = $this->input->post('id');
        $numberofchild = $this->check_children($id);
        if ($numberofchild == 0) {
            $this->db->where('id_user_group', $id, 'tahun');
            $res = $this->db->delete('aset_user_group');
            if (!$res) {
                // if query returns null
                $msg = $this->db->_error_message();
                echo '{"status":"error", "msg":"' . $msg . '"}';
            } else {
                echo '{"status":"sukses"}';
            }
        } else {

            echo '{"status":"have_childs"}';
        }
    }

    public function deleteformmodule()
    {

        $param = $this->input->post('formData');
        $numberofchild = $this->check_children_module_by_2_params($param["kode_module"], $param["id_user_group"]);

        if ($numberofchild == 0) {
            $this->db->where('id_user_group', $param["id_user_group"])->where('kode_module', $param["kode_module"]);
            $res = $this->db->delete('aset_group_modules');
            if (!$res) {
                // if query returns null
                $msg = $this->db->_error_message();
                echo '{"status":"error", "msg":"' . $msg . '"}';
            } else {
                echo '{"status":"sukses"}';
            }
        } else {
            echo '{"status":"have_childs"}';
        }
    }

    public function index()
    {
        header("Access-Control-Allow-Origin: *");
        $data = array();
        /*         * keterangan parameter
          $this->template->load('default_layout', 'contents' , 'index', $data);
          1.default_layout = nama template utama untuk merender menu header dan footer wajib diisi
          2.contents = nama variabel  yang akan di passing kedalam template wajib diisi dan dibuat filenya nama file view akan diambil variabel content
          3.index = nama view yang akan di load
         * */
        $title = "Modul User/Role";
        $js_file = $this->load->view('reff_users_group/js_file', '', true);
        $modal_filter = $this->load->view('reff_users_group/modal_filter', '', true);
        $modal_tambah = $this->load->view('reff_users_group/modal_tambah', '', true);
        $modal_tambah_modul = $this->load->view('reff_users_group/modal_tambah_modul', '', true);
        $modal_akses_modul = $this->load->view('reff_users_group/modal_akses_modul', '', true);
        $modal_edit_modul = $this->load->view('reff_users_group/modal_edit_modul', '', true);
        $modal_edit = $this->load->view('reff_users_group/modal_edit', '', true);
        $modal_view = $this->load->view('reff_users_group/modal_view', '', true);
        $data = array(
            "modal_filter" => $modal_filter,
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "modal_view" => $modal_view,
            "modal_akses_modul" => $modal_akses_modul,
            "modal_tambah_modul" => $modal_tambah_modul,
            "modal_edit_modul" => $modal_edit_modul,
            "title" => $title,
            "jv_script" => $js_file
        );
        // $this->template->set('title', $title);
        // $this->template->set('jv_script', $js_file);
        $this->load->view('reff_users_group/index', $data);
    }

}
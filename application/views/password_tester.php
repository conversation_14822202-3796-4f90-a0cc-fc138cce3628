<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KONJAS - Password Hash Tester</title>
    <link rel="stylesheet" href="<?php echo base_url(); ?>assets/vendor/bootstrap/css/bootstrap.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .tester-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        .hash-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
        }
        .match {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .no-match {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="tester-card">
        <h3 class="text-center mb-4">KONJAS Password Hash Tester</h3>
        <p class="text-muted text-center">Test password hashes to troubleshoot login issues</p>
        
        <div class="row">
            <div class="col-md-6">
                <h5>Password Testing</h5>
                <form id="hashForm">
                    <div class="mb-3">
                        <label for="password" class="form-label">Password to Test</label>
                        <input type="text" class="form-control" id="password" name="password" placeholder="Enter password">
                    </div>
                    <button type="submit" class="btn btn-primary">Generate MD5 Hash</button>
                </form>
                
                <div id="hashResult" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h5>User Hash Lookup</h5>
                <form id="userForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" placeholder="Enter username" value="03301">
                    </div>
                    <button type="submit" class="btn btn-info">Get User Hash</button>
                </form>
                
                <div id="userResult" class="mt-3"></div>
            </div>
        </div>
        
        <hr class="my-4">
        
        <div class="row">
            <div class="col-12">
                <h5>Common Password Patterns for User "033"</h5>
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-outline-secondary btn-sm m-1" onclick="testPassword('033')">033</button>
                        <button class="btn btn-outline-secondary btn-sm m-1" onclick="testPassword('033!!@#')">033!!@#</button>
                        <button class="btn btn-outline-secondary btn-sm m-1" onclick="testPassword('03301')">03301</button>
                        <button class="btn btn-outline-secondary btn-sm m-1" onclick="testPassword('03301!!@#')">03301!!@#</button>
                        <button class="btn btn-outline-secondary btn-sm m-1" onclick="testPassword('033123')">033123</button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-outline-secondary btn-sm m-1" onclick="testPassword('password')">password</button>
                        <button class="btn btn-outline-secondary btn-sm m-1" onclick="testPassword('123456')">123456</button>
                        <button class="btn btn-outline-secondary btn-sm m-1" onclick="testPassword('admin')">admin</button>
                        <button class="btn btn-outline-secondary btn-sm m-1" onclick="testPassword('033033')">033033</button>
                        <button class="btn btn-outline-secondary btn-sm m-1" onclick="testPassword('033!')">033!</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h6>Known Hash from Database (user 03301):</h6>
            <div class="hash-result">
                92c03384b1939b4cbee235eec046e00d
            </div>
        </div>
        
        <div class="text-center mt-3">
            <a href="<?php echo base_url('login'); ?>" class="btn btn-outline-primary">Back to Login</a>
            <a href="<?php echo base_url('login/test'); ?>" class="btn btn-outline-success">Login Test Page</a>
        </div>
    </div>

    <script src="<?php echo base_url(); ?>assets/vendor/jquery/jquery.min.js"></script>
    <script>
        const knownHash = '92c03384b1939b4cbee235eec046e00d';
        
        function md5(str) {
            // Simple MD5 implementation for client-side testing
            // Note: This is a simplified version, for production use a proper library
            return CryptoJS.MD5(str).toString();
        }
        
        function testPassword(password) {
            $('#password').val(password);
            $('#hashForm').submit();
        }
        
        $('#hashForm').on('submit', function(e) {
            e.preventDefault();
            
            const password = $('#password').val();
            if (!password) {
                alert('Please enter a password');
                return;
            }
            
            // Send to server to generate hash
            $.ajax({
                url: '<?php echo base_url("login/hash_password"); ?>',
                method: 'POST',
                data: { password: password },
                dataType: 'json',
                success: function(response) {
                    const isMatch = response.hash === knownHash;
                    const resultClass = isMatch ? 'match' : 'no-match';
                    const matchText = isMatch ? '✅ MATCH!' : '❌ No match';
                    
                    $('#hashResult').html(`
                        <div class="hash-result ${resultClass}">
                            <strong>Password:</strong> ${password}<br>
                            <strong>MD5 Hash:</strong> ${response.hash}<br>
                            <strong>Match Status:</strong> ${matchText}
                        </div>
                    `);
                    
                    if (isMatch) {
                        alert('🎉 Password found! The password for user 03301 is: ' + password);
                    }
                },
                error: function() {
                    $('#hashResult').html('<div class="hash-result no-match">Error generating hash</div>');
                }
            });
        });
        
        $('#userForm').on('submit', function(e) {
            e.preventDefault();
            
            const username = $('#username').val();
            if (!username) {
                alert('Please enter a username');
                return;
            }
            
            $.ajax({
                url: '<?php echo base_url("login/get_user_hash"); ?>',
                method: 'POST',
                data: { username: username },
                dataType: 'json',
                success: function(response) {
                    if (response.found) {
                        $('#userResult').html(`
                            <div class="hash-result">
                                <strong>User:</strong> ${response.username}<br>
                                <strong>ID:</strong> ${response.id}<br>
                                <strong>Hash:</strong> ${response.hash}<br>
                                <strong>Hash Type:</strong> ${response.hash_type}
                            </div>
                        `);
                    } else {
                        $('#userResult').html('<div class="hash-result no-match">User not found</div>');
                    }
                },
                error: function() {
                    $('#userResult').html('<div class="hash-result no-match">Error looking up user</div>');
                }
            });
        });
    </script>
    
    <!-- Include CryptoJS for MD5 hashing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
</body>
</html>

<script nonce="R2Rra24fVm5xa2Mg">
$(".login-btn").prop("disabled", true)
// step-1
const captcha = new Captcha($('#canvas'), {
    length: 4
});

function refresh_captcha() {
    var val = $('input[name="code"]').val()
    const ans = captcha.valid(val);
    // if (ans == false) {
    //     alert("salah")
    // } else {
    //     alert("benar")
    // }

}

function valid() {
    var val = $('input[name="code"]').val()
    if (val.length == 4) {
        const ans = captcha.valid(val);

        // $("#info-cap").empty()
        if (ans == false) {
            // alert("Captcha Anda Salah")
            $(".login-btn").prop("disabled", true)
            $(".login-btn").attr("title", "Captcha yang anda masukan salah !!")
            $(".captcha-section").removeClass("success").addClass("error")
            // $("#info-cap").html("<i class='fa fa-times'></i>")
        } else {
            $(".login-btn").prop("disabled", false)
            $(".login-btn").attr("title", "Klik Login")
            $(".captcha-section").removeClass("error").addClass("success")
            // $("#info-cap").html("<i class='fa fa-check'></i>")
        }
    } else {
        $(".login-btn").prop("disabled", true)
        $(".login-btn").attr("title", "Captcha yang anda masukan salah !!")
        $(".captcha-section").removeClass("success").addClass("error")
        // $("#info-cap").html("<i class='fa fa-times'></i>")
    }
}

function close_alert() {
    $("#alert_login").css({
        display: "none"
    });
}

// function doesConnectionExist() {
//     var xhr = new XMLHttpRequest();
//     var file = "https://www.kirupa.com/blank.png";
//     var randomNum = Math.round(Math.random() * 10000);

//     xhr.open('HEAD', file + "?rand=" + randomNum, true);
//     xhr.send();

//     xhr.addEventListener("readystatechange", processRequest, false);

//     function processRequest(e) {
//         if (xhr.readyState == 4) {
//             if (xhr.status >= 200 && xhr.status < 304) {
//                 alert("connection exists!");
//             } else {
//                 alert("connection doesn't exist!");
//             }
//         }
//     }
// }

function login() {
    event.preventDefault()
    var username = $("#username").val();
    var password = $("#password").val();
    var val = $('input[name="code"]').val()
    if (val == '') {
        swal({
            icon: "warning",
            text: "Captcha tidak boleh kosong !!",
            showConfirmButton: false,
            timer: 2000,
            type: "success"
        });
        return
    }
    // console.log(email)
    // return
    var settings = {
        "url": "<?php echo base_url(); ?>login/aksi_login",
        "method": "POST",
        "timeout": 0,
        "headers": {
            "Content-Type": "application/x-www-form-urlencoded"
        },
        "data": {
            "user": username,
            "pass": password,
            "<?php echo $this->security->get_csrf_token_name(); ?> ": "<?php echo $this->security->get_csrf_hash(); ?> "
        },
    };
    const ans = captcha.valid(val);
    if (ans == true) {
        $.ajax(settings).done(function(response) {
            var x = JSON.parse(response)
            console.log(x)
            //  return;
            if (x.status == 'fail') {
                swal({
                    icon: "warning",
                    text: x.msg,
                    showConfirmButton: false,
                    timer: 2000,
                    type: "success"
                });
            } else {
                swal({
                    icon: "success",
                    text: x.msg,
                    showConfirmButton: false,
                    timer: 2000,
                    type: "success"
                });

                if (x.group == 1) {
                    window.location.href = "<?php echo base_url() . "data_users/page"; ?> ";
                } else {
                    window.location.href = "<?php echo base_url() . "perjanjian_konsesijasa/page"; ?> ";
                }


            }
        });
    } else {
        swal({
            icon: "warning",
            text: "Captcha salah !!",
            showConfirmButton: false,
            timer: 2000,
            type: "success"
        });
    }
}

function check_connection() {
    var status = "";
    var url = "<?php echo base_url('/login/check_connection') ?>";
    $.ajax({
        //type: "GET",
        url: url,
        async: false,
        success: function(msg) {
            status = "conn_active";
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            if (textStatus == 'timeout') {
                status = "conn_error";
            }
        }
    });

    return status;
    //alert(status);
}


$(".login-btn").click(function() {
    login();
    return false;
});

// Handle form submission
$(".modern-login-form").submit(function(e) {
    e.preventDefault();
    login();
    return false;
});

$(".reset100-form-btn").click(function() {
    update_password();
    return false;
});

var yuhu = $('#password') || $('#email');
yuhu.keypress(function(e) {
    if (e.which == 13) {
        login();
        return false;
    }
});


function lupa_password() {
    $('.form-group').removeClass('has-error'); // clear error class
    $('.help-block').empty(); // clear error string
    $('#modal-tambah').modal('show');
    $('.tbhItem').text('Form bbws');
}

function reset_password() {
    var email = $("#email_reset").val()
    var dt = tampil_data('v_user', 'email', email);
    var kode = '';
    var nama = '';
    if (dt.length < 1) {
        swal({

            title: "Email anda tidak terdaftar",
            text: "",
            icon: "warning",
            // buttons: false,
            showConfirmButton: true,
            timer: 3000,
            dangerMode: true,
        });
        return;
    } else {
        kode = dt[0].kode;
        name = dt[0].nama;

    }


    url = "<?php echo base_url(); ?>login/send_email/";

    var data_post = {
        "email": email,
        "kode": kode,
        "nama": nama,
        "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
    };
    console.log(data_post);
    jQuery.ajax({
        contentType: 'application/x-www-form-urlencoded',
        dataType: "json",
        type: "POST",
        data: data_post,
        url: url,
        success: function(data) {
            if (data == 0) {
                swal({
                    title: "",
                    text: "Cek Email anda, untuk konfirmasi reset password",
                    showConfirmButton: true,
                    // timer:1000,
                    type: "success"
                });

            } else {
                swal({
                    title: "",
                    text: "Gagal Mengirim Email",
                    showConfirmButton: false,
                    timer: 1000,
                    type: "warning"
                });
            }
        }
    });
    //   var params = {"formData": ''};
    //   console.log(params)
    //   $.get(url, params).done(function (data) {
    //     if(data==0){
    //       swal({
    //             title: "",
    //             text: "Cek Email anda, untuk konfirmasi reset password",
    //             showConfirmButton: true,
    //             // timer:1000,
    //             type:"success"
    //           });

    //     }else{
    //       swal({
    //             title: "",
    //             text: "Gagal Mengirim Email",
    //             showConfirmButton: false,
    //             timer:1000,
    //             type:"warning"
    //           });
    //     }
    //   });


}

function update_password() {
    var password_b = $("#password_b").val();
    var k_password_b = $("#k_password_b").val();
    var kode = $("#kode").val();

    // var dt = tampil_data('aset_users','password',kode);
    // console.log(dt)

    if (password_b != k_password_b) {
        swal({

            title: "Gagal",
            text: "Password Tidak Sesuai Konirmasi Password",
            icon: "warning",
            // buttons: false,
            showConfirmButton: true,
            timer: 3000,
            dangerMode: true,
        });
        return;
    }
    url = "<?php echo base_url(); ?>login/update_password/";
    var data_post = {
        "password": password_b,
        "kode": kode,
        "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
    };
    console.log(data_post);
    jQuery.ajax({
        contentType: 'application/x-www-form-urlencoded',
        dataType: "json",
        type: "POST",
        data: data_post,
        url: url,
        success: function(data) {
            if (data == 0) {

                swal({
                        title: "Berhasil Merubah Password",
                        text: "Silahkan Login.",
                        icon: "warning",
                        buttons: true,
                        dangerMode: true,
                    })
                    .then((willDelete) => {
                        if (willDelete) {
                            var urls = "<?php echo base_url(); ?>login";
                            window.location.href = urls
                        } else {
                            // swal("Anda Tidak Menjadi Menghapus Data");
                        }
                    });

            } else {
                swal({
                    title: "",
                    text: "Url sudah tidak valid",
                    showConfirmButton: false,
                    timer: 1000,
                    type: "warning"
                });
            }
        }
    });
}

function tampil_data(table, colum, id) {
    var url = ''
    var tadata = ''
    urls = "<?php echo base_url(); ?>login/tampildata/" + table + "/" + colum + "/" + id;
    $.ajax({
        url: urls,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function(data) {
            tadata = data;
        },
        failure: function(errMsg) {
            alert(errMsg);
        }
    });
    return tadata;
}
</script>
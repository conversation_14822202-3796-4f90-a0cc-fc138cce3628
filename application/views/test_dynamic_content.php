<div class="content">
    <div class="page-inner mt--5">
        <div class="row mt--2">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Dynamic Content Loading Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h4>Test Dynamic Content Loading</h4>
                                <p>This page demonstrates the dynamic content loading functionality. Click the buttons below to load different content without refreshing the entire page.</p>
                                
                                <div class="btn-group-vertical mb-3" role="group">
                                    <div class="btn-group mb-2" role="group">
                                        <button type="button" class="btn btn-primary ajax-link"
                                                onclick="loadPerjanjianKonsesiJasa()">
                                            Load <PERSON>janjian <PERSON>
                                        </button>
                                        <button type="button" class="btn btn-warning ajax-link"
                                                onclick="loadSLO()">
                                            Load SLO
                                        </button>
                                        <button type="button" class="btn btn-secondary ajax-link"
                                                onclick="loadSaldoAwal()">
                                            Load <PERSON>
                                        </button>
                                    </div>
                                    <div class="btn-group mb-2" role="group">
                                        <button type="button" class="btn btn-dark ajax-link"
                                                onclick="loadSaldoAwalLP()">
                                            Load Saldo Awal LP
                                        </button>
                                        <button type="button" class="btn btn-outline-primary ajax-link"
                                                onclick="loadPerolehanMutasiLP()">
                                            Load Perolehan Mutasi LP
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary ajax-link"
                                                onclick="loadPerolehanMutasiKuantitas()">
                                            Load Perolehan Mutasi Kuantitas
                                        </button>
                                    </div>
                                    <div class="btn-group mb-2" role="group">
                                        <button type="button" class="btn btn-success ajax-link"
                                                onclick="loadDashboard()">
                                            Load Dashboard
                                        </button>
                                    </div>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-info ajax-link"
                                                onclick="loadPage('ajax_content', 'load_view/test_dynamic_content', 'Test Page')">
                                            Reload This Page
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <h6>How it works:</h6>
                                    <ul>
                                        <li>Only the content inside <code>.pcoded-inner-content</code> div changes</li>
                                        <li>The sidebar, header, and other layout elements remain unchanged</li>
                                        <li>Browser history is updated so back/forward buttons work</li>
                                        <li>Page title is updated dynamically</li>
                                        <li>Loading states are shown during content transitions</li>
                                    </ul>
                                </div>
                                
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h6>Navigation Links</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>You can also add the <code>data-ajax-load="true"</code> attribute to any link to make it load content dynamically:</p>
                                        <pre><code>&lt;a href="<?php echo base_url('perjanjian_konsesijasa/page'); ?>" data-ajax-load="true"&gt;
    Perjanjian Konsesi Jasa
&lt;/a&gt;</code></pre>
                                        
                                        <p>Or use the <code>ajax-link</code> class:</p>
                                        <pre><code>&lt;a href="<?php echo base_url('dashboard'); ?>" class="ajax-link" data-title="Dashboard"&gt;
    Dashboard
&lt;/a&gt;</code></pre>
                                        
                                        <div class="mt-3">
                                            <a href="<?php echo base_url('perjanjian_konsesijasa/page'); ?>" 
                                               data-ajax-load="true" 
                                               class="btn btn-outline-primary">
                                                Test Link with data-ajax-load
                                            </a>
                                            
                                            <a href="<?php echo base_url('dashboard'); ?>" 
                                               class="ajax-link btn btn-outline-success ml-2" 
                                               data-title="Dashboard">
                                                Test Link with ajax-link class
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h6>JavaScript API</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>You can also load content programmatically using JavaScript:</p>
                                        <pre><code>// Load a specific page
loadPage('controller_name', 'method_name', 'Page Title');

// Load perjanjian konsesi jasa
loadPerjanjianKonsesiJasa();

// Load dashboard
loadDashboard();

// Access the dynamic loader directly
window.dynamicLoader.loadContent('/some/url', 'Page Title');</code></pre>
                                    </div>
                                </div>
                                
                                <div class="alert alert-warning mt-4">
                                    <h6>Note:</h6>
                                    <p>This functionality requires that your controllers return proper content when called via AJAX. The system automatically handles loading states, error handling, and browser history management.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    console.log('Test dynamic content page loaded');
    
    // Example of handling the contentLoaded event
    $(document).on('contentLoaded', function(event, response) {
        console.log('Content loaded event received:', response);
    });
});
</script>

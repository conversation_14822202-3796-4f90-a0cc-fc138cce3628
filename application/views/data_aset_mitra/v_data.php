<style>
table {
    width: 100% !important;
}



/* #modal-detail-mtl {
  width: 1000px;
  margin: auto;
} */
</style>
<!-- MODAL TAMBAH keuangan ada d admin-->




<div id="modal-tambah-mtl" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Data</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class='fa fa-close' style='color:white;'></i>
                </button>
            </div>
            <div class="modal-body">
                <form method="post" class="form-user" id="submit">

                    <div class="form-group yuh">
                        <label class="control-label col-xs-2" for="nm_satker">Nama Aset Mitra</label>
                        <div class="col-xs-9">
                            <input type="hidden" name="id" class="form-control" value="" id="id" />

                            <input type="text" name="nama" class="form-control" placeholder=" " id="nama" required
                                autofocus />
                        </div>
                    </div>
                    <div class="form-group" id="daf">
                        <div class="col-xs-12">
                            <button class="btn btn-tambah" id="op">
                                <i class="fa fa-refresh"></i> Simpan
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>






<div class="content">

    <div class="page-inner mt--5">
        <div class="row mt--2">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="col-md-3" id="hidetambah">
                            <button class="btn btn-tambah" id="tambah" onclick="tambahdata()">
                                <i class="fa fa-plus"></i> Tambah Aset Mitra
                            </button>
                            <br>
                            <br>
                        </div>
                        <div class="dt-responsive table-responsive">

                            <table class="display table table-bordered table-striped table-hover tab1" id="table-users">
                                <thead>
                                    <tr style="text-align:center;">
                                        <th id="th">No</th>
                                        <th id="th">Nama Aset Mitra</th>
                                        <th id="th" style="width:200px">Option</th>
                                    </tr>
                                </thead>

                                </tbody>
                            </table>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>
<!-- <table id="tblToExcl">
    <thead>
        <tr style="text-align:center;" role="row">
            <th>No
            </th>
            <th>Nama Aset Mitra
            </th>
            <th>Option</th>
        </tr>
    </thead>
    <tbody>
        <tr role="row" class="odd">
            <td rowspan="2">1</td>

            <td>12.533.551.000.000</td>
            <td rowspan="2">
                <button class="btn btn-danger btn-xs" title="Hapus Data" onclick="hapus_data('1')"><i
                        class="fa fa-trash"></i></button>
                <button class="btn btn-primary btn-xs" title="Edit Data" onclick="edit_data('1')"><i
                        class="fa fa-edit"></i></button>
            </td>
        </tr>
        <tr role="row" class="even">

            <td>80.410.000.000.000</td>
        </tr>
    </tbody>
</table> -->
<?php echo $jv_script; ?>
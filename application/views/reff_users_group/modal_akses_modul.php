<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!--
{ "data": "kd_prov" },
{ "data": "id_kabkot" },
{ "data": "kd_kab_kota_bpiw" },
{ "data": "kab_kota" },
{ "data": "kd_kab_irmsv3" },
{ "data": "kd_kab_rams" },
{ "data": "kd_kab_bps" },
{ "data": "kd_kab_rkakl" }
-->
<!-- Slide Right Modal -->
<div class="modal fade" id="modal-akses-module" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-slideright modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="modal-header">
                    <h4 class="modal-title" id="modalTitle">Form Data</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        <i class='fa fa-close' style='color:white;'></i>
                    </button>
                </div>
                <!--
                kode_module
                    id_user_group
                    id_sub_user_group
                    id_sub_sub_user_group
                    id
                -->
                <div class="modal-body">
                    <!-- form start -->
                    <input type="hidden" id="modeform" />
                    <form role="form" id="frm-akses-module">
                        <div class="box-body">
                            <div class="col-xs-12">
                                <div class="form-group" style="display:none;">
                                    <label class="control-label col-xs-2" for="exampleInputPassword1">ID User
                                        Group</label>
                                    <div class="col-xs-9" <input readonly type="text" class="form-control col-md-8"
                                        id="id_user_group" placeholder="">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-xs-2" for="exampleInputPassword1">&nbsp;</label>
                                    <div class="col-xs-9">
                                        <!-- <input type="text" class="form-control" style="width:40%;" id="search_module"
                                            placeholder="Search Module"> -->
                                    </div>
                                </div>
                                <div class="form-group" id="container-jstree-8">
                                    <label class="control-label col-xs-2" id="label-tree_8">Pilih Module</label>
                                    <div id="jstree_8">

                                    </div>
                                </div>
                                <div class="form-group" style="display:none;">
                                    <label class="control-label col-xs-2" for="exampleInputPassword1">Modul
                                        Terpilih</label>
                                    <div class="col-xs-9" <input readonly type="hidden" class="form-control col-md-8"
                                        id="kode_module" placeholder="">
                                        <input readonly type="text" class="form-control col-md-8" id="nama_module"
                                            placeholder="">
                                    </div>
                                </div>
                                <div class="form-group" style="display:none;">
                                    <label class="control-label col-xs-2" for="exampleInputPassword1">URL</label>
                                    <div class="col-xs-9" <input readonly type="text" class="form-control col-md-8"
                                        id="url" placeholder="">
                                    </div>
                                </div>
                                <br>
                            </div>
                        </div>
                        <!-- /.box-body -->
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-sm btn-default" type="button" data-bs-dismiss="modal">Close</button>
                <button onclick="assignModule()" class="btn btn-sm btn-primary" type="button><i class=" fa
                    fa-check"></i>Simpan</button>
            </div>
        </div>
    </div>
</div>
<!-- END Slide Right Modal -->
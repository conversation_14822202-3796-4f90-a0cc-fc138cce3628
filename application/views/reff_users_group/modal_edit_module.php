<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<!--
{ "data": "kd_prov" },
{ "data": "id_kabkot" },
{ "data": "kd_kab_kota_bpiw" },
{ "data": "kab_kota" },
{ "data": "kd_kab_irmsv3" },
{ "data": "kd_kab_rams" },
{ "data": "kd_kab_bps" },
{ "data": "kd_kab_rkakl" }
-->
 <!-- Slide Right Modal -->
 <div class="modal fade" id="modal-edit-module" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-slideright modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title" id="modalTitle"><?php //echo $title; ?></h3>
                </div>
                <!--
                kode_module
                    id_user_group
                    id_sub_user_group
                    id_sub_sub_user_group
                    id
                -->
                <div class="block-content">
                    <!-- form start -->
                    <input type="hidden" id="modeform"/>
                    <form role="form" id="frm-edit-module">
                      <div class="box-body">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <label for="exampleInputPassword1">ID User Group</label>
                                <input readonly type="text" class="form-control col-md-8" id="id_user_group" placeholder="">
                            </div>
                            <div class="form-group">
                                <label for="exampleInputPassword1">Kode Modul</label>
                                <input maxlength="3" type="text" class="form-control col-md-8" id="kode_module" placeholder="">
                            </div>
                             <div class="form-group">
                                <label for="exampleInputPassword1">Nama Modul</label>
                                <input type="text" class="form-control col-md-8" id="nama_module" placeholder="">
                            </div>
                            <div class="form-group">
                                <input id="check_parent" onclick="setAsParent()" type="checkbox">Jadikan sebagai Level 1
                            </div>
                            <div class="form-group" id="container-jstree-7">
                                <label id="label-tree_8">Induk</label>
                                <div id="jstree_8">
                                       
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputPassword1">Induk Terpilih</label>
                                 <input readonly type="text" class="form-control col-md-8" id="parent" placeholder="">
                            </div>
                            <div class="form-group">
                                <label for="exampleInputPassword1">URL</label>
                                 <input type="text" class="form-control col-md-8" id="alias" placeholder="">
                            </div>
                            <br>
                        </div>
                      </div>
                      <!-- /.box-body -->
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-sm btn-default" type="button" data-bs-dismiss="modal">Close</button>
                <button onclick="simpanFormModule()" class="btn btn-sm btn-primary" type="button><i class="fa fa-check"></i>Simpan</button>
            </div>
        </div>
    </div>
</div>
<!-- END Slide Right Modal -->

<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<div class="modal fade" id="modal-tambah" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Role</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class='fa fa-close' style='color:white;'></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- form start -->
                <form role="form" id="frm-tambah">
                    <input type="hidden" id="modeform" />
                    <div class="box-body">
                        <div class="col-xs-12">
                            <!--
                            <div class="form-group">
                                <label class="control-label col-xs-2" for="exampleInputPassword1">ID User Group</label>
                                <input type="text" class="form-control col-md-8" id="id_user_group" placeholder="">
                            </div>
                            -->
                            <div class="form-group">
                                <label class="control-label col-xs-2" for="exampleInputPassword1">User Group</label>
                                <div class="col-xs-9">
                                    <input type="text" class="form-control col-md-8" id="nama" placeholder="">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-xs-9">
                                    <input id="check_parent" onclick="setAsParent()" type="checkbox">&nbsp;Jadikan
                                    sebagai
                                    Level 1

                                    <div class="form-group" id="container-jstree4">
                                        <label class="control-label col-xs-2" for="exampleInputPassword1">Induk</label>
                                        <div id="jstree_4">

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-2" for="exampleInputPassword1">Induk
                                    Terpilih</label>
                                <div class="col-xs-9">
                                    <input readonly type="text" class="form-control col-md-8" id="id_sub_user_group"
                                        placeholder="">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-2" for="exampleInputPassword1">Alias</label>
                                <div class="col-xs-9">
                                    <input type="text" class="form-control col-md-8" id="alias" placeholder="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /.box-body -->
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default waves-effect " data-bs-dismiss="modal">Close</button>
                <button type="button" onclick="simpanForm()"
                    class="btn btn-primary waves-effect waves-light ">Simpan</button>
            </div>
        </div>
    </div>
</div>
<!-- END Slide Right Modal -->
<script>
var tahun_layout = $("#tahun_layout").val()
var tahunSaatIni = new Date().getFullYear();
var id_user_group = "<?php echo $this->session->userdata('id_user_group'); ?>"
var id_user = "<?php echo $this->session->userdata('id_user'); ?>"
var id_eselon = "<?php echo $this->session->userdata('kode_eselon_1'); ?>"
var id_satker = "<?php echo $this->session->userdata('kode_satker'); ?>"

var kolom_group = ''
var kode_group = ''
var edit_ak = 'none;'
var hapus_ak = 'none;'
if (id_user_group == 3) {
    kode_group = id_satker
    kolom_group = "kode_satker"
    edit_ak = ''
    hapus_ak = ''
} else if (id_user_group == 4) {
    kolom_group = 'kode_eselon_1';
    kode_group = id_eselon;

} else {
    $(".show_eselon").show()
    kolom_group = '1';
    kode_group = 1;

}

function htmlTableToExcel() {
    sheetJSExportTable(document.getElementById("tabexport"));
}

function sheetJSExportTable(table, name) {
    var elt = table;
    var wb = XLSX.utils.table_to_book(elt, {
        sheet: "Sheet JS",
        raw: true
    });
    let range = XLSX.utils.decode_range(wb.Sheets['Sheet JS']['!ref']);
    let borderStyle = {
        top: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        },
        bottom: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        },
        left: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        },
        right: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        }
    }
    wb.Sheets['Sheet JS']['!rows'] = [{
        hpx: 50
    }]
    let columnWidths = [{
            wpx: 20
        },
        {
            wpx: 350
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 200
        },
        {
            wpx: 350
        },
        {
            wpx: 250
        },
        {
            wpx: 250
        },
        {
            wpx: 150
        },
        {
            wpx: 150
        },
        {
            wpx: 150
        },
        {
            wpx: 150
        },
        {
            wpx: 100
        },
    ];

    // Assign column widths to the worksheet
    wb.Sheets['Sheet JS']['!cols'] = columnWidths;

    wb.Sheets['Sheet JS']["!merges"].forEach(item => {
        if (item.e.r == item.s.r && item.e.c != item.s.c) {
            // 列合并
            let R = item.s.r;
            let countLength = item.e.c - item.s.c;
            for (let i = item.s.c; i <= item.e.c; i++) {
                let cell = {
                    c: i,
                    r: R
                };
                let cell_ref = XLSX.utils.encode_cell(cell);
                if (!wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref] = {
                        t: "s",
                        v: ""
                    };
                }
            }
        } else if (item.e.c == item.s.c && item.e.r != item.s.r) {
            // 行合并
            let C = item.s.c;
            let countLength = item.e.r - item.s.r;
            for (let i = item.s.r; i <= item.e.r; i++) {
                let cell = {
                    c: C,
                    r: i
                };
                let cell_ref = XLSX.utils.encode_cell(cell);
                if (!wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref] = {
                        t: "s",
                        v: ""
                    };
                }
            }
        }
    })
    for (let C = range.s.c; C <= range.e.c; ++C) {
        for (let R = range.s.r; R <= range.e.r; ++R) {
            let cell = {
                c: C,
                r: R
            };
            let cell_ref = XLSX.utils.encode_cell(cell);
            if (C === 4 || C === 13 && R >= 3) {
                if (wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref].t = 'n'; // Set type to number
                    wb.Sheets['Sheet JS'][cell_ref].z = '0';
                }
            }
            if (R == 0 || R == 1 || R == 2) {
                var sis = "15"
                if (R == 1 || R == 2) {
                    sis = "13"
                }
                wb.Sheets['Sheet JS'][cell_ref].s = {
                    alignment: {
                        horizontal: "center",
                        vertical: "center"
                    },
                    font: {
                        name: "",
                        sz: sis,
                        bold: true
                    },
                    border: borderStyle,
                };
            } else {
                if (wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref].s = {
                        font: {
                            name: "",
                            sz: "12"
                        },
                        border: borderStyle,
                    };
                }
            }
        }
    }
    var wopts = {
        bookType: 'xlsx',
        bookSST: false,
        type: 'binary'
    };
    var wbout = XLSXX.write(wb, wopts); // 使用xlsx-style 写入
    saveAs(new Blob([s2ab(wbout)], {
        type: ""
    }), "Perjanjian KonsesiJasa.xlsx")
}

function s2ab(s) {
    var buf = new ArrayBuffer(s.length);
    var view = new Uint8Array(buf);
    for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
    return buf;
}

function reset() {
    // $("#form-filter select").val('')
    $('#nama_p_filter').val('').selectpicker('refresh');
    $('#bdn_usaha_filter').val('').selectpicker('refresh');
    $('#pem_konsesi_filter').val('').selectpicker('refresh');
    $('#pulau_filter').val('').selectpicker('refresh');
    $('#jenis_p_filter').val('').selectpicker('refresh');
    $('#eselon_filter').val('').selectpicker('refresh');
    to_filter('')
}

function close_filter(element) {

    var yuhu = $("#styleSelector").attr('class');
    $(".selector-toggle").hide()
    if (yuhu == '') {
        $(".selector-toggle").show()
    }

    $('#styleSelector').toggleClass('open');
}

function to_filter(a) {
    var nama = $("#nama_p_filter").val()
    var bdn = $("#bdn_usaha_filter").val()
    var pem = $("#pem_konsesi_filter").val()
    var pulau = $("#pulau_filter").val()
    var jenis = $("#jenis_p_filter").val()
    var eselon = $("#eselon_filter").val()
    user(nama, bdn, pem, pulau, jenis, eselon)
}
$(".browse-button0 input:file").change(function() {
    $("input[id='file_slo0']").each(function() {
        var fileInput = $(this)[0]; // Dapatkan elemen input file
        var maxFileSizeInBytes = 15 * 1024 * 1024; // Batasan ukuran file 15 MB
        var allowedFileTypes = ["pdf"]; // Hanya izinkan file PDF

        var file = fileInput.files[0];
        var fileSize = file.size;
        var fileType = file.name.split('.').pop().toLowerCase();

        // Validasi ukuran file
        if (fileSize > maxFileSizeInBytes) {
            alert("Ukuran file terlalu besar. Maksimum 15 MB diizinkan.");
            $(this).val(''); // Clear the file input
            return false;
        }
        if (allowedFileTypes.indexOf(fileType) === -1) {
            alert("Hanya file PDF yang diizinkan.");
            $(this).val(''); // Clear the file input
            return false;
        }

        var fileName = $(this).val().split('/').pop().split('\\').pop();
        $(".filename0").val(fileName);
        $(".browse-button-text0").html('<i class="fa fa-refresh"></i> ');
        $(".clear-button0").show();
        // $('.input-group-btn').html(` <button class="btn btn-primary upload-button" type="button"  style="height:54px !important;">
        //                             <i class="fa fa-download"></i>
        //                           </button>`)
    });
});

//actions happening when the button is clicked
$('.clear-button0').click(function() {
    $('.filename0').val("");
    $('.clear-button0').hide();
    $('.browse-button0 input:file').val("");
    $(".browse-button-text0").html('<i class="fa fa-folder-open"></i> ');
});

function tabelToExport() {
    $('#tes').empty();
    if (id_user_group == 3) {
        var rows = tampil_data('v_perjanjian_pengusahaan', kolom_group, kode_group);
    } else {
        var rows = tampil_data('v_perjanjian_pengusahaan_kemen', kolom_group, kode_group);

    }
    var tabs = `<table id="tabexport" border="1px" style="width:4000px">
    <thead>
    <tr>
    <th colspan="15"><center><b> Laporan Dokumen Perjanjian Konsesi Jasa</b></center></th>
    </tr>
    <tr style="text-align:center;">
                                    <th rowspan="2">No</th>
                                    <th rowspan="2">Nama Perjanjian Konsesi Jasa</th>
                                    <th colspan="3">Perjanjian Konsesi Jasa</th>
                                    <th rowspan="2">Badan Usaha</th>
                                    <th rowspan="2">Pemberi Konsesi</th>
                                    <th rowspan="2">Ruang Lingkup</th>
                                    <th rowspan="2">Skema Kompensasi</th>
                                    <th rowspan="2">Tanggal Mulai Konsesi</th>
                                    <th rowspan="2">Tanggal Akhir Konsesi</th>
                                    <th rowspan="2">Pulau</th>
                                    <th rowspan="2">Jenis Aset</th>
                                    <th rowspan="2">Kuantitas</th>
                                    <th rowspan="2">Satuan</th>
                                </tr>
                                <tr>
                                    <th>Nomor Perjanjian</th>
                                    <th>Tanggal</th>
                                    <th>Nilai Investasi</th>
                                </tr>
    </thead>    <tbody>`;



    for (var i = 0; i < rows.length; i++) {
        var id = rows[i].id_perjanjian_pengusahaan
        if (id.includes(',')) {
            id = id.split(',')[0]
        }
        var dtx = get_dataDetailById(id);
        var rowspan = dtx.length;
        for (let j = 0; j < rowspan; j++) {
            var row = '<tr>';

            if (j === 0 || (i === 0 && rowspan === 1)) {
                row +=
                    `<td  class="xl68"   rowspan="${rowspan}">${i + 1}</td>`; // Menambahkan nomor urut
                row +=
                    `<td  class="xl68"   rowspan="${rowspan}">${rows[i].nm_perjanjian}</td>`; // Menambahkan nama perjanjian
            }

            if (j < dtx.length) {
                row +=
                    `<td  class="xl68" align="left"  >${dtx[j].no_perjanjian}</td>`;
                row +=
                    `<td class="xl68"  >${ubahFormatTanggal(dtx[j].tgl_perjanjian)}</td>`;
                row +=
                    `<td class="xl69"  align="right"   style="mso-number-format:'0.00';">${dtx[j].nilai_investasi === null ? '' : parseFloat(dtx[j].nilai_investasi)}</td>`;
            } else {
                row +=
                    `<td  class="xl68"   ></td><td  class="xl68"   ></td><td  class="xl68"   ></td>`; // Sel kosong jika tidak ada data dari dtx
            }

            if (j === 0 || (i === 0 && rowspan === 1)) {
                row +=
                    `<td rowspan="${rowspan}"  class="xl68"  >${rows[i].nm_mitra === null ? '' : rows[i].nm_mitra}</td>`;
                row +=
                    `<td rowspan="${rowspan}" class="xl68"  >${rows[i].nm_pemberi_konsesi === null ? '' : rows[i].nm_pemberi_konsesi}</td>`;
                row +=
                    `<td rowspan="${rowspan}" class="xl68"  >${rows[i].ruang_lingkup === null ? '' : rows[i].ruang_lingkup}</td>`;
                row +=
                    `<td rowspan="${rowspan}" class="xl68"  >${rows[i].nm_skema_kompensasi === null ? '' : rows[i].nm_skema_kompensasi}</td>`;
                row +=
                    `<td rowspan="${rowspan}" class="xl68"  >${ubahFormatTanggal(rows[i].tgl_awal_konsesi) === null ? '' : ubahFormatTanggal(rows[i].tgl_awal_konsesi)}</td>`;
                row +=
                    `<td rowspan="${rowspan}" class="xl68"  >${ubahFormatTanggal(rows[i].tgl_akhir_konsesi) === null ? '' : ubahFormatTanggal(rows[i].tgl_akhir_konsesi)}</td>`;
                row +=
                    `<td rowspan="${rowspan}" class="xl68"  >${rows[i].nm_pulau === null ? '' : rows[i].nm_pulau}</td>`;
                row +=
                    `<td rowspan="${rowspan}" class="xl68"  >${rows[i].nm_jenis_pengusahaan === null ? '' : rows[i].nm_jenis_pengusahaan}</td>`;
                row +=
                    `<td rowspan="${rowspan}" class="xl68"  >${rows[i].kuantitas === null ? '' : rows[i].kuantitas}</td>`;
                row +=
                    `<td rowspan="${rowspan}" class="xl68"  >${rows[i].nm_satuan === null ? '' : rows[i].nm_satuan}</td>`;
            }
            row += '</tr>';


            tabs += row;
        }
    }


    tabs += '</tbody></table>';
    $('#tes').html(tabs);
}

$(document).ready(function() {
    // var dt = tampil_data('perjanjian_pengusahaan_detail', '1', '1');
    $("#status_op").append(`<option value ="Operasi">Operasi</option>
                                    <option value ="Sebagian Beroperasi">Sebagian Beroperasi</option>
                                    <option value ="Konstruksi">Konstruksi</option>`

    )
    $('#status_op').val('').selectpicker('refresh');

    tahun_layout = tahun_layout === null ? tahunSaatIni : tahun_layout
    var dtx = tampil_data('v_perjanjian_pengusahaan_detail', kolom_group, kode_group);
    var json_data = JSON.stringify(dtx);
    $("#simpandatadetailperjanjian").val(json_data)
    console.log("kolom_group:", kolom_group);
    console.log("kode_group:", kode_group);


    combo('jenis_p_filter', 'ref_jenis_pengusahaan~nm_jenis_pengusahaan~id_jenis_pengusahaan')
    combo('nama_p_filter', 'v_perjanjian_pengusahaan~nm_perjanjian~nm_perjanjian', kolom_group, kode_group)
    combo('bdn_usaha_filter', 'ref_badan_usaha~nm_mitra~id_badan_usaha')
    combo('pem_konsesi_filter', 'ref_satker~nm_satker~kode_satker', kolom_group, kode_group)
    combo('pulau_filter', 'ref_pulau~nm_pulau~id_pulau')
    combo('eselon_filter', 'ref_eselon_1~nm_unit_eselon_1~kode_eselon_1')
    tabelToExport()
    if (tahun_layout == '') {
        swal({
            title: "Harap pilih Tahun Anggaran",
            // text: tex,
            icon: "warning",
            timer: 2000,
            dangerMode: true,
        });
        return
    }
    $("#submit").submit(function(e) {
        e.preventDefault()
        // alert("asd");

        var data = new FormData($('.form-user')[
            0]); // Membuat objek FormData dari form dengan kelas .form-user

        var tahun_layout = $("#tahun_layout").val();
        data.append('tahun_layout', tahun_layout); // Menambahkan tahun_layout ke objek FormData


        $.ajax({
            type: 'POST',
            url: "<?php echo base_url('index.php/perjanjian_konsesijasa/insert_data'); ?>",
            data: data,
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            success: function(data) {
                var ops = ''
                if (data.split('_')[1] == 'insert') {
                    ops = 'Menambah';
                } else {
                    ops = 'Merubah';
                }
                if (data.split('_')[0] == 0) {
                    swal({
                        title: "",
                        text: "Berhasil " + ops + " Data",
                        showConfirmButton: false,
                        timer: 1000,
                        type: "success"
                    });
                    var dt = tampil_data('perjanjian_pengusahaan_detail', '1', '1');
                    var json_data = JSON.stringify(dt);
                    $("#simpandatadetailperjanjian").val(json_data)
                    tabel.ajax.reload();
                    tabelToExport()
                    $("#modal-tambah-mtl").modal("hide");
                } else {
                    swal({
                        title: "",
                        text: "Gagal " + ops + " Data",
                        showConfirmButton: false,
                        timer: 1000,
                        type: "warning"
                    });
                }

            }
        });
    });
    user()

})

function user(nama = '', bdn = '', pem = '', pulau = '', jenis = '', eselon = '') {
    var tab = $('#table-users').DataTable();
    tab.destroy()
    data_user(nama, bdn, pem, pulau, jenis, eselon)
    var tom =
        `<button style="margin-top: 8px;" class="btn btn-default buttons-excel buttons-html5" tabindex="0" aria-controls="table-users" type="button"  onclick="htmlTableToExcel('xlsx')"><span></span></button>`
    $(".tombolTambah").append(tom)
    var toms = ''
    if (id_user_group == 3) {
        toms = `
        <button class="btn btn-tambah" id="tambah" onclick="tambahdata()">
                                <i class="fa fa-plus"></i>  Perjanjian Pengusahaan
                            </button>`
    }
    $(".tombolTambah").append(toms)
    var tomF = `<button style="margin-top: 8px;"  class="btn btn-tambah" id="tambah" onclick="close_filter('')">
                                            <i class="fa fa-filter"></i>
                                        </button><br>
                        <br>`
    $(".tombolFilter").html(tomF)
}

function formatRupiah(angka) {
    if (angka === null) {
        return ''; // Mengembalikan string kosong jika angka adalah null
    }

    var number_string = angka.toString();
    var split = number_string.split(',');
    var sisa = split[0].length % 3;
    var rupiah = split[0].substr(0, sisa);
    var ribuan = split[0].substr(sisa).match(/\d{3}/gi);

    if (ribuan) {
        var separator = sisa ? '.' : '';
        rupiah += separator + ribuan.join('.');
    }

    rupiah = split[1] !== undefined ? rupiah + ',' + split[1] : rupiah;
    return "Rp " + rupiah;
}

function hurufBesarKata(dt) {

    if (dt === null || dt == '') {
        return ''
    }
    var text = dt

    // Pisahkan teks menjadi array kata-kata
    var words = text.split(" ");

    // Ubah setiap kata menjadi format yang diinginkan (huruf besar untuk huruf pertama, huruf kecil untuk yang lainnya)
    var formattedWords = words.map(function(word) {
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    });

    // Gabungkan kembali kata-kata menjadi teks yang diformat
    var formattedText = formattedWords.join(" ");

    // Ubah teks dalam elemen sesuai dengan teks yang telah diformat
    return formattedText;
}

function get_dataDetailById(id) {
    var dt = $("#simpandatadetailperjanjian").val()
    var js = JSON.parse(dt)
    // Mencari semua elemen dalam array yang memiliki ID yang sesuai
    var data = js.filter(function(element) {
        return element.id_perjanjian_pengusahaan === id.toString();
    });

    // Jika data ditemukan, tampilkan semua data
    return data
}
var tabel = null


function data_user(nama, bdn, pem, pulau, jenis, eselon) {
    tahun_layout = tahun_layout === null ? tahunSaatIni : tahun_layout
    tabel = $('#table-users').DataTable({
        "processing": true,
        "serverSide": true, // Default sortingnya berdasarkan kolom / field ke 0 (paling pertama)
        "ajax": {
            "url": "<?php echo base_url('index.php/perjanjian_konsesijasa/ssp') ?>", // URL file untuk proses select datanya
            "type": "POST",
            "data": function(d) {
                d.badan = bdn;
                d.tahun = tahun_layout;
                d.nama = nama;
                d.pem = pem;
                d.pulau = pulau;
                d.jenis = jenis;
                d.eselon = eselon;
            }
        },
        "pageLength": 10,
        "aLengthMenu": [
            [5, 10, 50, 5000],
            [5, 10, 50, "All"]
        ],
        dom: "<'row'<'col-sm-12 col-md-3 tombolTambah'><'col-sm-12 col-md-6'><'col-sm-12 col-md-3 tombolFilter'><'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
            "<'row'<'col-sm-12'tr>>" +
            "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

        buttons: [{
            extend: 'excelHtml5',
            text: '',
            title: 'Perjanjian Konsesi Jasa',
            exportOptions: {
                columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14],
                stripHtml: false,
                modifier: {
                    order: 'current',
                    page: 'all',
                    selected: null,
                },
                format: {
                    body: function(data, row, column, node) {
                        var tempDiv = document.createElement('div');
                        tempDiv.innerHTML = data;

                        // Mendapatkan teks dari elemen div yang sudah dikonversi
                        var textData = tempDiv.textContent || tempDiv.innerText;

                        // Mengganti tag '<br>' dengan karakter newline ('\n')
                        textData = textData.replace(/<br\s*\/?>/ig, "\n");

                        return textData;

                        // return data;
                    }
                }
            }
        }],
        "scrollX": true,
        "order": [
            [0, "desc"]
        ],
        "columns": [{
                "render": function(data, type, row, meta) {
                    // Mengambil halaman yang sedang aktif
                    var page = tabel.page.info().page;
                    // Menghitung nomor urut unik berdasarkan halaman yang sedang aktif
                    var uniqueIndex = page * tabel.page.len() + meta.row + 1;
                    return uniqueIndex;
                }
            }, {
                "data": "nm_perjanjian"
            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var id = row.id_perjanjian_pengusahaan
                    if (id.includes(',')) {
                        id = id.split(',')[0]
                    }
                    var dt = get_dataDetailById(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push(
                            "<div id='kol'>" + dt[i].no_perjanjian +
                            "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                }


            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var id = row.id_perjanjian_pengusahaan
                    if (id.includes(',')) {
                        id = id.split(',')[0]
                    }
                    var dt = get_dataDetailById(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push("<div id='kol'>" + ubahFormatTanggal(dt[i].tgl_perjanjian) +
                            "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                }


            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var id = row.id_perjanjian_pengusahaan
                    if (id.includes(',')) {
                        id = id.split(',')[0]
                    }
                    var dt = get_dataDetailById(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        if (dt[i].nilai_investasi) {
                            noPpjtValues.push("<div id='kol' style='text-align:right !important;'>" +
                                formatAngkaKeRupiah(dt[i].nilai_investasi) +
                                "</div>");
                        }
                    }

                    return noPpjtValues.join('\r\n');
                },
                "createdRow": function(row, data, dataIndex) {
                    $(row).find('td').addClass('newline');
                }


            },
            {
                "data": "nm_mitra"
            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var nm = row.nm_pemberi_konsesi
                    var noPpjtValues = [];
                    if (nm.includes(',')) {
                        nm = nm.split(',')
                        for (let i = 0; i < nm.length; i++) {
                            noPpjtValues.push("<div id='kol'>" + hurufBesarKata(nm[i]) +
                                "</div>");
                        }
                        return noPpjtValues.join('\r\n');
                    } else {
                        return hurufBesarKata(nm);
                    }

                }

            },
            {
                "data": "ruang_lingkup"
            },
            {
                "data": "nm_skema_kompensasi"
            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var nm = row.tgl_awal_konsesi
                    return ubahFormatTanggal(nm);

                }

            },

            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var nm = row.tgl_akhir_konsesi
                    return ubahFormatTanggal(nm);

                }

            },
            {
                "data": "nm_pulau"
            },
            {
                "data": "nm_jenis_pengusahaan"
            },
            {
                "data": "status"
            },
            {
                "data": "kuantitas"
            },
            {
                "data": "nm_satuan"
            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var nm = row.updated_at
                    var hasil = ''
                    if (nm) {

                        // Memisahkan tanggal dan waktu dengan fungsi split
                        var dateTimeArray = nm.split(" ");
                        var tanggal = dateTimeArray[0];
                        var jam = dateTimeArray[1];

                        hasil = "<b>Tanggal : " + tanggal +
                            "<hr style='margin:2px'>Jam : " +
                            jam + "</b>";

                    }
                    return hasil

                }

            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var id = row.id_perjanjian_pengusahaan
                    if (id.includes(',')) {
                        id = id.split(',')[0]
                    }
                    var html_button = [
                        "<button class='btn btn-danger btn-xs' title = 'Hapus Data' onclick=hapus_data('" +
                        id +
                        "') style='display:" + hapus_ak + "'><i class='fa fa-trash'></i></button>",
                        "<button class='btn btn-primary btn-xs' title='Edit Data' onclick=edit_data('" +
                        id +
                        "') style='display:" + edit_ak + "'><i class='fa fa-edit'></i></button>",
                        "<button class='btn btn-warning btn-xs' title='Download file' onclick=download_data('" +
                        id +
                        "')><i class='fa fa-download'></i></button>",


                    ].join("\n");
                    return html_button;

                }
            }
        ],
        "language": {
            "decimal": "",
            "emptyTable": "Data tidak ditemukan",
            "info": "Data _START_ s/d _END_ dari _TOTAL_",
            "infoEmpty": "Tidak ada data",
            "infoFiltered": "(tersaring dari _MAX_)",
            "infoPostFix": "",
            "thousands": ",",
            "lengthMenu": "_MENU_  data per halaman",
            "loadingRecords": '<div class="preloader3 loader-block"><div class="circ1"></div><div class="circ2"></div> <div class="circ3"></div> <div class="circ4"></div></div>',
            "processing": '<div class="preloader3 loader-block"><div class="circ1"></div><div class="circ2"></div> <div class="circ3"></div> <div class="circ4"></div></div>',
            "search": "Cari:",
            "zeroRecords": "Tidak ada data ditemukan",
            "aria": {
                "sortAscending": ": aktifkan untuk mengurutkan naik",
                "sortDescending": ": aktifkan untuk mengurutkan turun"
            }
        }


    });
}


function ubahFormatTanggal(tanggal) {
    if (tanggal == null || tanggal == '') {
        return ''
    }
    // Membagi tanggal menjadi bagian-bagian
    var tanggalBagian = tanggal.split('-');

    // Menyusun ulang tanggal dalam format yang diinginkan
    var tanggalBaru = tanggalBagian[2] + '-' + tanggalBagian[1] + '-' + tanggalBagian[0];

    return tanggalBaru;
}


function downloadFile(url, filename) {
    var a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.style.display = 'none';
    a.click();
    document.body.removeChild(a);
}

function download_data(id) {
    var dt_lain = tampil_data('perjanjian_pengusahaan_detail', 'id_perjanjian_pengusahaan', id);
    console.log(dt_lain)

    $("#box-download").empty()
    var le_lain = dt_lain.length - 1;
    console.log(le_lain);
    for (let i = 0; i < dt_lain.length; i++) {
        if (dt_lain[i].no_perjanjian != null) {
            var but =
                `  <button type="button" style="height:35px;font-size: 20px;color:red;" onclick="remove_comboi('${i}')" name="remove" id="${i}" class="btn btn-defaukt btn_remove" ><i class="fa fa-trash"></i></button>`;
            var link = '#'
            var target = ''
            var icon = '<i class="fa fa-times"></i>'
            var title = 'Tidak bisa download File tidak tersedia'
            var filename = 'File Tidak Tersedia'
            if (dt_lain[i].file_perjanjian) {
                // Use secure download endpoint instead of direct file access
                link = `<?php echo base_url(); ?>index.php/perjanjian_konsesijasa/secure_download/${btoa(dt_lain[i].id_perjanjian_pengusahaan_dtl)}`
                target = '_blank'
                var icon = '<i class="fa fa-download"></i>'
                title = 'Download File'
                filename = dt_lain[i].file_perjanjian.split("~")[1]



            }
            var html = `
            <div class="form-group row " >
              <div class="col-sm-4">
              <input type="text" class="form-control" placeholder=""   value="${dt_lain[i].no_perjanjian}" readonly>
              <input type="hidden" class="form-control" placeholder=""   value="${dt_lain[i].id_slo_dtl}">
              </div>
              <div class="col-sm-2">
                <input type="date" class="form-control" placeholder=""   value="${dt_lain[i].tgl_perjanjian}" readonly>
              </div>
              <div class="col-sm-3">
                <input type="text" class="form-control" placeholder=""   value="${formatRupiah(dt_lain[i].nilai_investasi)}" onkeyup="formatNumber(this)" readonly>
              </div>
              <div class="col-sm-3">
                      <div class="input-group">
                          <input type="text" class="form-control button-yuhu"   placeholder="${filename}"  readonly>
                          <div class="btn btn-tambah browse-button button-yuhu" style="padding:10px 0px;">
                            <a style='color:white' title='${title}' href='${link}' download='${filename}' disabled>
                                <span class="browse-button-text">
                               ${icon} </span>
                            </a>
                            </div>
                    </div>
                  </div>
              <div class="col-sm-1" style="text-align:left;">

              </div>
            </div>
          `;


            $('#box-download').append(html);
        }
    }
    $("#modal-download").modal("show");
}

function tambahdata() {
    $(".form-user input").val('')
    $(".form-user select").val('')
    $(".form-user textarea").val('')
    $("#tahun_pembukuan").val(tahun_layout)
    $("#tahun_perolehan").val(tahun_layout)

    combo('jenis_p', 'ref_jenis_pengusahaan~nm_jenis_pengusahaan~id_jenis_pengusahaan')
    combo('nm_pengusahaan', 'ref_pengusahaan~nm_pengusahaan~id_pengusahaan')
    combo('barang', 'ref_barang~nm_barang~kode_barang')
    combo('bdn_usaha', 'ref_badan_usaha~nm_mitra~id_badan_usaha')
    combo('skema_kompensasi', 'ref_skema_kompensasi~nm_skema_kompensasi~id_skema_kompensasi')
    // combo('satker', 'ref_satker~nm_satker~kode_satker', 'kode_satker', id_satker, id_satker)
    combo('pem_konsesi', 'ref_satker~nm_satker~kode_satker', 'kode_satker', id_satker, id_satker)
    combo('pulau', 'ref_pulau~nm_pulau~id_pulau')
    combo('prov', 'ref_provinsi~nama_prov~kd_prov')
    combo('satuan', 'ref_satuan~nm_satuan~id_satuan')
    $("#bang_lai").empty()
    bang_l()


    $("#modal-tambah-mtl").modal("show");

}

function edit_data(id) {
    $(".form-user input").val('')
    $(".form-user select").val('')
    var dt = tampil_data('perjanjian_pengusahaan', 'id_perjanjian_pengusahaan', id);
    $("#id").val(dt[0].id_perjanjian_pengusahaan)
    $("#kuantitas").val(dt[0].kuantitas)
    $("#ruang_l").val(dt[0].ruang_lingkup)
    $("#nama_p").val(dt[0].nm_perjanjian)
    $("#tgl_mk").val(dt[0].tgl_awal_konsesi)
    $("#tgl_ak").val(dt[0].tgl_akhir_konsesi)
    $("#status_op").val(dt[0].status)
    $("#nup").val(dt[0].nup)
    $("#kd_pengusahaan").val(dt[0].kode_pengusahaan)
    $("#tahun_pembukuan").val(tahun_layout)
    $("#tahun_perolehan").val(tahun_layout)
    combo('jenis_p', 'ref_jenis_pengusahaan~nm_jenis_pengusahaan~id_jenis_pengusahaan', '1', '1', dt[0]
        .id_jenis_pengusahaan)
    combo('nm_pengusahaan', 'ref_pengusahaan~nm_pengusahaan~id_pengusahaan', '1', '1', dt[0].id_pengusahaan)
    combo('bdn_usaha', 'ref_badan_usaha~nm_mitra~id_badan_usaha', '1', '1', dt[0].id_badan_usaha)
    combo('skema_kompensasi', 'ref_skema_kompensasi~nm_skema_kompensasi~id_skema_kompensasi', '1', '1', dt[0]
        .id_skema_kompensasi)
    combo('pem_konsesi', 'ref_satker~nm_satker~kode_satker', 'kode_satker', id_satker, dt[0].pemberi_konsesi)
    combo('pulau', 'ref_pulau~nm_pulau~id_pulau', '1', '1', dt[0].id_pulau)
    combo('satuan', 'ref_satuan~nm_satuan~id_satuan', '1', '1', dt[0].satuan)
    combo('barang', 'ref_barang~nm_barang~kode_barang', '1', '1', dt[0].kode_barang)

    var dt_lain = tampil_data('perjanjian_pengusahaan_detail', 'id_perjanjian_pengusahaan', id);
    console.log(dt_lain)
    $(".yuhui").remove()
    $("#bang_lai").empty()
    var le_lain = dt_lain.length - 1;
    console.log(le_lain);
    $("#urutan").val(dt_lain.length)
    for (let i = 0; i < dt_lain.length; i++) {
        if (dt_lain[i].no_perjanjian != null) {
            var but =
                `  <button type="button" style="height:35px;font-size: 20px;color:red;" onclick="remove_comboi('${i}')" name="remove" id="${i}" class="btn btn-defaukt btn_remove" ><i class="fa fa-trash"></i></button>`;
            var yuh = 'yuhui'
            var filename = ''
            if (dt_lain[i].file_perjanjian) {
                filename = dt_lain[i].file_perjanjian.split("~")[1]
            }
            $("#yuhui").remove()
            $("#yuhui" + i).remove()
            var html = `
            <div class="form-group row yuhui" id="yuhui${i}">
              <div class="col-sm-3">
              <input type="text" class="form-control" placeholder="" name="no_dok[]" id="no_dok${i}"  value="${dt_lain[i].no_perjanjian}">
              <input type="hidden" class="form-control" placeholder="" name="id_detail[]" id="id_detail${i}"  value="${dt_lain[i].id_perjanjian_pengusahaan_dtl}">
              </div>
              <div class="col-sm-2">
                <input type="date" class="form-control" placeholder="" id="tgl_op${i}" name="tgl_op[]"  value="${dt_lain[i].tgl_perjanjian}">
              </div>
              <div class="col-sm-3">
                <input type="text" class="form-control" placeholder="" id="nilai${i}" name="nilai[]"  value="${formatAngkaKeRupiah(dt_lain[i].nilai_investasi)}" onkeyup="formatNumber(this)">
              </div>
              <div class="col-sm-3">
                      <div class="input-group">
                            <div class="btn btn-primary browse-button${i} button-yuhu">
                                <span class="browse-button-text${i}">
                                <i class="fa fa-folder-open"></i> </span>
                               <input type="file" class="form-control" style="width:50px;"  placeholder="" id="file_slo${i}"
                          name="file_slo[]" >
                                </div>
                            <div type="button" class="btn btn-danger clear-button${i} button-yuhu" style="display:none;">
                              <span class="fa fa-times"></span>
                                        </div>

                          <input type="text" class="form-control filename${i} button-yuhu" name="filename[]"  placeholder="Masukan File" readonly>
                          <input type="hidden" class="form-control filenames${i} button-yuhu" name="filenames[]">
                          <span class="input-group-btn">

                          </span>
                    </div>
                  </div>
              <div class="col-sm-1" style="text-align:left;">
              ${but}
              </div>
            </div>
          `;

            $('#bang_lai').append(html);
            if (dt_lain[i].file_perjanjian) {
                $(".filename" + i).val(filename);
                $(".filenames" + i).val(dt_lain[i].file_perjanjian);
                $(".browse-button-text" + i).html('<i class="fa fa-refresh"></i> ');
                $(".clear-button" + i).show();
            }


            $(".browse-button" + i + " input:file").change(function() {
                $("input[id='file_slo" + i + "']").each(function() {
                    var fileInput = $(this)[0]; // Dapatkan elemen input file
                    var maxFileSizeInBytes = 15 * 1024 * 1024; // Batasan ukuran file 15 MB
                    var allowedFileTypes = ["pdf"]; // Hanya izinkan file PDF

                    var file = fileInput.files[0];
                    var fileSize = file.size;
                    var fileType = file.name.split('.').pop().toLowerCase();

                    // Validasi ukuran file
                    if (fileSize > maxFileSizeInBytes) {
                        alert("Ukuran file terlalu besar. Maksimum 15 MB diizinkan.");
                        $(this).val(''); // Clear the file input
                        return false;
                    } else if (allowedFileTypes.indexOf(fileType) === -1) {
                        alert("Hanya file PDF yang diizinkan.");
                        $(this).val(''); // Clear the file input
                        return false;
                    } else {
                        var fileName = $(this).val().split('/').pop().split('\\').pop();
                        $(".filename" + i).val(fileName);
                        $(".browse-button-text" + i).html('<i class="fa fa-refresh"></i> ');
                        $(".clear-button" + i).show();
                    }
                });
            });
            $('.clear-button' + i).click(function() {
                $('.filename' + i).val("");
                $('.filenames' + i).val("");
                $('.clear-button' + i).hide();
                $('.browse-button' + i + ' input:file').val("");
                $(".browse-button-text" + i).html('<i class="fa fa-folder-open"></i> ');
            });

        }
    }
    $("#modal-tambah-mtl").modal("show");
}
var ii = 0;
var yy = 0;

function bang_l(val = '') {
    var jumlahDiv = $("#urutan").val();

    ii = +jumlahDiv
    if (val != '') {
        ii = val + yy + jumlahDiv - 1;
    }
    ii++;
    yy++;

    var html = `
            <div class="form-group row" id="yuhui${ii}">

              <div class="col-sm-3">
              <input type="text" class="form-control" placeholder="" name="no_dok[]" id="no_dok${ii}" >
              </div>
              <div class="col-sm-2">
                <input type="date" class="form-control" placeholder="" id="tgl_op${ii}" name="tgl_op[]" >
              </div>
              <div class="col-sm-3">
                <input type="text" class="form-control" placeholder="" id="nilai${ii}" name="nilai[]"  onkeyup="formatNumber(this)">
              </div>
              <div class="col-sm-3">
                      <div class="input-group">
                            <div class="btn btn-primary browse-button${ii} button-yuhu">
                                <span class="browse-button-text${ii}">
                                <i class="fa fa-folder-open"></i> </span>
                               <input type="file" class="form-control" style="width:50px;"  placeholder="" id="file_slo${ii}"
                          name="file_slo[]" >
                                </div>
                            <div type="button" class="btn btn-danger clear-button${ii} button-yuhu" style="display:none;">
                              <span class="fa fa-times"></span>
                                        </div>

                          <input type="text" class="form-control filename${ii} button-yuhu" name="filename[]"  placeholder="Masukan File" readonly>
                          <input type="hidden" class="form-control filenames${ii} button-yuhu" name="filenames[]">
                          <span class="input-group-btn">

                          </span>
                    </div>
                  </div>
              <div class="col-sm-1" style="text-align:left;">
              <button type="button" style="height:35px;font-size: 20px;color:red;" onclick="remove_comboi('${ii}')" name="remove" id="${ii}" class="btn btn-defaukt btn_remove" ><i class="fa fa-trash"></i></button>
              </div>
            </div>
          `;
    $('#bang_lai').append(html);
    $("#urutan").val(ii)
    var jumlah = ii;
    upload_div(jumlah)

}

function upload_div(dt) {
    for (let ii = 0; ii < dt + 1; ii++) {
        $(".browse-button" + ii + " input:file").change(function() {
            $("input[id='file_slo" + ii + "']").each(function() {
                var fileInput = $(this)[0]; // Dapatkan elemen input file
                var maxFileSizeInBytes = 15 * 1024 * 1024; // Batasan ukuran file 15 MB
                var allowedFileTypes = ["pdf"]; // Hanya izinkan file PDF

                var file = fileInput.files[0];
                var fileSize = file.size;
                var fileType = file.name.split('.').pop().toLowerCase();

                // Validasi ukuran file
                if (fileSize > maxFileSizeInBytes) {
                    alert("Ukuran file terlalu besar. Maksimum 15 MB diizinkan.");
                    $(this).val(''); // Clear the file input
                    return false;
                } else if (allowedFileTypes.indexOf(fileType) === -1) {
                    alert("Hanya file PDF yang diizinkan.");
                    $(this).val(''); // Clear the file input
                    return false;
                } else {
                    var fileName = $(this).val().split('/').pop().split('\\').pop();
                    $(".filename" + ii).val(fileName);
                    $(".filenames" + ii).val(fileName);
                    $(".browse-button-text" + ii).html('<i class="fa fa-refresh"></i> ');
                    $(".clear-button" + ii).show();
                }
            });
        });

        //actions happening when the button is clicked
        $('.clear-button' + ii).click(function() {
            $('.filename' + ii).val("");
            $('.filenames' + ii).val("");
            $('.clear-button' + ii).hide();
            $('.browse-button' + ii + ' input:file').val("");
            $(".browse-button-text" + ii).html('<i class="fa fa-folder-open"></i> ');
        });
    }
}


function remove_comboi(x) {
    // alert("ASDASd")
    // alert(x.value)
    // var button_id = $(this).attr("id");
    $('#yuhui' + x + '').remove();
}

// function htmlTableToExcel(type) {
//     var data = document.getElementById('tabexport');
//     var excelFile = XLSX.utils.table_to_book(data, {
//         sheet: "sheet1",
//         cols: wscols // Gunakan objek wscols untuk menentukan lebar kolom
//     });

//     XLSX.write(excelFile, {
//         bookType: type,
//         bookSST: true,
//         type: 'base64'
//     });

//     XLSX.writeFile(excelFile, 'Pejanjian Konsesi.' + type);
// }
// function htmlTableToExcel(type) {
//     // Ganti 'tabexport' dengan ID tabel HTML Anda
//     var data = document.getElementById('tabexport');

//     // Membuat objek workbook
//     var wb = XLSX.utils.table_to_book(data, {
//         sheet: "sheet1"
//     });

//     // Mengkonversi workbook ke format yang sesuai dengan tampilan Excel
//     var excelData = XLSX.write(wb, {
//         bookType: type,
//         type: 'base64'
//     });

//     // Konversi data Excel ke blob
//     var blob = new Blob([s2ab(atob(excelData))], {
//         type: 'application/octet-stream'
//     });

//     // Membuat tautan unduhan
//     var url = window.URL.createObjectURL(blob);
//     var a = document.createElement('a');
//     a.href = url;
//     a.download = 'Perjanjian_Konsesi.' + type;

//     // Mengklik tautan unduhan secara otomatis
//     a.click();

//     // Menghapus objek URL setelah unduhan
//     window.URL.revokeObjectURL(url);
// }

// // Fungsi untuk mengonversi base64 ke array buffer
// function s2ab(s) {
//     var buf = new ArrayBuffer(s.length);
//     var view = new Uint8Array(buf);
//     for (var i = 0; i < s.length; i++) {
//         view[i] = s.charCodeAt(i) & 0xFF;
//     }
//     return buf;
// }

function encodeID(id) {
    return btoa(id).split("").reverse().join(""); // Base64 + Reverse String
}


// function tampil_data(table, colum = '1', id = '1') {
//     // id = encodeID(id);
//     var url = ''
//     var tadata = ''
//     urls = "<?php echo base_url(); ?>index.php/perjanjian_konsesijasa/tampildata/" + table + "/" + colum + "/id-" +
//         btoa(id);
//     $.ajax({
//         url: urls,
//         contentType: "application/json; charset=utf-8",
//         dataType: "json",
//         async: false,
//         success: function(data) {
//             tadata = data;
//         },
//         failure: function(errMsg) {
//             alert(errMsg);
//         }
//     });
//     return tadata;
// }
function tampil_data(table, colum = '1', id = '1') {
    var tadata = '';

    $.ajax({
        url: "<?php echo base_url(); ?>index.php/perjanjian_konsesijasa/tampildata/",
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        data: JSON.stringify({
            table: table,
            colum: colum,
            id: btoa(id) // Encode ID ke Base64 sebelum dikirim
        }),
        async: false,
        success: function(data) {
            console.log("data perjanjian_konsesi:", data)
            // ✅ Replace 'idx' pada pemberi_konsesi jika ada
            tadata = data.map(row => {
                if (row.pemberi_konsesi) {
                    row.pemberi_konsesi = atob(row.pemberi_konsesi);
                }
                return row;
            });
        },
        failure: function(errMsg) {
            alert(errMsg);
        }
    });

    return tadata;
}



// function combo(divname, table = '', colum = '1', id = '1', set = '') {

//     url3 = "<?php echo base_url(); ?>index.php/perjanjian_konsesijasa/tampildata/" + table + "/" + colum + "/" +
//         id +
//         "/combobox";
//     $('#' + divname).val('').selectpicker('refresh');

//     $.get(url3).done(function(data3) {
//         jdata3 = JSON.parse(data3);
//         //*
//         $('#' + divname).empty();
//         $('#' + divname).append(new Option("--Pilih--", ""));

//         if (divname == 'pem_konsesi') {
//             $.each(jdata3, function(i, el) {
//                 $('#' + divname).append(new Option("(" + el.kode + ") " + el.nama, el.kode));

//             });
//         } else {
//             $.each(jdata3, function(i, el) {
//                 $('#' + divname).append(new Option(el.nama, el.kode));

//             });
//         }

//         $('#' + divname).val(set).selectpicker('refresh');

//     }).fail(function() {
//         alert("error");
//     }).always(function() {
//         // alert("finished");
//     });

// }

function combo(divname, table = '', colum = '1', id = '1', set = '') {
    // URL tetap sama, tetapi kita akan menggunakan POST, bukan GET
    var url3 = "<?php echo base_url(); ?>index.php/perjanjian_konsesijasa/tampildata/";

    // Clear combobox
    $('#' + divname).val('').selectpicker('refresh');

    // Mengirim data menggunakan POST
    $.ajax({
        url: url3,
        type: 'POST',
        contentType: 'application/json; charset=utf-8',
        dataType: 'json',
        data: JSON.stringify({
            table: table,
            colum: colum,
            id: btoa(id), // Encode id ke Base64
            set: set,
            combobox: "combobox" // Memberikan informasi bahwa ini adalah permintaan untuk combobox
        }),
        success: function(data3) {
            $('#' + divname).empty();
            $('#' + divname).append(new Option("--Pilih--", ""));

            // Menambahkan opsi berdasarkan jenis divname
            if (divname == 'pem_konsesi') {
                $.each(data3, function(i, el) {
                    $('#' + divname).append(new Option("(" + el.kode + ") " + el.nama, el.kode));
                });
            } else {
                $.each(data3, function(i, el) {
                    $('#' + divname).append(new Option(el.nama, el.kode));
                });
            }

            // Set nilai combobox
            $('#' + divname).val(set).selectpicker('refresh');
        },
        failure: function(errMsg) {
            alert(errMsg);
        },
        error: function() {
            alert("Error loading data");
        }
    });
}



function hanyaAngka(a) {
    var inputValue = $(a).val();
    var newValue = inputValue.replace(/[^0-9,.]/g, ""); // Hanya biarkan angka, koma, dan titik

    // Hapus titik atau koma jika muncul lebih dari sekali
    newValue = newValue.replace(/(\.|,)[,.]+/g, "$1");

    $(a).val(newValue);
}

function formatRupiah(angka) {
    if (angka == '' || angka == null) {
        var formattedAngka = ''
    } else {
        var formattedAngka = "Rp " + angka.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }
    return formattedAngka;
}

function formatNumber(inputElement) {
    let inputValue = inputElement.value;

    // Menghapus semua karakter selain angka dan tanda minus ("-")
    let cleanedValue = inputValue.replace(/[^\d-]/g, '');

    // Membuat format angka dengan -Rp jika angka negatif, Rp jika positif atau nol
    if (cleanedValue.length > 0 && cleanedValue !== '-') {
        if (cleanedValue.charAt(0) === '-') {
            inputElement.value = "-" + formatRupiah(cleanedValue.substring(
                1)); // Hilangkan tanda minus sementara untuk pemformatan
        } else {
            inputElement.value = formatRupiah(cleanedValue);
        }
    } else {
        inputElement.value = cleanedValue; // Biarkan tanda minus atau input kosong
    }
}

function formatAngkaKeRupiah(angka) {
    if (angka == '' || angka == null) {
        return ''
    } else {
        var angkaString = angka.toString(); // Mengonversi angka menjadi string
        var tandaMinus = '';

        if (angkaString[0] === '-') {
            tandaMinus = '-';
            angkaString = angkaString.substring(1); // Hilangkan tanda minus
        }

        var angkaSplit = angkaString.split('.'); // Membagi angka menjadi bagian bulat dan desimal

        var bagianBulat = angkaSplit[0].replace(/\B(?=(\d{3})+(?!\d))/g, '.'); // Memisahkan ribuan dengan titik
        var hasil = tandaMinus + 'Rp ' + bagianBulat;

        if (angkaSplit.length > 1) {
            hasil += '.' + angkaSplit[1]; // Menggunakan titik sebagai pemisah desimal
        }

        return hasil;
    }
}


function hapus_data(id) {
    swal({
            title: "Anda yakin menghapus data ?",
            // text: tex,
            icon: "warning",
            buttons: true,
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
                var url = ''
                url =
                    "<?php echo base_url(); ?>index.php/perjanjian_konsesijasa/delete_data/perjanjian_pengusahaan/id_perjanjian_pengusahaan/" +
                    id;
                $.post(url, {
                    id: id
                }).done(function(data) {
                    if (data == 0) {
                        swal({
                            title: "",
                            text: "Berhasil Menghapus Data",
                            icon: "success",
                            showConfirmButton: false,
                            timer: 3000,
                            type: "success"
                        });
                        tabel.ajax.reload();
                        tabelToExport()
                    }
                })
            } else {
                swal("Anda Tidak Menjadi Menghapus Data");
            }
        });

}
</script>
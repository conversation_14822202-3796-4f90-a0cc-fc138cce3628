<style>
th {
    text-align: center;
}

.modal-dialog {
    width: 1000px !important;
}

.control-label {
    flex: none;
    width: 25%;
}

:not(.input-group)>.bootstrap-select.form-control:not([class*="col-"]) {
    width: 100%;
    border: 1px solid #80808033 !important;
    margin-top: 10px;
}

input[type="file"] {
    top: 0;
    right: 0;
    margin: 0;

    padding: 0;
    cursor: pointer;
    opacity: 0;
    filter: alpha(opacity=0);
    margin-top: -46px;
    width: 100% !important;
    height: 63px !important;
}

.input-group {
    margin-bottom: 0px;
}

#bang_l .fa {

    font-size: 10px;
}

.button-yuhu {
    width: 35px;
    height: 36px !important;
    margin-right: 1px;
    padding: 10px;
}

#kol {

    padding: 2px 0px 2px 0px;
    border-bottom: 1px solid #bbbbc62e;


}

#boxperjanjian {
    padding: 10px 0px 10px 8px;
    border: 1px dashed #eee;
    margin-top: 20px;
    border-radius: 10px
}

.buttons-html5::before {
    content: "\f1c3";
    /* Icon kode font-awesome, dapat disesuaikan */
    font-family: FontAwesome;
    /* Font-awesome font family */
    margin-right: 5px;
    /* Spasi antara ikon dan teks */
}

/* Berikan warna latar belakang */
.buttons-html5 {
    background-color: #4CAF50;
    /* Warna latar belakang */
    color: white;
    /* Warna teks */
    border: none;
    /* Hilangkan border */
    padding: 10px 20px;
    /* Ukuran tombol */
    cursor: pointer;
}

/* Efek hover */
.buttons-html5:hover {
    background-color: #45a049;
    /* Warna latar belakang hover */
}

/* Stil untuk tombol "Export Excel" dalam DataTables */
.dataTables_wrapper .buttons-excel {
    margin-bottom: 10px;
    height: 39px;
    border-radius: 5px;
    margin-right: 8px;
    font-size: 20px;
    padding: 5px 10px 5px 10px;
    margin-bottom: 10px;
    /* Spasi bawah tombol */
}

/* 
.dataTables_scrollHeadInner table {
    border-bottom: none;
} */

#styleSelector .form-control {

    background-color: #fff;
    border: 1px solid #80808042;
}

.tombolFilter {
    text-align: right;

}

.buttons-excel {
    background-color: #449f31d6 !important;
}

tfoot th {
    text-align: right;
}

/* .table-bordered> :not(caption)>*>* {
    border-width: 1px;
    border-left-width: 1px;
} */
</style>
<textarea id="simpandatadetailperjanjian" style="display:none;"></textarea>
<textarea id="simpandatadetailtarif" style="display:none;"></textarea>
<textarea id="simpandatadetailslo" style="display:none;"></textarea>




<div class="content">

    <div class="page-inner mt--5">
        <div class="row mt--2">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="card-header">
                            <h5 style="font-weight:100;">
                                Laporan > <a href="#"> Rekapitulasi Nilai Aset Per Jenis Pengusahaan</a>
                            </h5>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Tahun
                                    </label>

                                    <input type="text" name="tahun_l" class="form-control" placeholder=" " id="tahun_l"
                                        required readonly />
                                </div>
                                <!-- <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        * Periode s/d
                                    </label>
                                    <select class="form-control" name="kat_periode_l" id="kat_periode_l"
                                        data-live-search="true" onchange="to_filter_l(this,1)">
                                    </select>
                                </div> -->
                                <div class="form-group yuh" id="peri_l">
                                    <label class="control-label col-xs-12" for="">
                                        * Periode
                                    </label>
                                    <select class="form-control" name="periode_l" id="periode_l"
                                        data-live-search="true">
                                    </select>
                                </div>


                            </div>
                            <div class="col-md-6">
                                <div class="form-group yuh show_kemen" id="form" style="display:none;">
                                    <label class="control-label col-xs-12" for="">
                                        Level Laporan
                                    </label>
                                    <select class="form-control" name="level_l" id="level_l" data-live-search="true"
                                        onchange="to_filter_l(this,2)">
                                    </select>
                                </div>
                                <div class="form-group yuh show_eselon" id="esel_l" style="display:none;">
                                    <label class="control-label col-xs-12" for="">
                                        Eselon
                                    </label>
                                    <select class="form-control" name="eselon_l" id="eselon_l" data-live-search="true">
                                    </select>
                                </div>
                                <div class="form-group yuh show_satker" id="sat_l" style="display:none;">
                                    <label class="control-label col-xs-12" for="">
                                        Satker
                                    </label>
                                    <select class="form-control" name="satker_l" id="satker_l" data-live-search="true">
                                    </select>
                                </div>
                                <!-- <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        * Jenis Laporan
                                    </label>
                                    <select class="form-control" name="jenis_l" id="jenis_l"
                                        data-live-search="true"></select>
                                </div> -->

                            </div>
                            <div class="col-md-6">
                                <div class="form-group" id="daf">
                                    <div class="col-xs-12">
                                        <button class="btn btn-tambah" id="op" onclick="lihat_data()">
                                            <i class="fa fa-eye"></i> Lihat Data
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="dt-responsive table-responsive">

                                <table class="display table table-bordered table-striped table-hover tab1"
                                    id="table-users" style="display:none;">
                                    <thead>
                                        <tr style="text-align:center;">
                                            <th id="th" class="INFO" rowspan="3">No</th>
                                            <th id="th" class="INFO" rowspan="3">Unit Organisasi</th>
                                            <th id="th" class="INFO" colspan="5">SALDO PER
                                                (BULANAN/TRIWULAN/SEMESTER/TAHUNAN)</th>
                                        </tr>
                                        <tr>
                                            <th id="th" class="INFO" colspan="2">Nilai Aset</th>
                                            <th id="th" class="INFO" rowspan="2">Total Aset Konsesi Jasa</th>
                                            <th id="th" class="INFO" colspan="2"> Kewajiban</th>
                                        </tr>
                                        <tr>
                                            <th id="th" class="INFO" style="background:#E74C3C !important;">Mitra</th>
                                            <th id="th" class="INFO" style="background:#ae46bc  !important;">Pemerintah
                                            </th>
                                            <th id="th" class="INFO" style="background:#4ECDC4 !important;">User Pays
                                            </th>
                                            <th id="th" class="INFO" style="background:#F39C12 !important;">Govt. Pays
                                            </th>
                                        </tr>
                                    </thead>
                                    <tfoot>
                                        <tr>
                                            <th></th>
                                            <th>Total:</th>

                                            <th style="background:#E74C3C !important;"></th>
                                            <th style="background:#ae46bc  !important;">
                                            </th>
                                            <th></th>
                                            <th style="background:#4ECDC4 !important;">
                                            </th>
                                            <th style="background:#F39C12 !important;">
                                            </th>

                                        </tr>
                                    </tfoot>
                                    <tbody>
                                    </tbody>
                                </table>


                                <div id="tes" style="display:none;">
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="styleSelector" class="" id="formfilter">
            <div style="display:none;" class="selector-toggle">
                <div id="yuhuu" class="close" onclick="close_filter(this)"></div>
            </div>



            <h6 style="color: white; background: #13208d8c; padding: 10px; border-radius: 10px; text-align: center;">
                Filtering Data</h6>

            <div class="row">
                <div class="col-md-12">
                    <!-- <div class="" id="form">
                        <label class="col-xs-12" for="">
                            Nama Perjanjian Konsesi Jasa
                        </label>
                        <div class="col-xs-12">
                            <select type="text" name="nama_p_filter" class="form-control" placeholder=" "
                                id="nama_p_filter" data-live-search="true" onchange="to_filter(this)"></select>
                        </div>
                    </div> -->
                    <div class="" id="form">
                        <label class="col-xs-12" for="">
                            Badan Usaha
                        </label>
                        <div class="col-xs-12">

                            <select class="form-control" name="bdn_usaha_filter" id="bdn_usaha_filter"
                                data-live-search="true" onchange="to_filter(this)"></select>
                        </div>
                        <!-- <input type="text" name="nama" class="form-control" placeholder=" " id="nama" required autofocus /> -->
                    </div>
                    <!-- <div class="show_eselon" id="form " style="display:none;">
                        <label class="col-xs-12" for="">
                            Eselon I
                        </label>
                        <div class="col-xs-12">

                            <select class="form-control" name="eselon_filter" id="eselon_filter" data-live-search="true"
                                onchange="to_filter(this)"></select>
                        </div>
                    </div>
                    <div class="" id="form">
                        <label class="col-xs-12" for="">
                            Pemberi Konsesi
                        </label>
                        <div class="col-xs-12">

                            <select class="form-control" name="pem_konsesi_filter" id="pem_konsesi_filter"
                                data-live-search="true" onchange="to_filter(this)"></select>
                        </div>
                    </div> -->
                    <div class="" id="form">
                        <label class="col-xs-12" for="">
                            Jenis Aset Konsesi Jasa
                        </label>
                        <div class="col-xs-12">
                            <select class="form-control" name="jenis_p_filter" id="jenis_p_filter"
                                data-live-search="true" onchange="to_filter(this)"></select>
                        </div>
                    </div>
                    <div class="" id="form">
                        <label class="col-xs-12" for="">
                            Ruang Lingkup
                        </label>
                        <div class="col-xs-12">
                            <select class="form-control" name="ruang_filter" id="ruang_filter" data-live-search="true"
                                onchange="to_filter(this)"></select>
                        </div>
                    </div>
                    <div class="" id="form">
                        <label class="col-xs-12" for="">
                            Pulau
                        </label>
                        <div class="col-xs-12">
                            <select class="form-control" name="pulau_filter" id="pulau_filter" data-live-search="true"
                                onchange="to_filter(this)"></select>
                        </div>
                    </div>

                    <div class="form-group" id="daf">
                        <div class="col-xs-12">
                            <button class="btn btn-tambah" id="op" onclick="reset()" style="background: #01a9ac;">
                                <i class="fa fa-refresh"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
            </div>



        </div>
        <?php echo $jv_script; ?>
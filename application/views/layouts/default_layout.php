<!DOCTYPE html>
<html lang="en">

<head>
    <title>Sistem Informasi Dokumen Sumber Pencatatan Aset Konsesi Jasa - Partisipasi Mitra</title>
    <!--meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' unsafe-inline; style-src 'self' unsafe-inline"-->
    <link rel="icon" type="image/png" href="<?php echo base_url(); ?>assets/img/logopu.png" />
    <!-- HTML5 Shim and Respond.js IE10 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 10]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
      <![endif]-->
    <!-- Meta -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="#">
    <meta name="keywords"
        content="Admin , Responsive, Landing, Bootstrap, App, Template, Mobile, iOS, Android, apple, creative app">
    <meta name="author" content="#">
    <script type="text/javascript">
    <?php
        require_once (FCPATH . "env.php");
        echo 'var WGI_APP_BASE_URL = "' . WGI_APP_BASE_URL . '"; ';
        echo 'var WGI_NODE_API_URL = "' . WGI_NODE_API_URL . '"; ';

        // $group = $this->session->users['id_user_group_real'];
        $username = $this->session->userdata('username');
        // $nm_satker = $this->session->users['nm_satker'];
        // $logo_bujt =  base_url() . $this->session->users['logo'];
        // $logo_bpjt = base_url().'assets/themes/adminity/images/avatar-4.jpg';
        // 	if($group == 1){
        // 		$logo = $logo_bpjt;
        // 		$login_name = $username;
        // 		$css_logo = "width:30px; height:30px;";
        // 	}else{
        // 		$logo = $logo_bujt;
        // 		$login_name = $username . ' ('.$nm_bujt.') ';
        // 		$css_logo = "width:auto; height:30px;";
        // 	}
        
        ?>
    </script>

    <link rel="stylesheet" type="text/css"
        href="<?php echo base_url(); ?>assets/themes/adminity/bower_components/bootstrap/dist/css/bootstrap.min.css">
    <!-- themify-icons line icon -->
    <link rel="stylesheet" type="text/css"
        href="<?php echo base_url(); ?>assets/themes/adminity/icon/themify-icons/themify-icons.css">
    <!-- ico font -->
    <link rel="stylesheet" type="text/css"
        href="<?php echo base_url(); ?>assets/themes/adminity/icon/icofont/css/icofont.css">
    <!-- feather Awesome -->
    <link rel="stylesheet" type="text/css"
        href="<?php echo base_url(); ?>assets/themes/adminity/icon/feather/css/feather.css">

    <!-- sweet alert framework -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/themes/adminity/css/sweetalert.css">

    <!-- animation nifty modal window effects css -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/themes/adminity/css/component.css">

    <!-- Select 2 css -->
    <!-- <link rel="stylesheet" href="<?php echo base_url(); ?>assets/themes/adminity/bower_components/select2/dist/css/select2.min.css" /> -->
    <!-- Multi Select css -->
    <!-- <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/themes/adminity/bower_components/bootstrap-multiselect/dist/css/bootstrap-multiselect.css" />
    <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/themes/adminity/bower_components/multiselect/css/multi-select.css" /> -->

    <!-- Data Table Css -->
    <link rel="stylesheet" type="text/css"
        href="<?php echo base_url(); ?>assets/themes/adminity/bower_components/datatables.net-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css"
        href="<?php echo base_url(); ?>assets/themes/adminity/pages/data-table/css/buttons.dataTables.min.css">
    <link rel="stylesheet" type="text/css"
        href="<?php echo base_url(); ?>assets/themes/adminity/bower_components/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css">

    <!-- Style.css -->
    <!-- Date-Dropper css -->
    <!-- <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/themes/adminity/bower_components/datedropper/datedropper.min.css" /> -->
    <link rel="stylesheet"
        href="<?php echo base_url(); ?>assets/js/plugins/bootstrap-datepicker/bootstrap-datepicker3.min.css">
    <link rel="stylesheet"
        href="<?php echo base_url(); ?>assets/js/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.css">

    <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/themes/adminity/css/style.css">
    <!-- <link rel="stylesheet" type="text/css"
        href="<?php echo base_url(); ?>assets/themes/adminity/css/jquery.mCustomScrollbar.css"> -->
    <!-- <link rel="stylesheet" href="<?php echo base_url(); ?>assets/jstree/jstree.css"> -->

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jstree/3.2.1/themes/default/style.min.css" />
    <!-- Select 2 css -->
    <link rel="stylesheet"
        href="<?php echo base_url(); ?>assets/themes/adminity/bower_components/select2/dist/css/select2.min.css" />
    <!-- Multi Select css -->
    <!-- <link rel="stylesheet" type="text/css" href="<?php //echo base_url();                                                   ?>assets/themes/adminity/bower_components/bootstrap-multiselect/dist/css/bootstrap-multiselect.css" />
    <link rel="stylesheet" type="text/css" href="<?php //echo base_url();                                                   ?>assets/themes/adminity/bower_components/multiselect/css/multi-select.css" /> -->



    <link rel="stylesheet" href="<?php echo base_url(); ?>assets/css/bootselect/bootstrap-select.css">
    <link rel="stylesheet" href="<?php echo base_url(); ?>assets/font/fonts-google.css">

    <!-- <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" /> -->

    <!-- <link rel="stylesheet" href="<?php //echo base_url();                                                    ?>assets/js/bootselect-multiple/css/bootstrap-multiselect.css"> -->
    <!-- <link rel="stylesheet" id="css-main" href="<?php //echo base_url();                                                    ?>assets/css/bootselect/bootstrap-multiselect.css"> -->
    <!-- <link rel="stylesheet" href="<?php //echo base_url();                                                    ?>assets/css/bootselect/ajax-bootstrap-select.css"> -->

    <!-- <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;1,300&display=swap" rel="stylesheet"> -->



    <style>
    .bootstrap-select>.dropdown-toggle {
        font-weight: 500 !important;
        padding: 6px !important;
        font-size: 13px !important;
    }


    .bt-menu-control-sticky {
        z-index: 999999;
    }

    #mobile-collapse {
        top: calc(75% - 20px) !important;
    }

    body {
        font-family: 'Poppins' !important;
    }

    table td> :not(caption)>*>* {
        padding: 1rem .5rem !important;
        padding-top: 1.5rem;
        padding-right: 0.5rem;
        background-color: var(--bs-table-bg);
        border-bottom-width: 1px;
        box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
    }

    .table-bordered> :not(caption)>* {
        border-width: 0px !important;
    }

    table th {
        font-family: 'Poppins';
        font-size: 13px;
        color: #fff;

        font-weight: unset !important;

        background: #13208d8c !important;
    }

    table.table-bordered.dataTable th,
    table.table-bordered.dataTable td {
        /* border-left-width: 0; */
        border-bottom: 1px solid white;
    }

    table td {
        font-family: 'Poppins';
        font-size: 12px;
        color: #666;
        line-height: 1.2;
        font-weight: unset !important;
        padding-top: 5px;
        padding-bottom: 5px;
        border-bottom: 1px solid #f2f2f2;
    }

    .dataTables_wrapper .sorting::before,
    .dataTables_wrapper .sorting_desc::before,
    .dataTables_wrapper .sorting_asc::before {
        content: "\f062";
        /* Panah ke atas (ascending) */
        font-family: FontAwesome;
        /* Menggunakan font ikon (misalnya, FontAwesome) */
        font-size: 8px;
        /* Ukuran font yang lebih kecil */
    }

    .page-item.active .page-link {
        background-color: #13208d8c;
        border-color: #13208d8c;
    }

    /* CSS untuk tombol "btn btn-primary" */
    .btn.btn-tambah {
        background-color: #13208d8c;
        font-size: 13px;
        /* Warna latar belakang tombol */
        color: #fff;
        /* Warna teks pada tombol */
        border: none;
        /* Menghilangkan border */
        border-radius: 4px;
        /* Membuat sudut tombol sedikit melengkung */
        padding: 10px 20px;
        /* Padding untuk menjaga teks tetap legible */
        cursor: pointer;
        /* Menampilkan kursor tangan saat dihover */
        transition: background-color 0.3s ease;
        /* Animasi perubahan warna latar belakang saat dihover */
    }

    table .btn {
        padding: 5px;
        font-size: 10px;
        border-radius: 10px;
        margin: auto;
        text-align: center;
        padding-left: 9px;
    }

    #modal-tambah-mtl {
        background: rgba(0, 0, 0, 0.5);
        /* Latar belakang semi-transparan saat modal aktif */
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .modal-dialog {
        width: 600px !important;
        /* Lebar modal */
        background: #fff;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    }

    @media (min-width: 576px) {
        .modal-dialog {
            max-width: 1000px !important;
            margin: 1.75rem auto;
        }
    }

    .modal-header {
        background: #13208d8c;
        /* Warna latar belakang header */
        color: #fff;
        /* Warna teks header */
        padding: 15px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
    }

    .modal-title {
        margin: 0;
        font-size: 15px;
    }

    .modal-body {
        padding: 20px;
    }

    /* CSS untuk mempercantik form */
    .form-group {
        margin-bottom: 20px;
    }

    .control-label {
        font-weight: 500;
        font-size: 13px;
        flex: 1;

    }

    .form-control {
        border: 1px solid #ccc;
        border-radius: 5px;
        padding: 7px;
        width: 100%;
        font-size: 12px !important;
        transition: border-color 0.2s;
        flex: 2;
    }

    .form-control:focus {
        border-color: #007bff;
        /* Warna border saat input di-fokuskan */
    }

    .btn-primary {
        background: #007bff;
        color: #fff;
        border: none;
        border-radius: 5px;
        padding: 10px 20px;
        cursor: pointer;
    }

    .btn-primary:hover {
        background: #0056b3;
        /* Warna latar belakang saat tombol di-hover */
    }

    /* CSS untuk label dan kolom input di samping */
    .col-xs-2 {
        display: flex;
        align-items: center;
        font-weight: 500;
        padding-right: 10px;
    }

    .col-xs-9 {
        flex: 1;
    }

    /* CSS untuk label dan form inputan */
    .form-group {
        display: flex;
        flex-direction: row;
        /* Mengatur elemen label dan input dalam satu baris */
        margin-bottom: 20px;
        align-items: center;
        /* Menyamakan tinggi elemen label dan input */
    }




    .col-xs-2 {
        display: flex;
        align-items: center;
        font-weight: 500;
        padding-right: 10px;
        flex: 1;
        /* Label dan input akan memakan setengah dari lebar kolom */
    }

    .yuh {
        border-bottom: 1px solid #6762620d;
    }

    .header-navbar .navbar-wrapper .navbar-container .nav-left li>a,
    .header-navbar .navbar-wrapper .navbar-container .nav-right li>a {
        font-size: 13px !important;
    }

    .dropdown-item.active,
    .dropdown-item:active {
        background-color: #13208d7d;
        color: white;
    }

    .btn-light {
        background-color: #fff !important;
    }

    /* Dynamic Content Loading Styles */
    .content-loading {
        opacity: 0.7;
        pointer-events: none;
    }

    .content-loading .spinner-border {
        width: 3rem;
        height: 3rem;
    }

    .ajax-link {
        cursor: pointer;
    }

    .ajax-link:hover {
        text-decoration: none;
    }

    /* Loading overlay */
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .open .selector-toggle>#yuhuu::before {
        content: "\e8f6";
        -webkit-animation: mymove 0s infinite linear;
        animation: mymove 0s infinite linear;
    }

    .selector-toggle>#yuhuu::before {
        font-family: 'feather';
        font-size: 20px;
        content: "\e8c3";
        position: relative;
        float: left;
        left: 13px;
        line-height: 35px;
        transition: 0.5s;
        -webkit-transition: 0.5s;
        -ms-transition: 0.5s;
        -moz-transition: 0.5s;
        -o-transition: 0.5s;
        -webkit-animation: mymove 1.3s infinite linear;
        animation: mymove 1.3s infinite linear;
    }

    element {}

    .pcoded[theme-layout="horizontal"] .selector-toggle>#yuhuu,
    .pcoded[vertical-placement="left"] .selector-toggle>#yuhuu {
        left: -75px;
        border-right: 0;
        border-radius: 50% 0 0 50%;
    }

    .pcoded .selector-toggle>#yuhuu {
        position: absolute;
        top: 200px;
        width: 55px;
        height: 55px;
        display: block;
        cursor: pointer;
        text-align: center;
        background: #01a9ac;
        color: #fff;
        -webkit-box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.2);
        box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.2);
        padding: 10px 8px;
    }

    .preloader3 {
        background-color: transparent;
    }

    .preloader3>div {
        background-color: #13208d8c;
    }

    .dataTables_scrollHead {
        position: sticky;
        top: 0;
        z-index: 100;
        background-color: #fff;
        /* Warna latar belakang sesuai kebutuhan */
    }

    .dataTables_scrollBody {
        max-height: 700px;
        /* Tinggi maksimal untuk scrollbar, sesuaikan sesuai kebutuhan */
        overflow-y: auto;
    }
    </style>

    <!-- Peta Style -->
    <link rel="stylesheet" href="<?php echo base_url(); ?>assets/fonts/font-awesome-4.7.0/css/font-awesome.min.css">

    <script src="<?php echo base_url(); ?>assets/vendor/jquery/jquery.min.js"></script>
    <!-- <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.3.1/jspdf.umd.min.js"></script>
    <script type="text/javascript" src="//cdn.rawgit.com/niklasvh/html2canvas/0.5.0-alpha2/dist/html2canvas.min.js">
    </script> -->
    <!--script type="text/javascript" src="<?php //echo base_url() . 'assets/semantic/js/jquery-2.1.4.min.js';                                                    ?>"></script-->
    <script type="text/javascript"
        src="<?php echo base_url() . 'assets/semantic/plugins/nicescrool/jquery.nicescroll.min.js'; ?>"></script>
    <!-- <script type="text/javascript"
        src="<?php echo base_url() . 'assets/semantic/js/chartjs/dist/Chart.bundle.min.js'; ?>"></script> 2/10/2025-->
    <!-- <script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script> -->
    <!-- <script type="text/javascript" src="<?php echo base_url() . 'assets/chart/highcharts.js'; ?>"></script>
    <script type="text/javascript" src="<?php echo base_url() . 'assets/chart/ex/exporting.js'; ?>"></script>
    <script type="text/javascript" src="<?php echo base_url() . 'assets/chart/ex/export-data.js'; ?>"></script> -->
    <!-- <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/jquery/dist/jquery.min.js"></script> -->
    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/jquery-ui/jquery-ui.min.js"></script>
    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/popper.js/dist/umd/popper.min.js">
    </script>
    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/bootstrap/dist/js/bootstrap.min.js">
    </script>
    <script type="text/javascript" src="<?php echo base_url(); ?>assets/js/bootstrap.bundle.js"></script>


    <!-- jquery slimscroll js -->
    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/jquery-slimscroll/jquery.slimscroll.js">
    </script>
    <!-- modernizr js -->
    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/modernizr/modernizr.js"></script>




    <!-- modalEffects js nifty modal window effects -->
    <!-- <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/js/modalEffects.js"></script> -->
    <!-- <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/js/classie.js"></script>	 -->
    <!-- Date-dropper js -->
    <!-- <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/datedropper/datedropper.min.js"></script> -->
    <script src="<?php echo base_url(); ?>assets/js/plugins/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>

    <!-- Chart js -->
    <!-- <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/chart.js/dist/Chart.js"></script> 10/2/2025-->
    <!-- amchart js -->
    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/pages/widget/amchart/amcharts.js"></script>
    <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/pages/widget/amchart/serial.js">
    </script>
    <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/pages/widget/amchart/light.js">
    </script>
    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/js/jquery.mCustomScrollbar.concat.min.js"></script>
    <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/js/SmoothScroll.js"></script>
    <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/js/pcoded.min.js"></script>
    <!-- data-table js -->
    <script
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/datatables.net/js/jquery.dataTables.min.js">
    </script>
    <script
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/datatables.net-buttons/js/dataTables.buttons.min.js">
    </script>
    <script src="<?php echo base_url(); ?>assets/themes/adminity/pages/data-table/js/jszip.min.js"></script>
    <script src="<?php echo base_url(); ?>assets/themes/adminity/pages/data-table/js/pdfmake.min.js"></script>
    <script src="<?php echo base_url(); ?>assets/themes/adminity/pages/data-table/js/vfs_fonts.js"></script>
    <script
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/datatables.net-buttons/js/buttons.print.min.js">
    </script>
    <script
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/datatables.net-buttons/js/buttons.html5.min.js">
    </script>
    <script src="<?php echo base_url(); ?>assets/themes/adminity/pages/data-table/js/dataTables.bootstrap4.min.js">
    </script>
    <script
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/datatables.net-responsive/js/dataTables.responsive.min.js">
    </script>
    <script
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js">
    </script>




    <!-- i18next.min.js -->
    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/i18next/i18next.min.js"></script>
    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/i18next-xhr-backend/i18nextXHRBackend.min.js">
    </script>
    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/i18next-browser-languagedetector/i18nextBrowserLanguageDetector.min.js">
    </script>
    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/jquery-i18next/jquery-i18next.min.js">
    </script>
    <!-- custom js -->
    <!-- <script src="<?php echo base_url(); ?>assets/themes/adminity/pages/data-table/js/data-table-custom.js"></script> -->
    <script src="<?php echo base_url(); ?>assets/themes/adminity/js/vartical-layout.min.js"></script>
    <!-- <script type="text/javascript" src="<? // echo base_url();                                                   ?>assets/themes/adminity/pages/dashboard/custom-dashboard.js"></script> -->
    <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/js/script.min.js"></script>

    <!-- sweet alert modal.js intialize js -->
    <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/js/sweetalert.js"></script>
    <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/js/modal.js"></script>


    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/themes/adminity/bower_components/select2/dist/js/select2.full.min.js">
    </script>

    <!-- modalEffects js nifty modal window effects -->
    <script type="text/javascript" src="<?php echo base_url(); ?>assets/themes/adminity/js/classie.js"></script>
    <!-- <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/wayjs/way.js"></script> -->
    <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/webgis/js/lookuptool.js">
    </script>
    <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/webgis/js/validasi.js"></script>
    <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/webgis/js/formater.js"></script>

    <script type="text/javascript" src="<?php echo base_url(); ?>assets/js/jstree.min.js"></script>

    <script type="text/javascript"
        src="<?php echo base_url(); ?>assets/js/plugins/jquery-validation/jquery.validate.min.js"></script>


    <!-- Peta Script -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
        integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous">
    </script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.0/js/bootstrap.min.js"
        integrity="sha384-3qaqj0lc6sV/qpzrc1N5DC6i1VRn/HyX4qdPaiEFbn54VjQBEU341pvjz7Dv3n6P" crossorigin="anonymous">
    </script>


    <script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.0.11/handlebars.min.js"></script> -->

    <!-- Plugins. Comment out any unused libaries to improve the page load times -->
    <!-- <script src="<?php echo base_url(); ?>assets/bootleaf-master/src/plugins/bing/leaflet-bing-layer.min.js"></script>
    <script src="<?php echo base_url(); ?>assets/bootleaf-master/src/plugins/geocoders/Control.Geocoder.js"></script>
    <script src="<?php echo base_url(); ?>assets/bootleaf-master/src/plugins/geocoders/Control.Geocoder.harmony.js">
    </script>
    <script src="<?php echo base_url(); ?>assets/bootleaf-master/src/plugins/leaflet-history/leaflet-history.js">
    </script>
    <script
        src="<?php echo base_url(); ?>assets/bootleaf-master/src/plugins/leaflet-groupedlayercontrol/leaflet.groupedlayercontrol.js">
        </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.markercluster/1.1.0/leaflet.markercluster.js"></script> -->

    <!-- <script src="<?php echo base_url(); ?>assets/bootleaf-master/src/plugins/proj4js/proj4leaflet.js"></script> -->

    <!-- <script
        src="https://cdn.jsdelivr.net/gh/ashl1/datatables-rowsgroup@fbd569b8768155c7a9a62568e66a64115887d7d0/dataTables.rowsGroup.js">
    </script>
    <script src="https://cdn.datatables.net/plug-ins/1.13.6/api/fnFakeRowspan.js">
    </script> -->

    <script src="<?php echo base_url(); ?>assets/js/wgi_ajaxcache.js"></script>




    <!-- Select 2 js -->

    <!-- Multiselect js -->
    <!-- <script type="text/javascript" src="<?php //echo base_url();                                                   ?>assets/themes/adminity/bower_components/bootstrap-multiselect/dist/js/bootstrap-multiselect.js"></script>
<script type="text/javascript" src="<?php //echo base_url();                                                   ?>assets/themes/adminity/bower_components/multiselect/js/jquery.multi-select.js"></script>
<script type="text/javascript" src="<?php //echo base_url();                                                   ?>assets/themes/adminity/js/jquery.quicksearch.js"></script> -->

    <script type="text/javascript" src="<?php echo base_url(); ?>assets/js/bootselect/bootstrap-select.js"></script>

    <!-- <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script> -->
    <!-- <script type="text/javascript" src="<?php //echo base_url();                                                    ?>assets/js/bootselect-multiple/js/bootstrap-multiselect.js"></script> -->
    <!-- <script type="text/javascript" src="<?php //echo base_url();                                                    ?>assets/js/bootselect/bootstrap-multiselect.js"></script> -->
    <!-- <script type="text/javascript" src="<?php //echo base_url();                                                    ?>assets/js/bootselect/ajax-bootstrap-select.js"></script> -->

    <script type="text/javascript" src="<?php echo base_url(); ?>assets/xlsx/FileSaver.js"></script>
    <script type="text/javascript" src="<?php echo base_url(); ?>assets/xlsx/xlsx2.js"></script>
    <script type="text/javascript" src="<?php echo base_url(); ?>assets/xlsx/xlsx.js"></script>
</head>

<body>
    <!-- Pre-loader start -->
    <div class="theme-loader">
        <div class="ball-scale">
            <div class='contain'>
                <div class="ring">
                    <div class="frame"></div>
                </div>
                <div class="ring">
                    <div class="frame"></div>
                </div>
                <div class="ring">
                    <div class="frame"></div>
                </div>
                <div class="ring">
                    <div class="frame"></div>
                </div>
                <div class="ring">
                    <div class="frame"></div>
                </div>
                <div class="ring">
                    <div class="frame"></div>
                </div>
                <div class="ring">
                    <div class="frame"></div>
                </div>
                <div class="ring">
                    <div class="frame"></div>
                </div>
                <div class="ring">
                    <div class="frame"></div>
                </div>
                <div class="ring">
                    <div class="frame"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- Pre-loader end -->
    <div id="pcoded" class="pcoded">
        <div class="pcoded-overlay-box"></div>
        <div class="pcoded-container navbar-wrapper">

            <nav class="navbar header-navbar pcoded-header">
                <div class="navbar-wrapper">

                    <div class="navbar-logo">
                        <a class="mobile-menu" id="mobile-collapse" href="<?php echo base_url(); ?>#">
                            <i class="feather icon-menu" style="color:transparent;"></i>
                        </a>
                        <!-- <a href="<?php echo base_url(); ?>#">
                            <img class="img-fluid" src="<?php echo base_url(); ?>assets/themes/adminity/images/logo.png" alt="Theme-Logo" />
                        </a> -->
                        <a class="mobile-options">
                            <i class="feather icon-more-horizontal"></i>
                        </a>
                    </div>

                    <div class="navbar-container">
                        <ul class="nav-left">


                        </ul>
                        <ul class="nav-right">
                            <li style="font-size:10px;margin-top:5px;">
                                <div class="" id="form" style="width:200px;">
                                    <select class="form-control" name="tahun_layout" id="tahun_layout"
                                        style="width:100%;" data-live-search="true" onchange="to_link()">

                                    </select>
                                </div>
                            </li>
                            <li class="user-profile header-notification">
                                <div class="dropdown-primary dropdown">
                                    <div class="dropdown-toggle" data-bs-toggle="dropdown">
                                        <img src="<?php echo base_url(); ?>assets/themes/adminity/images/avatar-4.jpg"
                                            class="img-radius" alt="User-Profile-Image">
                                        <span>

                                            <?php
                                            $group = $this->session->userdata('id_user_group');
                                            $sgroup = $this->session->userdata('id_user_group');
                                            $username = $this->session->userdata('username');
                                            $fullname = $this->session->userdata('nama');
                                            $text_nm = ($group == '1') ? $username : $fullname;
                                            echo $text_nm;
                                            ?>
                                        </span>
                                        <i class="feather icon-chevron-down"></i>
                                    </div>
                                    <ul class="show-notification profile-notification dropdown-menu"
                                        data-dropdown-in="fadeIn" data-dropdown-out="fadeOut">

                                        <li>
                                            <a href="<?php echo base_url(); ?>login/logout">
                                                <i class="feather icon-log-out"></i> Logout
                                            </a>
                                        </li>
                                    </ul>

                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <div class="pcoded-main-container">
                <div class="pcoded-wrapper">
                    <nav class="pcoded-navbar">
                        <div class="pcoded-inner-navbar main-menu" style="background:#5e649b !important;">
                            <!-- <div class="pcoded-navigatio-lavel">Monitoring PPJT</div> -->
                            <ul class="pcoded-item pcoded-left-item" style="margin-top:20px;">
                                <?php
                                $url = $this->uri->uri_string();
                                $ci = &get_instance();
                                $ci->load->database();
                                $thang = 2023;


                                $this->db->order_by('urutan', 'asc');
                                $this->db->where("id_user_group", $group);
                                $this->db->where("tahun", $thang);

                                $armenu = $ci->db->get('v_group_module')->result_array();

                                foreach ($armenu as $menu) {
                                    $this->db->order_by('urutan', 'asc');
                                    $submenu = $this->db->get_where('v_group_module', array('id_user_group' => $menu['id_user_group'], 'parent' => $menu['kode_module'], 'tahun' => $thang))->result_array();

                                    $col = ($url == $menu['url']) ? "#6f88886e" : "";

                                    $showsta = 'block';
                                    $ispar = ($menu['url'] === '#' && !empty($menu['parent'])) ? 'style="display:none;"' : 'style="display:block;"';

                                    if (count($submenu) == 0) {
                                        $menuDisplayStyle = ($menu['id_user_group'] == $group && $menu['parent'] == '') ? 'block' : 'none';
                                        ?>
                                <li style='background:<?= $col ?>;display:<?= $menuDisplayStyle ?>' class="">
                                    <a href="<?= base_url() . $menu['url'] ?>">
                                        <span class="pcoded-micon"><i class="<?= $menu['icon'] ?>"></i></span>
                                        <span class="pcoded-mtext">
                                            <?= $menu['nama_module'] ?>
                                        </span>
                                    </a>
                                </li>
                                <?php
                                    } else {
                                        $parents = $this->db->get_where('v_group_module', array('id_user_group' => $group, 'url' => $url, 'tahun' => $thang));

                                        if ($parents->num_rows() > 0) {
                                            if ($parents->row()->parent !== NULL && $parents->row()->parent !== '') {
                                                $parents1 = $this->db->get_where('v_group_module', array('kode_module' => $parents->row()->parent, 'id_user_group' => $group, 'tahun' => $thang))->row();
                                            } else {
                                                $parents1 = $this->db->get_where('v_group_module', array('id_user_group' => $group, 'url' => $url, 'tahun' => $thang))->row();
                                            }

                                            $op = ($parents1->parent == $menu['kode_module']) ? 'open' : '';
                                            $col = ($op === 'open') ? "#6f88886e" : "";
                                        } else {
                                            $op = '';
                                            $col = "";
                                        }
                                        $onclick = '';
                                        ?>
                                <li <?= $ispar ?> class="pcoded-hasmenu">
                                    <a href="<?= base_url() . $menu['url'] ?>">
                                        <span class="pcoded-micon"><i class="<?= $menu['icon'] ?>"></i></span>
                                        <span class="pcoded-mtext">
                                            <?= $menu['nama_module'] ?>
                                        </span>
                                    </a>
                                    <?php
                                    }
                                    ?>
                                    <ul <?= $ispar ?> class="pcoded-submenu">
                                        <?php
                                            foreach ($submenu as $x) {
                                                $this->db->order_by('urutan', 'desc');
                                                $subsummenu = $this->db->get_where('v_group_module', array('id_user_group' => $group, 'parent' => $x['kode_module'], 'tahun' => $thang))->result_array();

                                                $col = ($url == $x['url']) ? "#368a8a6e" : "#24242400";

                                                if (count($subsummenu) == 0) {
                                                    ?>
                                        <li class="">
                                            <a href="<?= base_url() . $x['url'] ?>">
                                                <span class="pcoded-mtext">
                                                    <?= $x['nama_module'] ?>
                                                </span>
                                            </a>
                                        </li>
                                        <?php
                                                } else {
                                                    $parentss = $this->db->get_where('v_group_module', array('id_user_group' => $group, 'url ' => $url, 'tahun' => $thang));

                                                    if ($parentss->num_rows() > 0) {
                                                        $op = ($parentss->row()->parent == $x['kode_module']) ? 'open' : '';
                                                        $col = ($op === 'open') ? "#90bdbd6e" : "";
                                                    } else {
                                                        $op = '';
                                                        $col = "";
                                                    }
                                                    ?>
                                        <li class="pcoded-hasmenu">
                                            <a href="<?= base_url() . $x['url'] ?>">
                                                <span class="pcoded-mtext">
                                                    <?= $x['nama_module'] ?>
                                                </span>
                                            </a>
                                            <ul class="pcoded-submenu">
                                                <?php
                                                            foreach ($subsummenu as $xx) {
                                                                $cols = ($url == $xx['url']) ? "#6f88886e" : "#24242400";
                                                                ?>
                                                <li class="">
                                                    <a href="<?= base_url() . $xx['url'] ?>">
                                                        <span class="pcoded-mtext">
                                                            <?= $xx['nama_module'] ?>
                                                        </span>
                                                    </a>
                                                </li>
                                                <?php
                                                            }
                                                            ?>
                                            </ul>
                                        </li>
                                        <?php
                                                }
                                            }
                                            ?>
                                    </ul>
                                </li>
                                <?php
                                }
                                ?>

                                <li class="">
                                    <a href="<?php echo base_url(); ?>login/logout">
                                        <span class="pcoded-micon"><i class="feather icon-power"></i></span>
                                        <span class="pcoded-mtext">Logout</span>
                                    </a>
                                </li>
                            </ul>

                    </nav>


                    <div class="pcoded-content">
                        <div class="pcoded-inner-content">
                            <div class="main-body">
                                <div class="page-wrapper">

                                    <!-- <div class="page-body"> -->
                                    <!-- <div class="row"> -->
                                    <div class="content">
                                        <?= $contents; ?>
                                    </div>
                                    <!-- </div> -->
                                    <!-- </div> -->
                                </div>

                                <div id="styleSelector">

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="text" id="pageLink" value="">


    <script>
    $(document).ready(function() {
        var currentLocation = window.location.pathname;
        var parts = currentLocation.split('/');

        // Mengambil bagian terakhir dari path URL
        var lpMonitoringLk = parts[parts.length - 2];


        var group = "<?php echo $this->session->userdata('id_user_group'); ?>"
        var link = "<?php echo base_url(); ?>" + lpMonitoringLk
        if (group == 1) {
            link = "<?php echo base_url(); ?>data_users"
        }
        $("#pageLink").val(link)
        var tah = sessionStorage.getItem('tahun_lay');
        if (tah == null || tah == '') {
            tah = new Date().getFullYear();
        }


        combo_tahun('tahun_layout', tah)
        $('nav a').on('click', function(e) {
            e.preventDefault();
            var pageRef = $(this).attr('href');

            if (pageRef == '<?php echo base_url(); ?>login/logout' || pageRef == 'login/logout' ||
                pageRef == '<?php echo base_url(); ?>page/login/logout') {
                window.location.href = pageRef;

            } else if (pageRef == '<?php echo base_url(); ?>#' || pageRef ==
                '<?php echo base_url(); ?>NULL') {
                return;
            } else {
                // Use dynamic content loader instead of callPage
                if (window.dynamicLoader) {
                    const title = $(this).text().trim();
                    window.dynamicLoader.loadContent(pageRef, title);
                } else {
                    // Fallback to old method if dynamic loader not available
                    callPage(pageRef);
                }
            }
            $("#pageLink").val(pageRef)
        });

        function combo_tahun(divname, set = '') {
            // Mendapatkan tahun saat ini
            const tahunSaatIni = new Date().getFullYear();
            $('#' + divname).val('').selectpicker('refresh');
            $('#' + divname).append(new Option("--Pilih Tahun Anggaran--", ""));
            for (let tahun = 2022; tahun <= tahunSaatIni; tahun++) {
                $('#' + divname).append(new Option("Tahun Anggaran " + tahun, tahun));
            }
            $('#' + divname).val(set).selectpicker('refresh');


        }

    });

    function to_link() {
        var tahun_layout = $("#tahun_layout").val()
        sessionStorage.removeItem('tahun_lay');
        sessionStorage.setItem('tahun_lay', tahun_layout);
        var pageRef = $("#pageLink").val();
        if (pageRef == '<?php echo base_url(); ?>login/logout' || pageRef == 'login/logout' ||
            pageRef == '<?php echo base_url(); ?>page/login/logout') {
            window.location.href = pageRef;

        } else if (pageRef == '<?php echo base_url(); ?>#' || pageRef ==
            '<?php echo base_url(); ?>NULL') {
            return;
        } else {
            // Use dynamic content loader instead of callPage
            if (window.dynamicLoader) {
                window.dynamicLoader.loadContent(pageRef, 'Page');
            } else {
                // Fallback to old method if dynamic loader not available
                callPage(pageRef);
            }
        }
    }

    function callPage(pageRefInput) {

        var data_post = {
            "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
        };

        $.ajax({
            dataType: "text",
            type: "POST",
            data: data_post,
            url: pageRefInput,
            success: function(data) {
                $('.content').empty();
                $('.content').html(data);
            },
            error: function(xhr, ajaxOptions, thrownError) {

            }
        });

    }
    </script>

    <!-- Dynamic Content Loader -->
    <script src="<?php echo base_url(); ?>assets/js/dynamic-content.js"></script>

    <script>
    // Initialize dynamic content loading for navigation
    $(document).ready(function() {
        // Add data-ajax-load attribute to sidebar navigation links
        $('.pcoded-navigatio-lavel').next('ul').find('a').each(function() {
            const href = $(this).attr('href');
            if (href && href.indexOf(WGI_APP_BASE_URL) === 0) {
                $(this).attr('data-ajax-load', 'true');
            }
        });

        // Handle content loaded event
        $(document).on('contentLoaded', function(event, response) {
            console.log('Content loaded:', response.title);

            // Re-initialize any specific plugins here if needed
            if (typeof user === 'function') {
                // Re-initialize DataTables if the user function exists
                setTimeout(function() {
                    if (typeof user === 'function') {
                        user();
                    }
                }, 100);
            }
        });
    });
    </script>
</body>

</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo isset($title) ? $title : 'Login'; ?></title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/vendor/bootstrap/css/bootstrap.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/fonts/font-awesome-4.7.0/css/font-awesome.min.css">
    
    <!-- Animate CSS -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/vendor/animate/animate.css">
    
    <!-- CSS Hamburgers -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/vendor/css-hamburgers/hamburgers.min.css">
    
    <!-- Select2 -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/vendor/select2/select2.min.css">
    
    <!-- Login CSS -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/css/util.css">
    <link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>assets/css/main.css">
    
    <!-- Custom CSS -->
    <style>
        .limiter {
            width: 100%;
            margin: 0 auto;
        }
        
        .container-login100 {
            width: 100%;
            min-height: 100vh;
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            padding: 15px;
            background: #9053c7;
            background: -webkit-linear-gradient(-135deg, #c850c0, #4158d0);
            background: -o-linear-gradient(-135deg, #c850c0, #4158d0);
            background: -moz-linear-gradient(-135deg, #c850c0, #4158d0);
            background: linear-gradient(-135deg, #c850c0, #4158d0);
        }
        
        .wrap-login100 {
            width: 960px;
            background: #fff;
            border-radius: 10px;
            overflow: hidden;
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            padding: 177px 130px 33px 95px;
        }
        
        .login100-form {
            width: 290px;
        }
        
        .login100-form-title {
            display: block;
            font-family: Poppins-Bold;
            font-size: 39px;
            color: #333333;
            line-height: 1.2;
            text-align: center;
        }
        
        .wrap-input100 {
            width: 100%;
            position: relative;
            border-bottom: 2px solid #d9d9d9;
            margin-bottom: 37px;
        }
        
        .label-input100 {
            font-family: Poppins-Regular;
            font-size: 13px;
            color: #666666;
            line-height: 1.5;
            padding-left: 5px;
        }
        
        .input100 {
            font-family: Poppins-Medium;
            font-size: 18px;
            color: #555555;
            line-height: 1.2;
            display: block;
            width: 100%;
            height: 55px;
            background: transparent;
            padding: 0 5px 0 38px;
            border: none;
            outline: none;
        }
        
        .focus-input100 {
            position: absolute;
            display: block;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
        }
        
        .focus-input100::before {
            content: "";
            display: block;
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            -webkit-transition: all 0.4s;
            -o-transition: all 0.4s;
            -moz-transition: all 0.4s;
            transition: all 0.4s;
            background: #6675df;
        }
        
        .focus-input100::after {
            font-family: FontAwesome;
            font-size: 18px;
            color: #999999;
            content: attr(data-symbol);
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            height: calc(100% - 20px);
            bottom: 0;
            left: 0;
            padding-left: 13px;
            padding-top: 3px;
        }
        
        .input100:focus + .focus-input100::before {
            width: 100%;
        }
        
        .has-val.input100 + .focus-input100::before {
            width: 100%;
        }
        
        .input100:focus + .focus-input100::after {
            color: #6675df;
        }
        
        .has-val.input100 + .focus-input100::after {
            color: #6675df;
        }
        
        .container-login100-form-btn {
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            padding-top: 13px;
        }
        
        .wrap-login100-form-btn {
            width: 100%;
            display: block;
            position: relative;
            z-index: 1;
            border-radius: 25px;
            overflow: hidden;
            margin: 0 auto;
        }
        
        .login100-form-bgbtn {
            position: absolute;
            z-index: -1;
            width: 300%;
            height: 100%;
            background: #a64bf4;
            background: -webkit-linear-gradient(right, #21d4fd, #b721ff, #21d4fd, #b721ff);
            background: -o-linear-gradient(right, #21d4fd, #b721ff, #21d4fd, #b721ff);
            background: -moz-linear-gradient(right, #21d4fd, #b721ff, #21d4fd, #b721ff);
            background: linear-gradient(right, #21d4fd, #b721ff, #21d4fd, #b721ff);
            top: 0;
            left: -100%;
            -webkit-transition: all 0.4s;
            -o-transition: all 0.4s;
            -moz-transition: all 0.4s;
            transition: all 0.4s;
        }
        
        .login100-form-btn {
            font-family: Poppins-Medium;
            font-size: 16px;
            color: #fff;
            line-height: 1.2;
            text-transform: uppercase;
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 20px;
            width: 100%;
            height: 50px;
            background: transparent;
            border: none;
            outline: none;
        }
        
        .wrap-login100-form-btn:hover .login100-form-bgbtn {
            left: 0;
        }
        
        .captcha-box {
            text-align: center;
            margin: 10px 0;
        }
        
        #canvas {
            border: 1px solid #ddd;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <?php echo isset($contents) ? $contents : ''; ?>
    
    <!-- jQuery -->
    <script src="<?php echo base_url(); ?>assets/vendor/jquery/jquery-3.2.1.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="<?php echo base_url(); ?>assets/vendor/bootstrap/js/popper.js"></script>
    <script src="<?php echo base_url(); ?>assets/vendor/bootstrap/js/bootstrap.min.js"></script>
    
    <!-- Select2 -->
    <script src="<?php echo base_url(); ?>assets/vendor/select2/select2.min.js"></script>
    
    <!-- Tilt JS -->
    <script src="<?php echo base_url(); ?>assets/vendor/tilt/tilt.jquery.min.js"></script>
    
    <!-- Main JS -->
    <script src="<?php echo base_url(); ?>assets/js/main.js"></script>
    
    <!-- Custom JavaScript -->
    <?php echo isset($jv_script) ? $jv_script : ''; ?>
</body>
</html>

<style>
table {
    width: 100% !important;
}



/* #modal-detail-mtl {
  width: 1000px;
  margin: auto;
} */
</style>

<div id="modal-tambah-mtl" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Data</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class='fa fa-close' style='color:white;'></i>
                </button>
            </div>
            <div class="modal-body">
                <form method="post" class="form-user" id="submit">
                    <div class="form-group yuh">
                        <label class="control-label col-xs-2" for="nama">Nama</label>
                        <div class="col-xs-9">
                            <input type="hidden" name="id" class="form-control" value="" id="id" />
                            <input type="text" name="nama" class="form-control" placeholder="Nama" id="nama"
                                autofocus />
                        </div>
                    </div>
                    <div class="form-group yuh">
                        <label class="control-label col-xs-2" for="username">Username</label>
                        <div class="col-xs-9">
                            <input type="text" name="username" class="form-control" placeholder="Username" id="username"
                                autofocus />
                        </div>
                    </div>
                    <div class="form-group yuh">
                        <label class="control-label col-xs-2" for="role">Role</label>
                        <div class="col-xs-9">
                            <select class="form-control" name="role" id="role" onchange="to_group(this)"
                                data-live-search="true"></select>
                        </div>
                    </div>
                    <div class="form-group yuh" style="display:none;" id="boxsatker">
                        <label class="control-label col-xs-2" for="satker" id="labelsatker"></label>
                        <div class="col-xs-9">
                            <select class="form-control" name="satker" id="satker" onchange=""
                                data-live-search="true"></select>
                        </div>
                    </div>
                    <div class="form-group yuh">
                        <label class="control-label col-xs-2" for="password">Password</label>
                        <div class="col-xs-9">
                            <input type="password" name="password" class="form-control" placeholder="Password"
                                id="password" autofocus />
                        </div>
                    </div>
                    <div class="form-group yuh">
                        <label class="control-label col-xs-2" for="password_k">Konfirmasi Password</label>
                        <div class="col-xs-9">
                            <input type="password" name="password_k" class="form-control"
                                placeholder="Konfirmasi Password" id="password_k" autofocus />
                        </div>
                    </div>
                    <div class="form-group" id="daf">
                        <div class="col-xs-12">
                            <button class="btn btn-tambah" id="op">
                                <i class="fa fa-refresh"></i> Simpan
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<div class="content">

    <div class="page-inner mt--5">
        <div class="row mt--2">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="col-md-3" id="hidetambah">
                            <button class="btn btn-tambah" id="tambah" onclick="tambahdata()">
                                <i class="fa fa-plus"></i> Tambah User
                            </button>
                            <br>
                            <br>
                        </div>
                        <div class="dt-responsive table-responsive">

                            <table class="display table table-bordered table-striped table-hover tab1" id="table-users">
                                <thead>
                                    <tr style="text-align:center;">
                                        <th id="th" class="INFO">No</th>
                                        <th id="th" class="INFO">Nama</th>
                                        <th id="th" class="INFO">Username</th>
                                        <th id="th" class="INFO">Role</th>
                                        <th id="th" class="INFO">Eselon I</th>
                                        <th id="th" class="INFO">Satker</th>
                                        <th id="th" class="INFO" style="width:200px">Option</th>
                                </thead>

                                </tbody>
                            </table>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>



<?php echo $tambah; ?>
<?php echo $jv_script; ?>
<script>
$(document).ready(function() {
    $("#submit").submit(function(e) {
        e.preventDefault()
        // alert("asd");

        var data = $('.form-user').serialize();
        console.log(new FormData(this))
        var pas = $("#password").val()
        var pas_k = $("#password_k").val()
        if (pas != pas_k) {
            swal({
                title: "",
                text: "Password Tidak Sesuai",
                showConfirmButton: false,
                timer: 1000,
                type: "warning"
            });
            return
        }
        $.ajax({
            type: 'POST',
            url: "<?php echo base_url('data_users/insert_data'); ?>",
            data: new FormData(this),
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            success: function(data) {
                var ops = ''
                if (data.split('_')[1] == 'insert') {
                    ops = 'Menambah';
                } else {
                    ops = 'Merubah';
                }
                if (data.split('_')[0] == 0) {
                    swal({
                        title: "",
                        text: "Berhasil " + ops + " Data",
                        showConfirmButton: false,
                        timer: 1000,
                        type: "success"
                    });
                    tabel.ajax.reload();
                    $("#modal-tambah-mtl").modal("hide");
                } else {
                    swal({
                        title: "",
                        text: "Gagal " + ops + " Data",
                        showConfirmButton: false,
                        timer: 1000,
                        type: "warning"
                    });
                }

            }
        });
    });
    user()

})

function user() {
    var tab = $('#table-users').DataTable();
    tab.destroy()
    data_user()
}

var tabel = null

function data_user() {
    tabel = $('#table-users').DataTable({
        "processing": true,
        "serverSide": true,
        "ordering": true, // Set true agar bisa di sorting
        "order": [
            [0, 'desc']
        ], // Default sortingnya berdasarkan kolom / field ke 0 (paling pertama)
        "ajax": {
            "url": "<?php echo base_url('data_users/ssp') ?>", // URL file untuk proses select datanya
            "type": "POST"
        },
        "deferRender": true,
        "aLengthMenu": [
            [5, 10, 50],
            [5, 10, 50]
        ],
        "scrollX": true,
        "pageLength": 10,
        "columns": [{
                "render": function(data, type, row, meta) {
                    // Mengambil halaman yang sedang aktif
                    var page = tabel.page.info().page;
                    // Menghitung nomor urut unik berdasarkan halaman yang sedang aktif
                    var uniqueIndex = page * tabel.page.len() + meta.row + 1;
                    return uniqueIndex;
                }
            }, {
                "data": "fullname"
            },
            {
                "data": "username"
            },
            {
                "data": "nm_role"
            },
            {
                "data": "nm_unit_eselon_1"
            },

            {
                "data": "nm_satker"
            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi

                    var html_button = [
                        "<button class='btn btn-danger btn-xs' title = 'Hapus Data' onclick=hapus_data('" +
                        row.id + "')><i class='fa fa-trash'></i></button>",
                        "<button class='btn btn-primary btn-xs' title='Edit Data' onclick=edit_data('" +
                        row.id + "')><i class='fa fa-edit'></i></button>",


                    ].join("\n");
                    return html_button;

                }
            }
        ],
        "language": {
            "decimal": "",
            "emptyTable": "Data tidak ditemukan",
            "info": "Data _START_ s/d _END_ dari _TOTAL_",
            "infoEmpty": "Tidak ada data",
            "infoFiltered": "(tersaring dari _MAX_)",
            "infoPostFix": "",
            "thousands": ",",
            "lengthMenu": "_MENU_  data per halaman",
            "loadingRecords": '<div class="preloader3 loader-block"><div class="circ1"></div><div class="circ2"></div> <div class="circ3"></div> <div class="circ4"></div></div>',
            "processing": '<div class="preloader3 loader-block"><div class="circ1"></div><div class="circ2"></div> <div class="circ3"></div> <div class="circ4"></div></div>',
            "search": "Cari:",
            "zeroRecords": "Tidak ada data ditemukan",
            "aria": {
                "sortAscending": ": aktifkan untuk mengurutkan naik",
                "sortDescending": ": aktifkan untuk mengurutkan turun"
            }
        }
    });
}




function tampil_data(table, colum, id) {
    var url = ''
    var tadata = ''
    urls = "<?php echo base_url(); ?>data_users/tampildata/" + table + "/" + colum + "/" + id;
    $.ajax({
        url: urls,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function(data) {
            tadata = data;
        },
        failure: function(errMsg) {
            alert(errMsg);
        }
    });
    return tadata;
}


function tambahdata() {
    $(".form-user input").val('')
    $(".form-user select").val('')
    $(".form-user textarea").val('')
    $("#boxsatker").hide()
    $("#satker").empty()
    $("#modal-tambah-mtl").modal("show");
    combo('role', 'aset_user_group~nama~id_user_group')
    // combo('satker', 'ref_satker~nm_satker~kode_satker')
}

function to_group(a, set = '') {
    $("#boxsatker").hide()
    $("#satker").empty()
    b = a.value;
    if (b === undefined) {
        b = a
    }

    if (b == 3) {
        $("#labelsatker").text("Satker")
        combo('satker', 'ref_satker~nm_satker~kode_satker', '1', '1', set)
        $("#boxsatker").show()

    } else if (b == 4) {
        $("#labelsatker").text("Eselon I")
        combo('satker', 'ref_eselon_1~nm_unit_eselon_1~kode_eselon_1', '1', '1', set)
        $("#boxsatker").show()
    } else {
        $("#satker").append("<option value=''></option>")
    }

}

function edit_data(id) {
    $(".form-user input").val('')
    $(".form-user select").val('')
    var dt = tampil_data('user', 'id', id);
    $("#id").val(dt[0].id)
    $("#username").val(dt[0].username)
    $("#nama").val(dt[0].fullname)
    to_group(dt[0].id_user_group)
    if (dt[0].id_user_group == 3) {
        to_group(dt[0].id_user_group, dt[0].kode_satker)
    } else if (dt[0].id_user_group == 4) {
        to_group(dt[0].id_user_group, dt[0].kode_eselon_1)
    }

    combo('role', 'aset_user_group~nama~id_user_group', '1', '1', dt[0].id_user_group)

    // combo('satker', 'ref_satker~nm_satker~kode_satker', '1', '1', dt[0].kode_satker)

    $("#modal-tambah-mtl").modal("show");
}


function to_divisi(a, set = '') {
    b = a.value;
    if (b === undefined) {
        b = a
    }
    combo('divisi', 'data_divisi~nama~kode', 'id_direktorat', b, set)
}

function to_departemen(a, set = '') {
    b = a.value;
    if (b === undefined) {
        b = a
    }
    combo('departemen', 'data_departemen~nama~kode', 'divisi', b, set)
}

function combo(divname, table = '', colum = '1', id = '1', set = '') {

    url3 = "<?php echo base_url(); ?>data_users/tampildata/" + table + "/" + colum + "/" + id + "/combobox";
    $('#' + divname).val('').selectpicker('refresh');

    $.get(url3).done(function(data3) {
        jdata3 = JSON.parse(data3);
        //*
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));

        if (divname == 'pem_konsesi') {
            $.each(jdata3, function(i, el) {
                $('#' + divname).append(new Option("(" + el.kode + ") " + el.nama, el.kode));

            });
        } else {
            $.each(jdata3, function(i, el) {
                $('#' + divname).append(new Option(el.nama, el.kode));

            });
        }

        $('#' + divname).val(set).selectpicker('refresh');

    }).fail(function() {
        alert("error");
    }).always(function() {
        // alert("finished");
    });

}


function hapus_data(id) {
    swal({
            title: "Anda yakin menghapus data ?",
            // text: tex,
            icon: "warning",
            buttons: true,
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
                var url = ''
                url = "<?php echo base_url(); ?>data_users/delete_data/user/id/" + id;
                $.post(url, {
                    id: id
                }).done(function(data) {
                    if (data == 0) {
                        swal({
                            title: "",
                            text: "Berhasil Menghapus Data",
                            icon: "success",
                            showConfirmButton: false,
                            timer: 3000,
                            type: "success"
                        });
                        tabel.ajax.reload();
                    }
                })
            } else {
                swal("Anda Tidak Menjadi Menghapus Data");
            }
        });

}
</script>
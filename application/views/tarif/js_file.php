<script>
var tahun_layout = $("#tahun_layout").val()
var id_user_group = "<?php echo $this->session->userdata('id_user_group'); ?>"
var id_user = "<?php echo $this->session->userdata('id_user'); ?>"
var id_eselon = "<?php echo $this->session->userdata('kode_eselon_1'); ?>"
var id_satker = "<?php echo $this->session->userdata('kode_satker'); ?>"

var kolom_group = ''
var kode_group = ''
var edit_ak = 'none;'
var hapus_ak = 'none;'
if (id_user_group == 3) {
    kode_group = id_satker
    kolom_group = "kode_satker"
    edit_ak = ''
    hapus_ak = ''
} else if (id_user_group == 4) {
    kolom_group = 'kode_eselon_1';
    kode_group = id_eselon;
} else {
    $(".show_eselon").show()
    kolom_group = '1';
    kode_group = 1;

}

function htmlTableToExcel() {
    sheetJSExportTable(document.getElementById("tabexport"));
}

function sheetJSExportTable(table, name) {
    var elt = table;
    var wb = XLSX.utils.table_to_book(elt, {
        sheet: "Sheet JS",
        raw: true
    });
    let range = XLSX.utils.decode_range(wb.Sheets['Sheet JS']['!ref']);
    let borderStyle = {
        top: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        },
        bottom: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        },
        left: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        },
        right: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        }
    }
    wb.Sheets['Sheet JS']['!rows'] = [{
        hpx: 50
    }]
    let columnWidths = [{
            wpx: 20
        },
        {
            wpx: 350
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 200
        },
        {
            wpx: 150
        },
        {
            wpx: 250
        },
        {
            wpx: 150
        },
        {
            wpx: 100
        },
    ];

    // Assign column widths to the worksheet
    wb.Sheets['Sheet JS']['!cols'] = columnWidths;
    wb.Sheets['Sheet JS']["!merges"].forEach(item => {
        if (item.e.r == item.s.r && item.e.c != item.s.c) {
            // 列合并
            let R = item.s.r;
            let countLength = item.e.c - item.s.c;
            for (let i = item.s.c; i <= item.e.c; i++) {
                let cell = {
                    c: i,
                    r: R
                };
                let cell_ref = XLSX.utils.encode_cell(cell);
                if (!wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref] = {
                        t: "s",
                        v: ""
                    };
                }
            }
        } else if (item.e.c == item.s.c && item.e.r != item.s.r) {
            // 行合并
            let C = item.s.c;
            let countLength = item.e.r - item.s.r;
            for (let i = item.s.r; i <= item.e.r; i++) {
                let cell = {
                    c: C,
                    r: i
                };
                let cell_ref = XLSX.utils.encode_cell(cell);
                if (!wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref] = {
                        t: "s",
                        v: ""
                    };
                }
            }
        }
    })
    for (let C = range.s.c; C <= range.e.c; ++C) {
        for (let R = range.s.r; R <= range.e.r; ++R) {
            let cell = {
                c: C,
                r: R
            };
            let cell_ref = XLSX.utils.encode_cell(cell);
            if (C === 4 && R >= 3) {
                if (wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref].t = 'n'; // Set type to number
                    wb.Sheets['Sheet JS'][cell_ref].z = '0';
                }
            }
            if (R == 0 || R == 1 || R == 2) {
                var sis = "15"
                if (R == 1 || R == 2) {
                    sis = "13"
                }
                wb.Sheets['Sheet JS'][cell_ref].s = {
                    alignment: {
                        horizontal: "center",
                        vertical: "center"
                    },
                    font: {
                        name: "",
                        sz: sis,
                        bold: true
                    },
                    border: borderStyle,
                };
            } else {
                if (wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref].s = {
                        font: {
                            name: "",
                            sz: "12"
                        },
                        border: borderStyle,
                    };
                }
            }
        }
    }
    var wopts = {
        bookType: 'xlsx',
        bookSST: false,
        type: 'binary'
    };
    var wbout = XLSXX.write(wb, wopts); // 使用xlsx-style 写入
    saveAs(new Blob([s2ab(wbout)], {
        type: ""
    }), "SK Tarif-COD.xlsx")
}

function s2ab(s) {
    var buf = new ArrayBuffer(s.length);
    var view = new Uint8Array(buf);
    for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
    return buf;
}
// function htmlTableToExcel() {
//     var filename = "SK_TARIF-COD.xlsx"; // Ganti "nama_file_excel.xlsx" dengan nama yang Anda inginkan
//     var data = encodeURIComponent($('#tes').html());
//     var url = 'data:application/vnd.ms-excel;charset=UTF-8,' + data;

//     var downloadLink = document.createElement("a");
//     downloadLink.href = url;
//     downloadLink.download = filename;

//     document.body.appendChild(downloadLink);
//     downloadLink.click();
//     document.body.removeChild(downloadLink);
// }
$(".browse-button0 input:file").change(function() {
    $("input[id='file_tarif0']").each(function() {
        var fileName = $(this).val().split('/').pop().split('\\').pop();
        $(".filename0").val(fileName);
        $(".browse-button-text0").html('<i class="fa fa-refresh"></i> ');
        $(".clear-button0").show();
        // $('.input-group-btn').html(` <button class="btn btn-primary upload-button" type="button"  style="height:54px !important;">
        //                             <i class="fa fa-download"></i>
        //                           </button>`)
    });
});

//actions happening when the button is clicked
$('.clear-button0').click(function() {
    $('.filename0').val("");
    $('.filenames0').val("");
    $('.clear-button0').hide();
    $('.browse-button0 input:file').val("");
    $(".browse-button-text0").html('<i class="fa fa-folder-open"></i> ');
});

$(document).ready(function() {
    tahun_layout = tahun_layout === null ? tahunSaatIni : tahun_layout
    var dt = tampil_data('v_perjanjian_pengusahaan_detail', kolom_group, kode_group);
    var json_data = JSON.stringify(dt);
    $("#simpandatadetailperjanjian").val(json_data)

    var dt2 = tampil_data('v_tarif_detail', kolom_group + '~tahun', kode_group + '~' + tahun_layout);

    var json_data2 = JSON.stringify(dt2);
    $("#simpandatadetailtarif").val(json_data2)
    combo('jenis_p_filter', 'ref_jenis_pengusahaan~nm_jenis_pengusahaan~id_jenis_pengusahaan')
    combo('nama_p_filter', 'v_tarif~nm_perjanjian~nm_perjanjian', kolom_group, kode_group)
    combo('bdn_usaha_filter', 'ref_badan_usaha~nm_mitra~id_badan_usaha')
    combo('pem_konsesi_filter', 'ref_satker~nm_satker~kode_satker', kolom_group, kode_group)
    combo('pulau_filter', 'ref_pulau~nm_pulau~id_pulau')
    combo('eselon_filter', 'ref_eselon_1~nm_unit_eselon_1~kode_eselon_1')
    tabelToExport()


    if (tahun_layout == '') {
        swal({
            title: "Harap pilih Tahun Anggaran",
            // text: tex,
            icon: "warning",
            timer: 2000,
            dangerMode: true,
        });
        return
    }
    $("#submit").submit(function(e) {
        e.preventDefault()
        // alert("asd");

        var data = new FormData($('.form-user')[
            0]); // Membuat objek FormData dari form dengan kelas .form-user

        var tahun_layout = $("#tahun_layout").val();
        data.append('tahun_layout', tahun_layout); // Menambahkan tahun_layout ke objek FormData


        $.ajax({
            type: 'POST',
            url: "<?php echo base_url('index.php/tarif/insert_data'); ?>",
            data: data,
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            success: function(data) {
                var ops = ''
                if (data.split('_')[1] == 'insert') {
                    ops = 'Menambah';
                } else {
                    ops = 'Merubah';
                }
                if (data.split('_')[0] == 0) {
                    swal({
                        title: "",
                        text: "Berhasil " + ops + " Data",
                        showConfirmButton: false,
                        timer: 1000,
                        type: "success"
                    });
                    var dt2 = tampil_data('tarif_detail', '1', '1');
                    var json_data2 = JSON.stringify(dt2);
                    $("#simpandatadetailtarif").val(json_data2)
                    tabel.ajax.reload();
                    tabelToExport()
                    $("#modal-tambah-mtl").modal("hide");
                } else {
                    swal({
                        title: "",
                        text: "Gagal " + ops + " Data",
                        showConfirmButton: false,
                        timer: 1000,
                        type: "warning"
                    });
                }

            }
        });
    });
    user()

})
var ii = 0;
var yy = 0;

function bang_l(val = '') {
    var jumlahDiv = $("#urutan").val();
    ii = +jumlahDiv
    if (val != '') {
        ii = val + yy + jumlahDiv - 1;
    }
    ii++;
    yy++;

    var html = `
            <div class="form-group row" id="yuhui${ii}">
              <div class="col-sm-4">
              <input type="text" class="form-control" placeholder="" name="no_dok[]" id="no_dok${ii}" >
              </div>
              <div class="col-sm-3">
                <input type="date" class="form-control tglop" placeholder="" id="tgl_op${ii}" name="tgl_op[]" onchange="masamanfaat(this)">
              </div>
              <div class="col-sm-4">
                      <div class="input-group">
                            <div class="btn btn-primary browse-button${ii} button-yuhu">
                                <span class="browse-button-text${ii}">
                                <i class="fa fa-folder-open"></i> </span>
                               <input type="file" class="form-control" style="width:50px;"  placeholder="" id="file_tarif${ii}"
                          name="file_tarif[]" >  
                                </div> 
                            <div type="button" class="btn btn-danger clear-button${ii} button-yuhu" style="display:none;">
                              <span class="fa fa-times"></span>  
                                        </div>
 
                          <input type="text" class="form-control filename${ii} button-yuhu" name="filename[]"  placeholder="Masukan File" readonly>
                          <input type="hidden" class="form-control filenames${ii} button-yuhu" name="filenames[]">
                          
                          <span class="input-group-btn">
                           
                          </span>
                    </div>
                  </div>
              <div class="col-sm-1" style="text-align:left;">
              <button type="button" style="height:35px;font-size: 20px;color:red;" onclick="remove_comboi('${ii}')" name="remove" id="${ii}" class="btn btn-defaukt btn_remove" ><i class="fa fa-trash"></i></button>
              </div>
            </div>
          `;
    $('#bang_lai').append(html);
    $("#urutan").val(ii)
    var jumlah = ii;
    upload_div(jumlah)

}

function upload_div(dt) {
    for (let ii = 0; ii < dt + 1; ii++) {
        $(".browse-button" + ii + " input:file").change(function() {
            $("input[id='file_tarif" + ii + "']").each(function() {
                var fileInput = $(this)[0]; // Dapatkan elemen input file
                var maxFileSizeInBytes = 15 * 1024 * 1024; // Misalnya, batasan ukuran file 15 MB
                var allowedFileTypes = ["pdf", "xlsx", "xls"]; // Hanya izinkan file PDF

                var file = fileInput.files[0];
                var fileSize = file.size;
                var fileType = file.name.split('.').pop().toLowerCase();

                // Validasi ukuran file
                if (fileSize > maxFileSizeInBytes) {
                    alert("Ukuran file terlalu besar. Maksimum 15 MB diizinkan.");
                    return
                } else if (allowedFileTypes.indexOf(fileType) === -1) {
                    alert("Hanya file PDF yang diizinkan.");
                    return
                } else {
                    var fileName = $(this).val().split('/').pop().split('\\').pop();
                    $(".filename" + ii).val(fileName);
                    $(".filenames" + ii).val(fileName);
                    $(".browse-button-text" + ii).html('<i class="fa fa-refresh"></i> ');
                    $(".clear-button" + ii).show();
                }
            });
        });

        //actions happening when the button is clicked
        $('.clear-button' + ii).click(function() {
            $('.filename' + ii).val("");
            $('.filenames' + ii).val("");
            $('.clear-button' + ii).hide();
            $('.browse-button' + ii + ' input:file').val("");
            $(".browse-button-text" + ii).html('<i class="fa fa-folder-open"></i> ');
        });
    }
}

function tabelToExport() {
    $('#tes').empty();
    if (id_user_group == 3) {
        var slo = tampil_data('v_tarif', kolom_group + '~tahun', kode_group + '~' + tahun_layout);

    } else {
        var slo = tampil_data('v_tarif_kemen', kolom_group + '~tahun', kode_group + '~' + tahun_layout);

    }




    // Membangun tabel
    var tabs = `<table id="tabexport" border="1px" style="width:4000px">
    <thead>
    <tr>
    <th colspan="10"><center><b>Laporan SK TARIF/COD</b></center></th>
    </tr>
    <tr style="text-align:center;">
                                        <th rowspan="2" id="th" class="INFO">No</th>
                                        <th rowspan="2" id="th" class="INFO">Nama Perjanjian Konsesi Jasa</th>
                                        <th colspan="3" id="th" class="INFO">Perjanjian Konsesi Jasa</th>
                                        <th rowspan="2" id="th" class="INFO">Badan Usaha</th>

                                        <th rowspan="2" id="th" class="INFO">Tanggal Akhir Konsesi</th>
                                        <th colspan="2" id="th" class="INFO">SK TARIF/COD</th>
                                        <th rowspan="2" id="th" class="INFO">Masa Manfaat</th>
                                    </tr>
                                    <tr style="text-align:center;">
                                        <th id="th" class="INFO">Nomor Perjanjian</th>
                                        <th id="th" class="INFO">Tanggal</th>
                                        <th id="th" class="INFO">Nilai Investasi</th>
                                        <th id="th" class="INFO">Nomor SK TARIF/COD</th>
                                        <th id="th" class="INFO">Tanggal SK TARIF/COD</th>
                                    </tr>
    </thead>
    <tbody>`;

    for (var i = 0; i < slo.length; i++) {
        var id = slo[i].id_perjanjian_pengusahaan
        if (id.includes(',')) {
            id = id.split(',')[0]
        }
        var dtx = get_dataDetailById(id);

        var dts = get_dataDetailByIdTarif(slo[i].id_tarif);
        var rowspan = Math.max(dtx.length, dts.length);

        for (let j = 0; j < rowspan; j++) {
            var row = '<tr>';

            if (j === 0 || (i === 0 && rowspan === 1)) {
                row +=
                    `<td  class="xl68"   rowspan="${rowspan}">${i + 1}</td>`; // Menambahkan nomor urut
                row +=
                    `<td  class="xl68"   rowspan="${rowspan}">${slo[i].nm_perjanjian}</td>`; // Menambahkan nama perjanjian
            }

            if (j < dtx.length) {
                row +=
                    `<td  class="xl68" align="left"  >${dtx[j].no_perjanjian}</td>`;
                row +=
                    `<td class="xl68"  >${ubahFormatTanggal(dtx[j].tgl_perjanjian)}</td>`;
                row +=
                    `<td class="xl69"  align="right"   >${dtx[j].nilai_investasi === null ? '' : dtx[j].nilai_investasi}</td>`;
            } else {
                row +=
                    `<td  class="xl68"   ></td><td  class="xl68"   ></td><td  class="xl68"   ></td>`; // Sel kosong jika tidak ada data dari dtx
            }


            if (j === 0 || (i === 0 && rowspan === 1)) {
                row +=
                    `<td  class="xl68"   rowspan="${rowspan}">${slo[i].nm_mitra}</td>`; // Menambahkan nama perjanjian
                row +=
                    `<td  class="xl68"   rowspan="${rowspan}">${ubahFormatTanggal(slo[i].tgl_akhir_konsesi)}</td>`; // Menambahkan nama perjanjian
            }
            if (j < dts.length) {
                row +=
                    `<td class="xl68" align="left"  >${dts[j].no_dok_tarif}</td>`;
                row +=
                    `<td class="xl68"  >${ubahFormatTanggal(dts[j].tgl_tarif) === null ? '' : ubahFormatTanggal(dts[j].tgl_tarif)}</td>`;
            } else {
                row +=
                    `<td  class="xl68"   ></td><td  class="xl68"   ></td>   `; // Sel kosong jika tidak ada data dari dts
            }
            if (j === 0 || (i === 0 && rowspan === 1)) {
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${slo[i].masa_manfaat === null ? '' : slo[i].masa_manfaat}</td>`; // Menambahkan nama perjanjian

            }
            row += '</tr>';
            tabs += row;
        }
    }

    tabs += '</tbody></table>';
    $('#tes').html(tabs);
}

function remove_comboi(x) {
    // alert("ASDASd")
    // alert(x.value)
    // var button_id = $(this).attr("id");   
    $('#yuhui' + x + '').remove();
    var tgl_ak = $("#tgl_ak").val().split("-")[0]
    var tgl_tarif = $(".tglop:last").val().split("-")[0]
    var hasil = (parseInt(tgl_ak) - parseInt(tgl_tarif)) * 2
    $("#masa").val(hasil)
}

function reset() {
    // $("#form-filter select").val('')
    $('#nama_p_filter').val('').selectpicker('refresh');
    $('#bdn_usaha_filter').val('').selectpicker('refresh');
    $('#pem_konsesi_filter').val('').selectpicker('refresh');
    $('#pulau_filter').val('').selectpicker('refresh');
    $('#jenis_p_filter').val('').selectpicker('refresh');
    $('#eselon_filter').val('').selectpicker('refresh');

    to_filter('')
}

function close_filter(element) {

    var yuhu = $("#styleSelector").attr('class');
    $(".selector-toggle").hide()
    if (yuhu == '') {
        $(".selector-toggle").show()
    }

    $('#styleSelector').toggleClass('open');
}

function to_filter(a) {
    var nama = $("#nama_p_filter").val()
    var bdn = $("#bdn_usaha_filter").val()
    var pem = $("#pem_konsesi_filter").val()
    var pulau = $("#pulau_filter").val()
    var jenis = $("#jenis_p_filter").val()
    var eselon = $("#eselon_filter").val()
    user(nama, bdn, pem, pulau, jenis, eselon)
}

function user(nama = '', bdn = '', pem = '', pulau = '', jenis = '', eselon = '') {

    var tab = $('#table-users').DataTable();
    tab.destroy()
    data_user(nama, bdn, pem, pulau, jenis, eselon)
    var tom =
        `<button style="margin-top: 8px;" class="btn btn-default buttons-excel buttons-html5" tabindex="0" aria-controls="table-users" type="button"  onclick="htmlTableToExcel('xlsx')"><span></span></button>`
    $(".tombolTambah").append(tom)

    if (id_user_group == 3) {
        var toms = `
        <button class="btn btn-tambah" id="tambah" onclick="tambahdata()">
                                <i class="fa fa-plus"></i>  SK Tarif/COD
                            </button>`
    }
    $(".tombolTambah").append(toms)
    var tomF = `<button style="margin-top: 8px;"  class="btn btn-tambah" id="tambah" onclick="close_filter('')">
                                            <i class="fa fa-filter"></i>
                                        </button><br>
                        <br>`
    $(".tombolFilter").html(tomF)
}

function formatAngkaKeRupiah(angka) {
    if (angka === null) {
        return ''; // Mengembalikan string kosong jika angka adalah null
    }

    var number_string = angka.toString();
    var split = number_string.split(',');
    var sisa = split[0].length % 3;
    var rupiah = split[0].substr(0, sisa);
    var ribuan = split[0].substr(sisa).match(/\d{3}/gi);

    if (ribuan) {
        var separator = sisa ? '.' : '';
        rupiah += separator + ribuan.join('.');
    }

    rupiah = split[1] !== undefined ? rupiah + ',' + split[1] : rupiah;
    return "Rp " + rupiah;
}


function get_dataDetailById(id) {
    if (id == '' || id == null) {
        return []
    }
    var dt = $("#simpandatadetailperjanjian").val()
    var js = JSON.parse(dt)
    // Mencari semua elemen dalam array yang memiliki ID yang sesuai
    var data = js.filter(function(element) {
        return element.id_perjanjian_pengusahaan === id.toString();
    });

    // Jika data ditemukan, tampilkan semua data
    return data
}

function get_dataDetailByIdTarif(id) {
    if (id == '' || id == null) {
        return []
    }
    var dt = $("#simpandatadetailtarif").val()
    var js = JSON.parse(dt)

    // Mencari semua elemen dalam array yang memiliki ID yang sesuai
    var data = js.filter(function(element) {
        return element.id_tarif === id.toString();
    });

    // Jika data ditemukan, tampilkan semua data
    return data
}
var tabel = null


function data_user(nama, bdn, pem, pulau, jenis, eselon) {
    // get_dataDetailById("314");
    tabel = $('#table-users').DataTable({
        "processing": true,
        "serverSide": true,
        "ordering": true, // Set true agar bisa di sorting
        "bAutoWidth": false,
        "order": [
            [0, 'asc']
        ], // Default sortingnya berdasarkan kolom / field ke 0 (paling pertama)
        "ajax": {
            "url": "<?php echo base_url('index.php/tarif/ssp') ?>", // URL file untuk proses select datanya
            "type": "POST",
            "data": function(d) {
                d.badan = bdn;
                d.tahun = tahun_layout;
                d.nama = nama;
                d.pem = pem;
                d.pulau = pulau;
                d.jenis = jenis;
                d.eselon = eselon;
            }
        },
        "deferRender": true,
        "pageLength": 10,
        "aLengthMenu": [
            [5, 10, 50, 5000],
            [5, 10, 50, "All"]
        ],
        dom: "<'row'<'col-sm-12 col-md-3 tombolTambah'><'col-sm-12 col-md-6'><'col-sm-12 col-md-3 tombolFilter'><'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
            "<'row'<'col-sm-12'tr>>" +
            "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

        buttons: [{
            extend: 'excelHtml5',
            text: '',
            title: 'SK TARIF/COD',
            exportOptions: {
                columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
                stripHtml: false,
                modifier: {
                    order: 'current',
                    page: 'all',
                    selected: null,
                },
                format: {
                    body: function(data, row, column, node) {
                        var tempDiv = document.createElement('div');
                        tempDiv.innerHTML = data;

                        // Mendapatkan teks dari elemen div yang sudah dikonversi
                        var textData = tempDiv.textContent || tempDiv.innerText;

                        // Mengganti tag '<br>' dengan karakter newline ('\n')
                        textData = textData.replace(/<br\s*\/?>/ig, "\n");

                        return textData;

                        // return data;
                    }
                }
            }
        }],
        "scrollX": true,
        "pageLength": 10,
        "order": [
            [0, "desc"]
        ],
        "columns": [{
                "render": function(data, type, row, meta) {
                    // Mengambil halaman yang sedang aktif
                    var page = tabel.page.info().page;
                    // Menghitung nomor urut unik berdasarkan halaman yang sedang aktif
                    var uniqueIndex = page * tabel.page.len() + meta.row + 1;
                    return uniqueIndex;
                }
            }, {
                "data": "nm_perjanjian"
            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var id = row.id_perjanjian_pengusahaan
                    if (id.includes(',')) {
                        id = id.split(',')[0]
                    }
                    var dt = get_dataDetailById(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push(
                            "<div id='kol'>" + dt[i].no_perjanjian +
                            "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                }


            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var id = row.id_perjanjian_pengusahaan
                    if (id.includes(',')) {
                        id = id.split(',')[0]
                    }
                    var dt = get_dataDetailById(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push("<div id='kol'>" + ubahFormatTanggal(dt[i].tgl_perjanjian) +
                            "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                }


            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var id = row.id_perjanjian_pengusahaan
                    if (id.includes(',')) {
                        id = id.split(',')[0]
                    }
                    var dt = get_dataDetailById(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        if (dt[i].nilai_investasi) {
                            noPpjtValues.push("<div id='kol' style='text-align:right !important;'>" +
                                formatAngkaKeRupiah(dt[i].nilai_investasi) +
                                "</div>");
                        }
                    }

                    return noPpjtValues.join('\r\n');
                },
                "createdRow": function(row, data, dataIndex) {
                    $(row).find('td').addClass('newline');
                }


            },
            {
                "data": "nm_mitra"
            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var nm = row.tgl_akhir_konsesi
                    return ubahFormatTanggal(nm);

                }

            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var id = row.id_tarif
                    var dt = get_dataDetailByIdTarif(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push(
                            "<div id='kol'>" + dt[i].no_dok_tarif + "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                },
                "createdRow": function(row, data, dataIndex) {
                    $(row).addClass('bottom-border'); // Add a class to the row
                }


            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var id = row.id_tarif
                    var dt = get_dataDetailByIdTarif(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push("<div id='kol'>" + ubahFormatTanggal(dt[i].tgl_tarif) +
                            "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                },
                "createdRow": function(row, data, dataIndex) {
                    $(row).addClass('bottom-border'); // Add a class to the row
                }


            },
            {
                "data": "masa_manfaat"
            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi
                    var nm = row.updated_at
                    var hasil = ''
                    if (nm) {

                        // Memisahkan tanggal dan waktu dengan fungsi split
                        var dateTimeArray = nm.split(" ");
                        var tanggal = dateTimeArray[0];
                        var jam = dateTimeArray[1];

                        hasil = "<b>Tanggal : " + tanggal +
                            "<hr style='margin:2px'>Jam : " +
                            jam + "</b>";

                    }
                    return hasil

                }

            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi

                    var html_button = [
                        "<button class='btn btn-danger btn-xs' title = 'Hapus Data' onclick=hapus_data('" +
                        row.id_tarif + "') style='display:" + hapus_ak +
                        "'><i class='fa fa-trash'></i></button>",
                        "<button class='btn btn-primary btn-xs' title='Edit Data' onclick=edit_data('" +
                        row.id_tarif + "') style='display:" + edit_ak +
                        "'><i class='fa fa-edit'></i></button>",
                        "<button class='btn btn-warning btn-xs' title='Download file' onclick=download_data('" +
                        row.id_tarif +
                        "','" + row.id_perjanjian_pengusahaan +
                        "')><i class='fa fa-download'></i></button>",


                    ].join("\n");
                    return html_button;

                }
            }
        ],
        "language": {
            "decimal": "",
            "emptyTable": "Data tidak ditemukan",
            "info": "Data _START_ s/d _END_ dari _TOTAL_",
            "infoEmpty": "Tidak ada data",
            "infoFiltered": "(tersaring dari _MAX_)",
            "infoPostFix": "",
            "thousands": ",",
            "lengthMenu": "_MENU_  data per halaman",
            "loadingRecords": '<div class="preloader3 loader-block"><div class="circ1"></div><div class="circ2"></div> <div class="circ3"></div> <div class="circ4"></div></div>',
            "processing": '<div class="preloader3 loader-block"><div class="circ1"></div><div class="circ2"></div> <div class="circ3"></div> <div class="circ4"></div></div>',
            "search": "Cari:",
            "zeroRecords": "Tidak ada data ditemukan",
            "aria": {
                "sortAscending": ": aktifkan untuk mengurutkan naik",
                "sortDescending": ": aktifkan untuk mengurutkan turun"
            }
        }


    });
}

function ubahFormatTanggal(tanggal) {
    if (tanggal == null || tanggal == '') {
        return ''
    }
    // Membagi tanggal menjadi bagian-bagian
    var tanggalBagian = tanggal.split('-');

    // Menyusun ulang tanggal dalam format yang diinginkan
    var tanggalBaru = tanggalBagian[2] + '-' + tanggalBagian[1] + '-' + tanggalBagian[0];

    return tanggalBaru;
}




// function tampil_data(table, colum = '1', id = '1') {
//     var url = ''
//     var tadata = ''
//     urls = "<?php echo base_url(); ?>index.php/perjanjian_konsesijasa/tampildata/" + table + "/" + colum + "/idx" + id;
//     $.ajax({
//         url: urls,
//         contentType: "application/json; charset=utf-8",
//         dataType: "json",
//         async: false,
//         success: function(data) {
//             tadata = data;
//         },
//         failure: function(errMsg) {
//             alert(errMsg);
//         }
//     });
//     return tadata;
// }

function tampil_data(table, colum = '1', id = '1', combobox = '') {
    var tadata = '';
    const url = "<?php echo base_url(); ?>index.php/perjanjian_konsesijasa/tampildata";
    const data = {
        table: table,
        colum: colum,
        id: btoa(id), // base64 encode agar sama dengan di PHP
        combobox: combobox
    };

    $.ajax({
        url: url,
        type: 'POST',
        data: JSON.stringify(data),
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function(result) {
            tadata = result;
        },
        error: function(err) {
            console.error("Gagal fetch data:", err);
        }
    });
    return tadata;
}


function to_kolom(a, set = '') {
    b = a.value;
    if (b === undefined) {
        b = a
    }
    // alert(b)
    // if (a.id == 'nm_perjanjian') {
    if (b == '') {
        $("#perjanjiandata").hide()
    } else {
        $("#perjanjiandata").show()

        var dt = tampil_data('perjanjian_pengusahaan', 'id_perjanjian_pengusahaan', b);
        combo('bdn_usaha', 'ref_badan_usaha~nm_mitra~id_badan_usaha', 'id_badan_usaha', dt[0].id_badan_usaha,
            dt[0]
            .id_badan_usaha)
        $("#tgl_ak").val(dt[0].tgl_akhir_konsesi)

        var dt_lain = tampil_data('perjanjian_pengusahaan_detail', 'id_perjanjian_pengusahaan', b);
        console.log(dt_lain)

        $("#bang_perjanjian").empty()
        var le_lain = dt_lain.length - 1;
        console.log(le_lain);
        for (let i = 0; i < dt_lain.length; i++) {
            if (dt_lain[i].no_perjanjian != null) {
                var but =
                    `  <button type="button" style="height:35px;font-size: 20px;color:red;" onclick="remove_comboi('${i}')" name="remove" id="${i}" class="btn btn-defaukt btn_remove" ><i class="fa fa-trash"></i></button>`;
                var link = '#'
                var target = ''
                var icon = '<i class="fa fa-times"></i>'
                var title = 'Tidak bisa download File tidak tersedia'
                var filename = 'File Tidak Tersedia'
                if (dt_lain[i].file_perjanjian) {
                    link = `<?php echo base_url(); ?>assets/file_perjanjian/${dt_lain[i].file_perjanjian}`
                    target = '_blank'
                    var icon = '<i class="fa fa-download"></i>'
                    title = 'Download File'
                    filename = dt_lain[i].file_perjanjian.split("~")[1]
                }
                var html = `
            <div class="form-group row " >
              <div class="col-sm-4">
              <input type="text" class="form-control" placeholder=""   value="${dt_lain[i].no_perjanjian}" readonly>
              <input type="hidden" class="form-control" placeholder=""   value="${dt_lain[i].id_tarif_dtl}">
              </div>
              <div class="col-sm-2">
                <input type="date" class="form-control" placeholder=""   value="${dt_lain[i].tgl_perjanjian}" readonly>
              </div>
              <div class="col-sm-3">
                <input type="text" class="form-control" placeholder=""   value="${formatAngkaKeRupiah(dt_lain[i].nilai_investasi)}" onkeyup="formatNumber(this)" readonly>
              </div>
              <div class="col-sm-3">
                      <div class="input-group">                       
                          <input type="text" class="form-control button-yuhu"   placeholder="${filename}"  readonly>
                          <div class="btn btn-tambah browse-button button-yuhu" style="padding:10px 0px;">
                            <a style='color:white' title='${title}' href='${link}' download='${filename}' disabled>
                                <span class="browse-button-text">
                               ${icon} </span>
                            </a>
                            </div> 
                    </div>
                  </div>
              <div class="col-sm-1" style="text-align:left;">
     
              </div>
            </div>
          `;


                $('#bang_perjanjian').append(html);
            }
        }
        if ($(".tglop:last").val() !== '' && $(".tglop:last").val() !== undefined) {
            if (dt[0].tgl_akhir_konsesi) {
                var tgl_ak = dt[0].tgl_akhir_konsesi.split("-")[0];
                var tgl_tarif = $(".tglop:last").val().split("-")[0];
                var hasil = (parseInt(tgl_ak) - parseInt(tgl_tarif)) * 2;
                $("#masa").val(hasil);
            }
        }

    }

    // }
}

function download_data(id_tarif, id_perjanjian) {
    // var dt_lain = tampil_data('perjanjian_pengusahaan_detail', 'id_perjanjian_pengusahaan', id_perjanjian);
    // $("#box-download").empty()
    // for (let i = 0; i < dt_lain.length; i++) {
    //     if (dt_lain[i].no_perjanjian != null) {
    //         var but =
    //             `  <button type="button" style="height:35px;font-size: 20px;color:red;" onclick="remove_comboi('${i}')" name="remove" id="${i}" class="btn btn-defaukt btn_remove" ><i class="fa fa-trash"></i></button>`;
    //         var link = '#'
    //         var target = ''
    //         var icon = '<i class="fa fa-times"></i>'
    //         var title = 'Tidak bisa download File tidak tersedia'
    //         var filename = 'File Tidak Tersedia'
    //         if (dt_lain[i].file_perjanjian) {
    //             link = `<?php echo base_url(); ?>assets/file_perjanjian/${dt_lain[i].file_perjanjian}`
    //             target = '_blank'
    //             var icon = '<i class="fa fa-download"></i>'
    //             title = 'Download File'
    //             filename = dt_lain[i].file_perjanjian.split("~")[1]



    //         }
    //         var html = `
    //         <div class="form-group row " >
    //           <div class="col-sm-4">
    //           <input type="text" class="form-control" placeholder=""   value="${dt_lain[i].no_perjanjian}" readonly>
    //           <input type="hidden" class="form-control" placeholder=""   value="${dt_lain[i].id_tarif_dtl}">
    //           </div>
    //           <div class="col-sm-2">
    //             <input type="date" class="form-control" placeholder=""   value="${dt_lain[i].tgl_perjanjian}" readonly>
    //           </div>
    //           <div class="col-sm-3">
    //             <input type="text" class="form-control" placeholder=""   value="${formatAngkaKeRupiah(dt_lain[i].nilai_investasi)}" onkeyup="formatNumber(this)" readonly>
    //           </div>
    //           <div class="col-sm-3">
    //                   <div class="input-group">                       
    //                       <input type="text" class="form-control button-yuhu"   placeholder="${filename}"  readonly>
    //                       <div class="btn btn-tambah browse-button button-yuhu" style="padding:10px 0px;">
    //                         <a style='color:white' title='${title}' href='${link}' download='${filename}' disabled>
    //                             <span class="browse-button-text">
    //                            ${icon} </span>
    //                         </a>
    //                         </div> 
    //                 </div>
    //               </div>
    //           <div class="col-sm-1" style="text-align:left;">

    //           </div>
    //         </div>
    //       `;


    //         $('#box-download').append(html);
    //     }
    // }

    var dt_lain_2 = tampil_data('tarif_detail', 'id_tarif', id_tarif);
    $("#box-download2").empty()
    for (let i = 0; i < dt_lain_2.length; i++) {
        if (dt_lain_2[i].no_dok_tarif != null) {
            var but =
                `  <button type="button" style="height:35px;font-size: 20px;color:red;" onclick="remove_comboi('${i}')" name="remove" id="${i}" class="btn btn-defaukt btn_remove" ><i class="fa fa-trash"></i></button>`;
            var link = '#'
            var target = ''
            var icon = '<i class="fa fa-times"></i>'
            var title = 'Tidak bisa download File tidak tersedia'
            var filename = 'File Tidak Tersedia'
            if (dt_lain_2[i].file) {
                link = `<?php echo base_url(); ?>assets/file_tarif/${dt_lain_2[i].file}`
                target = '_blank'
                var icon = '<i class="fa fa-download"></i>'
                title = 'Download File'
                filename = dt_lain_2[i].file.split("~")[1]



            }
            var html = `
            <div class="form-group row " >
              <div class="col-sm-4">
              <input type="text" class="form-control" placeholder=""   value="${dt_lain_2[i].no_dok_tarif}" readonly>
              <input type="hidden" class="form-control" placeholder=""   value="${dt_lain_2[i].id_tarif_dtl}">
              </div>
              <div class="col-sm-4">
                <input type="date" class="form-control" placeholder=""   value="${dt_lain_2[i].tgl_tarif}" readonly>
              </div>
              <div class="col-sm-4">
                      <div class="input-group">                       
                          <input type="text" class="form-control button-yuhu"   placeholder="${filename}"  readonly>
                          <div class="btn btn-tambah browse-button button-yuhu" style="padding:10px 0px;">
                            <a style='color:white' title='${title}' href='${link}' download='${filename}' disabled>
                                <span class="browse-button-text">
                               ${icon} </span>
                            </a>
                            </div> 
                    </div>
                  </div>
              <div class="col-sm-1" style="text-align:left;">
     
              </div>
            </div>
          `;


            $('#box-download2').append(html);
        }
    }
    $("#modal-download").modal("show");
}

function tambahdata() {
    $(".form-user input").val('')
    $(".form-user select").val('')
    $(".form-user textarea").val('')

    combo('nm_perjanjian', 'perjanjian_pengusahaan~nm_perjanjian~id_perjanjian_pengusahaan', 'created_by', id_user)
    // combo('nm_perjanjian', 'perjanjian_pengusahaan~nm_perjanjian~id_perjanjian_pengusahaan', 'tahun', tahun_layout)
    // combo('bdn_usaha', 'ref_badan_usaha~nm_mitra~id_badan_usaha')

    $("#bang_lai").empty()
    bang_l()


    $("#modal-tambah-mtl").modal("show");

}

function masamanfaat(a) {
    b = a.value;
    if (b === undefined) {
        b = a
    }
    var tgl_ak = $("#tgl_ak").val().split("-")[0]
    var tgl_tarif = b.split("-")[0]
    var hasil = (parseInt(tgl_ak) - parseInt(tgl_tarif)) * 2
    $("#masa").val(hasil)
}

function edit_data(id) {
    $(".form-user input").val('')
    $(".form-user select").val('')
    var dt = tampil_data('tarif', 'id_tarif', id);
    $("#id").val(dt[0].id_tarif)
    combo('nm_perjanjian', 'perjanjian_pengusahaan~nm_perjanjian~id_perjanjian_pengusahaan', 'created_by', id_user, dt[
        0].id_perjanjian_pengusahaan)

    var dt_lain = tampil_data('tarif_detail', 'id_tarif', id);
    $("#urutan").val(dt_lain.length)
    console.log(dt_lain)
    $(".yuhui").remove()
    $("#bang_lai").empty()
    var le_lain = dt_lain.length - 1;
    console.log(le_lain);
    for (let i = 0; i < dt_lain.length; i++) {
        if (dt_lain[i].no_dok_tarif != null) {
            var but =
                `  <button type="button" style="height:35px;font-size: 20px;color:red;" onclick="remove_comboi('${i}')" name="remove" id="${i}" class="btn btn-defaukt btn_remove" ><i class="fa fa-trash"></i></button>`;
            var yuh = 'yuhui'
            var filename = ''
            if (dt_lain[i].file) {
                filename = dt_lain[i].file.split("~")[1]
            }
            $("#yuhui").remove()
            $("#yuhui" + i).remove()
            var html = `
            <div class="form-group row yuhui" id="yuhui${i}">
              <div class="col-sm-4">
              <input type="text" class="form-control" placeholder="" name="no_dok[]" id="no_dok${i}"  value="${dt_lain[i].no_dok_tarif}">
              <input type="hidden" class="form-control" placeholder="" name="id_detail[]" id="id_detail${i}"  value="${dt_lain[i].id_tarif_detail}">
              </div>
              <div class="col-sm-3">
                <input type="date" class="form-control tglop" placeholder="" id="tgl_op${i}" name="tgl_op[]"  value="${dt_lain[i].tgl_tarif}" onchange="masamanfaat(this)">
              </div>
              <div class="col-sm-4">
                      <div class="input-group">
                            <div class="btn btn-primary browse-button${i} button-yuhu">
                                <span class="browse-button-text${i}">
                                <i class="fa fa-folder-open"></i> </span>
                               <input type="file" class="form-control" style="width:50px;"  placeholder="" id="file_tarif${i}"
                          name="file_tarif[]" >  
                                </div> 
                            <div type="button" class="btn btn-danger clear-button${i} button-yuhu" style="display:none;">
                              <span class="fa fa-times"></span>  
                                        </div>
 
                          <input type="text" class="form-control filename${i} button-yuhu" name="filename[]"  placeholder="Masukan File" readonly>
                          <input type="hidden" class="form-control filenames${i} button-yuhu" name="filenames[]">
                          
                          <span class="input-group-btn">
                           
                          </span>
                    </div>
                  </div>
              <div class="col-sm-1" style="text-align:left;">
              ${but}
              </div>
            </div>
          `;

            $('#bang_lai').append(html);
            if (dt_lain[i].file) {
                $(".filename" + i).val(filename);
                $(".filenames" + i).val(dt_lain[i].file);
                $(".browse-button-text" + i).html('<i class="fa fa-refresh"></i> ');
                $(".clear-button" + i).show();
            }


            $(".browse-button" + i + " input:file").change(function() {
                $("input[id='file_tarif" + i + "']").each(function() {
                    var fileInput = $(this)[0]; // Dapatkan elemen input file
                    var maxFileSizeInBytes = 15 * 1024 * 1024; // Misalnya, batasan ukuran file 15 MB
                    var allowedFileTypes = ["pdf", "xlsx", "xls"]; // Hanya izinkan file PDF

                    var file = fileInput.files[0];
                    var fileSize = file.size;
                    var fileType = file.name.split('.').pop().toLowerCase();

                    // Validasi ukuran file
                    if (fileSize > maxFileSizeInBytes) {
                        alert("Ukuran file terlalu besar. Maksimum 15 MB diizinkan.");
                        return
                    } else if (allowedFileTypes.indexOf(fileType) === -1) {
                        alert("Hanya file PDF yang diizinkan.");
                        return
                    } else {
                        var fileName = $(this).val().split('/').pop().split('\\').pop();
                        $(".filename" + i).val(fileName);
                        $(".browse-button-text" + i).html('<i class="fa fa-refresh"></i> ');
                        $(".clear-button" + i).show();
                    }
                });
            });
            $('.clear-button' + i).click(function() {
                $('.filename' + i).val("");
                $('.filenames' + i).val("");
                $('.clear-button' + i).hide();
                $('.browse-button' + i + ' input:file').val("");
                $(".browse-button-text" + i).html('<i class="fa fa-folder-open"></i> ');
            });
            // var html = ' <div class="form-group row ' + yuh + '" id="yuhui' + i + '"><div class="col-sm-6">' +
            //     '<select id="bl' + i + '" name="bl[]" class=" form-control" data-live-search="true">' +
            //     '<option value="">--Pilih Bangunan Lain-lain--</option>  ' +
            //     ' </select></div>' +
            //     '<div class="col-sm-5">' +
            //     '<input min="0" type="number" class="form-control ' + yuh + '" placeholder="" value="' + dt_lain[i]
            //     .jml_bang_lain + '" id="jml_bang_lain' + i +
            //     '" name="jml_bang_lain[]" onkeypress="return isNumber(event)">' +
            //     '</div>' +
            //     '<div class="col-sm-1">' + but + '</div></div>';
            // $('#bang_lai').append(html);
            // initCombobox('bl' + i, 34);
            // setTimeout(function() {
            //     // alert(dt_lain[i].id_jnsbgnln)
            //     $('#bl' + i).val(dt_lain[i].id_jnsbgnln).selectpicker('refresh');
            // }, 1000);

        }
    }
    to_kolom(dt[0].id_perjanjian_pengusahaan)
    $("#modal-tambah-mtl").modal("show");
}



function combo(divname, table = '', colum = '1', id = '1', set = '') {

    url3 = "<?php echo base_url(); ?>index.php/tarif/tampildata/" + table + "/" + colum + "/" + id +
        "/combobox";
    $('#' + divname).val('').selectpicker('refresh');

    $.get(url3).done(function(data3) {
        jdata3 = JSON.parse(data3);
        //*
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));

        if (divname == 'pem_konsesi') {
            $.each(jdata3, function(i, el) {
                $('#' + divname).append(new Option("(" + el.kode + ") " + el.nama, el.kode));

            });
        } else {
            $.each(jdata3, function(i, el) {
                $('#' + divname).append(new Option(el.nama, el.kode));

            });
        }

        $('#' + divname).val(set).selectpicker('refresh');

    }).fail(function() {
        alert("error");
    }).always(function() {
        // alert("finished");
    });

}

function hanyaAngka(a) {
    var inputValue = $(a).val();
    var newValue = inputValue.replace(/[^0-9,.]/g, ""); // Hanya biarkan angka, koma, dan titik

    // Hapus titik atau koma jika muncul lebih dari sekali
    newValue = newValue.replace(/(\.|,)[,.]+/g, "$1");

    $(a).val(newValue);
}

function formatRupiah(angka) {
    if (angka == '' || angka == null) {
        var formattedAngka = ''
    } else {
        var formattedAngka = "Rp " + angka.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }
    return formattedAngka;
}

function formatNumber(inputElement) {
    let inputValue = inputElement.value;

    // Menghapus semua karakter selain angka dan tanda minus ("-")
    let cleanedValue = inputValue.replace(/[^\d-]/g, '');

    // Membuat format angka dengan -Rp jika angka negatif, Rp jika positif atau nol
    if (cleanedValue.length > 0 && cleanedValue !== '-') {
        if (cleanedValue.charAt(0) === '-') {
            inputElement.value = "-" + formatRupiah(cleanedValue.substring(
                1)); // Hilangkan tanda minus sementara untuk pemformatan
        } else {
            inputElement.value = formatRupiah(cleanedValue);
        }
    } else {
        inputElement.value = cleanedValue; // Biarkan tanda minus atau input kosong
    }
}

function formatAngkaKeRupiah(angka) {
    if (angka == '' || angka == null) {
        return ''
    } else {
        var angkaString = angka.toString(); // Mengonversi angka menjadi string
        var tandaMinus = '';

        if (angkaString[0] === '-') {
            tandaMinus = '-';
            angkaString = angkaString.substring(1); // Hilangkan tanda minus
        }

        var angkaSplit = angkaString.split('.'); // Membagi angka menjadi bagian bulat dan desimal

        var bagianBulat = angkaSplit[0].replace(/\B(?=(\d{3})+(?!\d))/g, '.'); // Memisahkan ribuan dengan titik
        var hasil = tandaMinus + 'Rp ' + bagianBulat;

        if (angkaSplit.length > 1) {
            hasil += '.' + angkaSplit[1]; // Menggunakan titik sebagai pemisah desimal
        }

        return hasil;
    }
}

function hapus_data(id) {
    swal({
            title: "Anda yakin menghapus data ?",
            // text: tex,
            icon: "warning",
            buttons: true,
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
                var url = ''
                url =
                    "<?php echo base_url(); ?>index.php/tarif/delete_data/tarif/id_tarif/" +
                    id;
                $.post(url, {
                    id: id
                }).done(function(data) {
                    if (data == 0) {
                        swal({
                            title: "",
                            text: "Berhasil Menghapus Data",
                            icon: "success",
                            showConfirmButton: false,
                            timer: 3000,
                            type: "success"
                        });
                        tabel.ajax.reload();
                        tabelToExport()

                    }
                })
            } else {
                swal("Anda Tidak Menjadi Menghapus Data");
            }
        });

}
</script>
<script>
var tahun_layout = $("#tahun_layout").val()
var id_user_group = "<?php echo $this->session->userdata('id_user_group'); ?>"
var id_user = "<?php echo $this->session->userdata('id_user'); ?>"
var id_eselon = "<?php echo $this->session->userdata('kode_eselon_1'); ?>"
var id_satker = "<?php echo $this->session->userdata('kode_satker'); ?>"

var kolom_group = ''
var kode_group = ''
var edit_ak = 'none;'
var hapus_ak = 'none;'
if (id_user_group == 3) {
    kode_group = id_satker
    kolom_group = "kode_satker"
    edit_ak = ''
    hapus_ak = ''
} else if (id_user_group == 4) {
    kolom_group = 'kode_eselon_1';
    kode_group = id_eselon;
} else {
    $(".show_eselon").show()
    kolom_group = '1';
    kode_group = 1;
    // if (id_user_group == 1) {
    //     edit_ak = ''
    //     hapus_ak = ''
    // }
}


function htmlTableToExcel() {
    sheetJSExportTable(document.getElementById("tabexport"));
}

function sheetJSExportTable(table, name) {
    var elt = table;
    var wb = XLSX.utils.table_to_book(elt, {
        sheet: "Sheet JS",
        raw: true
    });
    let range = XLSX.utils.decode_range(wb.Sheets['Sheet JS']['!ref']);
    let borderStyle = {
        top: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        },
        bottom: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        },
        left: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        },
        right: {
            style: "thin",
            color: {
                rgb: "000000"
            }
        }
    }
    wb.Sheets['Sheet JS']['!rows'] = [{
        hpx: 50
    }]
    let columnWidths = [{
            wpx: 20
        },
        {
            wpx: 350
        },
        {
            wpx: 200
        },
        {
            wpx: 150
        },
        {
            wpx: 200
        },
        {
            wpx: 300
        },
        {
            wpx: 300
        },
        {
            wpx: 300
        },
        {
            wpx: 300
        },
        {
            wpx: 300
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 150
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
        {
            wpx: 200
        },
        {
            wpx: 100
        },
    ];

    // Assign column widths to the worksheet
    wb.Sheets['Sheet JS']['!cols'] = columnWidths;
    wb.Sheets['Sheet JS']["!merges"].forEach(item => {
        if (item.e.r == item.s.r && item.e.c != item.s.c) {
            // 列合并
            let R = item.s.r;
            let countLength = item.e.c - item.s.c;
            for (let i = item.s.c; i <= item.e.c; i++) {
                let cell = {
                    c: i,
                    r: R
                };
                let cell_ref = XLSX.utils.encode_cell(cell);
                if (!wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref] = {
                        t: "s",
                        v: ""
                    };
                }
            }
        } else if (item.e.c == item.s.c && item.e.r != item.s.r) {
            // 行合并
            let C = item.s.c;
            let countLength = item.e.r - item.s.r;
            for (let i = item.s.r; i <= item.e.r; i++) {
                let cell = {
                    c: C,
                    r: i
                };
                let cell_ref = XLSX.utils.encode_cell(cell);
                if (!wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref] = {
                        t: "s",
                        v: ""
                    };
                }
            }
        }
    })

    const columnsToConvert = [4, 12, 14, 16, 18, 20, 22, 24, 25, 26, 28, 30, 32, 34, 36, 38,
        40
    ]; // Kolom E, M, O, Q, S, U, W, Y, AA, AC, AE, AG, AI, AK, AM, AO


    for (let C = range.s.c; C <= range.e.c; ++C) {
        for (let R = range.s.r; R <= range.e.r; ++R) {
            let cell = {
                c: C,
                r: R
            };
            let cell_ref = XLSX.utils.encode_cell(cell);
            if (columnsToConvert.includes(C) && R >= 4) {
                if (wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref].t = 'n'; // Set type to number
                    wb.Sheets['Sheet JS'][cell_ref].z = '0'; // Set format to no decimals
                }
            }
            if (R == 0 || R == 1 || R == 2 || R == 3) {
                var sis = "15"
                if (R == 1 || R == 2 || R == 3) {
                    sis = "13"
                }
                wb.Sheets['Sheet JS'][cell_ref].s = {
                    alignment: {
                        horizontal: "center",
                        vertical: "center"
                    },
                    font: {
                        name: "",
                        sz: sis,
                        bold: true
                    },
                    border: borderStyle,
                };
            } else {
                if (wb.Sheets['Sheet JS'][cell_ref]) {
                    wb.Sheets['Sheet JS'][cell_ref].s = {
                        font: {
                            name: "",
                            sz: "12"
                        },
                        border: borderStyle,
                    };
                }
            }
        }
    }
    var wopts = {
        bookType: 'xlsx',
        bookSST: false,
        type: 'binary'
    };
    var wbout = XLSXX.write(wb, wopts); // 使用xlsx-style 写入
    saveAs(new Blob([s2ab(wbout)], {
        type: ""
    }), "Perolehan-Mutasi Kuantitas.xlsx")
}

function s2ab(s) {
    var buf = new ArrayBuffer(s.length);
    var view = new Uint8Array(buf);
    for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
    return buf;
}

// $(".browse-button0 input:file").change(function() {
//     $("input[id='file_tarif0']").each(function() {
//         var fileName = $(this).val().split('/').pop().split('\\').pop();
//         $(".filename0").val(fileName);
//         $(".browse-button-text0").html('<i class="fa fa-refresh"></i> ');
//         $(".clear-button0").show();
//         // $('.input-group-btn').html(` <button class="btn btn-primary upload-button" type="button"  style="height:54px !important;">
//         //                             <i class="fa fa-download"></i>
//         //                           </button>`)
//     });
// });

// //actions happening when the button is clicked
// $('.clear-button0').click(function() {
//     $('.filename0').val("");
//     $('.filenames0').val("");
//     $('.clear-button0').hide();
//     $('.browse-button0 input:file').val("");
//     $(".browse-button-text0").html('<i class="fa fa-folder-open"></i> ');
// });

function tabex() {
    var period = $("#periode_filter").val()
    var slo = tampil_data('v_perolehan_mutasi_kuantitas', kolom_group, kode_group);

    var tabs = `<table id="tabexport" border="1px" style="width:4000px">
    <thead>
    <tr>
    <th colspan="42"><center><b> Laporan Kuantitas Aset Konsesi Jasa – Partisipasi Mitra ${period} TA ${tahun_layout}</b></center></th>
    </tr>
    <tr style="text-align:center;">
          <th id="th" class="INFO" rowspan="3">No</th>
          <th id="th" class="INFO" rowspan="3">Nama Perjanjian Konsesi Jasa</th>
          <th id="th" class="INFO" colspan="3" rowspan="1">Perjanjian Konsesi Jasa</th>
          <th id="th" class="INFO" rowspan="3">Badan Usaha</th>
          <th id="th" class="INFO" colspan="2" rowspan="1">SLO/BAST</th>
          <th id="th" class="INFO" colspan="2" rowspan="1">SK TARIF/COD</th>
          <th id="th" class="INFO" rowspan="3">Kode Satker</th>
          <th id="th" class="INFO" rowspan="3">Satuan Kerja</th>
          <th id="th" class="INFO" colspan="10" rowspan="1">Saldo Awal Kuantitas</th>
          <th id="th" class="INFO" colspan="10" rowspan="1">Mutasi ${period}</th>
          <th id="th" class="INFO" colspan="10" rowspan="1">Saldo Akhir Kuantitas</th>

      </tr>
      <tr style="text-align:center;">
          <th id="th" rowspan="2" class="INFO">Nomor Perjanjian</th>
          <th id="th" rowspan="2" class="INFO">Tanggal</th>
          <th id="th" rowspan="2" class="INFO">Nilai Investasi</th>
          <th id="th" rowspan="2" class="INFO">Nomor SLO/BAST</th>
          <th id="th" rowspan="2" class="INFO">Tanggal SLO/BAST</th>
          <th id="th" rowspan="2" class="INFO">Nomor Tarif</th>
          <th id="th" rowspan="2" class="INFO">Tanggal Tarif</th>  

          <th id="th" rowspan="2" class="INFO">Tanah</th>
          <th id="th" rowspan="2" class="INFO">Satuan</th>
          <th id="th" rowspan="2" class="INFO">KDP</th>
          <th id="th" rowspan="2" class="INFO">Satuan</th>
          <th id="th" class="INFO" colspan="6">Nontanah</th>

          <th id="th" rowspan="2" class="INFO">Tanah</th>
          <th id="th" rowspan="2" class="INFO">Satuan</th>
          <th id="th" rowspan="2" class="INFO">KDP</th>
          <th id="th" rowspan="2" class="INFO">Satuan</th>
          <th id="th" class="INFO" colspan="6">Nontanah</th>

          <th id="th" rowspan="2" class="INFO">Tanah</th>
          <th id="th" rowspan="2" class="INFO">Satuan</th>
          <th id="th" rowspan="2" class="INFO">KDP</th>
          <th id="th" rowspan="2" class="INFO">Satuan</th>
          <th id="th" class="INFO" colspan="6">Nontanah</th>
      </tr>
      <tr style="text-align:center;">
          <th id="th" class="INFO">Jalan, Irigasi, dan Jaringan</th>
          <th id="th" class="INFO">Satuan</th>
          <th id="th" class="INFO">Gedung dan Bangunan</th>
          <th id="th" class="INFO">Satuan</th>
          <th id="th" class="INFO">Peralatan dan Mesin</th>
          <th id="th" class="INFO">Satuan</th>

          <th id="th" class="INFO">Jalan, Irigasi, dan Jaringan</th>
          <th id="th" class="INFO">Satuan</th>
          <th id="th" class="INFO">Gedung dan Bangunan</th>
          <th id="th" class="INFO">Satuan</th>
          <th id="th" class="INFO">Peralatan dan Mesin</th>
          <th id="th" class="INFO">Satuan</th>

          <th id="th" class="INFO">Jalan, Irigasi, dan Jaringan</th>
          <th id="th" class="INFO">Satuan</th>
          <th id="th" class="INFO">Gedung dan Bangunan</th>
          <th id="th" class="INFO">Satuan</th>
          <th id="th" class="INFO">Peralatan dan Mesin</th>
          <th id="th" class="INFO">Satuan</th>
      </tr>
    </thead>
    <tbody>`;

    for (var i = 0; i < slo.length; i++) {
        var dtx = get_dataDetailById(slo[i].id_perjanjian_pengusahaan);
        var dtt = get_dataDetailByIdtarif(slo[i].id_tarif);
        var dts = get_dataDetailByIdslo(slo[i].id_slo);

        var rowspan = Math.max(dtx.length, dts.length, dtt.length);
        var tanah_satuan = ''
        var kdp_satuan = ''
        var jij_satuan = ''
        var gedung_satuan = ''
        var peralatan_satuan = ''
        var tanah = ''
        var kdp = ''
        var jij = ''
        var gedung = ''
        var peralatan = ''

        var sa_tanah_s1 = slo[i].sa_tanah_smst1 === null || '' ? 0 : slo[i].sa_tanah_smst1
        var sa_kdp_s1 = slo[i].sa_kdp_smst1 === null || '' ? 0 : slo[i].sa_kdp_smst1
        var sa_jij_s1 = slo[i].sa_jij_smst1 === null || '' ? 0 : slo[i].sa_jij_smst1
        var sa_gedung_s1 = slo[i].sa_gedung_smst1 === null || '' ? 0 : slo[i].sa_gedung_smst1
        var sa_peralatan_s1 = slo[i].sa_peralatan_mesin_smst1 === null || '' ? 0 : slo[i].sa_peralatan_mesin_smst1


        var sa_tanah_s2 = slo[i].sa_tanah_smst2 === null || '' ? 0 : slo[i].sa_tanah_smst2
        var sa_kdp_s2 = slo[i].sa_kdp_smst2 === null || '' ? 0 : slo[i].sa_kdp_smst2
        var sa_jij_s2 = slo[i].sa_jij_smst2 === null || '' ? 0 : slo[i].sa_jij_smst2
        var sa_gedung_s2 = slo[i].sa_gedung_smst2 === null || '' ? 0 : slo[i].sa_gedung_smst2
        var sa_peralatan_s2 = slo[i].sa_peralatan_mesin_smst2 === null || '' ? 0 : slo[i].sa_peralatan_mesin_smst2

        var sa_tanah_tw3 = slo[i].sa_tanah_tw3 === null || '' ? 0 : slo[i].sa_tanah_tw3
        var sa_kdp_tw3 = slo[i].sa_kdp_tw3 === null || '' ? 0 : slo[i].sa_kdp_tw3
        var sa_jij_tw3 = slo[i].sa_jij_tw3 === null || '' ? 0 : slo[i].sa_jij_tw3
        var sa_gedung_tw3 = slo[i].sa_gedung_tw3 === null || '' ? 0 : slo[i].sa_gedung_tw3
        var sa_peralatan_tw3 = slo[i].sa_peralatan_mesin_tw3 === null || '' ? 0 : slo[i].sa_peralatan_mesin_tw3

        var sa_tanah = slo[i].sa_tanah === null || '' ? 0 : slo[i].sa_tanah
        var sa_kdp = slo[i].sa_kdp === null || '' ? 0 : slo[i].sa_kdp
        var sa_jij = slo[i].sa_jij === null || '' ? 0 : slo[i].sa_jij
        var sa_gedung = slo[i].sa_gedung === null || '' ? 0 : slo[i].sa_gedung
        var sa_peralatan = slo[i].sa_peralatan === null || '' ? 0 : slo[i].sa_peralatan

        var hasil_tanah_sa = parseFloat(sa_tanah_s1) + parseFloat(sa_tanah_tw3) + parseFloat(sa_tanah_s2) + parseFloat(
            sa_tanah);
        var hasil_kdp_sa = parseFloat(sa_kdp_s1) + parseFloat(sa_kdp_tw3) + parseFloat(sa_kdp_s2) + parseFloat(sa_kdp);
        var hasil_jij_sa = parseFloat(sa_jij_s1) + parseFloat(sa_jij_tw3) + parseFloat(sa_jij_s2) +
            parseFloat(sa_jij);
        var hasil_gedung_sa = parseFloat(sa_gedung_s1) + parseFloat(sa_gedung_tw3) + parseFloat(sa_gedung_s2) +
            parseFloat(sa_gedung);
        var hasil_peralatan_sa = parseFloat(sa_peralatan_s1) + parseFloat(sa_peralatan_tw3) + parseFloat(
            sa_peralatan_s2) + parseFloat(sa_peralatan);

        if (period == 'Semester I') {
            tanah = parseFloat(slo[i]
                .kuantitas_tanah_smst1 === null || '' ? 0 : slo[i].kuantitas_tanah_smst1)
            kdp = parseFloat(slo[i]
                .kuantitas_kdp_smst1 === null || '' ? 0 : slo[i].kuantitas_kdp_smst1)
            jij = parseFloat(
                slo[i]
                .kuantitas_jij_smst1 === null || '' ? 0 : slo[i].kuantitas_jij_smst1)
            gedung = parseFloat(slo[i]
                .kuantitas_gedung_bangunan_smst1 === null || '' ? 0 : slo[i].kuantitas_gedung_bangunan_smst1)
            peralatan = parseFloat(slo[i]
                .kuantitas_peralatan_mesin_smst1 === null || '' ? 0 : slo[i].kuantitas_peralatan_mesin_smst1)


        } else if (period == 'Triwulan III') {
            tanah = parseFloat(slo[i].kuantitas_tanah_tw3 === null || '' ? 0 : slo[i].kuantitas_tanah_tw3) + parseFloat(
                slo[i]
                .kuantitas_tanah_smst1 === null || '' ? 0 : slo[i].kuantitas_tanah_smst1)

            kdp = parseFloat(slo[i].kuantitas_kdp_tw3 === null || '' ? 0 : slo[i].kuantitas_kdp_tw3) + parseFloat(slo[i]
                .kuantitas_kdp_smst1 === null || '' ? 0 : slo[i].kuantitas_kdp_smst1)

            jij = parseFloat(slo[i].kuantitas_jij_tw3 === null || '' ? 0 : slo[i].kuantitas_jij_tw3) +
                parseFloat(
                    slo[i]
                    .kuantitas_jij_smst1 === null || '' ? 0 : slo[i].kuantitas_jij_smst1)

            gedung = parseFloat(slo[i].kuantitas_gedung_bangunan_tw3 === null || '' ? 0 : slo[i]
                .kuantitas_gedung_bangunan_tw3) + parseFloat(slo[i]
                .kuantitas_gedung_bangunan_smst1 === null || '' ? 0 : slo[i].kuantitas_gedung_bangunan_smst1)

            peralatan = parseFloat(slo[i].kuantitas_peralatan_mesin_tw3 === null || '' ? 0 : slo[i]
                .kuantitas_peralatan_mesin_tw3) + parseFloat(slo[i]
                .kuantitas_peralatan_mesin_smst1 === null || '' ? 0 : slo[i].kuantitas_peralatan_mesin_smst1)


        } else if (period == 'Tahunan Unaudited') {

            tanah = parseFloat(slo[i].kuantitas_tanah_tw3 === null || '' ? 0 : slo[i].kuantitas_tanah_tw3) + parseFloat(
                slo[i]
                .kuantitas_tanah_smst1 === null || '' ? 0 : slo[i].kuantitas_tanah_smst1) + parseFloat(slo[i]
                .kuantitas_tanah_smst2 === null || '' ? 0 : slo[i].kuantitas_tanah_smst2)

            kdp = parseFloat(slo[i].kuantitas_kdp_tw3 === null || '' ? 0 : slo[i].kuantitas_kdp_tw3) + parseFloat(slo[i]
                .kuantitas_kdp_smst1 === null || '' ? 0 : slo[i].kuantitas_kdp_smst1) + parseFloat(slo[i]
                .kuantitas_kdp_smst2 === null || '' ? 0 : slo[i].kuantitas_kdp_smst2)

            jij = parseFloat(slo[i].kuantitas_jij_tw3 === null || '' ? 0 : slo[i].kuantitas_jij_tw3) +
                parseFloat(
                    slo[i]
                    .kuantitas_jij_smst1 === null || '' ? 0 : slo[i].kuantitas_jij_smst1) + parseFloat(slo[i]
                    .kuantitas_jij_smst2 === null || '' ? 0 : slo[i].kuantitas_jij_smst2)

            gedung = parseFloat(slo[i].kuantitas_gedung_bangunan_tw3 === null || '' ? 0 : slo[i]
                    .kuantitas_gedung_bangunan_tw3) + parseFloat(slo[i]
                    .kuantitas_gedung_bangunan_smst1 === null || '' ? 0 : slo[i].kuantitas_gedung_bangunan_smst1) +
                parseFloat(slo[i]
                    .kuantitas_gedung_bangunan_smst2 === null || '' ? 0 : slo[i].kuantitas_gedung_bangunan_smst2)

            peralatan = parseFloat(slo[i].kuantitas_peralatan_mesin_tw3 === null || '' ? 0 : slo[i]
                    .kuantitas_peralatan_mesin_tw3) + parseFloat(slo[i]
                    .kuantitas_peralatan_mesin_smst1 === null || '' ? 0 : slo[i].kuantitas_peralatan_mesin_smst1) +
                parseFloat(
                    slo[i]
                    .kuantitas_peralatan_mesin_smst2 === null || '' ? 0 : slo[i].kuantitas_peralatan_mesin_smst2)

        } else if (period == 'Tahunan Audited') {
            tanah = parseFloat(slo[i].kuantitas_tanah_tw3 === null || '' ? 0 : slo[i].kuantitas_tanah_tw3) + parseFloat(
                slo[i]
                .kuantitas_tanah_smst1 === null || '' ? 0 : slo[i].kuantitas_tanah_smst1) + parseFloat(slo[i]
                .kuantitas_tanah_smst2 === null || '' ? 0 : slo[i].kuantitas_tanah_smst2) + parseFloat(slo[i]
                .kuantitas_tanah_audited === null || '' ? 0 : slo[i].kuantitas_tanah_audited)

            kdp = parseFloat(slo[i].kuantitas_kdp_tw3 === null || '' ? 0 : slo[i].kuantitas_kdp_tw3) + parseFloat(slo[i]
                .kuantitas_kdp_smst1 === null || '' ? 0 : slo[i].kuantitas_kdp_smst1) + parseFloat(slo[i]
                .kuantitas_kdp_smst2 === null || '' ? 0 : slo[i].kuantitas_kdp_smst2) + parseFloat(slo[i]
                .kuantitas_kdp_audited === null || '' ? 0 : slo[i].kuantitas_kdp_audited)

            jij = parseFloat(slo[i].kuantitas_jij_tw3 === null || '' ? 0 : slo[i].kuantitas_jij_tw3) +
                parseFloat(
                    slo[i]
                    .kuantitas_jij_smst1 === null || '' ? 0 : slo[i].kuantitas_jij_smst1) + parseFloat(slo[i]
                    .kuantitas_jij_smst2 === null || '' ? 0 : slo[i].kuantitas_jij_smst2) + parseFloat(slo[i]
                    .kuantitas_jij_audited === null || '' ? 0 : slo[i].kuantitas_jij_audited)

            gedung = parseFloat(slo[i].kuantitas_gedung_bangunan_tw3 === null || '' ? 0 : slo[i]
                    .kuantitas_gedung_bangunan_tw3) + parseFloat(slo[i]
                    .kuantitas_gedung_bangunan_smst1 === null || '' ? 0 : slo[i].kuantitas_gedung_bangunan_smst1) +
                parseFloat(slo[i]
                    .kuantitas_gedung_bangunan_smst2 === null || '' ? 0 : slo[i].kuantitas_gedung_bangunan_smst2) +
                parseFloat(slo[i]
                    .kuantitas_gedung_bangunan_audited === null || '' ? 0 : slo[i].kuantitas_gedung_bangunan_audited)

            peralatan = parseFloat(slo[i].kuantitas_peralatan_mesin_tw3 === null || '' ? 0 : slo[i]
                    .kuantitas_peralatan_mesin_tw3) + parseFloat(slo[i]
                    .kuantitas_peralatan_mesin_smst1 === null || '' ? 0 : slo[i].kuantitas_peralatan_mesin_smst1) +
                parseFloat(
                    slo[i]
                    .kuantitas_peralatan_mesin_smst2 === null || '' ? 0 : slo[i].kuantitas_peralatan_mesin_smst2) +
                parseFloat(
                    slo[i]
                    .kuantitas_peralatan_mesin_audited === null || '' ? 0 : slo[i].kuantitas_peralatan_mesin_audited)




        }

        var tanah_has = hasil_tanah_sa + tanah

        var kdp_has = hasil_kdp_sa + kdp

        var jij_has = hasil_jij_sa + jij

        var gedung_has = hasil_gedung_sa + gedung

        var peralatan_has = hasil_peralatan_sa + peralatan


        var sa = slo[i].sa_tanah_satuan;
        var sa1 = slo[i].sa_tanah_smst1_satuan;
        var sa2 = slo[i].sa_tanah_tw3_satuan;
        var sa3 = slo[i].sa_tanah_smst2_satuan;
        var sa_tanah_satuan = sa3 || sa2 || sa1 || sa || '';


        var tanah_sa = slo[i].satuan_tanah_audited;
        var tanah_sa1 = slo[i].satuan_tanah_smst1;
        var tanah_sa2 = slo[i].satuan_tanah_tw3;
        var tanah_sa3 = slo[i].satuan_tanah_smst2;
        var hasil_tanah = tanah_sa3 || tanah_sa2 || tanah_sa1 || tanah_sa || '';


        var sak_tanah_satuan = hasil_tanah || sa_tanah_satuan || '';

        var sa = slo[i].sa_kdp_satuan;
        var sa1 = slo[i].sa_kdp_smst1_satuan;
        var sa2 = slo[i].sa_kdp_tw3_satuan;
        var sa3 = slo[i].sa_kdp_smst2_satuan;
        var sa_kdp_satuan = sa3 || sa2 || sa1 || sa || '';


        var kdp_sa = slo[i].satuan_kdp_audited;
        var kdp_sa1 = slo[i].satuan_kdp_smst1;
        var kdp_sa2 = slo[i].satuan_kdp_tw3;
        var kdp_sa3 = slo[i].satuan_kdp_smst2;
        var hasil_kdp = kdp_sa3 || kdp_sa2 || kdp_sa1 || kdp_sa || '';

        var sak_kdp_satuan = hasil_kdp || sa_kdp_satuan || '';


        var sa = slo[i].sa_jij_satuan;
        var sa1 = slo[i].sa_jij_smst1_satuan;
        var sa2 = slo[i].sa_jij_tw3_satuan;
        var sa3 = slo[i].sa_jij_smst2_satuan;
        var sa_jij_satuan = sa3 || sa2 || sa1 || sa || '';

        var jij_sa = slo[i].satuan_jij_audited;
        var jij_sa1 = slo[i].satuan_jij_smst1;
        var jij_sa2 = slo[i].satuan_jij_tw3;
        var jij_sa3 = slo[i].satuan_jij_smst2;
        var hasil_jij = jij_sa3 || jij_sa2 || jij_sa1 || jij_sa || '';

        var sak_jij_satuan = hasil_jij || sa_jij_satuan || '';


        var sa = slo[i].sa_gedung_satuan;
        var sa1 = slo[i].sa_gedung_smst1_satuan;
        var sa2 = slo[i].sa_gedung_tw3_satuan;
        var sa3 = slo[i].sa_gedung_smst2_satuan;
        var sa_gedung_satuan = sa3 || sa2 || sa1 || sa || '';

        var gedung_bangunan_sa = slo[i].satuan_gedung_bangunan_audited;
        var gedung_bangunan_sa1 = slo[i].satuan_gedung_bangunan_smst1;
        var gedung_bangunan_sa2 = slo[i].satuan_gedung_bangunan_tw3;
        var gedung_bangunan_sa3 = slo[i].satuan_gedung_bangunan_smst2;
        var hasil_gedung_bangunan = gedung_bangunan_sa3 || gedung_bangunan_sa2 || gedung_bangunan_sa1 ||
            gedung_bangunan_sa || '';

        var sak_gedung_satuan = hasil_gedung_bangunan || sa_gedung_satuan || '';


        var sa = slo[i].sa_peralatan_mesin_satuan;
        var sa1 = slo[i].sa_peralatan_mesin_smst1_satuan;
        var sa2 = slo[i].sa_peralatan_mesin_tw3_satuan;
        var sa3 = slo[i].sa_peralatan_mesin_smst2_satuan;
        var sa_peralatan_mesin_satuan = sa3 || sa2 || sa1 || sa || '';

        var peralatan_mesin_sa = slo[i].satuan_peralatan_mesin_audited;
        var peralatan_mesin_sa1 = slo[i].satuan_peralatan_mesin_smst1;
        var peralatan_mesin_sa2 = slo[i].satuan_peralatan_mesin_tw3;
        var peralatan_mesin_sa3 = slo[i].satuan_peralatan_mesin_smst2;
        var hasil_peralatan_mesin = peralatan_mesin_sa3 || peralatan_mesin_sa2 || peralatan_mesin_sa1 ||
            peralatan_mesin_sa || '';

        var sak_peralatan_satuan = hasil_peralatan_mesin || sa_peralatan_mesin_satuan || '';



        // tanah_has = parseInt(sa_tanah) + parseInt(tanah_has)
        // kdp_has = parseInt(sa_kdp) + parseInt(kdp_has)
        // jij_has = parseInt(sa_jij) + parseInt(jij_has)
        // gedung_has = parseInt(sa_gedung) + parseInt(gedung_has)
        // peralatan_has = parseInt(sa_peralatan) + parseInt(peralatan_has)


        for (let j = 0; j < rowspan; j++) {
            var row = '<tr>';

            if (j === 0 || (i === 0 && rowspan === 1)) {
                row +=
                    `<td  class="xl68"   rowspan="${rowspan}">${i + 1}</td>`; // Menambahkan nomor urut
                row +=
                    `<td  class="xl68"   rowspan="${rowspan}">${slo[i].nm_perjanjian}</td>`;
            }

            if (j < dtx.length) {
                row +=
                    `<td  class="xl68" align="left"  >${dtx[j].no_perjanjian}</td>`;
                row +=
                    `<td class="xl68"  >${dtx[j].tgl_perjanjian}</td>`;
                row +=
                    `<td class="xl69"  align="right"   >${dtx[j].nilai_investasi === null ? '' : dtx[j].nilai_investasi}</td>`;
            } else {
                row +=
                    `<td  class="xl68"   ></td><td  class="xl68"   ></td><td  class="xl68"   ></td>`; // Sel kosong jika tidak ada data dari dtx
            }


            if (j === 0 || (i === 0 && rowspan === 1)) {
                row +=
                    `<td  class="xl68"   rowspan="${rowspan}">${slo[i].nm_mitra}</td>`;
            }
            if (j < dts.length) {
                row +=
                    `<td class="xl68" align="left"  >${dts[j].no_dok_slo}</td>`;
                row +=
                    `<td class="xl68"  >${dts[j].tgl_slo === null ? '' : dts[j].tgl_slo}</td>`;
            } else {
                row +=
                    `<td  class="xl68"   ></td><td  class="xl68"   ></td>   `; // Sel kosong jika tidak ada data dari dts
            }
            if (j < dtt.length) {
                row +=
                    `<td class="xl68" align="left"  >${dtt[j].no_dok_tarif}</td>`;
                row +=
                    `<td class="xl68"  >${dtt[j].tgl_tarif === null ? '' : dtt[j].tgl_tarif}</td>`;
            } else {
                row +=
                    `<td  class="xl68"   ></td><td  class="xl68"   ></td>   `; // Sel kosong jika tidak ada data dari dts
            }
            if (j === 0 || (i === 0 && rowspan === 1)) {
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${slo[i].pemberi_konsesi === null ? '' : slo[i].pemberi_konsesi}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${slo[i].nm_pemberi_konsesi === null ? '' : slo[i].nm_pemberi_konsesi}</td>`;

                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${hasil_tanah_sa === 0 ? 0 : hasil_tanah_sa}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${sa_tanah_satuan === null ? '' : sa_tanah_satuan}</td>`;

                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${hasil_kdp_sa === 0 ? 0 : hasil_kdp_sa}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${sa_kdp_satuan === null ? '' : sa_kdp_satuan}</td>`;

                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${hasil_jij_sa === 0 ? 0 : hasil_jij_sa}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${sa_jij_satuan === null ? '' : sa_jij_satuan}</td>`;

                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${hasil_gedung_sa === 0 ? 0 : hasil_gedung_sa}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${sa_gedung_satuan === null ? '' : sa_gedung_satuan}</td>`;

                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${hasil_peralatan_sa === 0 ? 0 : hasil_peralatan_sa}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${sa_peralatan_mesin_satuan === null ? '' : sa_peralatan_mesin_satuan}</td>`;




                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${tanah === 0 ? 0 : tanah}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${hasil_tanah === null ? '' : hasil_tanah}</td>`;

                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${kdp === 0 ? 0 : kdp}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${hasil_kdp === null ? '' : hasil_kdp}</td>`;

                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${jij === 0 ? 0 : jij}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${hasil_jij === null ? '' : hasil_jij}</td>`;

                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${gedung === 0 ? 0 : gedung}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${hasil_gedung_bangunan === null ? '' : hasil_gedung_bangunan}</td>`;

                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${peralatan === 0 ? 0 : peralatan}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${hasil_peralatan_mesin === null ? '' : hasil_peralatan_mesin}</td>`;


                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${tanah_has === 0 ? 0 : tanah_has}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${sak_tanah_satuan === null ? '' : sak_tanah_satuan}</td>`;


                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${kdp_has === 0 ? 0 : kdp_has}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${sak_kdp_satuan === null ? '' : sak_kdp_satuan}</td>`;


                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${jij_has === null ? 0 : jij_has}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${sak_jij_satuan === null ? '' : sak_jij_satuan}</td>`;

                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${gedung_has === null ? 0 : gedung_has}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${sak_gedung_satuan === null ? '' : sak_gedung_satuan}</td>`;

                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${peralatan_has === null ? 0 : peralatan_has}</td>`;
                row +=
                    `<td class="xl68"    rowspan="${rowspan}">${sak_peralatan_satuan === null ? '' : sak_peralatan_satuan}</td>`;


            }
            row += '</tr>';
            tabs += row;
        }
    }

    tabs += '</tbody></table>';
    $('#tes').html(tabs);
}

$(document).ready(function() {
    var dt = tampil_data('v_perjanjian_pengusahaan_detail', kolom_group, kode_group);
    var json_data = JSON.stringify(dt);
    $("#simpandatadetailperjanjian").val(json_data)

    var dt2 = tampil_data('v_tarif_detail', kolom_group + '~tahun', kode_group + '~' + tahun_layout);
    var json_data2 = JSON.stringify(dt2);
    $("#simpandatadetailtarif").val(json_data2)

    var dt3 = tampil_data('v_slo_detail', kolom_group + '~tahun', kode_group + '~' + tahun_layout);
    var json_data3 = JSON.stringify(dt3);
    $("#simpandatadetailslo").val(json_data3)


    combo('jenis_p_filter', 'ref_jenis_pengusahaan~nm_jenis_pengusahaan~id_jenis_pengusahaan')
    combo('nama_p_filter', 'v_perolehan_mutasi_kuantitas~nm_perjanjian~nm_perjanjian', kolom_group, kode_group)
    combo('bdn_usaha_filter', 'ref_badan_usaha~nm_mitra~id_badan_usaha')
    combo('pem_konsesi_filter', 'ref_satker~nm_satker~kode_satker', kolom_group, kode_group)
    combo('pulau_filter', 'ref_pulau~nm_pulau~id_pulau')
    combo('eselon_filter', 'ref_eselon_1~nm_unit_eselon_1~kode_eselon_1')


    if (tahun_layout == '') {
        swal({
            title: "Harap pilih Tahun Anggaran",
            // text: tex,
            icon: "warning",
            timer: 2000,
            dangerMode: true,
        });
        return
    }
    $("#submit").submit(function(e) {
        e.preventDefault()
        // alert("asd");
        var file_lk = $(".filenames0").val()
        var file_kertas = $(".filenames1").val()

        // if (file_lk == '' || file_kertas == '') {
        //     swal({
        //         title: "Harap Masukan",
        //         text: " File LK dan File BA Rekonsiliasi   ",
        //         icon: "warning",
        //         timer: 3000,
        //         showConfirmButton: true,
        //         dangerMode: true,
        //     })
        //     return
        // }
        var data = new FormData($('.form-user')[
            0]); // Membuat objek FormData dari form dengan kelas .form-user

        var tahun_layout = $("#tahun_layout").val();
        data.append('tahun_layout', tahun_layout); // Menambahkan tahun_layout ke objek FormData


        $.ajax({
            type: 'POST',
            url: "<?php echo base_url('index.php/perolehan_mutasi_kuantitas/insert_data'); ?>",
            data: data,
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            success: function(data) {
                var ops = ''
                if (data.split('_')[1] == 'insert') {
                    ops = 'Menambah';
                } else {
                    ops = 'Merubah';
                }
                if (data.split('_')[0] == 0) {
                    swal({
                        title: "",
                        text: "Berhasil " + ops + " Data",
                        showConfirmButton: false,
                        timer: 1000,
                        type: "success"
                    });
                    tabel.ajax.reload();
                    tabex()

                    $("#modal-tambah-mtl").modal("hide");
                } else {
                    swal({
                        title: "",
                        text: data.split('_')[1],
                        showConfirmButton: false,
                        timer: 1000,
                        type: "warning"
                    });
                }

            }
        });
    });
    user()
    tabex()

})

function ubahFormatTanggal(tanggal) {
    if (tanggal == null || tanggal == '') {
        return ''
    }
    // Membagi tanggal menjadi bagian-bagian
    var tanggalBagian = tanggal.split('-');

    // Menyusun ulang tanggal dalam format yang diinginkan
    var tanggalBaru = tanggalBagian[2] + '-' + tanggalBagian[1] + '-' + tanggalBagian[0];

    return tanggalBaru;
}

function bang_l(file1 = '', file2 = '') {
    var ii = 0;
    var iii = 1;
    var filename1 = ''
    var filename2 = ''
    if (file1) {
        filename1 = file1.split("~")[1]
    }

    if (file2) {
        filename2 = file2.split("~")[1]
    }

    var html = `
            <div class="form-group row" id="yuhui${ii}">
              
              <div class="col-sm-6">
                      <div class="input-group">
                            <div class="btn btn-primary browse-button${ii} button-yuhu">
                                <span class="browse-button-text${ii}">
                                <i class="fa fa-folder-open"></i> </span>
                               <input type="file" class="form-control" style="width:50px;"  placeholder="" id="file_lk${ii}"
                          name="file_lk"  >  
                                </div> 
                            <div type="button" class="btn btn-danger clear-button${ii} button-yuhu" style="display:none;">
                              <span class="fa fa-times"></span>  
                                        </div>
 
                          <input type="text" class="form-control filename${ii} button-yuhu" name="filename_lk"  placeholder="Masukan File" readonly>
                          <input type="hidden" class="form-control filenames${ii} button-yuhu" name="filenames_lk">
                          
                          <span class="input-group-btn">
                           
                          </span>
                    </div>
                  </div>
                  <div class="col-sm-6">
                      <div class="input-group">
                            <div class="btn btn-primary browse-button${iii} button-yuhu">
                                <span class="browse-button-text${iii}">
                                <i class="fa fa-folder-open"></i> </span>
                               <input type="file" class="form-control" style="width:50px;"  placeholder="" id="file_kertas${iii}"
                          name="file_kertas" >  
                                </div> 
                            <div type="button" class="btn btn-danger clear-button${iii} button-yuhu" style="display:none;">
                              <span class="fa fa-times"></span>  
                                        </div>
 
                          <input type="text" class="form-control filename${iii} button-yuhu" name="filename_kertas"  placeholder="Masukan File" readonly>
                          <input type="hidden" class="form-control filenames${iii} button-yuhu" name="filenames_kertas">
                          
                          <span class="input-group-btn">
                           
                          </span>
                    </div>
                  </div>
            </div>
          `;
    $('#bang_lai').append(html);

    if (file1) {
        $(".filename" + ii).val(filename1);
        $(".filenames" + ii).val(file1);
        $(".browse-button-text" + ii).html('<i class="fa fa-refresh"></i> ');
        $(".clear-button" + ii).show();
    }
    if (file2) {
        $(".filename" + iii).val(filename2);
        $(".filenames" + iii).val(file2);
        $(".browse-button-text" + iii).html('<i class="fa fa-refresh"></i> ');
        $(".clear-button" + iii).show();
    }
    $(".browse-button" + ii + " input:file").change(function() {
        $("input[id='file_lk" + ii + "']").each(function() {
            var fileInput = $(this)[0]; // Dapatkan elemen input file
            var maxFileSizeInBytes = 15 * 1024 * 1024; // Misalnya, batasan ukuran file 15 MB
            var allowedFileTypes = ["pdf"]; // Hanya izinkan file PDF

            var file = fileInput.files[0];
            var fileSize = file.size;
            var fileType = file.name.split('.').pop().toLowerCase();

            // Validasi ukuran file
            if (fileSize > maxFileSizeInBytes) {
                alert("Ukuran file terlalu besar. Maksimum 15 MB diizinkan.");
                $("#file_lk" + ii).val('');
                return
            } else if (allowedFileTypes.indexOf(fileType) === -1) {
                alert("Hanya file PDF,XLS dan XLSX yang diizinkan.");
                $("#file_lk" + ii).val('');
                return
            } else {
                var fileName = $(this).val().split('/').pop().split('\\').pop();
                $(".filename" + ii).val(fileName);
                $(".filenames" + ii).val(fileName);
                $(".browse-button-text" + ii).html('<i class="fa fa-refresh"></i> ');
                $(".clear-button" + ii).show();
            }
        });
    });

    //actions happening when the button is clicked
    $('.clear-button' + ii).click(function() {
        $('.filename' + ii).val("");
        $('.filenames' + ii).val("");
        $('.clear-button' + ii).hide();
        $('.browse-button' + ii + ' input:file').val("");
        $(".browse-button-text" + ii).html('<i class="fa fa-folder-open"></i> ');
    });

    $(".browse-button" + iii + " input:file").change(function() {
        $("input[id='file_kertas" + iii + "']").each(function() {
            var fileInput = $(this)[0]; // Dapatkan elemen input file
            var maxFileSizeInBytes = 15 * 1024 * 1024; // Misalnya, batasan ukuran file 15 MB
            var allowedFileTypes = ["pdf"]; // Hanya izinkan file PDF

            var file = fileInput.files[0];
            var fileSize = file.size;
            var fileType = file.name.split('.').pop().toLowerCase();

            // Validasi ukuran file
            if (fileSize > maxFileSizeInBytes) {
                alert("Ukuran file terlalu besar. Maksimum 15 MB diizinkan.");
                $("#file_kertas" + iii).val('');
                return
            } else if (allowedFileTypes.indexOf(fileType) === -1) {
                alert("Hanya file PDF yang diizinkan.");
                $("#file_kertas" + iii).val('');
                return
            } else {
                var fileName = $(this).val().split('/').pop().split('\\').pop();
                $(".filename" + iii).val(fileName);
                $(".filenames" + iii).val(fileName);
                $(".browse-button-text" + iii).html('<i class="fa fa-refresh"></i> ');
                $(".clear-button" + iii).show();
            }
        });
    });

    //actions happening when the button is clicked
    $('.clear-button' + iii).click(function() {
        $('.filename' + iii).val("");
        $('.filenames' + iii).val("");
        $('.clear-button' + iii).hide();
        $('.browse-button' + iii + ' input:file').val("");
        $(".browse-button-text" + iii).html('<i class="fa fa-folder-open"></i> ');
    });

}

function upload_div(dt) {
    for (let ii = 0; ii < dt + 1; ii++) {
        $(".browse-button" + ii + " input:file").change(function() {
            $("input[id='file_tarif" + ii + "']").each(function() {
                var fileInput = $(this)[0]; // Dapatkan elemen input file
                var maxFileSizeInBytes = 15 * 1024 * 1024; // Misalnya, batasan ukuran file 15 MB
                var allowedFileTypes = ["pdf", "xlsx", "xls"]; // Hanya izinkan file PDF

                var file = fileInput.files[0];
                var fileSize = file.size;
                var fileType = file.name.split('.').pop().toLowerCase();

                // Validasi ukuran file
                if (fileSize > maxFileSizeInBytes) {
                    alert("Ukuran file terlalu besar. Maksimum 15 MB diizinkan.");
                    return
                } else if (allowedFileTypes.indexOf(fileType) === -1) {
                    alert("Hanya file PDF,XLS dan XLSX yang diizinkan.");
                    return
                } else {
                    var fileName = $(this).val().split('/').pop().split('\\').pop();
                    $(".filename" + ii).val(fileName);
                    $(".filenames" + ii).val(fileName);
                    $(".browse-button-text" + ii).html('<i class="fa fa-refresh"></i> ');
                    $(".clear-button" + ii).show();
                }
            });
        });

        //actions happening when the button is clicked
        $('.clear-button' + ii).click(function() {
            $('.filename' + ii).val("");
            $('.filenames' + ii).val("");
            $('.clear-button' + ii).hide();
            $('.browse-button' + ii + ' input:file').val("");
            $(".browse-button-text" + ii).html('<i class="fa fa-folder-open"></i> ');
        });
    }
}


function remove_comboi(x) {
    // alert("ASDASd")
    // alert(x.value)
    // var button_id = $(this).attr("id");   
    $('#yuhui' + x + '').remove();
    var tgl_ak = $("#tgl_ak").val().split("-")[0]
    var tgl_tarif = $(".tglop:last").val().split("-")[0]
    var hasil = (parseInt(tgl_ak) - parseInt(tgl_tarif)) * 2
    $("#masa").val(hasil)
}

function reset() {
    // $("#form-filter select").val('')
    $('#nama_p_filter').val('').selectpicker('refresh');
    $('#bdn_usaha_filter').val('').selectpicker('refresh');
    $('#pem_konsesi_filter').val('').selectpicker('refresh');
    $('#pulau_filter').val('').selectpicker('refresh');
    $('#jenis_p_filter').val('').selectpicker('refresh');
    $('#eselon_filter').val('').selectpicker('refresh');
    to_filter('')
}

function close_filter(element) {

    var yuhu = $("#styleSelector").attr('class');
    $(".selector-toggle").hide()
    if (yuhu == '') {
        $(".selector-toggle").show()
    }

    $('#styleSelector').toggleClass('open');
}

function to_filter(a) {
    var nama = $("#nama_p_filter").val()
    var bdn = $("#bdn_usaha_filter").val()
    var pem = $("#pem_konsesi_filter").val()
    var pulau = $("#pulau_filter").val()
    var jenis = $("#jenis_p_filter").val()
    var eselon = $("#eselon_filter").val()
    var period = $("#periode_filter").val()
    user(nama, bdn, pem, pulau, jenis, eselon, period)
    tabex()
}

function user(nama = '', bdn = '', pem = '', pulau = '', jenis = '', eselon = '', period = 'Semester I') {

    var tab = $('#table-users').DataTable();
    tab.destroy()
    data_user(nama, bdn, pem, pulau, jenis, eselon, period)
    var tom =
        `<button style="margin-top: 8px;" class="btn btn-default buttons-excel buttons-html5" tabindex="0" aria-controls="table-users" type="button"  onclick="htmlTableToExcel('xlsx')"><span></span></button>`
    $(".tombolTambah").append(tom)

    var toms = `
    <button class="btn btn-tambah" id="tambah" onclick="tambahdata()" style='display:${edit_ak}'>
                            <i class="fa fa-plus"></i> Perolehan / Mutasi Kuantitas
                        </button>`
    $(".tombolTambah").append(toms)


    var tomF = `
<button style="margin-top: 8px;"  class="btn btn-tambah" id="tambah" onclick="close_filter('')">
                                        <i class="fa fa-filter"></i>
                                    </button><br>
                    <br>`
    $(".tombolFilter").html(tomF)
    var tomP = `
<select class="form-control" name="" id="periode_filter" onchange="to_filter(this)" style="margin-top: 12px;font-family: arial;">
                                <option value ="Semester I">Semester I</option>
                                <option value ="Triwulan III">Triwulan III</option>
                                <option value ="Tahunan Unaudited">Tahunan Unaudited</option>
                                <option value ="Tahunan Audited">Tahunan Audited</option>
                                </select>`
    $(".FilterPeriode").html(tomP)

    $("#periode_filter").val(period)


}

function formatRupiah(angka) {
    if (angka === null) {
        return ''; // Mengembalikan string kosong jika angka adalah null
    }

    var number_string = angka.toString();
    var split = number_string.split(',');
    var sisa = split[0].length % 3;
    var rupiah = split[0].substr(0, sisa);
    var ribuan = split[0].substr(sisa).match(/\d{3}/gi);

    if (ribuan) {
        var separator = sisa ? '.' : '';
        rupiah += separator + ribuan.join('.');
    }

    rupiah = split[1] !== undefined ? rupiah + ',' + split[1] : rupiah;
    return "" + rupiah;
}

function formatNumber(angka) {
    if (angka === null || angka == '') {
        return '0'; // Mengembalikan string kosong jika angka adalah null
    }

    var number_string = angka.toString();
    var split = number_string.split(',');
    var sisa = split[0].length % 3;
    var rupiah = split[0].substr(0, sisa);
    var ribuan = split[0].substr(sisa).match(/\d{3}/gi);

    if (ribuan) {
        var separator = sisa ? '.' : '';
        rupiah += separator + ribuan.join('.');
    }

    rupiah = split[1] !== undefined ? rupiah + ',' + split[1] : rupiah;
    return rupiah;
}


function get_dataDetailById(id) {
    if (id == null) {
        return []
    }
    var dt = $("#simpandatadetailperjanjian").val()
    var js = JSON.parse(dt)
    // Mencari semua elemen dalam array yang memiliki ID yang sesuai
    var data = js.filter(function(element) {
        return element.id_perjanjian_pengusahaan === id.toString();
    });

    // Jika data ditemukan, tampilkan semua data
    return data
}

function get_dataDetailByIdtarif(id) {
    if (id == null) {
        return []
    }
    var dt = $("#simpandatadetailtarif").val()
    var js = JSON.parse(dt)

    // Mencari semua elemen dalam array yang memiliki ID yang sesuai
    var data = js.filter(function(element) {
        return element.id_tarif === id.toString();
    });

    // Jika data ditemukan, tampilkan semua data
    return data
}


function get_dataDetailByIdslo(id) {
    if (id == null) {
        return []
    }
    var dt = $("#simpandatadetailslo").val()
    var js = JSON.parse(dt)

    // Mencari semua elemen dalam array yang memiliki ID yang sesuai
    var data = js.filter(function(element) {
        return element.id_slo === id.toString();
    });

    // Jika data ditemukan, tampilkan semua data
    return data
}

var tabel = null


function data_user(nama, bdn, pem, pulau, jenis, eselon, period) {
    // get_dataDetailById("314");
    tabel = $('#table-users').DataTable({
        "processing": true,
        "serverSide": true,
        "ordering": true, // Set true agar bisa di sorting
        "bAutoWidth": false,
        "order": [
            [0, 'asc']
        ], // Default sortingnya berdasarkan kolom / field ke 0 (paling pertama)
        "ajax": {
            "url": "<?php echo base_url('index.php/perolehan_mutasi_kuantitas/ssp') ?>", // URL file untuk proses select datanya
            "type": "POST",
            "data": function(d) {
                d.badan = bdn;
                d.tahun = tahun_layout;
                d.nama = nama;
                d.pem = pem;
                d.pulau = pulau;
                d.jenis = jenis;
                d.eselon = eselon;
            }
        },
        "deferRender": true,
        "pageLength": 10,
        "aLengthMenu": [
            [5, 10, 50, 5000],
            [5, 10, 50, "All"]
        ],
        dom: "<'row'<'col-sm-12 col-md-4 tombolTambah'><'col-sm-12 col-md-2'><'col-sm-12 col-md-3'><'col-sm-12 col-md-2 FilterPeriode'><'col-sm-12 col-md-1 tombolFilter'><'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
            "<'row'<'col-sm-12'tr>>" +
            "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

        buttons: [{
            extend: 'excelHtml5',
            text: '',
            title: 'SK tarif/COD',
            exportOptions: {
                columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
                stripHtml: false,
                modifier: {
                    order: 'current',
                    page: 'all',
                    selected: null,
                },
                format: {
                    body: function(data, row, column, node) {
                        var tempDiv = document.createElement('div');
                        tempDiv.innerHTML = data;

                        // Mendapatkan teks dari elemen div yang sudah dikonversi
                        var textData = tempDiv.textContent || tempDiv.innerText;

                        // Mengganti tag '<br>' dengan karakter newline ('\n')
                        textData = textData.replace(/<br\s*\/?>/ig, "\n");

                        return textData;

                        // return data;
                    }
                }
            }
        }],
        "scrollX": true,
        "pageLength": 10,
        "order": [
            [0, "desc"]
        ],
        "columns": [{
                "render": function(data, type, row, meta) {
                    // Mengambil halaman yang sedang aktif
                    var page = tabel.page.info().page;
                    // Menghitung nomor urut unik berdasarkan halaman yang sedang aktif
                    var uniqueIndex = page * tabel.page.len() + meta.row + 1;
                    return uniqueIndex;
                }
            }, {
                "data": "nm_perjanjian"
            },
            {
                "render": function(data, type, row) {
                    var id = row.id_perjanjian_pengusahaan
                    var dt = get_dataDetailById(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push(
                            "<div id='kol'>" + dt[i].no_perjanjian + "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                }


            },
            {
                "render": function(data, type, row) {
                    var id = row.id_perjanjian_pengusahaan
                    var dt = get_dataDetailById(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push("<div id='kol'>" + dt[i].tgl_perjanjian + "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                },


            },
            {
                "render": function(data, type, row) {
                    var id = row.id_perjanjian_pengusahaan
                    var dt = get_dataDetailById(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push("<div id='kol' style='text-align:right !important;'>" +
                            formatRupiah(dt[i].nilai_investasi) +
                            "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                },


            },
            {
                "data": "nm_mitra"
            },
            {
                "render": function(data, type, row) {
                    var id = row.id_slo
                    var dt = get_dataDetailByIdslo(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push(
                            "<div id='kol'>" + dt[i].no_dok_slo + "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                },
                "createdRow": function(row, data, dataIndex) {
                    $(row).addClass('bottom-border'); // Add a class to the row
                }


            },
            {
                "render": function(data, type, row) {
                    var id = row.id_slo
                    var dt = get_dataDetailByIdslo(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push("<div id='kol'>" + dt[i].tgl_slo + "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                },
                "createdRow": function(row, data, dataIndex) {
                    $(row).addClass('bottom-border'); // Add a class to the row
                }


            },
            {
                "render": function(data, type, row) {
                    var id = row.id_tarif
                    var dt = get_dataDetailByIdtarif(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push(
                            "<div id='kol'>" + dt[i].no_dok_tarif + "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                },
                "createdRow": function(row, data, dataIndex) {
                    $(row).addClass('bottom-border'); // Add a class to the row
                }


            },
            {
                "render": function(data, type, row) {
                    var id = row.id_tarif
                    var dt = get_dataDetailByIdtarif(id)
                    var noPpjtValues = [];
                    for (let i = 0; i < dt.length; i++) {
                        noPpjtValues.push("<div id='kol'>" + dt[i].tgl_tarif + "</div>");
                    }

                    return noPpjtValues.join('\r\n');
                },
                "createdRow": function(row, data, dataIndex) {
                    $(row).addClass('bottom-border'); // Add a class to the row
                }


            },

            {
                "data": "pemberi_konsesi"
            },
            {
                "data": "nm_pemberi_konsesi"
            },
            {
                "render": function(data, type, row) {

                    var sa = row.sa_tanah
                    var sa1 = row.sa_tanah_smst1
                    var sa2 = row.sa_tanah_tw3
                    var sa3 = row.sa_tanah_smst2
                    if (sa1 == '' || sa1 == null) {
                        sa1 = 0
                    }
                    if (sa2 == '' || sa2 == null) {
                        sa2 = 0
                    }
                    if (sa3 == '' || sa3 == null) {
                        sa3 = 0
                    }
                    if (sa == '' || sa == null) {
                        sa = 0
                    }
                    var hasil = (parseFloat(sa) + parseFloat(sa1) + parseFloat(sa2) +
                        parseFloat(sa3))
                    return formatNumber(hasil === 0 ? '' : hasil)

                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.sa_tanah_satuan
                    var sa1 = row.sa_tanah_smst1_satuan
                    var sa2 = row.sa_tanah_tw3_satuan
                    var sa3 = row.sa_tanah_smst2_satuan
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    return hasil
                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.sa_kdp
                    var sa1 = row.sa_kdp_smst1
                    var sa2 = row.sa_kdp_tw3
                    var sa3 = row.sa_kdp_smst2
                    if (sa1 == '' || sa1 == null) {
                        sa1 = 0
                    }
                    if (sa2 == '' || sa2 == null) {
                        sa2 = 0
                    }
                    if (sa3 == '' || sa3 == null) {
                        sa3 = 0
                    }
                    if (sa == '' || sa == null) {
                        sa = 0
                    }
                    var hasil = (parseFloat(sa) + parseFloat(sa1) + parseFloat(sa2) +
                        parseFloat(sa3))
                    return formatNumber(hasil === 0 ? '' : hasil)

                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.sa_kdp_satuan
                    var sa1 = row.sa_kdp_smst1_satuan
                    var sa2 = row.sa_kdp_tw3_satuan
                    var sa3 = row.sa_kdp_smst2_satuan
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    return hasil
                }
            },

            {
                "render": function(data, type, row) {

                    var sa = row.sa_jij
                    var sa1 = row.sa_jij_smst1
                    var sa2 = row.sa_jij_tw3
                    var sa3 = row.sa_jij_smst2
                    if (sa1 == '' || sa1 == null) {
                        sa1 = 0
                    }
                    if (sa2 == '' || sa2 == null) {
                        sa2 = 0
                    }
                    if (sa3 == '' || sa3 == null) {
                        sa3 = 0
                    }
                    if (sa == '' || sa == null) {
                        sa = 0
                    }
                    var hasil = (parseFloat(sa) + parseFloat(sa1) + parseFloat(sa2) +
                        parseFloat(sa3))
                    return formatNumber(hasil === 0 ? '' : hasil)

                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.sa_jij_satuan
                    var sa1 = row.sa_jij_smst1_satuan
                    var sa2 = row.sa_jij_tw3_satuan
                    var sa3 = row.sa_jij_smst2_satuan
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    return hasil
                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.sa_gedung
                    var sa1 = row.sa_gedung_smst1
                    var sa2 = row.sa_gedung_tw3
                    var sa3 = row.sa_gedung_smst2
                    if (sa1 == '' || sa1 == null) {
                        sa1 = 0
                    }
                    if (sa2 == '' || sa2 == null) {
                        sa2 = 0
                    }
                    if (sa3 == '' || sa3 == null) {
                        sa3 = 0
                    }
                    if (sa == '' || sa == null) {
                        sa = 0
                    }
                    var hasil = (parseFloat(sa) + parseFloat(sa1) + parseFloat(sa2) +
                        parseFloat(sa3))
                    return formatNumber(hasil === 0 ? '' : hasil)

                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.sa_gedung_satuan
                    var sa1 = row.sa_gedung_smst1_satuan
                    var sa2 = row.sa_gedung_tw3_satuan
                    var sa3 = row.sa_gedung_smst2_satuan
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    return hasil
                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.sa_peralatan
                    var sa1 = row.sa_peralatan_mesin_smst1
                    var sa2 = row.sa_peralatan_mesin_tw3
                    var sa3 = row.sa_peralatan_mesin_smst2
                    if (sa1 == '' || sa1 == null) {
                        sa1 = 0
                    }
                    if (sa2 == '' || sa2 == null) {
                        sa2 = 0
                    }
                    if (sa3 == '' || sa3 == null) {
                        sa3 = 0
                    }
                    if (sa == '' || sa == null) {
                        sa = 0
                    }
                    var hasil = (parseFloat(sa) + parseFloat(sa1) + parseFloat(sa2) +
                        parseFloat(sa3))
                    return formatNumber(hasil === 0 ? '' : hasil)

                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.sa_peralatan_mesin_satuan
                    var sa1 = row.sa_peralatan_mesin_smst1_satuan
                    var sa2 = row.sa_peralatan_mesin_tw3_satuan
                    var sa3 = row.sa_peralatan_mesin_smst2_satuan
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    return hasil
                }
            },

            {
                "render": function(data, type, row) {
                    if (period == 'Semester I') {
                        var hasil = row.kuantitas_tanah_smst1

                    } else if (period == 'Triwulan III') {
                        var hasil = parseFloat(row.kuantitas_tanah_tw3 === null || '' ? 0 : row
                            .kuantitas_tanah_tw3) + parseFloat(row.kuantitas_tanah_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_tanah_smst1);

                    } else if (period == 'Tahunan Unaudited') {
                        var hasil = parseFloat(row.kuantitas_tanah_tw3 === null || '' ? 0 : row
                            .kuantitas_tanah_tw3) + parseFloat(row.kuantitas_tanah_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_tanah_smst1) + parseFloat(row.kuantitas_tanah_smst2 ===
                            null || '' ?
                            0 :
                            row
                            .kuantitas_tanah_smst2);

                    } else if (period == 'Tahunan Audited') {
                        var hasil = parseFloat(row.kuantitas_tanah_tw3 === null || '' ? 0 : row
                            .kuantitas_tanah_tw3) + parseFloat(row.kuantitas_tanah_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_tanah_smst1) + parseFloat(row.kuantitas_tanah_smst2 ===
                            null || '' ?
                            0 :
                            row
                            .kuantitas_tanah_smst2) + parseFloat(row.kuantitas_tanah_audited ===
                            null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_tanah_audited);

                    }
                    return formatNumber(hasil === 0 ? '' : hasil)

                }
            },
            {
                "render": function(data, type, row) {
                    // if (period == 'Semester I') {
                    //     var hasil = row.satuan_tanah_smst1

                    // } else if (period == 'Triwulan III') {
                    //     var hasil = row.satuan_tanah_tw3

                    // } else if (period == 'Semseter II') {
                    //     var hasil = row.satuan_tanah_smst2

                    // } else if (period == 'Tahunan Audited') {
                    //     var hasil = row.satuan_tanah_audited

                    // }
                    var sa = row.satuan_tanah_audited
                    var sa1 = row.satuan_tanah_smst1
                    var sa2 = row.satuan_tanah_tw3
                    var sa3 = row.satuan_tanah_smst2
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    return hasil
                }
            },
            {
                "render": function(data, type, row) {
                    if (period == 'Semester I') {
                        var hasil = row.kuantitas_kdp_smst1

                    } else if (period == 'Triwulan III') {
                        var hasil = parseFloat(row.kuantitas_kdp_tw3 === null || '' ? 0 : row
                            .kuantitas_kdp_tw3) + parseFloat(row.kuantitas_kdp_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_smst1);

                    } else if (period == 'Tahunan Unaudited') {
                        var hasil = parseFloat(row.kuantitas_kdp_tw3 === null || '' ? 0 : row
                            .kuantitas_kdp_tw3) + parseFloat(row.kuantitas_kdp_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_smst1) + parseFloat(row.kuantitas_kdp_smst2 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_smst2);

                    } else if (period == 'Tahunan Audited') {
                        var hasil = parseFloat(row.kuantitas_kdp_tw3 === null || '' ? 0 : row
                            .kuantitas_kdp_tw3) + parseFloat(row.kuantitas_kdp_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_smst1) + parseFloat(row.kuantitas_kdp_smst2 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_smst2) + parseFloat(row.kuantitas_kdp_audited === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_audited);

                    }
                    return formatNumber(hasil === 0 ? '' : hasil)

                }
            },
            {
                "render": function(data, type, row) {
                    // if (period == 'Semester I') {
                    //     var hasil = row.satuan_kdp_smst1

                    // } else if (period == 'Triwulan III') {
                    //     var hasil = row.satuan_kdp_tw3

                    // } else if (period == 'Semseter II') {
                    //     var hasil = row.satuan_kdp_smst2

                    // } else if (period == 'Tahunan Audited') {
                    //     var hasil = row.satuan_kdp_audited

                    // }
                    var sa = row.satuan_kdp_audited
                    var sa1 = row.satuan_kdp_smst1
                    var sa2 = row.satuan_kdp_tw3
                    var sa3 = row.satuan_kdp_smst2
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    return hasil
                }
            },

            {
                "render": function(data, type, row) {
                    if (period == 'Semester I') {
                        var hasil = row.kuantitas_jij_smst1

                    } else if (period == 'Triwulan III') {
                        var hasil = parseFloat(row.kuantitas_jij_tw3 === null || '' ? 0 : row
                            .kuantitas_jij_tw3) + parseFloat(row.kuantitas_jij_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_smst1);

                    } else if (period == 'Tahunan Unaudited') {
                        var hasil = parseFloat(row.kuantitas_jij_tw3 === null || '' ? 0 : row
                            .kuantitas_jij_tw3) + parseFloat(row.kuantitas_jij_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_smst1) + parseFloat(row.kuantitas_jij_smst2 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_smst2);

                    } else if (period == 'Tahunan Audited') {
                        var hasil = parseFloat(row.kuantitas_jij_tw3 === null || '' ? 0 : row
                            .kuantitas_jij_tw3) + parseFloat(row.kuantitas_jij_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_smst1) + parseFloat(row.kuantitas_jij_smst2 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_smst2) + parseFloat(row.kuantitas_jij_audited === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_audited);

                    }
                    return formatNumber(hasil === 0 ? '' : hasil)

                }
            },
            {
                "render": function(data, type, row) {
                    // if (period == 'Semester I') {
                    //     var hasil = row.satuan_jij_smst1

                    // } else if (period == 'Triwulan III') {
                    //     var hasil = row.satuan_jij_tw3

                    // } else if (period == 'Semseter II') {
                    //     var hasil = row.satuan_jij_smst2

                    // } else if (period == 'Tahunan Audited') {
                    //     var hasil = row.satuan_jij_audited

                    // }
                    // return hasil
                    var sa = row.satuan_jij_audited
                    var sa1 = row.satuan_jij_smst1
                    var sa2 = row.satuan_jij_tw3
                    var sa3 = row.satuan_jij_smst2
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    return hasil
                }
            },
            {
                "render": function(data, type, row) {
                    if (period == 'Semester I') {
                        var hasil = row.kuantitas_gedung_bangunan_smst1

                    } else if (period == 'Triwulan III') {
                        var hasil = parseFloat(row.kuantitas_gedung_bangunan_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_gedung_bangunan_tw3) + parseFloat(row
                            .kuantitas_gedung_bangunan_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_smst1);

                    } else if (period == 'Tahunan Unaudited') {
                        var hasil = parseFloat(row.kuantitas_gedung_bangunan_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_gedung_bangunan_tw3) + parseFloat(row
                            .kuantitas_gedung_bangunan_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_smst1) + parseFloat(row
                            .kuantitas_gedung_bangunan_smst2 === null || '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_smst2);

                    } else if (period == 'Tahunan Audited') {
                        var hasil = parseFloat(row.kuantitas_gedung_bangunan_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_gedung_bangunan_tw3) + parseFloat(row
                            .kuantitas_gedung_bangunan_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_smst1) + parseFloat(row
                            .kuantitas_gedung_bangunan_smst2 === null || '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_smst2) + parseFloat(row
                            .kuantitas_gedung_bangunan_audited === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_audited);

                    }
                    return formatNumber(hasil === 0 ? '' : hasil)

                }
            },
            {
                "render": function(data, type, row) {
                    // if (period == 'Semester I') {
                    //     var hasil = row.satuan_gedung_bangunan_smst1

                    // } else if (period == 'Triwulan III') {
                    //     var hasil = row.satuan_gedung_bangunan_tw3

                    // } else if (period == 'Semseter II') {
                    //     var hasil = row.satuan_gedung_bangunan_smst2

                    // } else if (period == 'Tahunan Audited') {
                    //     var hasil = row.satuan_gedung_bangunan_audited

                    // }
                    // return hasil
                    var sa = row.satuan_gedung_bangunan_audited
                    var sa1 = row.satuan_gedung_bangunan_smst1
                    var sa2 = row.satuan_gedung_bangunan_tw3
                    var sa3 = row.satuan_gedung_bangunan_smst2
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    return hasil
                }
            },
            {
                "render": function(data, type, row) {
                    if (period == 'Semester I') {
                        var hasil = row.kuantitas_peralatan_mesin_smst1

                    } else if (period == 'Triwulan III') {
                        var hasil = parseFloat(row.kuantitas_peralatan_mesin_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_peralatan_mesin_tw3) + parseFloat(row
                            .kuantitas_peralatan_mesin_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_smst1);

                    } else if (period == 'Tahunan Unaudited') {
                        var hasil = parseFloat(row.kuantitas_peralatan_mesin_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_peralatan_mesin_tw3) + parseFloat(row
                            .kuantitas_peralatan_mesin_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_smst1) + parseFloat(row
                            .kuantitas_peralatan_mesin_smst2 === null || '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_smst2);

                    } else if (period == 'Tahunan Audited') {
                        var hasil = parseFloat(row.kuantitas_peralatan_mesin_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_peralatan_mesin_tw3) + parseFloat(row
                            .kuantitas_peralatan_mesin_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_smst1) + parseFloat(row
                            .kuantitas_peralatan_mesin_smst2 === null || '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_smst2) + parseFloat(row
                            .kuantitas_peralatan_mesin_audited === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_audited);

                    }
                    return formatNumber(hasil === 0 ? '' : hasil)

                }
            },
            {
                "render": function(data, type, row) {
                    // if (period == 'Semester I') {
                    //     var hasil = row.satuan_peralatan_mesin_smst1

                    // } else if (period == 'Triwulan III') {
                    //     var hasil = row.satuan_peralatan_mesin_tw3

                    // } else if (period == 'Semseter II') {
                    //     var hasil = row.satuan_peralatan_mesin_smst2

                    // } else if (period == 'Tahunan Audited') {
                    //     var hasil = row.satuan_peralatan_mesin_audited

                    // }
                    // return hasil
                    var sa = row.satuan_peralatan_mesin_audited
                    var sa1 = row.satuan_peralatan_mesin_smst1
                    var sa2 = row.satuan_peralatan_mesin_tw3
                    var sa3 = row.satuan_peralatan_mesin_smst2
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    return hasil
                }
            },
            {
                "render": function(data, type, row) {
                    var sa = row.sa_tanah
                    var sa1 = row.sa_tanah_smst1
                    var sa2 = row.sa_tanah_tw3
                    var sa3 = row.sa_tanah_smst2
                    if (sa1 == '' || sa1 == null) {
                        sa1 = 0
                    }
                    if (sa2 == '' || sa2 == null) {
                        sa2 = 0
                    }
                    if (sa3 == '' || sa3 == null) {
                        sa3 = 0
                    }
                    if (sa == '' || sa == null) {
                        sa = 0
                    }
                    if (period == 'Semester I') {
                        var hasil = parseFloat(row.kuantitas_tanah_smst1 === null || '' ? 0 : row
                            .kuantitas_tanah_smst1)

                    } else if (period == 'Triwulan III') {
                        var hasil = parseFloat(row.kuantitas_tanah_tw3 === null || '' ? 0 : row
                            .kuantitas_tanah_tw3) + parseFloat(row.kuantitas_tanah_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_tanah_smst1);
                        var hasil_sa = parseFloat(row.sa_tanah_smst1 === null || '' ? 0 : row
                            .sa_tanah_smst1)
                        var hasilJumlah = hasil + hasil_sa

                    } else if (period == 'Tahunan Unaudited') {
                        var hasil = parseFloat(row.kuantitas_tanah_tw3 === null || '' ? 0 : row
                            .kuantitas_tanah_tw3) + parseFloat(row.kuantitas_tanah_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_tanah_smst1) + parseFloat(row.kuantitas_tanah_smst2 ===
                            null || '' ?
                            0 :
                            row
                            .kuantitas_tanah_smst2);

                    } else if (period == 'Tahunan Audited') {
                        var hasil = parseFloat(row.kuantitas_tanah_tw3 === null || '' ? 0 : row
                            .kuantitas_tanah_tw3) + parseFloat(row.kuantitas_tanah_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_tanah_smst1) + parseFloat(row.kuantitas_tanah_smst2 ===
                            null || '' ?
                            0 :
                            row
                            .kuantitas_tanah_smst2) + parseFloat(row.kuantitas_tanah_audited ===
                            null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_tanah_audited);
                    }
                    var hasil_sa = (parseFloat(sa) + parseFloat(sa1) + parseFloat(sa2) +
                        parseFloat(sa3)) + hasil
                    return formatNumber(hasil_sa === 0 ? '' : hasil_sa)

                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.satuan_tanah_audited
                    var sa1 = row.satuan_tanah_smst1
                    var sa2 = row.satuan_tanah_tw3
                    var sa3 = row.satuan_tanah_smst2
                    var sa4 = row.sa_tanah_satuan
                    var sa5 = row.sa_tanah_smst1_satuan
                    var sa6 = row.sa_tanah_tw3_satuan
                    var sa7 = row.sa_tanah_smst2_satuan
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }
                    if (sa) {
                        hasil = sa
                    }
                    if (sa4) {
                        hasil = sa4
                    }
                    if (sa5) {
                        hasil = sa5
                    }
                    if (sa6) {
                        hasil = sa6
                    }

                    if (sa7) {
                        hasil = sa7
                    }
                    return hasil
                }
            },
            {
                "render": function(data, type, row) {
                    var sa = row.sa_kdp
                    var sa1 = row.sa_kdp_smst1
                    var sa2 = row.sa_kdp_tw3
                    var sa3 = row.sa_kdp_smst2
                    if (sa1 == '' || sa1 == null) {
                        sa1 = 0
                    }
                    if (sa2 == '' || sa2 == null) {
                        sa2 = 0
                    }
                    if (sa3 == '' || sa3 == null) {
                        sa3 = 0
                    }
                    if (sa == '' || sa == null) {
                        sa = 0
                    }
                    if (period == 'Semester I') {
                        var hasil = parseFloat(row.kuantitas_kdp_smst1 === null || '' ? 0 : row
                            .kuantitas_kdp_smst1)

                    } else if (period == 'Triwulan III') {
                        var hasil = parseFloat(row.kuantitas_kdp_tw3 === null || '' ? 0 : row
                            .kuantitas_kdp_tw3) + parseFloat(row.kuantitas_kdp_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_smst1);
                        var hasil_sa = parseFloat(row.sa_kdp_smst1 === null || '' ? 0 : row
                            .sa_kdp_smst1)
                        var hasilJumlah = hasil + hasil_sa

                    } else if (period == 'Tahunan Unaudited') {
                        var hasil = parseFloat(row.kuantitas_kdp_tw3 === null || '' ? 0 : row
                            .kuantitas_kdp_tw3) + parseFloat(row.kuantitas_kdp_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_smst1) + parseFloat(row.kuantitas_kdp_smst2 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_smst2);

                    } else if (period == 'Tahunan Audited') {
                        var hasil = parseFloat(row.kuantitas_kdp_tw3 === null || '' ? 0 : row
                            .kuantitas_kdp_tw3) + parseFloat(row.kuantitas_kdp_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_smst1) + parseFloat(row.kuantitas_kdp_smst2 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_smst2) + parseFloat(row.kuantitas_kdp_audited === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_kdp_audited);
                    }
                    var hasil_sa = (parseFloat(sa) + parseFloat(sa1) + parseFloat(sa2) +
                        parseFloat(sa3)) + hasil
                    return formatNumber(hasil_sa === 0 ? '' : hasil_sa)

                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.satuan_kdp_audited
                    var sa1 = row.satuan_kdp_smst1
                    var sa2 = row.satuan_kdp_tw3
                    var sa3 = row.satuan_kdp_smst2
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    var sa4 = row.sa_kdp_satuan
                    var sa5 = row.sa_kdp_smst1_satuan
                    var sa6 = row.sa_kdp_tw3_satuan
                    var sa7 = row.sa_kdp_smst2_satuan
                    if (sa4) {
                        hasil = sa4
                    }
                    if (sa5) {
                        hasil = sa5
                    }
                    if (sa6) {
                        hasil = sa6
                    }
                    if (sa7) {
                        hasil = sa7
                    }

                    return hasil
                }
            },
            {
                "render": function(data, type, row) {
                    var sa = row.sa_jij
                    var sa1 = row.sa_jij_smst1
                    var sa2 = row.sa_jij_tw3
                    var sa3 = row.sa_jij_smst2
                    if (sa1 == '' || sa1 == null) {
                        sa1 = 0
                    }
                    if (sa2 == '' || sa2 == null) {
                        sa2 = 0
                    }
                    if (sa3 == '' || sa3 == null) {
                        sa3 = 0
                    }
                    if (sa == '' || sa == null) {
                        sa = 0
                    }
                    if (period == 'Semester I') {
                        var hasil = parseFloat(row.kuantitas_jij_smst1 === null || '' ? 0 : row
                            .kuantitas_jij_smst1)

                    } else if (period == 'Triwulan III') {
                        var hasil = parseFloat(row.kuantitas_jij_tw3 === null || '' ? 0 : row
                            .kuantitas_jij_tw3) + parseFloat(row.kuantitas_jij_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_smst1);
                        var hasil_sa = parseFloat(row.sa_jij_smst1 === null || '' ? 0 : row
                            .sa_jij_smst1)
                        var hasilJumlah = hasil + hasil_sa

                    } else if (period == 'Tahunan Unaudited') {
                        var hasil = parseFloat(row.kuantitas_jij_tw3 === null || '' ? 0 : row
                            .kuantitas_jij_tw3) + parseFloat(row.kuantitas_jij_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_smst1) + parseFloat(row.kuantitas_jij_smst2 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_smst2);

                    } else if (period == 'Tahunan Audited') {
                        var hasil = parseFloat(row.kuantitas_jij_tw3 === null || '' ? 0 : row
                            .kuantitas_jij_tw3) + parseFloat(row.kuantitas_jij_smst1 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_smst1) + parseFloat(row.kuantitas_jij_smst2 === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_smst2) + parseFloat(row.kuantitas_jij_audited === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_jij_audited);
                    }
                    var hasil_sa = (parseFloat(sa) + parseFloat(sa1) + parseFloat(sa2) +
                        parseFloat(sa3)) + hasil
                    return formatNumber(hasil_sa === 0 ? '' : hasil_sa)

                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.satuan_jij
                    var sa1 = row.satuan_jij_smst1
                    var sa2 = row.satuan_jij_tw3
                    var sa3 = row.satuan_jij_smst2
                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    var sa4 = row.sa_jij_satuan
                    var sa5 = row.sa_jij_smst1_satuan
                    var sa6 = row.sa_jij_tw3_satuan
                    var sa7 = row.sa_jij_smst2_satuan

                    if (sa4) {
                        hasil = sa4
                    }
                    if (sa5) {
                        hasil = sa5
                    }
                    if (sa6) {
                        hasil = sa6
                    }
                    if (sa7) {
                        hasil = sa7
                    }

                    return hasil
                }
            },
            {
                "render": function(data, type, row) {
                    var sa = row.sa_gedung
                    var sa1 = row.sa_gedung_smst1
                    var sa2 = row.sa_gedung_tw3
                    var sa3 = row.sa_gedung_smst2
                    if (sa1 == '' || sa1 == null) {
                        sa1 = 0
                    }
                    if (sa2 == '' || sa2 == null) {
                        sa2 = 0
                    }
                    if (sa3 == '' || sa3 == null) {
                        sa3 = 0
                    }
                    if (sa == '' || sa == null) {
                        sa = 0
                    }
                    if (period == 'Semester I') {
                        var hasil = parseFloat(row.kuantitas_gedung_bangunan_smst1 === null || '' ? 0 :
                            row
                            .kuantitas_gedung_bangunan_smst1)

                    } else if (period == 'Triwulan III') {
                        var hasil = parseFloat(row.kuantitas_gedung_bangunan_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_gedung_bangunan_tw3) + parseFloat(row
                            .kuantitas_gedung_bangunan_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_smst1);
                        var hasil_sa = parseFloat(row.sa_gedung_bangunan_smst1 === null || '' ? 0 : row
                            .sa_gedung_bangunan_smst1)
                        var hasilJumlah = hasil + hasil_sa

                    } else if (period == 'Tahunan Unaudited') {
                        var hasil = parseFloat(row.kuantitas_gedung_bangunan_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_gedung_bangunan_tw3) + parseFloat(row
                            .kuantitas_gedung_bangunan_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_smst1) + parseFloat(row
                            .kuantitas_gedung_bangunan_smst2 === null || '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_smst2);

                    } else if (period == 'Tahunan Audited') {
                        var hasil = parseFloat(row.kuantitas_gedung_bangunan_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_gedung_bangunan_tw3) + parseFloat(row
                            .kuantitas_gedung_bangunan_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_smst1) + parseFloat(row
                            .kuantitas_gedung_bangunan_smst2 === null || '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_smst2) + parseFloat(row
                            .kuantitas_gedung_bangunan_audited === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_gedung_bangunan_audited);
                    }
                    var hasil_sa = (parseFloat(sa) + parseFloat(sa1) + parseFloat(sa2) +
                        parseFloat(sa3)) + hasil
                    return formatNumber(hasil_sa === 0 ? '' : hasil_sa)

                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.satuan_gedung_bangunan_audited
                    var sa1 = row.satuan_gedung_bangunan_smst1
                    var sa2 = row.satuan_gedung_bangunan_tw3
                    var sa3 = row.satuan_gedung_bangunan_smst2

                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    var sa4 = row.sa_gedung_satuan
                    var sa5 = row.sa_gedung_smst1_satuan
                    var sa6 = row.sa_gedung_tw3_satuan
                    var sa7 = row.sa_gedung_smst2_satuan
                    if (sa4) {
                        hasil = sa4
                    }
                    if (sa5) {
                        hasil = sa5
                    }
                    if (sa6) {
                        hasil = sa6
                    }
                    if (sa7) {
                        hasil = sa7
                    }


                    return hasil
                }
            },
            {
                "render": function(data, type, row) {
                    var sa = row.sa_peralatan
                    var sa1 = row.sa_peralatan_mesin_smst1
                    var sa2 = row.sa_peralatan_mesin_tw3
                    var sa3 = row.sa_peralatan_mesin_smst2
                    if (sa1 == '' || sa1 == null) {
                        sa1 = 0
                    }
                    if (sa2 == '' || sa2 == null) {
                        sa2 = 0
                    }
                    if (sa3 == '' || sa3 == null) {
                        sa3 = 0
                    }
                    if (sa == '' || sa == null) {
                        sa = 0
                    }
                    if (period == 'Semester I') {
                        var hasil = parseFloat(row.kuantitas_peralatan_mesin_smst1 === null || '' ? 0 :
                            row
                            .kuantitas_peralatan_mesin_smst1)

                    } else if (period == 'Triwulan III') {
                        var hasil = parseFloat(row.kuantitas_peralatan_mesin_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_peralatan_mesin_tw3) + parseFloat(row
                            .kuantitas_peralatan_mesin_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_smst1);
                        var hasil_sa = parseFloat(row.sa_peralatan_mesin_smst1 === null || '' ? 0 : row
                            .sa_peralatan_mesin_smst1)
                        var hasilJumlah = hasil + hasil_sa

                    } else if (period == 'Tahunan Unaudited') {
                        var hasil = parseFloat(row.kuantitas_peralatan_mesin_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_peralatan_mesin_tw3) + parseFloat(row
                            .kuantitas_peralatan_mesin_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_smst1) + parseFloat(row
                            .kuantitas_peralatan_mesin_smst2 === null || '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_smst2);

                    } else if (period == 'Tahunan Audited') {
                        var hasil = parseFloat(row.kuantitas_peralatan_mesin_tw3 === null || '' ? 0 :
                            row
                            .kuantitas_peralatan_mesin_tw3) + parseFloat(row
                            .kuantitas_peralatan_mesin_smst1 === null || '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_smst1) + parseFloat(row
                            .kuantitas_peralatan_mesin_smst2 === null || '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_smst2) + parseFloat(row
                            .kuantitas_peralatan_mesin_audited === null ||
                            '' ?
                            0 :
                            row
                            .kuantitas_peralatan_mesin_audited);
                    }
                    var hasil_sa = (parseFloat(sa) + parseFloat(sa1) + parseFloat(sa2) +
                        parseFloat(sa3)) + hasil
                    return formatNumber(hasil_sa === 0 ? '' : hasil_sa)
                }
            },
            {
                "render": function(data, type, row) {

                    var sa = row.satuan_peralatan_mesin_audited
                    var sa1 = row.satuan_peralatan_mesin_smst1
                    var sa2 = row.satuan_peralatan_mesin_tw3
                    var sa3 = row.satuan_peralatan_mesin_smst2

                    var hasil = ''
                    if (sa1) {
                        hasil = sa1
                    }
                    if (sa2) {
                        hasil = sa2
                    }
                    if (sa3) {
                        hasil = sa3
                    }

                    if (sa) {
                        hasil = sa
                    }
                    var sa4 = row.sa_peralatan_mesin_satuan
                    var sa5 = row.sa_peralatan_mesin_smst1_satuan
                    var sa6 = row.sa_peralatan_mesin_tw3_satuan
                    var sa7 = row.sa_peralatan_mesin_smst2_satuan

                    if (sa4) {
                        hasil = sa4
                    }
                    if (sa5) {
                        hasil = sa5
                    }
                    if (sa6) {
                        hasil = sa6
                    }
                    if (sa7) {
                        hasil = sa7
                    }

                    return hasil
                }
            },

            {
                "render": function(data, type, row) {
                    var html_button = [
                        "<button class='btn btn-danger btn-xs' title = 'Hapus Data' onclick=hapus_data('" +
                        row.id_perolehan_mutasi_kuantitas + "') style='display:" + hapus_ak +
                        "'><i class='fa fa-trash'></i></button>",
                        "<button class='btn btn-primary btn-xs' title='Edit Data' onclick=edit_data('" +
                        row.id_perolehan_mutasi_kuantitas + "') style='display:" + edit_ak +
                        "'><i class='fa fa-edit'></i></button>",
                        "<button class='btn btn-warning btn-xs' title='Download file' onclick=download_data('" +
                        row.id_perolehan_mutasi_kuantitas +
                        "')><i class='fa fa-download'></i></button>",


                    ].join("\n");
                    return html_button;

                }
            }
        ],
        "language": {
            "decimal": "",
            "emptyTable": "Data tidak ditemukan",
            "info": "Data _START_ s/d _END_ dari _TOTAL_",
            "infoEmpty": "Tidak ada data",
            "infoFiltered": "(tersaring dari _MAX_)",
            "infoPostFix": "",
            "thousands": ",",
            "lengthMenu": "_MENU_  data per halaman",
            "loadingRecords": '<div class="preloader3 loader-block"><div class="circ1"></div><div class="circ2"></div> <div class="circ3"></div> <div class="circ4"></div></div>',
            "processing": '<div class="preloader3 loader-block"><div class="circ1"></div><div class="circ2"></div> <div class="circ3"></div> <div class="circ4"></div></div>',
            "search": "Cari:",
            "zeroRecords": "Tidak ada data ditemukan",
            "aria": {
                "sortAscending": ": aktifkan untuk mengurutkan naik",
                "sortDescending": ": aktifkan untuk mengurutkan turun"
            }
        }


    });
}



// function tampil_data(table, colum = '1', id = '1') {
//     var url = ''
//     var tadata = ''
//     urls = "<?php echo base_url(); ?>index.php/perjanjian_konsesijasa/tampildata/" + table + "/" + colum + "/idx" + id;
//     $.ajax({
//         url: urls,
//         contentType: "application/json; charset=utf-8",
//         dataType: "json",
//         async: false,
//         success: function(data) {
//             tadata = data;
//         },
//         failure: function(errMsg) {
//             alert(errMsg);
//         }
//     });
//     return tadata;
// }

function tampil_data(table, colum = '1', id = '1', combobox = '') {
    var tadata = '';
    const url = "<?php echo base_url(); ?>index.php/perjanjian_konsesijasa/tampildata";
    const data = {
        table: table,
        colum: colum,
        id: btoa(id), // base64 encode agar sama dengan di PHP
        combobox: combobox
    };

    $.ajax({
        url: url,
        type: 'POST',
        data: JSON.stringify(data),
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function(result) {
            tadata = result;
        },
        error: function(err) {
            console.error("Gagal fetch data:", err);
        }
    });
    return tadata;
}

function to_show(a, set = '') {
    b = a.value;
    if (b === undefined) {
        b = a
    }
    $("#box-smst1").hide()
    $("#box-smst2").hide()
    $("#box-tw3").hide()
    $("#box-audited").hide()
    if (b == 'Semester I') {
        $("#box-smst1").show()
    } else if (b == 'Tahunan Unaudited') {
        $("#box-smst2").show()

    } else if (b == 'Triwulan III') {
        $("#box-tw3").show()

    } else if (b == 'Tahunan Audited') {
        $("#box-audited").show()
    }
}

function to_kolom(a, set = '') {
    b = a.value;
    if (b === undefined) {
        b = a
    }
    // alert(b)
    // if (a.id == 'nm_perjanjian') {
    if (b == '') {
        $("#perjanjiandata").hide()
    } else {
        $("#perjanjiandata").show()

        var dt = tampil_data('v_perjanjian_pengusahaan', 'id_perjanjian_pengusahaan', b);

        $("#bdn_usaha").val(dt[0].nm_mitra)
        $("#tgl_ak").val(dt[0].tgl_akhir_konsesi)

        var dt_perjanjian = get_dataDetailById(b)
        $("#bang_perjanjian").empty()
        for (let i = 0; i < dt_perjanjian.length; i++) {
            if (dt_perjanjian[i].no_perjanjian != null) {
                var html = `
            <div class="form-group row">
              <div class="col-sm-4">
              <input type="text" class="form-control" placeholder=""   value="${dt_perjanjian[i].no_perjanjian}" readonly>
              <input type="hidden" class="form-control" placeholder=""   value="${dt_perjanjian[i].id_tarif_dtl}">
              </div>
              <div class="col-sm-4">
                <input type="date" class="form-control" placeholder=""   value="${dt_perjanjian[i].tgl_perjanjian}" readonly>
              </div>
              <div class="col-sm-4">
                <input type="text" class="form-control" placeholder=""   value="${formatRupiah(dt_perjanjian[i].nilai_investasi)}" onkeyup="formatNumber(this)" readonly>
              </div>
          
              <div class="col-sm-1" style="text-align:left;">
     
              </div>
            </div>
          `;
                $('#bang_perjanjian').append(html);
            }
        }

        $("#bang_slo").empty()
        var dts = tampil_data('slo', 'id_perjanjian_pengusahaan~tahun', b + '~' + tahun_layout);

        var id_slo = ""
        if (dts.length > 0) {
            id_slo = dts[0].id_slo
        } else {
            id_slo = ''
        }
        $("#id_slo").val(id_slo)

        var dt_slo = get_dataDetailByIdslo(id_slo)
        for (let i = 0; i < dt_slo.length; i++) {
            if (dt_slo[i].no_dok_slo != null) {
                var html = `
            <div class="form-group row">
              <div class="col-sm-4">
              <input type="text" class="form-control" placeholder=""   value="${dt_slo[i].no_dok_slo}" readonly>
              <input type="hidden" class="form-control" placeholder=""   value="${dt_slo[i].id_slo_dtl}">
              </div>
              <div class="col-sm-4">
                <input type="date" class="form-control" id="tgl_slo${i}" placeholder=""   value="${dt_slo[i].tgl_slo}" readonly>
              </div>    
              <div class="col-sm-4">`
                if (i == dt_slo.length - 1) {
                    var has = parseInt(tahun_layout) - parseInt(dt_slo[i].tgl_slo.split("-")[0]);
                    has = has * 2
                    has = parseInt(dts[0].masa_manfaat) - has
                    html +=
                        `<input type="text" class="form-control" placeholder="" id="sisa_manfaat_slo" name="sisa_manfaat_slo" value="${has}"  readonly>`
                } else {
                    html += `-`
                }

                html += `  </div>
          
              <div class="col-sm-1" style="text-align:left;">
     
              </div>
            </div>
          `;
                $('#bang_slo').append(html);
            }
        }

        $("#bang_tarif").empty()
        var dtt = tampil_data('tarif', 'id_perjanjian_pengusahaan~tahun', b + '~' + tahun_layout);

        var id_tarif = ""
        if (dtt.length > 0) {
            id_tarif = dtt[0].id_tarif
        } else {
            id_tarif = ''
        }
        $("#id_tarif").val(id_tarif)

        var dt_tarif = get_dataDetailByIdtarif(id_tarif)
        for (let i = 0; i < dt_tarif.length; i++) {
            if (dt_tarif[i].no_dok_tarif != null) {
                var html = `
            <div class="form-group row">
              <div class="col-sm-4">
              <input type="text" class="form-control" placeholder=""   value="${dt_tarif[i].no_dok_tarif}" readonly>
              <input type="hidden" class="form-control" placeholder=""   value="${dt_tarif[i].id_slo_tarif}">
              </div>
              <div class="col-sm-4">
                <input type="date" class="form-control" placeholder="" id="tgl_tarif"  value="${dt_tarif[i].tgl_tarif}" readonly>
              </div>
              <div class="col-sm-4">`
                if (i == dt_tarif.length - 1) {
                    var has = parseInt(tahun_layout) - parseInt(dt_tarif[i].tgl_tarif.split("-")[0]);
                    has = has * 2
                    has = parseInt(dtt[0].masa_manfaat) - has
                    html +=
                        `<input type="text" class="form-control" placeholder="" id="sisa_manfaat_tarif"  name="sisa_manfaat_tarif" value="${has}"  readonly>`
                } else {
                    html += `-`
                }

                html += `  </div>
              <div class="col-sm-1" style="text-align:left;">
     
              </div>
            </div>
          `;
                $('#bang_tarif').append(html);
            }
        }

    }

    // }
}

function download_data(id_saldo) {
    var dt = tampil_data('perolehan_mutasi_kuantitas', 'id_perolehan_mutasi_kuantitas', id_saldo);
    // var dt_lain = tampil_data('perjanjian_pengusahaan_detail', 'id_perjanjian_pengusahaan', dt[0]
    //     .id_perjanjian_pengusahaan);
    // $("#box-download").empty()
    // for (let i = 0; i < dt_lain.length; i++) {
    //     if (dt_lain[i].no_perjanjian != null) {
    //         var but =
    //             `  <button type="button" style="height:35px;font-size: 20px;color:red;" onclick="remove_comboi('${i}')" name="remove" id="${i}" class="btn btn-defaukt btn_remove" ><i class="fa fa-trash"></i></button>`;
    //         var link = '#'
    //         var target = ''
    //         var icon = '<i class="fa fa-times"></i>'
    //         var title = 'Tidak bisa download File tidak tersedia'
    //         var filename = 'File Tidak Tersedia'
    //         if (dt_lain[i].file_perjanjian) {
    //             link = `<?php echo base_url(); ?>assets/file_perjanjian/${dt_lain[i].file_perjanjian}`
    //             target = '_blank'
    //             var icon = '<i class="fa fa-download"></i>'
    //             title = 'Download File'
    //             filename = dt_lain[i].file_perjanjian.split("~")[1]
    //         }
    //         var html = `
    //         <div class="form-group row " >
    //           <div class="col-sm-4">
    //           <input type="text" class="form-control" placeholder=""   value="${dt_lain[i].no_perjanjian}" readonly>
    //           <input type="hidden" class="form-control" placeholder=""   value="${dt_lain[i].id_perjanjian_pengusahaan}">
    //           </div>
    //           <div class="col-sm-2">
    //             <input type="date" class="form-control" placeholder=""   value="${dt_lain[i].tgl_perjanjian}" readonly>
    //           </div>
    //           <div class="col-sm-3">
    //             <input type="text" class="form-control" placeholder=""   value="${formatRupiah(dt_lain[i].nilai_investasi)}" onkeyup="formatNumber(this)" readonly>
    //           </div>
    //           <div class="col-sm-3">
    //                   <div class="input-group">                       
    //                       <input type="text" class="form-control button-yuhu"   placeholder="${filename}"  readonly>
    //                       <div class="btn btn-tambah browse-button button-yuhu" style="padding:10px 0px;">
    //                         <a style='color:white' title='${title}' href='${link}' download='${filename}' disabled>
    //                             <span class="browse-button-text">
    //                            ${icon} </span>
    //                         </a>
    //                         </div> 
    //                 </div>
    //               </div>
    //           <div class="col-sm-1" style="text-align:left;">

    //           </div>
    //         </div>
    //       `;


    //         $('#box-download').append(html);
    //     }
    // }

    // var dt_lain_2 = tampil_data('tarif_detail', 'id_tarif', dt[0].id_tarif);
    // $("#box-download2").empty()
    // for (let i = 0; i < dt_lain_2.length; i++) {
    //     if (dt_lain_2[i].no_dok_tarif != null) {
    //         var but =
    //             `  <button type="button" style="height:35px;font-size: 20px;color:red;" onclick="remove_comboi('${i}')" name="remove" id="${i}" class="btn btn-defaukt btn_remove" ><i class="fa fa-trash"></i></button>`;
    //         var link = '#'
    //         var target = ''
    //         var icon = '<i class="fa fa-times"></i>'
    //         var title = 'Tidak bisa download File tidak tersedia'
    //         var filename = 'File Tidak Tersedia'
    //         if (dt_lain_2[i].file) {
    //             link = `<?php echo base_url(); ?>assets/file_tarif/${dt_lain_2[i].file}`
    //             target = '_blank'
    //             var icon = '<i class="fa fa-download"></i>'
    //             title = 'Download File'
    //             filename = dt_lain_2[i].file.split("~")[1]



    //         }
    //         var html = `
    //         <div class="form-group row " >
    //           <div class="col-sm-4">
    //           <input type="text" class="form-control" placeholder=""   value="${dt_lain_2[i].no_dok_tarif}" readonly>
    //           <input type="hidden" class="form-control" placeholder=""   value="${dt_lain_2[i].id_tarif_dtl}">
    //           </div>
    //           <div class="col-sm-4">
    //             <input type="date" class="form-control" placeholder=""   value="${dt_lain_2[i].tgl_tarif}" readonly>
    //           </div>
    //           <div class="col-sm-4">
    //                   <div class="input-group">                       
    //                       <input type="text" class="form-control button-yuhu"   placeholder="${filename}"  readonly>
    //                       <div class="btn btn-tambah browse-button button-yuhu" style="padding:10px 0px;">
    //                         <a style='color:white' title='${title}' href='${link}' download='${filename}' disabled>
    //                             <span class="browse-button-text">
    //                            ${icon} </span>
    //                         </a>
    //                         </div> 
    //                 </div>
    //               </div>
    //           <div class="col-sm-1" style="text-align:left;">

    //           </div>
    //         </div>
    //       `;


    //         $('#box-download2').append(html);
    //     }
    // }
    // var dt_lain_3 = tampil_data('slo_detail', 'id_slo', dt[0].id_slo);
    // $("#box-download3").empty()
    // for (let i = 0; i < dt_lain_3.length; i++) {
    //     if (dt_lain_3[i].no_dok_slo != null) {
    //         var but =
    //             `  <button type="button" style="height:35px;font-size: 20px;color:red;" onclick="remove_comboi('${i}')" name="remove" id="${i}" class="btn btn-defaukt btn_remove" ><i class="fa fa-trash"></i></button>`;
    //         var link = '#'
    //         var target = ''
    //         var icon = '<i class="fa fa-times"></i>'
    //         var title = 'Tidak bisa download File tidak tersedia'
    //         var filename = 'File Tidak Tersedia'
    //         if (dt_lain_3[i].file) {
    //             link = `<?php echo base_url(); ?>assets/file_slo/${dt_lain_3[i].file}`
    //             target = '_blank'
    //             var icon = '<i class="fa fa-download"></i>'
    //             title = 'Download File'
    //             filename = dt_lain_3[i].file.split("~")[1]



    //         }
    //         var html = `
    //         <div class="form-group row " >
    //           <div class="col-sm-4">
    //           <input type="text" class="form-control" placeholder=""   value="${dt_lain_3[i].no_dok_slo}" readonly>
    //           <input type="hidden" class="form-control" placeholder=""   value="${dt_lain_3[i].id_slo_dtl}">
    //           </div>
    //           <div class="col-sm-4">
    //             <input type="date" class="form-control" placeholder=""   value="${dt_lain_3[i].tgl_slo}" readonly>
    //           </div>
    //           <div class="col-sm-4">
    //                   <div class="input-group">                       
    //                       <input type="text" class="form-control button-yuhu"   placeholder="${filename}"  readonly>
    //                       <div class="btn btn-tambah browse-button button-yuhu" style="padding:10px 0px;">
    //                         <a style='color:white' title='${title}' href='${link}' download='${filename}' disabled>
    //                             <span class="browse-button-text">
    //                            ${icon} </span>
    //                         </a>
    //                         </div> 
    //                 </div>
    //               </div>
    //           <div class="col-sm-1" style="text-align:left;">

    //           </div>
    //         </div>
    //       `;


    //         $('#box-download3').append(html);
    //     }
    // }


    $("#box-download4").empty()

    var file1 = dt[0].file_lk
    var file2 = dt[0].file_kertas_kerja_retrospektif
    var ii = 00;
    var iii = 11;

    var link1 = '#'
    var target1 = ''
    var icon1 = '<i class="fa fa-times"></i>'
    var title1 = 'Tidak bisa download File tidak tersedia'
    var filename1 = 'File Tidak Tersedia'
    var filename2 = 'File Tidak Tersedia'
    if (file1) {
        filename1 = file1.split("~")[1]
        link1 = `<?php echo base_url(); ?>assets/file_perolehan_mutasi/${file1}`
        target1 = '_blank'
        icon1 = '<i class="fa fa-download"></i>'
        title1 = 'Download File'
    }

    var link2 = '#'
    var target2 = ''
    var icon2 = '<i class="fa fa-times"></i>'
    var title2 = 'Tidak bisa download File tidak tersedia'

    if (file2) {
        filename2 = file2.split("~")[1]
        link2 = `<?php echo base_url(); ?>assets/file_perolehan_mutasi/${file2}`
        target2 = '_blank'
        icon2 = '<i class="fa fa-download"></i>'
        title2 = 'Download File'
    }

    var html = `
            <div class="form-group row" id="yuhui${ii}">
              
              <div class="col-sm-6">
                      <div class="input-group"> 
                          <input type="text" class="form-control xx${ii} button-yuhu" name="filename_lk" placeholder="${filename1}"  readonly>
                          <input type="hidden" class="form-control xx${ii} button-yuhu" name="filenames_lk">
                          <div class="btn btn-tambah browse-button button-yuhu" style="padding:10px 0px;">
                            <a style='color:white' title='${title1}' href='${link1}' download='${filename1}' disabled>
                                <span class="browse-button-text">
                               ${icon1} </span>
                            </a>
                            </div>
                          <span class="input-group-btn">
                           
                          </span>
                    </div>
                  </div>
                  <div class="col-sm-6">
                      <div class="input-group">
 
                          <input type="text" class="form-control xx${iii} button-yuhu" name="filename_kertas" value=""  placeholder="${filename2}" readonly>
                          <input type="hidden" class="form-control xx${iii} button-yuhu" name="filenames_kertas">
                          <div class="btn btn-tambah browse-button button-yuhu" style="padding:10px 0px;">
                            <a style='color:white' title='${title2}' href='${link2}' download='${filename2}' disabled>
                                <span class="browse-button-text">
                               ${icon2} </span>
                            </a>
                            </div>
                          <span class="input-group-btn">
                           
                          </span>
                    </div>
                  </div>
            </div>
          `;
    $('#box-download4').append(html);

    if (file1) {
        $(".filename" + ii).val(filename1);
        $(".browse-button-text" + ii).html('<i class="fa fa-refresh"></i> ');
        $(".clear-button" + ii).show();
    }
    if (file2) {
        $(".filename" + iii).val(filename2);
        $(".browse-button-text" + iii).html('<i class="fa fa-refresh"></i> ');
        $(".clear-button" + iii).show();
    }
    $("#modal-download").modal("show");
}

function tambahdata() {
    $(".form-user input").val('')
    $(".form-user select").val('')
    $(".form-user textarea").val('')
    $("#tahun_pembukuan").val(tahun_layout)
    $("#tahun_perolehan").val(tahun_layout)
    $('#p_perolehan').val('').selectpicker('refresh');
    $('#p_pembukuan').val('').selectpicker('refresh');
    combo('satuan_tanah_smst1', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_kdp_smst1', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_jij_smst1', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_gedung_bangunan_smst1', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_peralatan_mesin_smst1', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_tanah_tw3', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_kdp_tw3', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_jij_tw3', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_gedung_bangunan_tw3', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_peralatan_mesin_tw3', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_tanah_smst2', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_kdp_smst2', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_jij_smst2', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_gedung_bangunan_smst2', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_peralatan_mesin_smst2', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_tanah_audited', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_kdp_audited', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_jij_audited', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_gedung_bangunan_audited', 'ref_satuan~nm_satuan~nm_satuan')
    combo('satuan_peralatan_mesin_audited', 'ref_satuan~nm_satuan~nm_satuan')
    combo('nm_perjanjian', 'perjanjian_pengusahaan~nm_perjanjian~id_perjanjian_pengusahaan', 'created_by', id_user)
    combo('satker', 'ref_satker~nm_satker~kode_satker')
    // combo('bdn_usaha', 'ref_badan_usaha~nm_mitra~id_badan_usaha')

    $("#bang_lai").empty()
    bang_l()


    $("#modal-tambah-mtl").modal("show");

}

function masamanfaat(a) {
    b = a.value;
    if (b === undefined) {
        b = a
    }
    var tgl_ak = $("#tgl_ak").val().split("-")[0]
    var tgl_tarif = b.split("-")[0]
    var hasil = (parseInt(tgl_ak) - parseInt(tgl_tarif)) * 2
    $("#masa").val(hasil)
}

function edit_data(id) {
    $(".form-user input").val('')
    $(".form-user select").val('')
    var dt = tampil_data('perolehan_mutasi_kuantitas', 'id_perolehan_mutasi_kuantitas', id);
    $("#id").val(dt[0].id_perolehan_mutasi_kuantitas)
    $("#tahun_pembukuan").val(tahun_layout)
    $("#tahun_perolehan").val(tahun_layout)
    combo('nm_perjanjian', 'perjanjian_pengusahaan~nm_perjanjian~id_perjanjian_pengusahaan', 'created_by', id_user, dt[
        0].id_perjanjian_pengusahaan)
    $("#p_perolehan").val(dt[0].periode_perolehan)
    $("#p_pembukuan").val(dt[0].periode_pembukuan)
    $('#kuantitas_tanah_smst1').val(dt[0].kuantitas_tanah_smst1)
    $('#kuantitas_kdp_smst1').val(dt[0].kuantitas_kdp_smst1)
    $('#kuantitas_jij_smst1').val(dt[0].kuantitas_jij_smst1)
    $('#kuantitas_gedung_bangunan_smst1').val(dt[0].kuantitas_gedung_bangunan_smst1)
    $('#kuantitas_peralatan_mesin_smst1').val(dt[0].kuantitas_peralatan_mesin_smst1)
    combo('satuan_tanah_smst1', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_tanah_smst1)
    combo('satuan_kdp_smst1', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_kdp_smst1)
    combo('satuan_jij_smst1', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_jij_smst1)
    combo('satuan_gedung_bangunan_smst1', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0]
        .satuan_gedung_bangunan_smst1)
    combo('satuan_peralatan_mesin_smst1', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0]
        .satuan_peralatan_mesin_smst1)

    $('#kuantitas_tanah_tw3').val(dt[0].kuantitas_tanah_tw3)
    $('#kuantitas_kdp_tw3').val(dt[0].kuantitas_kdp_tw3)
    $('#kuantitas_jij_tw3').val(dt[0].kuantitas_jij_tw3)
    $('#kuantitas_gedung_bangunan_tw3').val(dt[0].kuantitas_gedung_bangunan_tw3)
    $('#kuantitas_peralatan_mesin_tw3').val(dt[0].kuantitas_peralatan_mesin_tw3)

    combo('satuan_tanah_tw3', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_tanah_tw3)
    combo('satuan_kdp_tw3', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_kdp_tw3)
    combo('satuan_jij_tw3', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_jij_tw3)
    combo('satuan_gedung_bangunan_tw3', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_gedung_bangunan_tw3)
    combo('satuan_peralatan_mesin_tw3', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_peralatan_mesin_tw3)

    $('#kuantitas_tanah_smst2').val(dt[0].kuantitas_tanah_smst2)
    $('#kuantitas_kdp_smst2').val(dt[0].kuantitas_kdp_smst2)
    $('#kuantitas_jij_smst2').val(dt[0].kuantitas_jij_smst2)
    $('#kuantitas_gedung_bangunan_smst2').val(dt[0].kuantitas_gedung_bangunan_smst2)
    $('#kuantitas_peralatan_mesin_smst2').val(dt[0].kuantitas_peralatan_mesin_smst2)
    combo('satuan_tanah_smst2', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_tanah_smst2)
    combo('satuan_kdp_smst2', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_kdp_smst2)
    combo('satuan_jij_smst2', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_jij_smst2)
    combo('satuan_gedung_bangunan_smst2', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0]
        .satuan_gedung_bangunan_smst2)
    combo('satuan_peralatan_mesin_smst2', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0]
        .satuan_peralatan_mesin_smst2)

    $('#kuantitas_tanah_audited').val(dt[0].kuantitas_tanah_audited)
    $('#kuantitas_kdp_audited').val(dt[0].kuantitas_kdp_audited)
    $('#kuantitas_jij_audited').val(dt[0].kuantitas_jij_audited)
    $('#kuantitas_gedung_bangunan_audited').val(dt[0].kuantitas_gedung_bangunan_audited)
    $('#kuantitas_peralatan_mesin_audited').val(dt[0].kuantitas_peralatan_mesin_audited)
    combo('satuan_tanah_audited', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_tanah_audited)
    combo('satuan_kdp_audited', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_kdp_audited)
    combo('satuan_jij_audited', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0].satuan_jij_audited)
    combo('satuan_gedung_bangunan_audited', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0]
        .satuan_gedung_bangunan_audited)
    combo('satuan_peralatan_mesin_audited', 'ref_satuan~nm_satuan~nm_satuan', '1', '1', dt[0]
        .satuan_peralatan_mesin_audited)







    $(".yuhui").remove()
    $("#bang_lai").empty()
    var periode = $("#periode_filter").val()

    to_show(periode)
    bang_l(dt[0].file_lk, dt[0].file_kertas_kerja_retrospektif)
    to_kolom(dt[0].id_perjanjian_pengusahaan)
    $("#modal-tambah-mtl").modal("show");
}



function combo(divname, table = '', colum = '1', id = '1', set = '') {

    url3 = "<?php echo base_url(); ?>index.php/perolehan_mutasi_kuantitas/tampildata/" + table + "/" + colum + "/" +
        id +
        "/combobox";
    $('#' + divname).val('').selectpicker('refresh');

    $.get(url3).done(function(data3) {
        jdata3 = JSON.parse(data3);
        //*
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));

        if (divname == 'pem_konsesi' || divname == 'satker') {
            $.each(jdata3, function(i, el) {
                $('#' + divname).append(new Option("(" + el.kode + ") " + el.nama, el.kode));

            });
        } else {
            $.each(jdata3, function(i, el) {
                $('#' + divname).append(new Option(el.nama, el.kode));

            });
        }

        $('#' + divname).val(set).selectpicker('refresh');

    }).fail(function() {
        alert("error");
    }).always(function() {
        // alert("finished");
    });

}

function hanyaAngka(a) {
    var inputValue = $(a).val();
    var newValue = inputValue.replace(/[^0-9,.]/g, ""); // Hanya biarkan angka, koma, dan titik

    // Hapus titik atau koma jika muncul lebih dari sekali
    newValue = newValue.replace(/(\.|,)[,.]+/g, "$1");

    $(a).val(newValue);
}

function formatRupiah(angka) {
    if (angka == '' || angka == null) {
        var formattedAngka = ''
    } else {
        var formattedAngka = "" + angka.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }
    return formattedAngka;
}

// function formatNumber(inputElement) {
//     let nilai = inputElement.value.replace(/\D/g, ''); // Menghapus karakter non-angka
//     inputElement.value = formatRupiah(nilai);
// }

function hapus_data(id) {
    swal({
            title: "Anda yakin menghapus data ?",
            // text: tex,
            icon: "warning",
            buttons: true,
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
                var url = ''
                url =
                    "<?php echo base_url(); ?>index.php/perolehan_mutasi_kuantitas/delete_data/perolehan_mutasi_kuantitas/id_perolehan_mutasi_kuantitas/" +
                    id;
                $.post(url, {
                    id: id
                }).done(function(data) {
                    if (data == 0) {
                        swal({
                            title: "",
                            text: "Berhasil Menghapus Data",
                            icon: "success",
                            showConfirmButton: false,
                            timer: 3000,
                            type: "success"
                        });
                        tabel.ajax.reload();
                        tabex()

                    }
                })
            } else {
                swal("Anda Tidak Menjadi Menghapus Data");
            }
        });

}
</script>
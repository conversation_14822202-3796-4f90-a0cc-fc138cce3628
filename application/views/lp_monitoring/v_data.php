<style>
.modal-dialog {
    width: 1000px !important;
}

.control-label {
    flex: none;
    width: 25%;
}

:not(.input-group)>.bootstrap-select.form-control:not([class*="col-"]) {
    width: 100%;
    border: 1px solid #80808033 !important;
    margin-top: 10px;
}

input[type="file"] {
    top: 0;
    right: 0;
    margin: 0;

    padding: 0;
    cursor: pointer;
    opacity: 0;
    filter: alpha(opacity=0);
    margin-top: -46px;
    width: 100% !important;
    height: 63px !important;
}

.input-group {
    margin-bottom: 0px;
}

#bang_l .fa {

    font-size: 10px;
}

.button-yuhu {
    width: 35px;
    height: 36px !important;
    margin-right: 1px;
    padding: 10px;
}

#kol {

    padding: 2px 0px 2px 0px;
    border-bottom: 1px solid #bbbbc62e;


}

#boxperjanjian {
    padding: 10px 0px 10px 8px;
    border: 1px dashed #eee;
    margin-top: 20px;
    border-radius: 10px
}

.buttons-html5::before {
    content: "\f1c3";
    /* Icon kode font-awesome, dapat disesuaikan */
    font-family: FontAwesome;
    /* Font-awesome font family */
    margin-right: 5px;
    /* Spasi antara ikon dan teks */
}

/* Berikan warna latar belakang */
.buttons-html5 {
    background-color: #4CAF50;
    /* Warna latar belakang */
    color: white;
    /* Warna teks */
    border: none;
    /* Hilangkan border */
    padding: 10px 20px;
    /* Ukuran tombol */
    cursor: pointer;
}

/* Efek hover */
.buttons-html5:hover {
    background-color: #45a049;
    /* Warna latar belakang hover */
}

/* Stil untuk tombol "Export Excel" dalam DataTables */
.dataTables_wrapper .buttons-excel {
    margin-bottom: 10px;
    height: 39px;
    border-radius: 5px;
    margin-right: 8px;
    font-size: 20px;
    padding: 5px 10px 5px 10px;
    margin-bottom: 10px;
    /* Spasi bawah tombol */
}

/* 
.dataTables_scrollHeadInner table {
    border-bottom: none;
} */

#styleSelector .form-control {

    background-color: #fff;
    border: 1px solid #80808042;
}

.tombolFilter {
    text-align: right;

}

.buttons-excel {
    background-color: #449f31d6 !important;
}

/* .table-bordered> :not(caption)>*>* {
    border-width: 1px;
    border-left-width: 1px;
} */
</style>
<textarea id="simpandatadetailperjanjian" style="display:none;"></textarea>
<textarea id="simpandatadetailtarif" style="display:none;"></textarea>
<textarea id="simpandatadetailslo" style="display:none;"></textarea>


<div class="content">

    <div class="page-inner mt--5">
        <div class="row mt--2">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">

                        <!-- <div class="col-md-3" id="hidetambah">
                            <button class="btn btn-tambah" id="tambah" onclick="tambahdata()">
                                <i class="fa fa-plus"></i> SK Tarif/COD
                            </button>
                            <br>
                            <br>
                        </div> -->
                        <div class="dt-responsive table-responsive">

                            <table class="display table table-bordered table-striped table-hover tab1" id="table-users">
                                <thead>
                                    <tr style="text-align:center;">
                                        <th id="th" class="INFO">No</th>
                                        <th id="th" class="INFO">Jenis</th>
                                        <th id="th" class="INFO">Jumlah Data</th>
                                        <th id="th" class="INFO">Jumlah Satker</th>
                                        <th id="th" class="INFO">Option</th>
                                    </tr>
                                </thead>
                                <tbody>


                                </tbody>
                            </table>
                            <div id="tes" style="display:none;">
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="styleSelector" class="" id="formfilter">
        <div style="display:none;" class="selector-toggle">
            <div id="yuhuu" class="close" onclick="close_filter(this)"></div>
        </div>



        <h6 style="color: white; background: #13208d8c; padding: 10px; border-radius: 10px; text-align: center;">
            Filtering Data</h6>

        <div class="row">
            <div class="col-md-12">
                <div class="" id="form">
                    <label class="col-xs-12" for="">
                        Nama Perjanjian Konsesi Jasa
                    </label>
                    <div class="col-xs-12">
                        <select type="text" name="nama_p_filter" class="form-control" placeholder=" " id="nama_p_filter"
                            data-live-search="true" onchange="to_filter(this)"></select>
                    </div>
                </div>
                <div class="" id="form">
                    <label class="col-xs-12" for="">
                        Badan Usaha
                    </label>
                    <div class="col-xs-12">

                        <select class="form-control" name="bdn_usaha_filter" id="bdn_usaha_filter"
                            data-live-search="true" onchange="to_filter(this)"></select>
                    </div>
                    <!-- <input type="text" name="nama" class="form-control" placeholder=" " id="nama" required autofocus /> -->
                </div>
                <div class="show_eselon" id="form " style="display:none;">
                    <label class="col-xs-12" for="">
                        Eselon I
                    </label>
                    <div class="col-xs-12">

                        <select class="form-control" name="eselon_filter" id="eselon_filter" data-live-search="true"
                            onchange="to_filter(this)"></select>
                    </div>
                </div>
                <div class="" id="form">
                    <label class="col-xs-12" for="">
                        Pemberi Konsesi
                    </label>
                    <div class="col-xs-12">

                        <select class="form-control" name="pem_konsesi_filter" id="pem_konsesi_filter"
                            data-live-search="true" onchange="to_filter(this)"></select>
                    </div>
                </div>

                <div class="" id="form">
                    <label class="col-xs-12" for="">
                        Pulau
                    </label>
                    <div class="col-xs-12">
                        <select class="form-control" name="pulau_filter" id="pulau_filter" data-live-search="true"
                            onchange="to_filter(this)"></select>
                    </div>
                </div>
                <div class="" id="form">
                    <label class="col-xs-12" for="">
                        Jenis Aset Konsesi Jasa
                    </label>
                    <div class="col-xs-12">
                        <select class="form-control" name="jenis_p_filter" id="jenis_p_filter" data-live-search="true"
                            onchange="to_filter(this)"></select>
                    </div>
                </div>
                <div class="form-group" id="daf">
                    <div class="col-xs-12">
                        <button class="btn btn-tambah" id="op" onclick="reset()" style="background: #01a9ac;">
                            <i class="fa fa-refresh"></i> Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>



    </div>
    <?php echo $jv_script; ?>
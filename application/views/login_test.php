<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KONJAS - Login Test</title>
    <link rel="stylesheet" href="<?php echo base_url(); ?>assets/vendor/bootstrap/css/bootstrap.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 500px;
            width: 100%;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-card">
        <h3 class="text-center mb-4">KONJAS Login Test</h3>
        <p class="text-muted text-center">Test your login credentials after security updates</p>
        
        <form id="testForm">
            <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <input type="text" class="form-control" id="username" name="username" required>
                <small class="form-text text-muted">Enter your username (e.g., admin)</small>
            </div>
            
            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" name="password" required>
                <small class="form-text text-muted">Enter your password</small>
            </div>
            
            <button type="submit" class="btn btn-primary w-100">Test Login</button>
        </form>
        
        <div id="result"></div>
        
        <div class="mt-4">
            <h5>Default Test Credentials:</h5>
            <ul class="list-unstyled">
                <li><strong>Username:</strong> admin</li>
                <li><strong>Password:</strong> admin</li>
            </ul>
            
            <h6 class="mt-3">Security Features Enabled:</h6>
            <ul class="small">
                <li>✅ CSRF Protection</li>
                <li>✅ Secure Password Hashing (Argon2ID)</li>
                <li>✅ MD5 Backward Compatibility</li>
                <li>✅ Input Validation</li>
                <li>✅ Security Headers</li>
                <li>✅ Session Security</li>
            </ul>
        </div>
        
        <div class="text-center mt-3">
            <a href="<?php echo base_url('login'); ?>" class="btn btn-outline-primary">Go to Main Login</a>
        </div>
    </div>

    <script src="<?php echo base_url(); ?>assets/vendor/jquery/jquery.min.js"></script>
    <script>
        $('#testForm').on('submit', function(e) {
            e.preventDefault();
            
            const username = $('#username').val();
            const password = $('#password').val();
            
            $('#result').html('<div class="test-result"><i class="fa fa-spinner fa-spin"></i> Testing login...</div>');
            
            $.ajax({
                url: '<?php echo base_url("login/aksi_login"); ?>',
                method: 'POST',
                data: {
                    user: username,
                    pass: password
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'sukses') {
                        $('#result').html(`
                            <div class="test-result success">
                                <h5>✅ Login Successful!</h5>
                                <p><strong>Message:</strong> ${response.msg}</p>
                                <p><strong>User Group:</strong> ${response.group}</p>
                                <p>Your credentials are working correctly with the new security system.</p>
                            </div>
                        `);
                    } else {
                        $('#result').html(`
                            <div class="test-result error">
                                <h5>❌ Login Failed</h5>
                                <p><strong>Error:</strong> ${response.msg}</p>
                                <p>Please check your username and password.</p>
                            </div>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    $('#result').html(`
                        <div class="test-result error">
                            <h5>❌ Connection Error</h5>
                            <p><strong>Error:</strong> ${error}</p>
                            <p>There was a problem connecting to the server.</p>
                        </div>
                    `);
                }
            });
        });
    </script>
</body>
</html>

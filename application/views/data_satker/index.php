<style>
table {
    width: 100% !important;
}


/ #modal-detail-mtl {
    width: 1000px;
    margin: auto;
}

/
</style>
<!-- MODAL TAMBAH keuangan ada d admin-->




<div id="modal-tambah-mtl" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Data</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class='fa fa-close' style='color:white;'></i>
                </button>
            </div>
            <div class="modal-body">
                <form method="post" class="form-user" id="submit">
                    <div class="form-group yuh">
                        <label class="control-label col-xs-2" for="nm_eselon">Pilih <PERSON>on 1:</label>
                        <div class="col-xs-9">
                            <input type="hidden" name="id" class="form-control" value="" id="id" />
                            <select class="form-control" name="eselon" id="eselon" onchange="to_name(this)"></select>
                            <input type="hidden" name="nm_eselon" class="form-control" value="" id="nm_eselon" />
                        </div>
                    </div>
                    <div class="form-group yuh">
                        <label class="control-label col-xs-2" for="kode_satker">Kode Satker:</label>
                        <div class="col-xs-9">
                            <input type="text" name="kode_satker" class="form-control" placeholder=" " id="kode_satker"
                                required autofocus />
                        </div>
                    </div>
                    <div class="form-group yuh">
                        <label class="control-label col-xs-2" for="nm_satker">Nama Satker:</label>
                        <div class="col-xs-9">
                            <input type="text" name="nm_satker" class="form-control" placeholder=" " id="nm_satker"
                                required autofocus />
                        </div>
                    </div>
                    <div class="form-group" id="daf">
                        <div class="col-xs-12">
                            <button class="btn btn-tambah" id="op">
                                <i class="fa fa-refresh"></i> Simpan
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>






<div class="content">
    <div class="page-inner mt--5">
        <div class="row mt--2">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="col-md-2" id="hidetambah">
                            <button class="btn btn-tambah" id="tambah" onclick="tambahdata()">
                                <i class="fa fa-plus"></i> Tambah Satker
                            </button>
                            <br>
                            <br>
                        </div>
                        <div class="dt-responsive table-responsive">
                            <table class="display table table-bordered table-striped table-hover tab1" id="table-users">
                                <thead>
                                    <tr style="text-align:center;">
                                        <th id="th" class="INFO">No</th>
                                        <th id="th" class="INFO">Kode Eselon 1</th>
                                        <th id="th" class="INFO">Nama Eselon 1</th>
                                        <th id="th" class="INFO">Kode Satker</th>
                                        <th id="th" class="INFO">Nama Satker</th>
                                        <th id="th" class="INFO" style="width:200px">Option</th>
                                </thead>

                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<?php echo $jv_script; ?>
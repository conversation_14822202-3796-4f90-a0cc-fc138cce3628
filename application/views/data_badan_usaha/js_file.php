<script>
$(document).ready(function() {
    $("#submit").submit(function(e) {
        e.preventDefault()
        // alert("asd");

        var data = $('.form-user').serialize();
        console.log(new FormData(this))

        $.ajax({
            type: 'POST',
            url: "<?php echo base_url('data_badan_usaha/insert_data'); ?>",
            data: new FormData(this),
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            success: function(data) {
                var ops = ''
                if (data.split('_')[1] == 'insert') {
                    ops = 'Menambah';
                } else {
                    ops = 'Merubah';
                }
                if (data.split('_')[0] == 0) {
                    swal({
                        title: "",
                        text: "Berhasil " + ops + " Data",
                        showConfirmButton: false,
                        timer: 1000,
                        type: "success"
                    });
                    tabel.ajax.reload();
                    $("#modal-tambah-mtl").modal("hide");
                } else {
                    swal({
                        title: "",
                        text: "Gagal " + ops + " Data",
                        showConfirmButton: false,
                        timer: 1000,
                        type: "warning"
                    });
                }

            }
        });
    });
    user()

})





function user() {
    var tab = $('#table-users').DataTable();
    tab.destroy()
    data_user()
    // var tom = `<button class="btn btn-tambah" id="tambah" onclick="tambahdata()">
    //                             <i class="fa fa-plus"></i> Tambah Badan Usaha/Mitra
    //                         </button>`
    // $(".tombolTambah").html(tom)
}

var tabel = null

function data_user() {
    tabel = $('#table-users').DataTable({
        "processing": true,
        "serverSide": true,
        "ordering": true, // Set true agar bisa di sorting
        "order": [
            [0, 'asc']
        ], // Default sortingnya berdasarkan kolom / field ke 0 (paling pertama)
        "ajax": {
            "url": "<?php echo base_url('data_badan_usaha/ssp') ?>", // URL file untuk proses select datanya
            "type": "POST"
        },

        "deferRender": true,
        "aLengthMenu": [
            [5, 10, 50],
            [5, 10, 50]
        ], // Combobox Limit
        "order": [
            [0, "desc"]
        ],
        "scrollX": true,
        "pageLength": 10,
        "columns": [{
                "render": function(data, type, row, meta) {
                    // Mengambil halaman yang sedang aktif
                    var page = tabel.page.info().page;
                    // Menghitung nomor urut unik berdasarkan halaman yang sedang aktif
                    var uniqueIndex = page * tabel.page.len() + meta.row + 1;
                    return uniqueIndex;
                }
            }, {
                "data": "nm_mitra"
            }, {
                "data": "no_telepon"
            }, {
                "data": "alamat"
            },
            {
                "render": function(data, type, row) { // Tampilkan kolom aksi

                    var html_button = [
                        "<button class='btn btn-danger btn-xs' title = 'Hapus Data' onclick=hapus_data('" +
                        row.id_badan_usaha + "')><i class='fa fa-trash'></i></button>",
                        "<button class='btn btn-primary btn-xs' title='Edit Data' onclick=edit_data('" +
                        row.id_badan_usaha + "')><i class='fa fa-edit'></i></button>",


                    ].join("\n");
                    return html_button;

                }
            }
        ],
        "language": {
            "decimal": "",
            "emptyTable": "Data tidak ditemukan",
            "info": "Data _START_ s/d _END_ dari _TOTAL_",
            "infoEmpty": "Tidak ada data",
            "infoFiltered": "(tersaring dari _MAX_)",
            "infoPostFix": "",
            "thousands": ",",
            "lengthMenu": "_MENU_  data per halaman",
            "loadingRecords": '<div class="preloader3 loader-block"><div class="circ1"></div><div class="circ2"></div> <div class="circ3"></div> <div class="circ4"></div></div>',
            "processing": '<div class="preloader3 loader-block"><div class="circ1"></div><div class="circ2"></div> <div class="circ3"></div> <div class="circ4"></div></div>',
            "search": "Cari:",
            "zeroRecords": "Tidak ada data ditemukan",
            "aria": {
                "sortAscending": ": aktifkan untuk mengurutkan naik",
                "sortDescending": ": aktifkan untuk mengurutkan turun"
            }
        }
    });
}




function tampil_data(table, colum, id) {
    var url = ''
    var tadata = ''
    urls = "<?php echo base_url(); ?>data_badan_usaha/tampildata/" + table + "/" + colum + "/" + id;
    $.ajax({
        url: urls,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function(data) {
            tadata = data;
        },
        failure: function(errMsg) {
            alert(errMsg);
        }
    });
    return tadata;
}


function tambahdata() {
    $(".form-user input").val('')
    $(".form-user select").val('')
    $(".form-user textarea").val('')
    $("#modal-tambah-mtl").modal("show");

}

function edit_data(id) {
    $(".form-user input").val('')
    $(".form-user select").val('')
    var dt = tampil_data('ref_badan_usaha', 'id_badan_usaha', id);
    $("#id").val(dt[0].id_badan_usaha)
    $("#nama").val(dt[0].nm_mitra)
    $("#nohp").val(dt[0].no_telepon)
    $("#alamat").val(dt[0].alamat)
    // combo('direktorat','data_badan_usaha~nama~id','1','1',dt[0].id)
    // combo('jabatan','data_jabatan~nama~kode','1','1',dt[0].kode_jabatan)
    // to_divisi(dt[0].id,dt[0].kode_divisi)
    // to_departemen(dt[0].kode_divisi,dt[0].kode_departemen)
    $("#modal-tambah-mtl").modal("show");
}


function combo(divname, table = '', colum = '1', id = '1', set = '') {

    url3 = "<?php echo base_url(); ?>data_badan_usaha/tampildata/" + table + "/" + colum + "/" + id +
        "/combobox";

    $.get(url3).done(function(data3) {
        jdata3 = JSON.parse(data3);
        //*
        $('#' + divname).empty();
        $('#' + divname).append(new Option("--Pilih--", ""));


        $.each(jdata3, function(i, el) {
            $('#' + divname).append(new Option(el.nama, el.kode));

        });
        $('#' + divname).val(set);

    }).fail(function() {
        alert("error");
    }).always(function() {
        // alert("finished");
    });
}


function hapus_data(id) {
    swal({
            title: "Anda yakin menghapus data ?",
            // text: tex,
            icon: "warning",
            buttons: true,
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
                var url = ''
                url =
                    "<?php echo base_url(); ?>data_badan_usaha/delete_data/ref_badan_usaha/id_badan_usaha/" +
                    id;
                $.post(url, {
                    id: id
                }).done(function(data) {
                    if (data == 0) {
                        swal({
                            title: "",
                            text: "Berhasil Menghapus Data",
                            icon: "success",
                            showConfirmButton: false,
                            timer: 3000,
                            type: "success"
                        });
                        tabel.ajax.reload();
                    }
                })
            } else {
                swal("Anda Tidak Menjadi Menghapus Data");
            }
        });

}
</script>
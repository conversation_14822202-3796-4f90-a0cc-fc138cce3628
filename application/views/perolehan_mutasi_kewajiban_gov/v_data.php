<style>
.modal-dialog {
    width: 1000px !important;
}

.control-label {
    flex: none;
    width: 25%;
}

:not(.input-group)>.bootstrap-select.form-control:not([class*="col-"]) {
    width: 100%;
    border: 1px solid #80808033 !important;
    margin-top: 10px;
}

input[type="file"] {
    top: 0;
    right: 0;
    margin: 0;

    padding: 0;
    cursor: pointer;
    opacity: 0;
    filter: alpha(opacity=0);
    margin-top: -46px;
    width: 100% !important;
    height: 63px !important;
}

.input-group {
    margin-bottom: 0px;
}

#bang_l .fa {

    font-size: 10px;
}

.button-yuhu {
    width: 35px;
    height: 36px !important;
    margin-right: 1px;
    padding: 10px;
}

#kol {

    padding: 2px 0px 2px 0px;
    border-bottom: 1px solid #bbbbc62e;


}

#boxperjanjian {
    padding: 10px 0px 10px 8px;
    border: 1px dashed #eee;
    margin-top: 20px;
    border-radius: 10px
}

.buttons-html5::before {
    content: "\f1c3";
    /* Icon kode font-awesome, dapat disesuaikan */
    font-family: FontAwesome;
    /* Font-awesome font family */
    margin-right: 5px;
    /* Spasi antara ikon dan teks */
}

/* Berikan warna latar belakang */
.buttons-html5 {
    background-color: #4CAF50;
    /* Warna latar belakang */
    color: white;
    /* Warna teks */
    border: none;
    /* Hilangkan border */
    padding: 10px 20px;
    /* Ukuran tombol */
    cursor: pointer;
}

/* Efek hover */
.buttons-html5:hover {
    background-color: #45a049;
    /* Warna latar belakang hover */
}

/* Stil untuk tombol "Export Excel" dalam DataTables */
.dataTables_wrapper .buttons-excel {
    margin-bottom: 10px;
    height: 39px;
    border-radius: 5px;
    margin-right: 8px;
    font-size: 20px;
    padding: 5px 10px 5px 10px;
    margin-bottom: 10px;
    /* Spasi bawah tombol */
}

/* 
.dataTables_scrollHeadInner table {
    border-bottom: none;
} */

#styleSelector .form-control {

    background-color: #fff;
    border: 1px solid #80808042;
}

.tombolFilter {
    text-align: right;

}

.buttons-excel {
    background-color: #449f31d6 !important;
}

/* .table-bordered> :not(caption)>*>* {
    border-width: 1px;
    border-left-width: 1px;
} */

/* Gaya tambahan untuk pesan validasi kustom */
input:invalid {
    border-color: red;
    /* Ubah warna border saat input tidak valid */
}

input:invalid::placeholder {
    color: red;
    /* Ubah warna placeholder saat input tidak valid */
}

input:invalid+.custom-validation-message::before {
    content: attr(data-custom-validation-message);
    /* Tampilkan pesan validasi kustom */
    color: red;
    /* Ubah warna teks pesan validasi kustom */
}
</style>
<textarea id="simpandatadetailperjanjian" style="display:none;"></textarea>
<textarea id="simpandatadetailtarif" style="display:none;"></textarea>
<textarea id="simpandatadetailslo" style="display:none;"></textarea>
<div id="modal-tambah-mtl" class="modal fade" role="dialog">
    <div class="modal-dialog">


        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Data</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class='fa fa-close' style='color:white;'></i>
                </button>
            </div>
            <div class="modal-body">
                <form method="post" class="form-user" id="submit">
                    <input type="hidden" name="id" class="form-control" value="" id="id" />
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group yuh" id="form">
                                <label class="control-label col-xs-12" for="">
                                    Nama Perjanjian Konsesi Jasa

                                </label>

                                <select class="form-control" name="nm_perjanjian" id="nm_perjanjian"
                                    onchange="to_kolom(this)" data-live-search="true"></select>
                            </div>
                            <div class="form-group yuh" id="form">
                                <div class="col-sm-3">
                                    <label class="control-label col-xs-12" for="" style="width:100% !important;">
                                        Periode Perolehan
                                    </label>
                                </div>
                                <div class="col-sm-4">
                                    <select class="form-control" name="p_perolehan" id="p_perolehan"
                                        data-live-search="true">
                                        <option value="">--Pilih--</option>
                                        <option value="Triwulan I s.d II">Triwulan I s.d II</option>
                                        <option value="Triwulan III">Triwulan III</option>
                                        <option value="Triwulan IV">Triwulan IV</option>
                                        <option value="Triwulan Audited">Triwulan Audited</option>
                                    </select>
                                </div>
                                <div class="col-sm-1"></div>
                                <div class="col-sm-2">
                                    <label class="control-label col-xs-12" for="" style="width:100% !important;">
                                        Tahun
                                    </label>
                                </div>
                                <div class="col-sm-2">
                                    <input type="text" class="form-control" name="tahun_pembukuan" id="tahun_pembukuan"
                                        readonly>
                                </div>
                            </div>
                            <div class="form-group yuh" id="form">
                                <div class="col-sm-3">
                                    <label class="control-label col-xs-12" for="" style="width:100% !important;">
                                        Periode Pembukuan
                                    </label>
                                </div>
                                <div class="col-sm-4">
                                    <select class="form-control" name="p_pembukuan" id="p_pembukuan"
                                        data-live-search="true" onchange="to_show(this)">
                                        <option value="">--Pilih--</option>
                                        <option value="Triwulan I s.d II">Triwulan I s.d II</option>
                                        <option value="Triwulan III">Triwulan III</option>
                                        <option value="Triwulan IV">Triwulan IV</option>
                                        <option value="Triwulan Audited">Triwulan Audited</option>
                                    </select>

                                </div>
                                <div class="col-sm-1"></div>
                                <div class="col-sm-2">
                                    <label class="control-label col-xs-12" for="" style="width:100% !important;">
                                        Tahun
                                    </label>
                                </div>
                                <div class="col-sm-2">
                                    <input type="text" class="form-control" name="tahun_perolehan" id="tahun_perolehan"
                                        readonly>
                                </div>
                                <input type="hidden" name="kategori" class="form-control" value="" id="kategori" />
                            </div>
                            <div class="form-group yuh" id="fix">
                                <label class="control-label col-xs-12" for="">
                                    Nilai Buku Kewajiban
                                </label>
                                <input class="form-control" name="nilai_buku_kewajiban" id="nilai_buku_kewajiban"
                                    onkeyup="formatNumber(this)" readonly>
                            </div>
                            <div class="form-group yuh" id="tempo">
                                <label class="control-label col-xs-12" for="">
                                    Nilai Buku Kewajiban x
                                </label>
                                <input class="form-control" name="nilai_buku_kewajiban_tempo"
                                    id="nilai_buku_kewajiban_tempo" onkeyup="formatNumber(this)" readonly>
                            </div>
                            <div id="box-1" style="display:none;">
                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Mutasi Kewajiban Triwulan I s.d II
                                    </label>
                                    <input class="form-control" name="nilai_mutasi_kewajiban_tw1_2"
                                        id="nilai_mutasi_kewajiban_tw1_2" onkeyup="formatNumber(this)">
                                </div>
                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Perolehan Triwulan I s.d II
                                    </label>
                                    <input class="form-control" name="nilai_perolehan_tw1_2" id="nilai_perolehan_tw1_2"
                                        onkeyup="formatNumber(this)">
                                </div>

                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Buku Triwulan I s.d II

                                    </label>
                                    <input class="form-control" name="nilai_buku_tw1_2" id="nilai_buku_tw1_2" readonly>
                                </div>
                            </div>
                            <div id="box-2" style="display:none;">
                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Mutasi Kewajiban Triwulan III
                                    </label>
                                    <input class="form-control" name="nilai_mutasi_kewajiban_tw3"
                                        id="nilai_mutasi_kewajiban_tw3" onkeyup="formatNumber(this)">
                                </div>
                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Perolehan Triwulan III
                                    </label>
                                    <input class="form-control" name="nilai_perolehan_tw3" id="nilai_perolehan_tw3"
                                        onkeyup="formatNumber(this)">
                                </div>

                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Buku Triwulan III

                                    </label>
                                    <input class="form-control" name="nilai_buku_tw3" id="nilai_buku_tw3" readonly>
                                </div>
                            </div>
                            <div id="box-3" style="display:none;">
                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Mutasi Kewajiban Triwulan IV
                                    </label>
                                    <input class="form-control" name="nilai_mutasi_kewajiban_tw4"
                                        id="nilai_mutasi_kewajiban_tw4" onkeyup="formatNumber(this)">
                                </div>
                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Perolehan Triwulan IV
                                    </label>
                                    <input class="form-control" name="nilai_perolehan_tw4" id="nilai_perolehan_tw4"
                                        onkeyup="formatNumber(this)">
                                </div>

                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Buku Triwulan IV

                                    </label>
                                    <input class="form-control" name="nilai_buku_tw4" id="nilai_buku_tw4" readonly>
                                </div>
                            </div>
                            <div id="box-4" style="display:none;">
                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Mutasi Kewajiban Audited
                                    </label>
                                    <input class="form-control" name="nilai_mutasi_kewajiban_audited"
                                        id="nilai_mutasi_kewajiban_audited" onkeyup="formatNumber(this)">
                                </div>
                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Perolehan Audited
                                    </label>
                                    <input class="form-control" name="nilai_perolehan_audited"
                                        id="nilai_perolehan_audited" onkeyup="formatNumber(this)">
                                </div>

                                <div class="form-group yuh" id="form">
                                    <label class="control-label col-xs-12" for="">
                                        Nilai Buku Audited

                                    </label>
                                    <input class="form-control" name="nilai_buku_audited" id="nilai_buku_audited"
                                        readonly>
                                </div>
                            </div>

                            <div class="form-group yuh" id="form">

                                <div class="col-md-12" style="text-align:center;">
                                    Dokumen Pendukung
                                    <input type="hidden" value="0" id="urutan">

                                    <div id="boxperjanjian">
                                        <div class="row">

                                        </div>
                                        <br>
                                        <div id="bang_lai">

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" id="daf">
                        <div class="col-xs-12">
                            <button class="btn btn-tambah" id="op">
                                <i class="fa fa-refresh"></i> Simpan
                            </button>
                        </div>
                    </div>
                </form>

            </div>
        </div>
    </div>
</div>


<div id="modal-download" class="modal fade" role="dialog">
    <div class="modal-dialog">


        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Dowload File</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class='fa fa-close' style='color:white;'></i>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="id" class="form-control" value="" id="id" />
                <div class="row">
                    <div class="col-md-12">

                        <div class="form-group yuh" id="form">

                            <div class="col-md-12" style="text-align:center;">
                                Dokumen Pendukung
                                <input type="hidden" value="0" id="urutan">

                                <div id="boxperjanjian">
                                    <div class="row">


                                    </div>
                                    <br>
                                    <div id="box-download4">

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>


<div class="content">

    <div class="page-inner mt--5">
        <div class="row mt--2">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <!-- <div class="col-md-3" id="hidetambah">
                            <button class="btn btn-tambah" id="tambah" onclick="tambahdata()">
                                <i class="fa fa-plus"></i> SK Tarif/COD
                            </button>
                            <br>
                            <br>
                        </div> -->
                        <div class="card-header">
                            <h5 style="font-weight:100;">
                                Pencatatan Nilai Aset > <a href="#">Perolehan / Mutasi Nilai Kewajiban</a>
                            </h5>
                        </div>
                        <div class="dt-responsive table-responsive">

                            <table class="display table table-bordered table-striped table-hover tab1" id="table-users">
                                <thead>
                                    <tr style="text-align:center;">
                                        <th rowspan="2">No</th>
                                        <th rowspan="2">Nama Perjanjian Konsesi Jasa</th>
                                        <th rowspan="2">Kode Satker</th>
                                        <th rowspan="2">Satuan Kerja</th>
                                        <th rowspan="2">Nilai Buku Kewajiban</th>
                                        <th colspan="2">Mutasi Triwulan I dan II</th>
                                        <th rowspan="2">Nilai buku Triwulan I s.d II</th>

                                        <th colspan="2">Mutasi Triwulan III</th>
                                        <th rowspan="2">Nilai buku Triwulan III</th>

                                        <th colspan="2">Mutasi Triwulan IV</th>
                                        <th rowspan="2">Nilai buku Triwulan IV</th>

                                        <th colspan="2">Mutasi Triwulan Audited</th>
                                        <th rowspan="2">Nilai buku Audited</th>

                                        <th rowspan="2">Option</th>
                                    </tr>
                                    <tr style="text-align:center;">
                                        <th class="INFO">Mutasi kewajiban</th>
                                        <th class="INFO">Nilai Perolehan</th>

                                        <th class="INFO">Mutasi kewajiban</th>
                                        <th class="INFO">Nilai Perolehan</th>

                                        <th class="INFO">Mutasi kewajiban</th>
                                        <th class="INFO">Nilai Perolehan</th>

                                        <th class="INFO">Mutasi kewajiban</th>
                                        <th class="INFO">Nilai Perolehan</th>

                                    </tr>

                                </thead>
                                <tbody>
                                    <!-- Tambahkan baris data di sini -->
                                </tbody>
                            </table>

                            <div id="tes" style="display:none;">
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="styleSelector" class="" id="formfilter">
        <div style="display:none;" class="selector-toggle">
            <div id="yuhuu" class="close" onclick="close_filter(this)"></div>
        </div>



        <h6 style="color: white; background: #13208d8c; padding: 10px; border-radius: 10px; text-align: center;">
            Filtering Data</h6>

        <div class="row">
            <div class="col-md-12">
                <div class="" id="form">
                    <label class="col-xs-12" for="">
                        Nama Perjanjian Konsesi Jasa
                    </label>
                    <div class="col-xs-12">
                        <select type="text" name="nama_p_filter" class="form-control" placeholder=" " id="nama_p_filter"
                            data-live-search="true" onchange="to_filter(this)"></select>
                    </div>
                </div>
                <div class="" id="form">
                    <label class="col-xs-12" for="">
                        Badan Usaha
                    </label>
                    <div class="col-xs-12">

                        <select class="form-control" name="bdn_usaha_filter" id="bdn_usaha_filter"
                            data-live-search="true" onchange="to_filter(this)"></select>
                    </div>
                    <!-- <input type="text" name="nama" class="form-control" placeholder=" " id="nama" required autofocus /> -->
                </div>
                <div class="show_eselon" id="form " style="display:none;">
                    <label class="col-xs-12" for="">
                        Eselon I
                    </label>
                    <div class="col-xs-12">

                        <select class="form-control" name="eselon_filter" id="eselon_filter" data-live-search="true"
                            onchange="to_filter(this)"></select>
                    </div>
                </div>
                <div class="" id="form">
                    <label class="col-xs-12" for="">
                        Pemberi Konsesi
                    </label>
                    <div class="col-xs-12">

                        <select class="form-control" name="pem_konsesi_filter" id="pem_konsesi_filter"
                            data-live-search="true" onchange="to_filter(this)"></select>
                    </div>
                </div>

                <div class="" id="form">
                    <label class="col-xs-12" for="">
                        Pulau
                    </label>
                    <div class="col-xs-12">
                        <select class="form-control" name="pulau_filter" id="pulau_filter" data-live-search="true"
                            onchange="to_filter(this)"></select>
                    </div>
                </div>
                <div class="" id="form">
                    <label class="col-xs-12" for="">
                        Jenis Aset Konsesi Jasa
                    </label>
                    <div class="col-xs-12">
                        <select class="form-control" name="jenis_p_filter" id="jenis_p_filter" data-live-search="true"
                            onchange="to_filter(this)"></select>
                    </div>
                </div>
                <div class="form-group" id="daf">
                    <div class="col-xs-12">
                        <button class="btn btn-tambah" id="op" onclick="reset()" style="background: #01a9ac;">
                            <i class="fa fa-refresh"></i> Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>



    </div>
    <?php echo $jv_script; ?>
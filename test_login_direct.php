<?php
/**
 * Direct Login Test Script
 */

$username = isset($argv[1]) ? $argv[1] : '03301';
$password = isset($argv[2]) ? $argv[2] : '03301!!@#';

echo "Testing login for: $username with password: $password\n";

// Test the login via HTTP POST
$url = 'http://localhost/konjas/login/aksi_login';
$data = array(
    'user' => $username,
    'pass' => $password
);

$options = array(
    'http' => array(
        'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
        'method'  => 'POST',
        'content' => http_build_query($data)
    )
);

$context  = stream_context_create($options);
$result = file_get_contents($url, false, $context);

echo "Response: $result\n";

// Also test the MD5 hash
echo "\nMD5 hash of password '$password': " . md5($password) . "\n";

// Test against known hash
$known_hash = '92c03384b1939b4cbee235eec046e00d'; // From database
echo "Known hash from database: $known_hash\n";
echo "Hashes match: " . (md5($password) === $known_hash ? "YES" : "NO") . "\n";
?>

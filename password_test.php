<?php
/**
 * Password Testing Script
 * 
 * This script helps test common password patterns for users
 */

// Include CodeIgniter bootstrap
require_once 'index.php';

echo "=== KONJAS Password Test ===\n\n";

// Get CI instance
$CI =& get_instance();
$CI->load->database();

// Test user
$test_user = isset($argv[1]) ? $argv[1] : '03301';

echo "Testing passwords for user: $test_user\n\n";

// Get user data
$CI->db->where('userlogin', $test_user);
$query = $CI->db->get('v_user');

if ($query->num_rows() == 0) {
    echo "User '$test_user' not found!\n";
    exit(1);
}

$user = $query->row();
$stored_hash = $user->password;

echo "User found:\n";
echo "- ID: " . $user->id . "\n";
echo "- Username: " . $user->username . "\n";
echo "- Password hash: " . $stored_hash . "\n";
echo "- Hash length: " . strlen($stored_hash) . "\n";
echo "- Hash type: " . (strlen($stored_hash) == 32 && ctype_xdigit($stored_hash) ? "MD5" : "Modern") . "\n\n";

// Common password patterns to test
$password_patterns = [
    $test_user,                    // Same as username
    $test_user . '!!@#',          // Username + special chars
    $test_user . '123',           // Username + numbers
    $test_user . '!',             // Username + !
    $test_user . '@',             // Username + @
    $test_user . '#',             // Username + #
    '123456',                     // Common password
    'password',                   // Common password
    'admin',                      // Common password
    '12345',                      // Common password
    $test_user . $test_user,      // Username repeated
    strtolower($test_user),       // Lowercase
    strtoupper($test_user),       // Uppercase
    '033!!@#',                    // Pattern mentioned by user
    '033',                        // Base pattern
];

echo "Testing common password patterns:\n";
echo "================================\n";

$found = false;
foreach ($password_patterns as $password) {
    $test_hash = md5($password);
    $match = ($test_hash === $stored_hash);
    
    echo sprintf("%-20s | %-32s | %s\n", 
        $password, 
        $test_hash, 
        $match ? "✓ MATCH!" : "✗"
    );
    
    if ($match) {
        $found = true;
        echo "\n🎉 PASSWORD FOUND: '$password'\n";
        break;
    }
}

if (!$found) {
    echo "\n❌ No matching password found in common patterns.\n";
    echo "\nTry running: php password_test.php $test_user\n";
    echo "Or provide the actual password to verify:\n";
    echo "php -r \"echo md5('your_password_here') . PHP_EOL;\"\n";
}

echo "\n=== Test Complete ===\n";
?>

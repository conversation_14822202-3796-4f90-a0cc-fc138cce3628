/* Modern Login Page Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body, html {
    height: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Main Container */
.modern-login-container {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

/* Login Card */
.login-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    max-width: 900px;
    width: 100%;
    /* min-height: 500px; */
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Logo Section */
.logo-section {
    /*background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%); */
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    color: white;
    text-align: center;
}

.logo-container {
    margin-bottom: 30px;
}

.pupr-logo {
    width: 250px;
    height: auto;
    /* filter: brightness(0) invert(1); */
    background: white;
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.system-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 5px 0;
    letter-spacing: 2px;
}

.system-title .highlight {
    color: #fbbf24;
}

.subtitle {
    font-size: 1rem;
    margin-top: 15px;
    opacity: 0.9;
    line-height: 1.4;
    font-weight: 300;
}

/* Form Section */
.form-section {
    flex: 1;
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.modern-login-form {
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
}

/* Input Groups */
.input-group {
    position: relative;
    margin-bottom: 25px;
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    z-index: 2;
}

.form-input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.form-input:focus {
    outline: none;
    border-color: #4f46e5;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    transform: translateY(-1px);
}

.input-group:hover .form-input {
    border-color: #6b7280;
}

.form-input::placeholder {
    color: #9ca3af;
}

/* Captcha Section */
.captcha-section {
    margin-bottom: 30px;
}

.captcha-label {
    font-size: 14px;
    color: #374151;
    margin-bottom: 10px;
    font-weight: 500;
}

.captcha-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.captcha-canvas {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
}

.captcha-input-container {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.captcha-input {
    flex: 1;
    min-width: 150px;
    padding: 12px 0px 12px 4px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    background: #f9fafb;
    transition: all 0.3s ease;
}

.captcha-input:focus {
    outline: none;
    border-color: #4f46e5;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.captcha-refresh {
    padding: 12px;
    background: #f3f4f6;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
}

.captcha-refresh:hover {
    background: #e5e7eb;
    color: #374151;
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-card {
        flex-direction: column;
        max-width: 400px;
        margin: 20px;
    }
    
    .logo-section {
        padding: 30px 20px;
    }
    
    .system-title h2 {
        font-size: 1.8rem;
    }
    
    .form-section {
        padding: 40px 30px;
    }
    
    .captcha-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .captcha-input-container {
        margin-top: 10px;
    }
}

@media (max-width: 480px) {
    .modern-login-container {
        padding: 10px;
    }
    
    .logo-section {
        padding: 20px 15px;
    }
    
    .system-title h2 {
        font-size: 1.5rem;
    }
    
    .form-section {
        padding: 30px 20px;
    }
}

/* Error and Success States */
.captcha-section.error {
    border: 2px solid #ef4444 !important;
    background: #fef2f2;
    padding: 15px;
    border-radius: 8px;
}

.captcha-section.success {
    border: 2px solid #10b981 !important;
    background: #f0fdf4;
    padding: 15px;
    border-radius: 8px;
}

.login-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
}

.login-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

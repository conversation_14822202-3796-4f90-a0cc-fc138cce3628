

function harusangka(jumlah)
    {
        var angka = (jumlah.which) ? jumlah.which : jumlah.keyCode

        if (angka > 31 && (angka < 48 || angka > 57))
        {
            
            alert("Kolom Tersebut Hanya Diperbolehkan Angka !!");


            return false;
        }

    }


    function harushuruf(event) {
        var charCode = (event.which) ? event.which : event.keyCode
        if ((charCode < 65 || charCode > 90) && (charCode < 97 || charCode > 122) && charCode > 32)
        {
            if ("return false")
            {
            alert("Kolom Tersebut Hanya Diperbolehkan Huruf !!");


                return false;
            } else
            {
                return true;
            }



        }
    }

    $(".webgis-input-number").keydown(function (e) {
        // Allow: backspace, delete, tab, escape, enter and .
        if ($.inArray(e.keyCode, [46, 8, 9, 27, 13, 110, 190]) !== -1 ||
             // Allow: Ctrl/cmd+A
            (e.keyCode == 65 && (e.ctrlKey === true || e.metaKey === true)) ||
             // Allow: Ctrl/cmd+C
            (e.keyCode == 67 && (e.ctrlKey === true || e.metaKey === true)) ||
             // Allow: Ctrl/cmd+X
            (e.keyCode == 88 && (e.ctrlKey === true || e.metaKey === true)) ||
             // Allow: home, end, left, right
            (e.keyCode >= 35 && e.keyCode <= 39)) {
                 // let it happen, don't do anything
            
                 return;
        }
        // Ensure that it is a number and stop the keypress
        if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
            e.preventDefault();
             alert("Isian Harus karakter Numerik!")
        }
    });

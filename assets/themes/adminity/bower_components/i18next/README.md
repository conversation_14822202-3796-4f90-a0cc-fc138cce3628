# i18next

[![<PERSON>](https://img.shields.io/travis/i18next/i18next/master.svg?style=flat-square)](https://travis-ci.org/i18next/i18next)
[![Coveralls](https://img.shields.io/coveralls/i18next/i18next/master.svg?style=flat-square)](https://coveralls.io/github/i18next/i18next)
[![cdnjs version](https://img.shields.io/cdnjs/v/i18next.svg?style=flat-square)](https://cdnjs.com/libraries/i18next)
[![npm version](https://img.shields.io/npm/v/i18next.svg?style=flat-square)](https://www.npmjs.com/package/i18next)
[![Bower](https://img.shields.io/bower/v/i18next.svg?style=flat-square)]()
[![David](https://img.shields.io/david/i18next/i18next.svg?style=flat-square)](https://david-dm.org/i18next/i18next)

i18next is a very popular internationalization framework for browser or any other javascript environment (eg. node.js).

![ecosystem](http://i18next.com/img/frameworks.png)


i18next provides:

- Flexible connection to [backend](http://i18next.com/docs/ecosystem/#backends) (loading translations via xhr, ...)
- Optional [caching](http://i18next.com/docs/ecosystem/#caches), user [language detection](http://i18next.com/docs/ecosystem/#languagedetector), ...
- Proper [pluralizations](http://i18next.com/translate/pluralSimple/)
- Translation [context](http://i18next.com/translate/context/)
- [Nesting](http://i18next.com/translate/nesting/), [Variable replacement](http://i18next.com/translate/interpolation/)
- Flexibility: prefer [keybased](http://i18next.com/translate/) or [gettext](http://i18next.com/translate/keyBasedFallback/) style? No problem...
- Extensibility: eg. [sprintf](http://i18next.com/docs/ecosystem/#postprocessors)
- ...


For more information visit the website:

- [Getting started](http://i18next.com/docs/)
- [Translation Functionality](http://i18next.com/translate/)
- [API](http://i18next.com/docs/api/)
- [Migration Guide from v1.11.x](http://i18next.com/docs/migration/)



Our focus is providing the core to building a booming ecosystem. Independent of the building blocks you choose, be it react, angular or even good old jquery proper translation capabilities are just [one step away](http://i18next.com/docs/ecosystem/#frameworks).

--------------
**NEWS: localization as a service - locize.com**

Having done a big rewrite of i18next in spring we are proud to announce the next big step to get your webproject translated with less effort. We just released [locize](http://locize.com/) a translation management system built around the i18next ecosystem.

![locize](http://i18next.com/img/locize_recap_medium_low.gif)

With using [locize](http://locize.com/) you directly support the future of i18next.

--------------

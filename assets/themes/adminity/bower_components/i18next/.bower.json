{"name": "i18next", "main": "./i18next.min.js", "dependencies": {}, "ignore": ["test/", "dist/", "src/", "coverage/", ".babelrc", ".giti<PERSON>re", ".editorconfig", ".es<PERSON><PERSON><PERSON>", ".eslintrc", ".n<PERSON><PERSON><PERSON>", "gulpfile.js", "package.json", "karma.conf.js"], "homepage": "https://github.com/i18next/i18next", "version": "7.1.3", "_release": "7.1.3", "_resolution": {"type": "version", "tag": "v7.1.3", "commit": "eb036b4b3db9c2dd2f0dc079ec4f07e01da23590"}, "_source": "https://github.com/i18next/i18next.git", "_target": "7.1.3", "_originalSource": "i18next"}
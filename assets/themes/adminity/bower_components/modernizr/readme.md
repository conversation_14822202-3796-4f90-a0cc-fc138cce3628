Modernizr [![Build Status](https://secure.travis-ci.org/Modernizr/Modernizr.png?branch=master)](http://travis-ci.org/Modernizr/Modernizr)
=========

### a JavaScript library allowing you to use CSS3 & HTML5 while maintaining control over unsupported browsers 

Modernizr tests which native CSS3 and HTML5 features are available in
the current UA and makes the results available to you in two ways:
as properties on a global `Modernizr` object, and as classes on the
`<html>` element. This information allows you to progressively enhance
your pages with a granular level of control over the experience.

Modernizr has an optional (*not included*) conditional resource loader
called `Modernizr.load()`, based on Yepnope.js ([yepnopejs.com](http://yepnopejs.com/)).
To get a build that includes `Modernizr.load()`, as well as choosing
which tests to include, go to [www.modernizr.com/download/](http://www.modernizr.com/download/)

[Full documentation on modernizr.com/docs/](http://www.modernizr.com/docs/)

* * *

Modernizr is dual-licensed under the [BSD and MIT licenses](http://www.modernizr.com/license/).

[modernizr.com](http://www.modernizr.com/)


#### Try it out: 

Run the test suite: [http://modernizr.github.com/Modernizr/test/](http://modernizr.github.com/Modernizr/test/)

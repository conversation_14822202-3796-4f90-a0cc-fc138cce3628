/*
 * Test for SubPixel Font Rendering
 * (to infer if GDI or DirectWrite is used on Windows)
 * Authors: <AUTHORS>
 * Web: https://github.com/gerritvanaaken/subpixeldetect
 */
Modernizr.addTest('subpixelfont', function() {
    var bool,
        styles = "#modernizr{position: absolute; top: -10em; visibility:hidden; font: normal 10px arial;}#subpixel{float: left; font-size: 33.3333%;}";
    
    // see https://github.com/Modernizr/Modernizr/blob/master/modernizr.js#L97
    Modernizr.testStyles(styles, function(elem) {
        var subpixel = elem.firstChild;

        subpixel.innerHTML = 'This is a text written in Arial';

        bool = window.getComputedStyle ?
            window.getComputedStyle(subpixel, null).getPropertyValue("width") !== '44px'
            : false;
    }, 1, ['subpixel']);

    return bool;
});

body {
	font-family: "Lucida Grande", Lucida, Verdana, sans-serif;
	font-size: 12px;
}

a {
	text-decoration: none;
}
a:hover {
	text-decoration: underline;
}

table, tr, th, td {
	border: 1px solid #AAA;
}

table {
	margin-top: 1em;
	width: 100%;
}

tbody th {
	text-align: left;
	font-size: 14px;
	width: 200px;
}

th h3 {
	margin: 3px;
	font-size: 12px;
}

th span.links {
	font-size: 10px;
}

dt {
	font-weight: bold;
}

tr:hover > th,
tr:hover > td + td { background-color: #FFC; }

div.test_wrap {
	display: -moz-inline-stack;
	display: inline-block;
	border: 1px solid #CCC;
	text-align: center;
	vertical-align: top;
	min-height: 50px;
	margin-right: 5px;
	background: white;
	position: relative;
}

div.test_wrap h3 {
	text-align: center;
	margin: 2px;
	font-size: 10px;
}

div.auto {
	display: -moz-inline-stack;
	display: inline-block;
	border: 1px solid;
	width: 30px;
	height: 30px;
}

div.square {
	display: -moz-inline-stack;
	display: inline-block;
	border: 1px solid;
	width: 30px;
	height: 30px;
	background: red;
}

div.info {
	display: none;
	position: absolute;
	top: 100%;
	z-index: 2;
	left: 0;
	background: white;
	padding: 2px;
	min-width: 300px;
	border: 1px solid;
	text-align: left;
}

div.test_wrap:hover > div.info {
	display: block;
}


div.vis_test {
	display: -moz-inline-stack;
	display: inline-block;
}

div.vis_ref {
	display: -moz-inline-stack;
	display: inline-block;
	border-left: 1px dashed black;
	margin-left: 5px;
	padding-left: 5px;
	vertical-align: top;
}

p.condition {
	font-style: italic;
	margin: 2px;
	clear: both;
}

.pass {
  background: lime;
}

.fail {
  background: red;
}

.partial {
  background: yellow;
}

.unknown {
  background: #aaa;
}

.current span {
    border-radius: 6px;
    -moz-border-radius: 6px;
    background: none repeat scroll 0 0 #E6EA69;
    color: black;
    float: right;
    font-size: 8px;
    padding: 0 1px;
}

#intro, #options {
	width: 400px;
	background: #EEE;
	border-radius: 10px;
	padding: 5px 10px;
	margin: 10px;
	float: left;
}

#intro dt::after {
	content: ':';
}

#intro dd {
	margin-bottom: 1em;
}

#opt_submit {
	display: block;
	margin: 10px;
}

#options label {
	display: block;
	margin: 5px;
}
{"name": "popperjs-monorepo", "version": "0.0.0", "license": "MIT", "private": true, "devDependencies": {"async": "^2.5.0", "babel-eslint": "^7.2.3", "bundlesize": "^0.12.1", "eslint-plugin-jasmine": "^2.7.1", "jsdoc-to-markdown": "^3.0.0", "lerna": "^2.0.0", "lint-staged": "^4.0.1", "opt-cli": "^1.5.1", "pre-commit": "^1.2.2", "prettier-eslint-cli": "^4.1.1", "wait-on": "^2.0.2", "wide-align": "^1.1.2"}, "scripts": {"precommit": "lint-staged", "postinstall": "lerna bootstrap", "bootstrap": "lerna bootstrap", "pretest": "$BUNDLESIZE && bundlesize-init", "test": "lerna run --parallel test && yarn build", "posttest": "$BUNDLESIZE && bundlesize", "build": "lerna run build", "docs": "yarn build && for dest in popper tooltip; do jsdoc2md ./packages/$dest/dist/$dest.js > ./docs/_includes/$dest-documentation.md; done", "docs:serve": "cd docs; { wait-on http://localhost:4000; open http://localhost:4000; } & jekyll serve"}, "lint-staged": {"*.js": ["prettier-eslint --write \"**/src/**/*.js\"", "git add"]}, "bundlesize": [{"path": "./packages/popper/dist/popper.min.js", "threshold": "7 Kb"}, {"path": "./packages/tooltip/dist/tooltip.min.js", "threshold": "2 Kb"}], "dependencies": {}}
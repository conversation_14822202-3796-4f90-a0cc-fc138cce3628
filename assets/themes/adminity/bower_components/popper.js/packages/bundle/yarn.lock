# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@comandeer/babel-plugin-banner@^1.0.0":
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/@comandeer/babel-plugin-banner/-/babel-plugin-banner-1.0.0.tgz#40bcce0bbee084b5b02545a33635d053c248356f"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"

babel-code-frame@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-code-frame/-/babel-code-frame-6.22.0.tgz#027620bee567a88c32561574e7fd0801d33118e4"
  dependencies:
    chalk "^1.1.0"
    esutils "^2.0.2"
    js-tokens "^3.0.0"

babel-core@6, babel-core@^6.21.0, babel-core@^6.24.1:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-core/-/babel-core-6.25.0.tgz#7dd42b0463c742e9d5296deb3ec67a9322dad729"
  dependencies:
    babel-code-frame "^6.22.0"
    babel-generator "^6.25.0"
    babel-helpers "^6.24.1"
    babel-messages "^6.23.0"
    babel-register "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.25.0"
    babel-traverse "^6.25.0"
    babel-types "^6.25.0"
    babylon "^6.17.2"
    convert-source-map "^1.1.0"
    debug "^2.1.1"
    json5 "^0.5.0"
    lodash "^4.2.0"
    minimatch "^3.0.2"
    path-is-absolute "^1.0.0"
    private "^0.1.6"
    slash "^1.0.0"
    source-map "^0.5.0"

babel-generator@^6.25.0:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-generator/-/babel-generator-6.25.0.tgz#33a1af70d5f2890aeb465a4a7793c1df6a9ea9fc"
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-types "^6.25.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.2.0"
    source-map "^0.5.0"
    trim-right "^1.0.1"

babel-helper-define-map@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-define-map/-/babel-helper-define-map-6.24.1.tgz#7a9747f258d8947d32d515f6aa1c7bd02204a080"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"
    lodash "^4.2.0"

babel-helper-evaluate-path@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/babel-helper-evaluate-path/-/babel-helper-evaluate-path-0.1.0.tgz#95d98c4ea36150483db2e7d3ec9e1954a72629cb"

babel-helper-flip-expressions@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/babel-helper-flip-expressions/-/babel-helper-flip-expressions-0.1.2.tgz#77f6652f9de9c42401d827bd46ebd2109e3ef18a"

babel-helper-function-name@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-function-name/-/babel-helper-function-name-6.24.1.tgz#d3475b8c03ed98242a25b48351ab18399d3580a9"
  dependencies:
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-get-function-arity@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-get-function-arity/-/babel-helper-get-function-arity-6.24.1.tgz#8f7782aa93407c41d3aa50908f89b031b1b6853d"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-is-nodes-equiv@^0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/babel-helper-is-nodes-equiv/-/babel-helper-is-nodes-equiv-0.0.1.tgz#34e9b300b1479ddd98ec77ea0bbe9342dfe39684"

babel-helper-is-void-0@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/babel-helper-is-void-0/-/babel-helper-is-void-0-0.1.1.tgz#72f21a3abba0bef3837f9174fca731aed9a02888"

babel-helper-mark-eval-scopes@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/babel-helper-mark-eval-scopes/-/babel-helper-mark-eval-scopes-0.1.1.tgz#4554345edf9f2549427bd2098e530253f8af2992"

babel-helper-optimise-call-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-optimise-call-expression/-/babel-helper-optimise-call-expression-6.24.1.tgz#f7a13427ba9f73f8f4fa993c54a97882d1244257"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-remove-or-void@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/babel-helper-remove-or-void/-/babel-helper-remove-or-void-0.1.1.tgz#9d7e1856dc6fafcb41b283a416730dc1844f66d7"

babel-helper-replace-supers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helper-replace-supers/-/babel-helper-replace-supers-6.24.1.tgz#bf6dbfe43938d17369a213ca8a8bf74b6a90ab1a"
  dependencies:
    babel-helper-optimise-call-expression "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-to-multiple-sequence-expressions@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/babel-helper-to-multiple-sequence-expressions/-/babel-helper-to-multiple-sequence-expressions-0.1.1.tgz#5f1b832b39e4acf954e9137f0251395c71196b35"

babel-helpers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-helpers/-/babel-helpers-6.24.1.tgz#3471de9caec388e5c850e597e58a26ddf37602b2"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "https://registry.yarnpkg.com/babel-messages/-/babel-messages-6.23.0.tgz#f3cdf4703858035b2a2951c6ec5edf6c62f2630e"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-minify-builtins@^0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/babel-plugin-minify-builtins/-/babel-plugin-minify-builtins-0.1.3.tgz#4f21a7dcb51f91a04ea71d47ff0e8e3b05fec021"
  dependencies:
    babel-helper-evaluate-path "^0.1.0"

babel-plugin-minify-constant-folding@^0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/babel-plugin-minify-constant-folding/-/babel-plugin-minify-constant-folding-0.1.3.tgz#57bd172adf8b8d74ad7c99612eb950414ebea3ca"
  dependencies:
    babel-helper-evaluate-path "^0.1.0"

babel-plugin-minify-dead-code-elimination@^0.1.7:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/babel-plugin-minify-dead-code-elimination/-/babel-plugin-minify-dead-code-elimination-0.1.7.tgz#774f536f347b98393a27baa717872968813c342c"
  dependencies:
    babel-helper-mark-eval-scopes "^0.1.1"
    babel-helper-remove-or-void "^0.1.1"
    lodash.some "^4.6.0"

babel-plugin-minify-flip-comparisons@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/babel-plugin-minify-flip-comparisons/-/babel-plugin-minify-flip-comparisons-0.1.2.tgz#e286b40b7599b18dfea195071e4279465cfc1884"
  dependencies:
    babel-helper-is-void-0 "^0.1.1"

babel-plugin-minify-guarded-expressions@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/babel-plugin-minify-guarded-expressions/-/babel-plugin-minify-guarded-expressions-0.1.2.tgz#dfc3d473b0362d9605d3ce0ac1e22328c60d1007"
  dependencies:
    babel-helper-flip-expressions "^0.1.2"

babel-plugin-minify-infinity@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/babel-plugin-minify-infinity/-/babel-plugin-minify-infinity-0.1.2.tgz#5f1cf67ddedcba13c8a00da832542df0091a1cd4"

babel-plugin-minify-mangle-names@^0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/babel-plugin-minify-mangle-names/-/babel-plugin-minify-mangle-names-0.1.3.tgz#bfa24661a6794fb03833587e55828b65449e06fe"
  dependencies:
    babel-helper-mark-eval-scopes "^0.1.1"

babel-plugin-minify-numeric-literals@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-minify-numeric-literals/-/babel-plugin-minify-numeric-literals-0.1.1.tgz#d4b8b0c925f874714ee33ee4b26678583d7ce7fb"

babel-plugin-minify-replace@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/babel-plugin-minify-replace/-/babel-plugin-minify-replace-0.1.2.tgz#b90b9e71ab4d3b36325629a91beabe13b0b16ac1"

babel-plugin-minify-simplify@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/babel-plugin-minify-simplify/-/babel-plugin-minify-simplify-0.1.2.tgz#a968f1658fdeb2fc759e81fe331d89829df0f6b9"
  dependencies:
    babel-helper-flip-expressions "^0.1.2"
    babel-helper-is-nodes-equiv "^0.0.1"
    babel-helper-to-multiple-sequence-expressions "^0.1.1"

babel-plugin-minify-type-constructors@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/babel-plugin-minify-type-constructors/-/babel-plugin-minify-type-constructors-0.1.2.tgz#db53c5b76cb8e2fcd45d862f17104c78761337ee"
  dependencies:
    babel-helper-is-void-0 "^0.1.1"

babel-plugin-transform-es2015-classes@^6.9.0:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-classes/-/babel-plugin-transform-es2015-classes-6.24.1.tgz#5a4c58a50c9c9461e564b4b2a3bfabc97a2584db"
  dependencies:
    babel-helper-define-map "^6.24.1"
    babel-helper-function-name "^6.24.1"
    babel-helper-optimise-call-expression "^6.24.1"
    babel-helper-replace-supers "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-inline-consecutive-adds@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-inline-consecutive-adds/-/babel-plugin-transform-inline-consecutive-adds-0.1.2.tgz#5442e9f1c19c78a7899f8a4dee6fd481f61001f5"

babel-plugin-transform-member-expression-literals@^6.8.4:
  version "6.8.5"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-member-expression-literals/-/babel-plugin-transform-member-expression-literals-6.8.5.tgz#e06ae305cf48d819822e93a70d79269f04d89eec"

babel-plugin-transform-merge-sibling-variables@^6.8.5:
  version "6.8.6"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-merge-sibling-variables/-/babel-plugin-transform-merge-sibling-variables-6.8.6.tgz#6d21efa5ee4981f71657fae716f9594bb2622aef"

babel-plugin-transform-minify-booleans@^6.8.2:
  version "6.8.3"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-minify-booleans/-/babel-plugin-transform-minify-booleans-6.8.3.tgz#5906ed776d3718250519abf1bace44b0b613ddf9"

babel-plugin-transform-property-literals@^6.8.4:
  version "6.8.5"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-property-literals/-/babel-plugin-transform-property-literals-6.8.5.tgz#67ed5930b34805443452c8b9690c7ebe1e206c40"
  dependencies:
    esutils "^2.0.2"

babel-plugin-transform-regexp-constructors@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-regexp-constructors/-/babel-plugin-transform-regexp-constructors-0.1.1.tgz#312ab7487cc88a1c62ee25ea1b6087e89b87799c"

babel-plugin-transform-remove-console@^6.8.4:
  version "6.8.5"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-remove-console/-/babel-plugin-transform-remove-console-6.8.5.tgz#fde9d2d3d725530b0fadd8d31078402410386810"

babel-plugin-transform-remove-debugger@^6.8.4:
  version "6.8.5"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-remove-debugger/-/babel-plugin-transform-remove-debugger-6.8.5.tgz#809584d412bf918f071fdf41e1fdb15ea89cdcd5"

babel-plugin-transform-remove-undefined@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-remove-undefined/-/babel-plugin-transform-remove-undefined-0.1.2.tgz#e1ebf51110f6b1e0665f28382ef73f95e5023652"

babel-plugin-transform-simplify-comparison-operators@^6.8.4:
  version "6.8.5"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-simplify-comparison-operators/-/babel-plugin-transform-simplify-comparison-operators-6.8.5.tgz#a838786baf40cc33a93b95ae09e05591227e43bf"

babel-plugin-transform-undefined-to-void@^6.8.2:
  version "6.8.3"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-undefined-to-void/-/babel-plugin-transform-undefined-to-void-6.8.3.tgz#fc52707f6ee1ddc71bb91b0d314fbefdeef9beb4"

babel-preset-babili@^0.1.3:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/babel-preset-babili/-/babel-preset-babili-0.1.4.tgz#ad9d6651002f5bc3f07cab300781167f54724bf2"
  dependencies:
    babel-plugin-minify-builtins "^0.1.3"
    babel-plugin-minify-constant-folding "^0.1.3"
    babel-plugin-minify-dead-code-elimination "^0.1.7"
    babel-plugin-minify-flip-comparisons "^0.1.2"
    babel-plugin-minify-guarded-expressions "^0.1.2"
    babel-plugin-minify-infinity "^0.1.2"
    babel-plugin-minify-mangle-names "^0.1.3"
    babel-plugin-minify-numeric-literals "^0.1.1"
    babel-plugin-minify-replace "^0.1.2"
    babel-plugin-minify-simplify "^0.1.2"
    babel-plugin-minify-type-constructors "^0.1.2"
    babel-plugin-transform-inline-consecutive-adds "^0.1.2"
    babel-plugin-transform-member-expression-literals "^6.8.4"
    babel-plugin-transform-merge-sibling-variables "^6.8.5"
    babel-plugin-transform-minify-booleans "^6.8.2"
    babel-plugin-transform-property-literals "^6.8.4"
    babel-plugin-transform-regexp-constructors "^0.1.1"
    babel-plugin-transform-remove-console "^6.8.4"
    babel-plugin-transform-remove-debugger "^6.8.4"
    babel-plugin-transform-remove-undefined "^0.1.2"
    babel-plugin-transform-simplify-comparison-operators "^6.8.4"
    babel-plugin-transform-undefined-to-void "^6.8.2"
    lodash.isplainobject "^4.0.6"

babel-register@^6.24.1:
  version "6.24.1"
  resolved "https://registry.yarnpkg.com/babel-register/-/babel-register-6.24.1.tgz#7e10e13a2f71065bdfad5a1787ba45bca6ded75f"
  dependencies:
    babel-core "^6.24.1"
    babel-runtime "^6.22.0"
    core-js "^2.4.0"
    home-or-tmp "^2.0.0"
    lodash "^4.2.0"
    mkdirp "^0.5.1"
    source-map-support "^0.4.2"

babel-runtime@^6.22.0:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-runtime/-/babel-runtime-6.25.0.tgz#33b98eaa5d482bb01a8d1aa6b437ad2b01aec41c"
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.10.0"

babel-template@^6.24.1, babel-template@^6.25.0:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-template/-/babel-template-6.25.0.tgz#665241166b7c2aa4c619d71e192969552b10c071"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.25.0"
    babel-types "^6.25.0"
    babylon "^6.17.2"
    lodash "^4.2.0"

babel-traverse@^6.24.1, babel-traverse@^6.25.0:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-traverse/-/babel-traverse-6.25.0.tgz#2257497e2fcd19b89edc13c4c91381f9512496f1"
  dependencies:
    babel-code-frame "^6.22.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-types "^6.25.0"
    babylon "^6.17.2"
    debug "^2.2.0"
    globals "^9.0.0"
    invariant "^2.2.0"
    lodash "^4.2.0"

babel-types@^6.24.1, babel-types@^6.25.0:
  version "6.25.0"
  resolved "https://registry.yarnpkg.com/babel-types/-/babel-types-6.25.0.tgz#70afb248d5660e5d18f811d91c8303b54134a18e"
  dependencies:
    babel-runtime "^6.22.0"
    esutils "^2.0.2"
    lodash "^4.2.0"
    to-fast-properties "^1.0.1"

babylon@^6.17.2:
  version "6.17.4"
  resolved "https://registry.yarnpkg.com/babylon/-/babylon-6.17.4.tgz#3e8b7402b88d22c3423e137a1577883b15ff869a"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/balanced-match/-/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"

bindings@^1.2.1:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/bindings/-/bindings-1.3.0.tgz#b346f6ecf6a95f5a815c5839fc7cdb22502f1ed7"

brace-expansion@^1.1.7:
  version "1.1.8"
  resolved "https://registry.yarnpkg.com/brace-expansion/-/brace-expansion-1.1.8.tgz#c07b211c7c952ec1f8efd51a77ef0d1d3990a292"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

chalk@^1.1.0:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"

convert-source-map@^1.1.0:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/convert-source-map/-/convert-source-map-1.5.0.tgz#9acd70851c6d5dfdd93d9282e5edf94a03ff46b5"

core-js@^2.4.0:
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/core-js/-/core-js-2.5.0.tgz#569c050918be6486b3837552028ae0466b717086"

debug@^2.1.1, debug@^2.2.0:
  version "2.6.8"
  resolved "https://registry.yarnpkg.com/debug/-/debug-2.6.8.tgz#e731531ca2ede27d188222427da17821d68ff4fc"
  dependencies:
    ms "2.0.0"

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/detect-indent/-/detect-indent-4.0.0.tgz#f76d064352cdf43a1cb6ce619c4ee3a9475de208"
  dependencies:
    repeating "^2.0.0"

escape-string-regexp@^1.0.2:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"

estree-walker@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/estree-walker/-/estree-walker-0.2.1.tgz#bdafe8095383d8414d5dc2ecf4c9173b6db9412e"

esutils@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/esutils/-/esutils-2.0.2.tgz#0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b"

globals@^9.0.0:
  version "9.18.0"
  resolved "https://registry.yarnpkg.com/globals/-/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/has-ansi/-/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  dependencies:
    ansi-regex "^2.0.0"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/home-or-tmp/-/home-or-tmp-2.0.0.tgz#e36c3f2d2cae7d746a857e38d18d5f32a7882db8"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

invariant@^2.2.0:
  version "2.2.2"
  resolved "https://registry.yarnpkg.com/invariant/-/invariant-2.2.2.tgz#9e1f56ac0acdb6bf303306f338be3b204ae60360"
  dependencies:
    loose-envify "^1.0.0"

is-finite@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/is-finite/-/is-finite-1.0.2.tgz#cc6677695602be550ef11e8b4aa6305342b6d0aa"
  dependencies:
    number-is-nan "^1.0.0"

js-tokens@^3.0.0:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/js-tokens/-/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"

jsesc@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/jsesc/-/jsesc-1.3.0.tgz#46c3fec8c1892b12b0833db9bc7622176dbab34b"

json5@^0.5.0:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/json5/-/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://registry.yarnpkg.com/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz#7c526a52d89b45c45cc690b88163be0497f550cb"

lodash.some@^4.6.0:
  version "4.6.0"
  resolved "https://registry.yarnpkg.com/lodash.some/-/lodash.some-4.6.0.tgz#1bb9f314ef6b8baded13b549169b2a945eb68e4d"

lodash@^4.2.0:
  version "4.17.4"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.4.tgz#78203a4d1c328ae1d86dca6460e369b57f4055ae"

loose-envify@^1.0.0:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/loose-envify/-/loose-envify-1.3.1.tgz#d1a8ad33fa9ce0e713d65fdd0ac8b748d478c848"
  dependencies:
    js-tokens "^3.0.0"

minimatch@^3.0.2:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  dependencies:
    brace-expansion "^1.1.7"

minimist@0.0.8:
  version "0.0.8"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"

mkdirp@^0.5.1:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  dependencies:
    minimist "0.0.8"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"

nan@^2.0.5:
  version "2.6.2"
  resolved "https://registry.yarnpkg.com/nan/-/nan-2.6.2.tgz#e4ff34e6c95fdfb5aecc08de6596f43605a7db45"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/number-is-nan/-/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"

object-assign@^4.1.0:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"

os-homedir@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/os-homedir/-/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"

os-tmpdir@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"

private@^0.1.6:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/private/-/private-0.1.7.tgz#68ce5e8a1ef0a23bb570cc28537b5332aba63ef1"

regenerator-runtime@^0.10.0:
  version "0.10.5"
  resolved "https://registry.yarnpkg.com/regenerator-runtime/-/regenerator-runtime-0.10.5.tgz#336c3efc1220adcedda2c9fab67b5a7955a33658"

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/repeating/-/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  dependencies:
    is-finite "^1.0.0"

rollup-plugin-babel@^2.7.1:
  version "2.7.1"
  resolved "https://registry.yarnpkg.com/rollup-plugin-babel/-/rollup-plugin-babel-2.7.1.tgz#16528197b0f938a1536f44683c7a93d573182f57"
  dependencies:
    babel-core "6"
    babel-plugin-transform-es2015-classes "^6.9.0"
    object-assign "^4.1.0"
    rollup-pluginutils "^1.5.0"

rollup-plugin-babili@^3.1.0:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/rollup-plugin-babili/-/rollup-plugin-babili-3.1.1.tgz#a8bfed146691fff982c397364a67f75a78754889"
  dependencies:
    "@comandeer/babel-plugin-banner" "^1.0.0"
    babel-core "^6.21.0"
    babel-preset-babili "^0.1.3"

rollup-pluginutils@^1.5.0:
  version "1.5.2"
  resolved "https://registry.yarnpkg.com/rollup-pluginutils/-/rollup-pluginutils-1.5.2.tgz#1e156e778f94b7255bfa1b3d0178be8f5c552408"
  dependencies:
    estree-walker "^0.2.1"
    minimatch "^3.0.2"

rollup@^0.43.0:
  version "0.43.1"
  resolved "https://registry.yarnpkg.com/rollup/-/rollup-0.43.1.tgz#a7770af9711bd21dda977e7cce3d0f63fdfdfa04"
  dependencies:
    source-map-support "^0.4.0"
  optionalDependencies:
    weak "^1.0.1"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/slash/-/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"

source-map-support@^0.4.0, source-map-support@^0.4.2:
  version "0.4.15"
  resolved "https://registry.yarnpkg.com/source-map-support/-/source-map-support-0.4.15.tgz#03202df65c06d2bd8c7ec2362a193056fef8d3b1"
  dependencies:
    source-map "^0.5.6"

source-map@^0.5.0, source-map@^0.5.6:
  version "0.5.6"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.5.6.tgz#75ce38f52bf0733c5a7f0c118d81334a2bb5f412"

strip-ansi@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  dependencies:
    ansi-regex "^2.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"

to-fast-properties@^1.0.1:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/to-fast-properties/-/to-fast-properties-1.0.3.tgz#b83571fa4d8c25b82e231b06e3a3055de4ca1a47"

trim-right@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/trim-right/-/trim-right-1.0.1.tgz#cb2e1203067e0c8de1f614094b9fe45704ea6003"

weak@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/weak/-/weak-1.0.1.tgz#ab99aab30706959aa0200cb8cf545bb9cb33b99e"
  dependencies:
    bindings "^1.2.1"
    nan "^2.0.5"

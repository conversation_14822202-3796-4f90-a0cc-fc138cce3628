{"name": "popper.js", "version": "1.12.2", "description": "A kickass library to manage your poppers", "homepage": "https://popper.js.org", "repository": {"type": "git", "url": "git+https://github.com/FezVrasta/popper.js.git"}, "author": "<PERSON> <<EMAIL>>", "contributors": ["Contributors (https://github.com/FezVrasta/popper.js/graphs/contributors)"], "license": "MIT", "bugs": {"url": "https://github.com/FezVrasta/popper.js/issues"}, "keywords": ["popper<PERSON>s", "component", "drop", "tooltip", "popover", "position", "attached"], "main": "dist/umd/popper.js", "module": "dist/esm/popper.js", "types": "index.d.ts", "scripts": {"prepare": "npm run build", "postpublish": "nuget-publish && ./bower-publish.sh", "prebuild": "npm run lint", "pretest": "npm run lint", "build": "node bundle.js", "lint": "eslint .", "test": "popper-karma", "test:dev": "BROWSERS=Chrome NODE_ENV=development npm run test", "coverage": "NODE_ENV=coverage npm run test"}, "devDependencies": {"@popperjs/bundle": "^1.0.2", "@popperjs/eslint-config-popper": "^1.0.0", "nuget-publish": "^1.0.1", "@popperjs/test": "^1.0.0", "@popperjs/test-utils": "^1.0.0", "eslint": "^4.1.1"}, "resolutions": {"micromatch": "^3.0.3"}}
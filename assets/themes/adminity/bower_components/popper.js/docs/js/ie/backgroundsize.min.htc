<component lightWeight="true">
<attach event="onpropertychange" onevent="handlePropertychange()" />
<attach event="ondetach" onevent="restore()" />
<attach event="onresize" for="window" onevent="handleResize()" />
<script type="text/javascript">
var rsrc=/url\(["']?(.*?)["']?\)/,positions={top:0,left:0,bottom:1,right:1,center:0.5},doc=element.document;init(); function init(){var b=doc.createElement("div"),a=doc.createElement("img"),c,d;b.style.position="absolute";b.style.zIndex=-1;b.style.top=0;b.style.right=0;b.style.left=0;b.style.bottom=0;b.style.overflow="hidden";a.style.position="absolute";a.style.width=a.style.width="auto";b.appendChild(a);element.insertBefore(b,element.firstChild);d=[element.currentStyle.backgroundPositionX,element.currentStyle.backgroundPositionY];element.bgsExpando=c={wrapper:b,img:a,backgroundSize:element.currentStyle["background-size"], backgroundPositionX:positions[d[0]]||parseFloat(d[0])/100,backgroundPositionY:positions[d[1]]||parseFloat(d[1])/100};"auto"==element.currentStyle.zIndex&&(element.style.zIndex=0);"static"==element.currentStyle.position&&(element.style.position="relative");refreshDisplay(element,c)&&(refreshDimensions(element,c),refreshBackgroundImage(element,c,function(){updateBackground(element,c)}))} function refreshDisplay(b,a){var c=b.currentStyle.display;c!=a.display&&(a.display=c,a.somethingChanged=!0);return"none"!=c}function refreshDimensions(b,a){var c=b.offsetWidth-(parseFloat(b.currentStyle.borderLeftWidth)||0)-(parseFloat(b.currentStyle.borderRightWidth)||0),d=b.offsetHeight-(parseFloat(b.currentStyle.borderTopWidth)||0)-(parseFloat(b.currentStyle.borderBottomWidth)||0);if(c!=a.innerWidth||d!=a.innerHeight)a.innerWidth=c,a.innerHeight=d,a.somethingChanged=!0} function refreshBackgroundImage(b,a,c){var d=a.img,e=(rsrc.exec(b.currentStyle.backgroundImage)||[])[1];if(e&&e!=a.backgroundSrc){a.backgroundSrc=e;a.somethingChanged=!0;d.onload=function(){var b=d.width,e=d.height;1==b&&1==e||(a.imgWidth=b,a.imgHeight=e,a.constrain=!1,c(),d.style.visibility="visible",d.onload=null)};d.style.visibility="hidden";d.src=a.backgroundSrc;if(d.readyState||d.complete)d.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==",d.src=a.backgroundSrc;a.ignoreNextPropertyChange= !0;b.style.backgroundImage="none"}else c()} function updateBackground(b,a){if(a.somethingChanged){var c=a.img,d=a.innerWidth/a.innerHeight,e=a.imgWidth/a.imgHeight,f=a.constrain;"contain"==a.backgroundSize?e>d?(a.constrain=d="width",e=Math.floor((a.innerHeight-a.innerWidth/e)*a.backgroundPositionY),c.style.top=e+"px",d!=f&&(c.style.width="100%",c.style.height="auto",c.style.left=0)):(a.constrain=d="height",e=Math.floor((a.innerWidth-a.innerHeight*e)*a.backgroundPositionX),c.style.left=e+"px",d!=f&&(c.style.width="auto",c.style.height="100%", c.style.top=0)):"cover"==a.backgroundSize&&(e>d?(a.constrain=d="height",e=Math.floor((a.innerHeight*e-a.innerWidth)*a.backgroundPositionX),c.style.left=-e+"px",d!=f&&(c.style.width="auto",c.style.height="100%",c.style.top=0)):(a.constrain=d="width",e=Math.floor((a.innerWidth/e-a.innerHeight)*a.backgroundPositionY),c.style.top=-e+"px",d!=f&&(c.style.width="100%",c.style.height="auto",c.style.left=0)));a.somethingChanged=!1}} function handlePropertychange(){var b=element.bgsExpando;b.ignoreNextPropertyChange?b.ignoreNextPropertyChange=!1:refreshDisplay(element,b)&&(refreshDimensions(element,b),refreshBackgroundImage(element,b,function(){updateBackground(element,b)}))}function handleResize(){var b=element.bgsExpando;"none"!=b.display&&(refreshDimensions(element,b),updateBackground(element,b))} function restore(){var b=element.bgsExpando;try{element.style.backgroundImage="url('"+b.backgroundSrc+"')",element.removeChild(b.wrapper),element.bgsExpando=null}catch(a){}};
</script>
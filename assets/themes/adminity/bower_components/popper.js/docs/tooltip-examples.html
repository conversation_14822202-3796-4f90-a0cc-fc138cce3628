---
layout: page-nowrap
title: Tooltip.js
description: Dead simple tooltips, powered by Popper.js
---


<section class="wrapper style5">
  <div class="inner">
      <script async type="text/javascript" src="//cdn.carbonads.com/carbon.js?zoneid=1673&serve=C6AILKT&placement=fezvrastagithubiopopperjs" id="_carbonads_js">
      </script>

      <h2>Tooltip.js</h2>
      <p>
          Since Popper.js is an engine, we wanted to provide a library that shows you how
          you can use it in the real world.
      </p>
      <p>
          Tooltip.js can create, show, hide and toggle tooltips. Its API is almost identical
          to the one used by Bootstrap for their tooltips, if you know Bootstrap, you already
          know Tooltip.js!
      </p>
  </div>
</section>

<section id="examples" class="wrapper style2 alt">
    <section class="spotlight half-height">
        <div class="example">
            {% include example10t.html %}
        </div>
        <div class="content">
            {% include example10t-code.html %}
        </div>
    </section>

    <section class="spotlight half-height">
        <div class="example">
            {% include example20t.html %}
        </div>
        <div class="content">
            {% include example20t-code.html %}
        </div>
    </section>
</section>

<!-- CTA -->
    <section id="cta" class="wrapper style5">
        <div class="inner">
            <header>
                <h2>Ready to start?</h2>
                <p>Visit our GitHub page to know how to get Tooltip.js and to learn more about it.</p>
            </header>
            <ul class="actions vertical">
                <li><a href="https://github.com/FezVrasta/popper.js" class="button fit special">GitHub</a></li>
                <li><a href="tooltip-documentation.html" class="button fit">Documentation</a></li>
            </ul>
        </div>
    </section>

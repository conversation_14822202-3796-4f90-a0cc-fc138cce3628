---
layout: landing
---
				<!-- Banner -->
					<section id="banner">
						<div class="inner">
							<h2>{{ site.title }}</h2>
							<p>{{ site.description | markdownify }}</p>
							<ul class="actions">
								<li><a href="https://github.com/FezVrasta/popper.js/releases" class="button special">Download v1</a></li>
							</ul>
						</div>
						<a href="#one" class="more scrolly">Learn More</a>
					</section>

				<!-- Introduction -->
					<section id="one" class="wrapper style1 special">
						<div class="inner">
							<header class="major">
								<h2>Easily position tooltips, popovers or anything<br />
								with just a line of code!</h2>
								<p>
									Trust us, managing poppers can be a pain, we have learned it the hard way!<br>
									For this reason we have created <strong>Popper.js</strong>, your new best friend.
								</p>

								<p>
									Popper.js is just <strong>~6KB</strong> minified and gzpipped, zero dependencies.<br />
									Its code base is in <strong>ES6</strong> and is <strong>automatically tested</strong> against several browsers.<br />
									If this is not enough, it plays super nicely with
									<a href="https://github.com/FezVrasta/popper.js/#react-vuejs-angularjs-emberjs-etc-integration" target="_blank"><strong>React</strong>, <strong>AngularJS</strong> and more</a>!
								</p>

								<script async type="text/javascript" src="//cdn.carbonads.com/carbon.js?zoneid=1673&serve=C6AILKT&placement=fezvrastagithubiopopperjs" id="_carbonads_js"></script>

							</header>
						</div>
					</section>

				<!-- Tooltip.js banner -->
					<section id="tooltip-banner" class="wrapper style5 small">
						<div class="inner">
							<header class="major">
								<h2 id="tooltipjs" tabindex="0">Tooltip.js</h2>
								<p>
									Looking for a dead simple tooltip library?<br />
									<b>Tooltip.js</b> is powered by <b>Popper.js</b> and supports all the features you may neeed for your tooltips.
								</p>
								<p>
									<a id="tooltip-learn-more" href="tooltip-examples.html" class="button">Learn more</a>
								</p>
							</header>
						</div>
                    </section>

				<!-- live examples -->
					<section id="two" class="wrapper alt style2">
						<section class="spotlight">
							<div class="example">
								{% include example10.html %}
							</div>
							<div class="content">
								{% include example10-code.html %}
							</div>
						</section>
						<section class="spotlight">
							<div class="example">
								{% include example20.html %}
							</div>
							<div class="content">
								{% include example20-code.html %}
							</div>
						</section>
						<section class="spotlight">
							<div class="example">
								{% include example30.html %}
							</div>
							<div class="content">
								{% include example30-code.html %}
							</div>
						</section>
						<section class="spotlight">
							<div class="example">
								{% include example40.html %}
							</div>
							<div class="content">
								{% include example40-code.html %}
							</div>
						</section>
						<section class="spotlight">
							<div class="example">
								{% include example50.html %}
							</div>
							<div class="content">
								{% include example50-code.html %}
							</div>
						</section>
					</section>

				<!-- Three -->
					<section id="three" class="wrapper style3 special">
						<div class="inner">
							<header class="major">
								<h2 id="features" tabindex="0">Features</h2>
								<p>We know, every popper has its own story. You must be able to fully customize<br>
								its behavior with ease. We have prepared a set of awesome options to satisfy your needs!</p>
								<p>But they will never be enough, for this reason, Popper.js supports plugins (we call them "Modifiers")</p>
							</header>
							<ul class="features">
								<li class="icon fa-arrows">
									<h3 id="placements" tabindex="0">Placements</h3>
									<p>
										You decide where the popper will stay, choose between the four sides of your
										reference element and shift it on the start or at the end of it.
									</p>
								</li>
								<li class="icon fa-square-o">
									<h3 id="boundaries" tabindex="0">Custom Boundaries</h3>
									<p>
										We don't want your poppers to get lost. Give them some boundaries to make sure
										they will stay within them.
									</p>
								</li>
								<li class="icon fa-exchange">
									<h3 id="flip" tabindex="0">Flip and Move</h3>
									<p>
										What if your popper hits walls? Will it act like a ghost, passing through it,
										or will it change side of its reference element? It's up to you.
									</p>
								</li>
								<li class="icon fa-caret-right">
									<h3 id="arrows" tabindex="0">Arrows</h3>
									<p>
										Poppers have arrows, usually.<br>
										We take care of them making sure they stay in the right place: between popper
										and reference element.
									</p>
								</li>
								<li class="icon fa-magnet">
									<h3 id="position" tabindex="0">Fixed or Absolute?</h3>
									<p>
										We automatically detect when your popper should be absolutely positioned or
										fixed. Don't worry about that!
									</p>
								</li>
								<li class="icon fa-bolt">
									<h3 id="acceleration" tabindex="0">Blazing fast!</h3>
									<p>
										No lag, period. Each position update takes almost no time to
										refresh your poppers. No compromises.
									</p>
								</li>
								<li class="icon fa-crosshairs">
									<h3 id="offsets" tabindex="0">Offsets</h3>
									<p>
										When you need surgical precision about the position of your popper,
										set an offset to it to shift it by the given amount of pixels.
									</p>
								</li>
								<li class="icon fa-puzzle-piece">
									<h3 id="modifiers" tabindex="0">Modifiers</h3>
									<p>
										Our plugin system allows you to add any kind of feature to Popper.js.<br />
										Most of the built-in behaviors are written as modifiers!
									</p>
								</li>
							</ul>
						</div>
					</section>

				<!-- CTA -->
					<section id="cta" class="wrapper style4">
						<div class="inner">
							<header>
								<h2>Ready to start?</h2>
								<p>Visit our GitHub page to know how to get Popper.js and to learn more about it.</p>
							</header>
							<ul class="actions vertical">
								<li><a href="https://github.com/FezVrasta/popper.js" class="button fit special">GitHub</a></li>
								<li><a href="popper-documentation.html" class="button fit">Documentation</a></li>
							</ul>
						</div>
					</section>

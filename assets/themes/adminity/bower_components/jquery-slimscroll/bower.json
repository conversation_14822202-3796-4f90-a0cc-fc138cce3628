{"name": "j<PERSON>y-slimscroll", "version": "1.3.8", "description": "slimScroll is a small jQuery plugin that transforms any div into a scrollable area. slimScroll doesn't occupy any visual space as it only appears on a user initiated mouse-over.", "keywords": ["scrollbar", "scroll", "slimscroll", "scrollable", "scrolling", "scroller", "ui", "jquery-plugin", "ecosystem:jquery"], "homepage": "http://rocha.la/jQuery-slimScroll/", "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://rocha.la/"}], "repository": {"type": "git", "url": "https://github.com/rochal/jQuery-slimScroll.git"}, "main": ["jquery.slimscroll.js", "jquery.slimscroll.min.js"], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}, {"type": "GPL", "url": "http://www.opensource.org/licenses/gpl-license.php"}], "moduleType": [], "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}
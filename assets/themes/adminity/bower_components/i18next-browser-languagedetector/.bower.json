{"name": "i18next-browser-languagedetector", "main": "./i18nextBrowserLanguageDetector.js", "dependencies": {}, "ignore": ["test/", "dist/", "src/", "coverage/", ".babelrc", ".giti<PERSON>re", ".editorconfig", ".es<PERSON><PERSON><PERSON>", ".eslintrc", ".n<PERSON><PERSON><PERSON>", "gulpfile.js", "package.json", "karma.conf.js"], "homepage": "https://github.com/i18next/i18next-browser-languageDetector", "version": "1.0.1", "_release": "1.0.1", "_resolution": {"type": "version", "tag": "v1.0.1", "commit": "f4a5624660c78948c6ca2e69689a20cf0ac01dd1"}, "_source": "https://github.com/i18next/i18next-browser-languageDetector.git", "_target": "1.0.1", "_originalSource": "i18next-browser-languagedetector"}
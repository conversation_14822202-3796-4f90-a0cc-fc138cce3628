### 1.4.1
- fixes ajax

### 1.4.0
- add `queryStringParams` option [PR255](https://github.com/i18next/i18next-xhr-backend/pull/255)

### 1.3.0
- add support for custom headers [PR250](https://github.com/i18next/i18next-xhr-backend/pull/250)
- update dev dependencies

### 1.2.1
- downgrade babel-preset-es2015-native-modules to correctly build es files

### 1.2.0
- support withCredentials flag on XHR [PR238](https://github.com/i18next/i18next-xhr-backend/pull/238)

### 1.1.0
- allows loadPath to be a function [PR236](https://github.com/i18next/i18next-xhr-backend/pull/236)

### 1.0.1
- change amd export to unnamed
- initial v1

{"name": "i18next-xhr-backend", "main": "./i18nextXHRBackend.js", "dependencies": {}, "ignore": ["test/", "dist/", "src/", "coverage/", ".babelrc", ".giti<PERSON>re", ".editorconfig", ".es<PERSON><PERSON><PERSON>", ".eslintrc", ".n<PERSON><PERSON><PERSON>", "gulpfile.js", "package.json", "karma.conf.js"], "homepage": "https://github.com/i18next/i18next-xhr-backend", "version": "1.4.1", "_release": "1.4.1", "_resolution": {"type": "version", "tag": "v1.4.1", "commit": "605a64e78dc3c20be491281142b8c396081e3457"}, "_source": "https://github.com/i18next/i18next-xhr-backend.git", "_target": "1.4.1", "_originalSource": "i18next-xhr-backend"}
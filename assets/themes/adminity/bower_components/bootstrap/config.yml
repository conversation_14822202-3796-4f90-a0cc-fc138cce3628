languageCode:           "en"
title:                  "Bootstrap"
baseURL:                "https://getbootstrap.com"
enableInlineShortcodes: true

markup:
  goldmark:
    renderer:
      unsafe:           true
  highlight:
    noClasses:          false
  tableOfContents:
    startLevel:         2
    endLevel:           6

buildDrafts:            true
buildFuture:            true

enableRobotsTXT:        true
metaDataFormat:         "yaml"
disableKinds:           ["404", "taxonomy", "term", "RSS"]

publishDir:             "_site"

module:
  mounts:
    - source:           dist
      target:           static/docs/5.0/dist
    - source:           site/assets
      target:           assets
    - source:           site/content
      target:           content
    - source:           site/data
      target:           data
    - source:           site/layouts
      target:           layouts
    - source:           site/static
      target:           static
    - source:           site/static/docs/5.0/assets/img/favicons/apple-touch-icon.png
      target:           static/apple-touch-icon.png
    - source:           site/static/docs/5.0/assets/img/favicons/favicon.ico
      target:           static/favicon.ico

params:
  description:          "The most popular HTML, CSS, and JS library in the world."
  authors:              "<PERSON>, <PERSON>, and Bootstrap contributors"
  social_image_path:    /docs/5.0/assets/brand/bootstrap-social.png
  social_logo_path:     /docs/5.0/assets/brand/bootstrap-social-logo.png

  current_version:      "5.0.2"
  current_ruby_version: "5.0.2"
  docs_version:         "5.0"
  rfs_version:          "9.0.3"
  github_org:           "https://github.com/twbs"
  repo:                 "https://github.com/twbs/bootstrap"
  twitter:              "getbootstrap"
  slack:                "https://bootstrap-slack.herokuapp.com/"
  opencollective:       "https://opencollective.com/bootstrap"
  blog:                 "https://blog.getbootstrap.com/"
  themes:               "https://themes.getbootstrap.com/"
  icons:                "https://icons.getbootstrap.com/"

  download:
    source:             "https://github.com/twbs/bootstrap/archive/v5.0.2.zip"
    dist:               "https://github.com/twbs/bootstrap/releases/download/v5.0.2/bootstrap-5.0.2-dist.zip"
    dist_examples:      "https://github.com/twbs/bootstrap/releases/download/v5.0.2/bootstrap-5.0.2-examples.zip"

  cdn:
    # See https://www.srihash.org for info on how to generate the hashes
    css:              "https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css"
    css_hash:         "sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC"
    css_rtl:          "https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.rtl.min.css"
    css_rtl_hash:     "sha384-gXt9imSW0VcJVHezoNQsP+TNrjYXoGcrqBZJpry9zJt8PCQjobwmhMGaDHTASo9N"
    js:               "https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js"
    js_hash:          "sha384-cVKIPhGWiC2Al4u+LWgxfKTRIcfu0JTxR+EQDz/bgldoEyl4H0zUF0QKbrJ0EcQF"
    js_bundle:        "https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
    js_bundle_hash:   "sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
    popper:           "https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"
    popper_hash:      "sha384-IQsoLXl5PILFhosVNubq5LC7Qb9DXgDA9i+tQ8Zj3iwWAwPtgFTxbJ8NT4GN1R8p"

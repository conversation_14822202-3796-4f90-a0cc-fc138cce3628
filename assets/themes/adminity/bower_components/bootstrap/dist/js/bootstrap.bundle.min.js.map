{"version": 3, "sources": ["../../js/src/dom/selector-engine.js", "../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/data.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["SelectorEngine", "find", "selector", "element", "document", "documentElement", "concat", "Element", "prototype", "querySelectorAll", "call", "findOne", "querySelector", "children", "filter", "child", "matches", "parents", "ancestor", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "getUID", "prefix", "Math", "floor", "random", "getElementById", "getSelector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "getElementFromSelector", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "j<PERSON>y", "getElement", "length", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "match", "toLowerCase", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "getClientRects", "getComputedStyle", "getPropertyValue", "isDisabled", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "index", "indexOf", "listLength", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "customEventsRegex", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "i", "len", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFn", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "this", "handlers", "previousFn", "replace", "dom<PERSON><PERSON>s", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "on", "one", "inNamespace", "isNamespace", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "isNative", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "key", "defineProperty", "get", "preventDefault", "elementMap", "Map", "Data", "set", "instance", "instanceMap", "size", "console", "error", "Array", "from", "remove", "delete", "BaseComponent", "constructor", "_element", "DATA_KEY", "dispose", "EVENT_KEY", "getOwnPropertyNames", "propertyName", "_queueCallback", "isAnimated", "[object Object]", "getInstance", "VERSION", "Error", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_destroyElement", "each", "data", "getOrCreateInstance", "alertInstance", "handle<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "KEY_TO_DIRECTION", "ArrowLeft", "ArrowRight", "Carousel", "super", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "start", "pointerType", "touches", "clientX", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "_getItemByOrder", "isNext", "_triggerSlideEvent", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "triggerSlidEvent", "completeCallBack", "action", "ride", "carouselInterface", "slideIndex", "dataApiClickHandler", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "id", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "style", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "bottom", "right", "basePlacements", "variationPlacements", "reduce", "acc", "placement", "placements", "modifierPhases", "getNodeName", "nodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "isShadowRoot", "applyStyles$1", "enabled", "phase", "_ref", "state", "elements", "styles", "assign", "effect", "_ref2", "initialStyles", "popper", "options", "strategy", "margin", "arrow", "reference", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "width", "height", "x", "y", "getLayoutRect", "clientRect", "offsetWidth", "rootNode", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getOffsetParent", "isFirefox", "userAgent", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "getContainingBlock", "getMainAxisFromPlacement", "round", "within", "mathMax", "mathMin", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "padding", "rects", "toPaddingObject", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "axisProp", "centerOffset", "_options$element", "requiresIfExists", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "_ref3", "dpr", "devicePixelRatio", "roundOffsetsByDPR", "_ref3$x", "_ref3$y", "hasX", "hasY", "sideX", "sideY", "win", "heightProp", "widthProp", "_Object$assign", "commonStyles", "computeStyles$1", "_ref4", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "data-popper-placement", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "pageXOffset", "pageYOffset", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "listScrollParents", "_element$ownerDocumen", "getScrollParent", "isBody", "visualViewport", "updatedList", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "html", "getViewportRect", "clientTop", "clientLeft", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "getDocumentRect", "getVariation", "computeOffsets", "variation", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "referenceElement", "clippingClientRect", "mainClippingParents", "clippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getClippingRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$1", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "data-popper-reference-hidden", "data-popper-escaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMin", "tetherMax", "preventedOffset", "_mainSide", "_altSide", "_offset", "_min", "_max", "_preventedOffset", "getCompositeRect", "elementOrVirtualElement", "isFixed", "isOffsetParentAnElement", "DEFAULT_OPTIONS", "modifiers", "areValidElements", "_len", "arguments", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "cleanupModifierEffects", "merged", "map", "visited", "result", "modifier", "dep", "depModifier", "orderModifiers", "current", "existing", "m", "_ref3$options", "cleanupFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "undefined", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "REGEXP_KEYDOWN", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "display", "popperConfig", "autoClose", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "getParentFromElement", "<PERSON><PERSON>", "_getPopperConfig", "isDisplayStatic", "focus", "_completeHide", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "_selectMenuItem", "items", "dropdownInterface", "toggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "clickEvent", "isActive", "stopPropagation", "getToggleButton", "clearMenus", "click", "dataApiKeydownHandler", "ScrollBarHelper", "getWidth", "documentWidth", "innerWidth", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_saveInitialAttribute", "styleProp", "scrollbarWidth", "_applyManipulationCallback", "_resetElementAttributes", "actualValue", "removeProperty", "callBack", "isOverflowing", "clickCallback", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "className", "append<PERSON><PERSON><PERSON>", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_isShown", "_ignoreBackdropClick", "_scrollBar", "_isAnimated", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "_enforceFocus", "_triggerBackdropTransition", "_resetAdjustments", "currentTarget", "isModalOverflowing", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "<PERSON><PERSON><PERSON>", "visibility", "_enforceFocusOnElement", "blur", "allReadyOpen", "el", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "*", "area", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "dataKey", "_getDelegateConfig", "_handlePopperPlacementChange", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "Popover", "_getContent", "method", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "item", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "navItem", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "complete", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "_onInteraction", "isInteracting"], "mappings": ";;;;;0OAaA,MAEMA,EAAiB,CACrBC,KAAI,CAACC,EAAUC,EAAUC,SAASC,kBACzB,GAAGC,UAAUC,QAAQC,UAAUC,iBAAiBC,KAAKP,EAASD,IAGvES,QAAO,CAACT,EAAUC,EAAUC,SAASC,kBAC5BE,QAAQC,UAAUI,cAAcF,KAAKP,EAASD,GAGvDW,SAAQ,CAACV,EAASD,IACT,GAAGI,UAAUH,EAAQU,UACzBC,OAAOC,GAASA,EAAMC,QAAQd,IAGnCe,QAAQd,EAASD,GACf,MAAMe,EAAU,GAEhB,IAAIC,EAAWf,EAAQgB,WAEvB,KAAOD,GAAYA,EAASE,WAAaC,KAAKC,cArBhC,IAqBgDJ,EAASE,UACjEF,EAASF,QAAQd,IACnBe,EAAQM,KAAKL,GAGfA,EAAWA,EAASC,WAGtB,OAAOF,GAGTO,KAAKrB,EAASD,GACZ,IAAIuB,EAAWtB,EAAQuB,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAAST,QAAQd,GACnB,MAAO,CAACuB,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAAKxB,EAASD,GACZ,IAAIyB,EAAOxB,EAAQyB,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKX,QAAQd,GACf,MAAO,CAACyB,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KC1CLC,EAASC,IACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnB7B,SAAS8B,eAAeJ,IAEjC,OAAOA,GAGHK,EAAchC,IAClB,IAAID,EAAWC,EAAQiC,aAAa,kBAEpC,IAAKlC,GAAyB,MAAbA,EAAkB,CACjC,IAAImC,EAAWlC,EAAQiC,aAAa,QAMpC,IAAKC,IAAcA,EAASC,SAAS,OAASD,EAASE,WAAW,KAChE,OAAO,KAILF,EAASC,SAAS,OAASD,EAASE,WAAW,OACjDF,EAAY,IAAGA,EAASG,MAAM,KAAK,IAGrCtC,EAAWmC,GAAyB,MAAbA,EAAmBA,EAASI,OAAS,KAG9D,OAAOvC,GAGHwC,EAAyBvC,IAC7B,MAAMD,EAAWiC,EAAYhC,GAE7B,OAAID,GACKE,SAASQ,cAAcV,GAAYA,EAGrC,MAGHyC,EAAyBxC,IAC7B,MAAMD,EAAWiC,EAAYhC,GAE7B,OAAOD,EAAWE,SAASQ,cAAcV,GAAY,MA0BjD0C,EAAuBzC,IAC3BA,EAAQ0C,cAAc,IAAIC,MA1FL,mBA6FjBC,EAAYC,MACXA,GAAsB,iBAARA,UAIO,IAAfA,EAAIC,SACbD,EAAMA,EAAI,SAGmB,IAAjBA,EAAI5B,UAGd8B,EAAaF,GACbD,EAAUC,GACLA,EAAIC,OAASD,EAAI,GAAKA,EAGZ,iBAARA,GAAoBA,EAAIG,OAAS,EACnCnD,EAAeW,QAAQqC,GAGzB,KAGHI,EAAkB,CAACC,EAAeC,EAAQC,KAC9CC,OAAOC,KAAKF,GAAaG,QAAQC,IAC/B,MAAMC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASd,EAAUc,GAAS,UArH5Cb,OADSA,EAsHsDa,GApHzD,GAAEb,EAGL,GAAGe,SAASrD,KAAKsC,GAAKgB,MAAM,eAAe,GAAGC,cALxCjB,IAAAA,EAwHX,IAAK,IAAIkB,OAAON,GAAeO,KAAKL,GAClC,MAAM,IAAIM,UACP,GAAEf,EAAcgB,0BAA0BV,qBAA4BG,yBAAiCF,UAM1GU,EAAYnE,MACX4C,EAAU5C,IAAgD,IAApCA,EAAQoE,iBAAiBpB,SAIgB,YAA7DqB,iBAAiBrE,GAASsE,iBAAiB,cAG9CC,EAAavE,IACZA,GAAWA,EAAQiB,WAAaC,KAAKC,gBAItCnB,EAAQwE,UAAUC,SAAS,mBAIC,IAArBzE,EAAQ0E,SACV1E,EAAQ0E,SAGV1E,EAAQ2E,aAAa,aAAoD,UAArC3E,EAAQiC,aAAa,aAG5D2C,EAAiB5E,IACrB,IAAKC,SAASC,gBAAgB2E,aAC5B,OAAO,KAIT,GAAmC,mBAAxB7E,EAAQ8E,YAA4B,CAC7C,MAAMC,EAAO/E,EAAQ8E,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAI/E,aAAmBgF,WACdhF,EAIJA,EAAQgB,WAIN4D,EAAe5E,EAAQgB,YAHrB,MAMLiE,EAAO,OAEPC,EAASlF,GAAWA,EAAQmF,aAE5BC,EAAY,KAChB,MAAMC,OAAEA,GAAWC,OAEnB,OAAID,IAAWpF,SAASsF,KAAKZ,aAAa,qBACjCU,EAGF,MAGHG,EAA4B,GAiB5BC,EAAQ,IAAuC,QAAjCxF,SAASC,gBAAgBwF,IAEvCC,EAAqBC,IAjBAC,IAAAA,EAAAA,EAkBN,KACjB,MAAMC,EAAIV,IAEV,GAAIU,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,mBA3BQ,YAAxBlG,SAASqG,YAENd,EAA0BxC,QAC7B/C,SAASsG,iBAAiB,mBAAoB,KAC5Cf,EAA0BjC,QAAQsC,GAAYA,OAIlDL,EAA0BpE,KAAKyE,IAE/BA,KAuBEW,EAAUX,IACU,mBAAbA,GACTA,KAIEY,EAAyB,CAACZ,EAAUa,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAH,EAAQX,GAIV,MACMe,EA/KiC5G,CAAAA,IACvC,IAAKA,EACH,OAAO,EAIT,IAAI6G,mBAAEA,EAAFC,gBAAsBA,GAAoBxB,OAAOjB,iBAAiBrE,GAEtE,MAAM+G,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBxE,MAAM,KAAK,GACnDyE,EAAkBA,EAAgBzE,MAAM,KAAK,GArFf,KAuFtB2E,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,GAkKgBK,CAAiCT,GADlC,EAGxB,IAAIU,GAAS,EAEb,MAAMC,EAAU,EAAGC,OAAAA,MACbA,IAAWZ,IAIfU,GAAS,EACTV,EAAkBa,oBA3PC,gBA2PmCF,GACtDb,EAAQX,KAGVa,EAAkBH,iBA/PG,gBA+P8Bc,GACnDG,WAAW,KACJJ,GACH3E,EAAqBiE,IAEtBE,IAYCa,EAAuB,CAACC,EAAMC,EAAeC,EAAeC,KAChE,IAAIC,EAAQJ,EAAKK,QAAQJ,GAGzB,IAAe,IAAXG,EACF,OAAOJ,GAAME,GAAiBC,EAAiBH,EAAK1E,OAAS,EAAI,GAGnE,MAAMgF,EAAaN,EAAK1E,OAQxB,OANA8E,GAASF,EAAgB,GAAK,EAE1BC,IACFC,GAASA,EAAQE,GAAcA,GAG1BN,EAAK9F,KAAKqG,IAAI,EAAGrG,KAAKsG,IAAIJ,EAAOE,EAAa,MC5RjDG,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAoB,4BACpBC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WASF,SAASC,EAAY9I,EAAS+I,GAC5B,OAAQA,GAAQ,GAAEA,MAAQR,OAAiBvI,EAAQuI,UAAYA,IAGjE,SAASS,EAAShJ,GAChB,MAAM+I,EAAMD,EAAY9I,GAKxB,OAHAA,EAAQuI,SAAWQ,EACnBT,EAAcS,GAAOT,EAAcS,IAAQ,GAEpCT,EAAcS,GAuCvB,SAASE,EAAYC,EAAQ7B,EAAS8B,EAAqB,MACzD,MAAMC,EAAe/F,OAAOC,KAAK4F,GAEjC,IAAK,IAAIG,EAAI,EAAGC,EAAMF,EAAapG,OAAQqG,EAAIC,EAAKD,IAAK,CACvD,MAAME,EAAQL,EAAOE,EAAaC,IAElC,GAAIE,EAAMC,kBAAoBnC,GAAWkC,EAAMJ,qBAAuBA,EACpE,OAAOI,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBrC,EAASsC,GACnD,MAAMC,EAAgC,iBAAZvC,EACpBmC,EAAkBI,EAAaD,EAAetC,EAEpD,IAAIwC,EAAYC,EAAaJ,GAO7B,OANiBd,EAAamB,IAAIF,KAGhCA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,EAAWhK,EAAS0J,EAAmBrC,EAASsC,EAAcM,GACrE,GAAiC,iBAAtBP,IAAmC1J,EAC5C,OAUF,GAPKqH,IACHA,EAAUsC,EACVA,EAAe,MAKbhB,EAAkB3E,KAAK0F,GAAoB,CAC7C,MAAMQ,EAAShE,GACN,SAAUqD,GACf,IAAKA,EAAMY,eAAkBZ,EAAMY,gBAAkBZ,EAAMa,iBAAmBb,EAAMa,eAAe3F,SAAS8E,EAAMY,eAChH,OAAOjE,EAAG3F,KAAK8J,KAAMd,IAKvBI,EACFA,EAAeO,EAAOP,GAEtBtC,EAAU6C,EAAO7C,GAIrB,MAAOuC,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBrC,EAASsC,GACvFT,EAASF,EAAShJ,GAClBsK,EAAWpB,EAAOW,KAAeX,EAAOW,GAAa,IACrDU,EAAatB,EAAYqB,EAAUd,EAAiBI,EAAavC,EAAU,MAEjF,GAAIkD,EAGF,YAFAA,EAAWN,OAASM,EAAWN,QAAUA,GAK3C,MAAMlB,EAAMD,EAAYU,EAAiBE,EAAkBc,QAAQrC,EAAgB,KAC7EjC,EAAK0D,EA5Fb,SAAoC5J,EAASD,EAAUmG,GACrD,OAAO,SAASmB,EAAQkC,GACtB,MAAMkB,EAAczK,EAAQM,iBAAiBP,GAE7C,IAAK,IAAIuH,OAAEA,GAAWiC,EAAOjC,GAAUA,IAAW+C,KAAM/C,EAASA,EAAOtG,WACtE,IAAK,IAAIqI,EAAIoB,EAAYzH,OAAQqG,KAC/B,GAAIoB,EAAYpB,KAAO/B,EAQrB,OAPAiC,EAAMa,eAAiB9C,EAEnBD,EAAQ4C,QAEVS,EAAaC,IAAI3K,EAASuJ,EAAMqB,KAAM7K,EAAUmG,GAG3CA,EAAG2E,MAAMvD,EAAQ,CAACiC,IAM/B,OAAO,MAyEPuB,CAA2B9K,EAASqH,EAASsC,GAzGjD,SAA0B3J,EAASkG,GACjC,OAAO,SAASmB,EAAQkC,GAOtB,OANAA,EAAMa,eAAiBpK,EAEnBqH,EAAQ4C,QACVS,EAAaC,IAAI3K,EAASuJ,EAAMqB,KAAM1E,GAGjCA,EAAG2E,MAAM7K,EAAS,CAACuJ,KAkG1BwB,CAAiB/K,EAASqH,GAE5BnB,EAAGiD,mBAAqBS,EAAavC,EAAU,KAC/CnB,EAAGsD,gBAAkBA,EACrBtD,EAAG+D,OAASA,EACZ/D,EAAGqC,SAAWQ,EACduB,EAASvB,GAAO7C,EAEhBlG,EAAQuG,iBAAiBsD,EAAW3D,EAAI0D,GAG1C,SAASoB,EAAchL,EAASkJ,EAAQW,EAAWxC,EAAS8B,GAC1D,MAAMjD,EAAK+C,EAAYC,EAAOW,GAAYxC,EAAS8B,GAE9CjD,IAILlG,EAAQuH,oBAAoBsC,EAAW3D,EAAI+E,QAAQ9B,WAC5CD,EAAOW,GAAW3D,EAAGqC,WAe9B,SAASuB,EAAaP,GAGpB,OADAA,EAAQA,EAAMiB,QAAQpC,EAAgB,IAC/BI,EAAae,IAAUA,EAGhC,MAAMmB,EAAe,CACnBQ,GAAGlL,EAASuJ,EAAOlC,EAASsC,GAC1BK,EAAWhK,EAASuJ,EAAOlC,EAASsC,GAAc,IAGpDwB,IAAInL,EAASuJ,EAAOlC,EAASsC,GAC3BK,EAAWhK,EAASuJ,EAAOlC,EAASsC,GAAc,IAGpDgB,IAAI3K,EAAS0J,EAAmBrC,EAASsC,GACvC,GAAiC,iBAAtBD,IAAmC1J,EAC5C,OAGF,MAAO4J,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBrC,EAASsC,GACvFyB,EAAcvB,IAAcH,EAC5BR,EAASF,EAAShJ,GAClBqL,EAAc3B,EAAkBtH,WAAW,KAEjD,QAA+B,IAApBoH,EAAiC,CAE1C,IAAKN,IAAWA,EAAOW,GACrB,OAIF,YADAmB,EAAchL,EAASkJ,EAAQW,EAAWL,EAAiBI,EAAavC,EAAU,MAIhFgE,GACFhI,OAAOC,KAAK4F,GAAQ3F,QAAQ+H,KAhDlC,SAAkCtL,EAASkJ,EAAQW,EAAW0B,GAC5D,MAAMC,EAAoBtC,EAAOW,IAAc,GAE/CxG,OAAOC,KAAKkI,GAAmBjI,QAAQkI,IACrC,GAAIA,EAAWtJ,SAASoJ,GAAY,CAClC,MAAMhC,EAAQiC,EAAkBC,GAEhCT,EAAchL,EAASkJ,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,uBA0CrEuC,CAAyB1L,EAASkJ,EAAQoC,EAAc5B,EAAkBiC,MAAM,MAIpF,MAAMH,EAAoBtC,EAAOW,IAAc,GAC/CxG,OAAOC,KAAKkI,GAAmBjI,QAAQqI,IACrC,MAAMH,EAAaG,EAAYpB,QAAQnC,EAAe,IAEtD,IAAK+C,GAAe1B,EAAkBvH,SAASsJ,GAAa,CAC1D,MAAMlC,EAAQiC,EAAkBI,GAEhCZ,EAAchL,EAASkJ,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,wBAK7E0C,QAAQ7L,EAASuJ,EAAOuC,GACtB,GAAqB,iBAAVvC,IAAuBvJ,EAChC,OAAO,KAGT,MAAM8F,EAAIV,IACJyE,EAAYC,EAAaP,GACzB6B,EAAc7B,IAAUM,EACxBkC,EAAWnD,EAAamB,IAAIF,GAElC,IAAImC,EACAC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CIhB,GAAetF,IACjBkG,EAAclG,EAAEnD,MAAM4G,EAAOuC,GAE7BhG,EAAE9F,GAAS6L,QAAQG,GACnBC,GAAWD,EAAYK,uBACvBH,GAAkBF,EAAYM,gCAC9BH,EAAmBH,EAAYO,sBAG7BR,GACFK,EAAMnM,SAASuM,YAAY,cAC3BJ,EAAIK,UAAU5C,EAAWoC,GAAS,IAElCG,EAAM,IAAIM,YAAYnD,EAAO,CAC3B0C,QAAAA,EACAU,YAAY,SAKI,IAATb,GACTzI,OAAOC,KAAKwI,GAAMvI,QAAQqJ,IACxBvJ,OAAOwJ,eAAeT,EAAKQ,EAAK,CAC9BE,IAAG,IACMhB,EAAKc,OAMhBT,GACFC,EAAIW,iBAGFb,GACFlM,EAAQ0C,cAAc0J,GAGpBA,EAAID,uBAA2C,IAAhBH,GACjCA,EAAYe,iBAGPX,IC3ULY,EAAa,IAAIC,IAEvB,IAAAC,EAAe,CACbC,IAAInN,EAAS4M,EAAKQ,GACXJ,EAAWjD,IAAI/J,IAClBgN,EAAWG,IAAInN,EAAS,IAAIiN,KAG9B,MAAMI,EAAcL,EAAWF,IAAI9M,GAI9BqN,EAAYtD,IAAI6C,IAA6B,IAArBS,EAAYC,KAMzCD,EAAYF,IAAIP,EAAKQ,GAJnBG,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKL,EAAY/J,QAAQ,QAOhIwJ,IAAG,CAAC9M,EAAS4M,IACPI,EAAWjD,IAAI/J,IACVgN,EAAWF,IAAI9M,GAAS8M,IAAIF,IAG9B,KAGTe,OAAO3N,EAAS4M,GACd,IAAKI,EAAWjD,IAAI/J,GAClB,OAGF,MAAMqN,EAAcL,EAAWF,IAAI9M,GAEnCqN,EAAYO,OAAOhB,GAGM,IAArBS,EAAYC,MACdN,EAAWY,OAAO5N,KC/BxB,MAAM6N,EACJC,YAAY9N,IACVA,EAAU+C,EAAW/C,MAMrBqK,KAAK0D,SAAW/N,EAChBkN,EAAKC,IAAI9C,KAAK0D,SAAU1D,KAAKyD,YAAYE,SAAU3D,OAGrD4D,UACEf,EAAKS,OAAOtD,KAAK0D,SAAU1D,KAAKyD,YAAYE,UAC5CtD,EAAaC,IAAIN,KAAK0D,SAAU1D,KAAKyD,YAAYI,WAEjD7K,OAAO8K,oBAAoB9D,MAAM9G,QAAQ6K,IACvC/D,KAAK+D,GAAgB,OAIzBC,eAAexI,EAAU7F,EAASsO,GAAa,GAC7C7H,EAAuBZ,EAAU7F,EAASsO,GAK1BC,mBAACvO,GACjB,OAAOkN,EAAKJ,IAAI9M,EAASqK,KAAK2D,UAGNO,2BAACvO,EAASmD,EAAS,IAC3C,OAAOkH,KAAKmE,YAAYxO,IAAY,IAAIqK,KAAKrK,EAA2B,iBAAXmD,EAAsBA,EAAS,MAG5EsL,qBAChB,MAtCY,QAyCCzI,kBACb,MAAM,IAAI0I,MAAM,uEAGCV,sBACjB,MAAQ,MAAK3D,KAAKrE,KAGAkI,uBAClB,MAAQ,IAAG7D,KAAK2D,UC7BpB,MAAMW,UAAcd,EAGH7H,kBACb,MAzBS,QA8BX4I,MAAM5O,GACJ,MAAM6O,EAAc7O,EAAUqK,KAAKyE,gBAAgB9O,GAAWqK,KAAK0D,SAC7DgB,EAAc1E,KAAK2E,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAY5C,kBAIxC9B,KAAK4E,eAAeJ,GAKtBC,gBAAgB9O,GACd,OAAOwC,EAAuBxC,IAAYA,EAAQkP,QAAS,UAG7DF,mBAAmBhP,GACjB,OAAO0K,EAAamB,QAAQ7L,EAzCX,kBA4CnBiP,eAAejP,GACbA,EAAQwE,UAAUmJ,OAvCE,QAyCpB,MAAMW,EAAatO,EAAQwE,UAAUC,SA1CjB,QA2CpB4F,KAAKgE,eAAe,IAAMhE,KAAK8E,gBAAgBnP,GAAUA,EAASsO,GAGpEa,gBAAgBnP,GACdA,EAAQ2N,SAERjD,EAAamB,QAAQ7L,EArDH,mBA0DEuO,uBAACpL,GACrB,OAAOkH,KAAK+E,MAAK,WACf,MAAMC,EAAOV,EAAMW,oBAAoBjF,MAExB,UAAXlH,GACFkM,EAAKlM,GAAQkH,SAKCkE,qBAACgB,GACnB,OAAO,SAAUhG,GACXA,GACFA,EAAMwD,iBAGRwC,EAAcX,MAAMvE,QAW1BK,EAAaQ,GAAGjL,SApFc,0BAJL,4BAwFyC0O,EAAMa,cAAc,IAAIb,IAS1FhJ,EAAmBgJ,GCxFnB,MAAMc,UAAe5B,EAGJ7H,kBACb,MArBS,SA0BX0J,SAEErF,KAAK0D,SAAS4B,aAAa,eAAgBtF,KAAK0D,SAASvJ,UAAUkL,OAvB7C,WA4BFnB,uBAACpL,GACrB,OAAOkH,KAAK+E,MAAK,WACf,MAAMC,EAAOI,EAAOH,oBAAoBjF,MAEzB,WAAXlH,GACFkM,EAAKlM,SChDb,SAASyM,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQ7I,OAAO6I,GAAKjM,WACfoD,OAAO6I,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASC,EAAiBlD,GACxB,OAAOA,EAAIpC,QAAQ,SAAUuF,GAAQ,IAAGA,EAAIjM,eDuC9C4G,EAAaQ,GAAGjL,SAzCc,2BAFD,4BA2CyCsJ,IACpEA,EAAMwD,iBAEN,MAAMiD,EAASzG,EAAMjC,OAAO4H,QA9CD,6BA+CdO,EAAOH,oBAAoBU,GAEnCN,WAUP/J,EAAmB8J,GCpDnB,MAAMQ,EAAc,CAClBC,iBAAiBlQ,EAAS4M,EAAKlJ,GAC7B1D,EAAQ2P,aAAc,WAAUG,EAAiBlD,GAAQlJ,IAG3DyM,oBAAoBnQ,EAAS4M,GAC3B5M,EAAQoQ,gBAAiB,WAAUN,EAAiBlD,KAGtDyD,kBAAkBrQ,GAChB,IAAKA,EACH,MAAO,GAGT,MAAMsQ,EAAa,GAUnB,OARAjN,OAAOC,KAAKtD,EAAQuQ,SACjB5P,OAAOiM,GAAOA,EAAIxK,WAAW,OAC7BmB,QAAQqJ,IACP,IAAI4D,EAAU5D,EAAIpC,QAAQ,MAAO,IACjCgG,EAAUA,EAAQC,OAAO,GAAG3M,cAAgB0M,EAAQ7E,MAAM,EAAG6E,EAAQxN,QACrEsN,EAAWE,GAAWZ,EAAc5P,EAAQuQ,QAAQ3D,MAGjD0D,GAGTI,iBAAgB,CAAC1Q,EAAS4M,IACjBgD,EAAc5P,EAAQiC,aAAc,WAAU6N,EAAiBlD,KAGxE+D,OAAO3Q,GACL,MAAM4Q,EAAO5Q,EAAQ6Q,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAM7Q,SAASsF,KAAKwL,UAC9BC,KAAMJ,EAAKI,KAAO/Q,SAASsF,KAAK0L,aAIpCC,SAASlR,IACA,CACL8Q,IAAK9Q,EAAQmR,UACbH,KAAMhR,EAAQoR,cCpCdC,EAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,EAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,EAAa,OACbC,EAAa,OACbC,EAAiB,OACjBC,EAAkB,QAElBC,EAAmB,CACvBC,UAAkBF,EAClBG,WAAmBJ,GA4CrB,MAAMK,UAAiBvE,EACrBC,YAAY9N,EAASmD,GACnBkP,MAAMrS,GAENqK,KAAKiI,OAAS,KACdjI,KAAKkI,UAAY,KACjBlI,KAAKmI,eAAiB,KACtBnI,KAAKoI,WAAY,EACjBpI,KAAKqI,YAAa,EAClBrI,KAAKsI,aAAe,KACpBtI,KAAKuI,YAAc,EACnBvI,KAAKwI,YAAc,EAEnBxI,KAAKyI,QAAUzI,KAAK0I,WAAW5P,GAC/BkH,KAAK2I,mBAAqBnT,EAAeW,QA3BjB,uBA2B8C6J,KAAK0D,UAC3E1D,KAAK4I,gBAAkB,iBAAkBhT,SAASC,iBAAmBgT,UAAUC,eAAiB,EAChG9I,KAAK+I,cAAgBnI,QAAQ3F,OAAO+N,cAEpChJ,KAAKiJ,qBAKWjC,qBAChB,OAAOA,EAGMrL,kBACb,MA3GS,WAgHXxE,OACE6I,KAAKkJ,OAAO1B,GAGd2B,mBAGOvT,SAASwT,QAAUtP,EAAUkG,KAAK0D,WACrC1D,KAAK7I,OAITH,OACEgJ,KAAKkJ,OAAOzB,GAGdL,MAAMlI,GACCA,IACHc,KAAKoI,WAAY,GAGf5S,EAAeW,QApEI,2CAoEwB6J,KAAK0D,YAClDtL,EAAqB4H,KAAK0D,UAC1B1D,KAAKqJ,OAAM,IAGbC,cAActJ,KAAKkI,WACnBlI,KAAKkI,UAAY,KAGnBmB,MAAMnK,GACCA,IACHc,KAAKoI,WAAY,GAGfpI,KAAKkI,YACPoB,cAActJ,KAAKkI,WACnBlI,KAAKkI,UAAY,MAGflI,KAAKyI,SAAWzI,KAAKyI,QAAQxB,WAAajH,KAAKoI,YACjDpI,KAAKuJ,kBAELvJ,KAAKkI,UAAYsB,aACd5T,SAAS6T,gBAAkBzJ,KAAKmJ,gBAAkBnJ,KAAK7I,MAAMuS,KAAK1J,MACnEA,KAAKyI,QAAQxB,WAKnB0C,GAAGlM,GACDuC,KAAKmI,eAAiB3S,EAAeW,QArGZ,wBAqG0C6J,KAAK0D,UACxE,MAAMkG,EAAc5J,KAAK6J,cAAc7J,KAAKmI,gBAE5C,GAAI1K,EAAQuC,KAAKiI,OAAOtP,OAAS,GAAK8E,EAAQ,EAC5C,OAGF,GAAIuC,KAAKqI,WAEP,YADAhI,EAAaS,IAAId,KAAK0D,SApIR,mBAoI8B,IAAM1D,KAAK2J,GAAGlM,IAI5D,GAAImM,IAAgBnM,EAGlB,OAFAuC,KAAKoH,aACLpH,KAAKqJ,QAIP,MAAMS,EAAQrM,EAAQmM,EACpBpC,EACAC,EAEFzH,KAAKkJ,OAAOY,EAAO9J,KAAKiI,OAAOxK,IAKjCiL,WAAW5P,GAOT,OANAA,EAAS,IACJkO,KACApB,EAAYI,kBAAkBhG,KAAK0D,aAChB,iBAAX5K,EAAsBA,EAAS,IAE5CF,EApMS,WAoMaE,EAAQyO,GACvBzO,EAGTiR,eACE,MAAMC,EAAYzS,KAAK0S,IAAIjK,KAAKwI,aAEhC,GAAIwB,GAnMgB,GAoMlB,OAGF,MAAME,EAAYF,EAAYhK,KAAKwI,YAEnCxI,KAAKwI,YAAc,EAEd0B,GAILlK,KAAKkJ,OAAOgB,EAAY,EAAIvC,EAAkBD,GAGhDuB,qBACMjJ,KAAKyI,QAAQvB,UACf7G,EAAaQ,GAAGb,KAAK0D,SApLJ,sBAoL6BxE,GAASc,KAAKmK,SAASjL,IAG5C,UAAvBc,KAAKyI,QAAQrB,QACf/G,EAAaQ,GAAGb,KAAK0D,SAvLD,yBAuL6BxE,GAASc,KAAKoH,MAAMlI,IACrEmB,EAAaQ,GAAGb,KAAK0D,SAvLD,yBAuL6BxE,GAASc,KAAKqJ,MAAMnK,KAGnEc,KAAKyI,QAAQnB,OAAStH,KAAK4I,iBAC7B5I,KAAKoK,0BAITA,0BACE,MAAMC,EAAQnL,KACRc,KAAK+I,eAnKU,QAmKQ7J,EAAMoL,aApKZ,UAoKgDpL,EAAMoL,YAE/DtK,KAAK+I,gBACf/I,KAAKuI,YAAcrJ,EAAMqL,QAAQ,GAAGC,SAFpCxK,KAAKuI,YAAcrJ,EAAMsL,SAMvBC,EAAOvL,IAEXc,KAAKwI,YAActJ,EAAMqL,SAAWrL,EAAMqL,QAAQ5R,OAAS,EACzD,EACAuG,EAAMqL,QAAQ,GAAGC,QAAUxK,KAAKuI,aAG9BmC,EAAMxL,KACNc,KAAK+I,eAlLU,QAkLQ7J,EAAMoL,aAnLZ,UAmLgDpL,EAAMoL,cACzEtK,KAAKwI,YAActJ,EAAMsL,QAAUxK,KAAKuI,aAG1CvI,KAAK+J,eACsB,UAAvB/J,KAAKyI,QAAQrB,QASfpH,KAAKoH,QACDpH,KAAKsI,cACPqC,aAAa3K,KAAKsI,cAGpBtI,KAAKsI,aAAenL,WAAW+B,GAASc,KAAKqJ,MAAMnK,GAtQ5B,IAsQ6Dc,KAAKyI,QAAQxB,YAIrGzR,EAAeC,KAjNO,qBAiNiBuK,KAAK0D,UAAUxK,QAAQ0R,IAC5DvK,EAAaQ,GAAG+J,EAlOI,wBAkOuBC,GAAKA,EAAEnI,oBAGhD1C,KAAK+I,eACP1I,EAAaQ,GAAGb,KAAK0D,SAxOA,0BAwO6BxE,GAASmL,EAAMnL,IACjEmB,EAAaQ,GAAGb,KAAK0D,SAxOF,wBAwO6BxE,GAASwL,EAAIxL,IAE7Dc,KAAK0D,SAASvJ,UAAU2Q,IA9NG,mBAgO3BzK,EAAaQ,GAAGb,KAAK0D,SAhPD,yBAgP6BxE,GAASmL,EAAMnL,IAChEmB,EAAaQ,GAAGb,KAAK0D,SAhPF,wBAgP6BxE,GAASuL,EAAKvL,IAC9DmB,EAAaQ,GAAGb,KAAK0D,SAhPH,uBAgP6BxE,GAASwL,EAAIxL,KAIhEiL,SAASjL,GACP,GAAI,kBAAkBvF,KAAKuF,EAAMjC,OAAO8N,SACtC,OAGF,MAAMb,EAAYtC,EAAiB1I,EAAMqD,KACrC2H,IACFhL,EAAMwD,iBACN1C,KAAKkJ,OAAOgB,IAIhBL,cAAclU,GAKZ,OAJAqK,KAAKiI,OAAStS,GAAWA,EAAQgB,WAC/BnB,EAAeC,KAhPC,iBAgPmBE,EAAQgB,YAC3C,GAEKqJ,KAAKiI,OAAOvK,QAAQ/H,GAG7BqV,gBAAgBlB,EAAOxM,GACrB,MAAM2N,EAASnB,IAAUtC,EACzB,OAAOpK,EAAqB4C,KAAKiI,OAAQ3K,EAAe2N,EAAQjL,KAAKyI,QAAQpB,MAG/E6D,mBAAmBpL,EAAeqL,GAChC,MAAMC,EAAcpL,KAAK6J,cAAc/J,GACjCuL,EAAYrL,KAAK6J,cAAcrU,EAAeW,QA9P3B,wBA8PyD6J,KAAK0D,WAEvF,OAAOrD,EAAamB,QAAQxB,KAAK0D,SAxRhB,oBAwRuC,CACtD5D,cAAAA,EACAoK,UAAWiB,EACX9H,KAAMgI,EACN1B,GAAIyB,IAIRE,2BAA2B3V,GACzB,GAAIqK,KAAK2I,mBAAoB,CAC3B,MAAM4C,EAAkB/V,EAAeW,QA3QrB,UA2Q8C6J,KAAK2I,oBAErE4C,EAAgBpR,UAAUmJ,OArRN,UAsRpBiI,EAAgBxF,gBAAgB,gBAEhC,MAAMyF,EAAahW,EAAeC,KA1Qb,mBA0QsCuK,KAAK2I,oBAEhE,IAAK,IAAI3J,EAAI,EAAGA,EAAIwM,EAAW7S,OAAQqG,IACrC,GAAIrC,OAAO8O,SAASD,EAAWxM,GAAGpH,aAAa,oBAAqB,MAAQoI,KAAK6J,cAAclU,GAAU,CACvG6V,EAAWxM,GAAG7E,UAAU2Q,IA5RR,UA6RhBU,EAAWxM,GAAGsG,aAAa,eAAgB,QAC3C,QAMRiE,kBACE,MAAM5T,EAAUqK,KAAKmI,gBAAkB3S,EAAeW,QA5R7B,wBA4R2D6J,KAAK0D,UAEzF,IAAK/N,EACH,OAGF,MAAM+V,EAAkB/O,OAAO8O,SAAS9V,EAAQiC,aAAa,oBAAqB,IAE9E8T,GACF1L,KAAKyI,QAAQkD,gBAAkB3L,KAAKyI,QAAQkD,iBAAmB3L,KAAKyI,QAAQxB,SAC5EjH,KAAKyI,QAAQxB,SAAWyE,GAExB1L,KAAKyI,QAAQxB,SAAWjH,KAAKyI,QAAQkD,iBAAmB3L,KAAKyI,QAAQxB,SAIzEiC,OAAO0C,EAAkBjW,GACvB,MAAMmU,EAAQ9J,KAAK6L,kBAAkBD,GAC/BtO,EAAgB9H,EAAeW,QA9SZ,wBA8S0C6J,KAAK0D,UAClEoI,EAAqB9L,KAAK6J,cAAcvM,GACxCyO,EAAcpW,GAAWqK,KAAKgL,gBAAgBlB,EAAOxM,GAErD0O,EAAmBhM,KAAK6J,cAAckC,GACtCE,EAAYrL,QAAQZ,KAAKkI,WAEzB+C,EAASnB,IAAUtC,EACnB0E,EAAuBjB,EA5TR,sBADF,oBA8TbkB,EAAiBlB,EA5TH,qBACA,qBA4TdE,EAAqBnL,KAAKoM,kBAAkBtC,GAElD,GAAIiC,GAAeA,EAAY5R,UAAUC,SAnUnB,UAqUpB,YADA4F,KAAKqI,YAAa,GAIpB,GAAIrI,KAAKqI,WACP,OAIF,GADmBrI,KAAKkL,mBAAmBa,EAAaZ,GACzCrJ,iBACb,OAGF,IAAKxE,IAAkByO,EAErB,OAGF/L,KAAKqI,YAAa,EAEd4D,GACFjM,KAAKoH,QAGPpH,KAAKsL,2BAA2BS,GAChC/L,KAAKmI,eAAiB4D,EAEtB,MAAMM,EAAmB,KACvBhM,EAAamB,QAAQxB,KAAK0D,SA9WZ,mBA8WkC,CAC9C5D,cAAeiM,EACf7B,UAAWiB,EACX9H,KAAMyI,EACNnC,GAAIqC,KAIR,GAAIhM,KAAK0D,SAASvJ,UAAUC,SAvWP,SAuWmC,CACtD2R,EAAY5R,UAAU2Q,IAAIqB,GAE1BtR,EAAOkR,GAEPzO,EAAcnD,UAAU2Q,IAAIoB,GAC5BH,EAAY5R,UAAU2Q,IAAIoB,GAE1B,MAAMI,EAAmB,KACvBP,EAAY5R,UAAUmJ,OAAO4I,EAAsBC,GACnDJ,EAAY5R,UAAU2Q,IAlXJ,UAoXlBxN,EAAcnD,UAAUmJ,OApXN,SAoXgC6I,EAAgBD,GAElElM,KAAKqI,YAAa,EAElBlL,WAAWkP,EAAkB,IAG/BrM,KAAKgE,eAAesI,EAAkBhP,GAAe,QAErDA,EAAcnD,UAAUmJ,OA7XJ,UA8XpByI,EAAY5R,UAAU2Q,IA9XF,UAgYpB9K,KAAKqI,YAAa,EAClBgE,IAGEJ,GACFjM,KAAKqJ,QAITwC,kBAAkB3B,GAChB,MAAK,CAACvC,EAAiBD,GAAgB5P,SAASoS,GAI5C9O,IACK8O,IAAcxC,EAAiBD,EAAaD,EAG9C0C,IAAcxC,EAAiBF,EAAaC,EAP1CyC,EAUXkC,kBAAkBtC,GAChB,MAAK,CAACtC,EAAYC,GAAY3P,SAASgS,GAInC1O,IACK0O,IAAUrC,EAAaC,EAAiBC,EAG1CmC,IAAUrC,EAAaE,EAAkBD,EAPvCoC,EAYa5F,yBAACvO,EAASmD,GAChC,MAAMkM,EAAO+C,EAAS9C,oBAAoBtP,EAASmD,GAEnD,IAAI2P,QAAEA,GAAYzD,EACI,iBAAXlM,IACT2P,EAAU,IACLA,KACA3P,IAIP,MAAMyT,EAA2B,iBAAXzT,EAAsBA,EAAS2P,EAAQtB,MAE7D,GAAsB,iBAAXrO,EACTkM,EAAK2E,GAAG7Q,QACH,GAAsB,iBAAXyT,EAAqB,CACrC,QAA4B,IAAjBvH,EAAKuH,GACd,MAAM,IAAI3S,UAAW,oBAAmB2S,MAG1CvH,EAAKuH,UACI9D,EAAQxB,UAAYwB,EAAQ+D,OACrCxH,EAAKoC,QACLpC,EAAKqE,SAIanF,uBAACpL,GACrB,OAAOkH,KAAK+E,MAAK,WACfgD,EAAS0E,kBAAkBzM,KAAMlH,MAIXoL,2BAAChF,GACzB,MAAMjC,EAAS9E,EAAuB6H,MAEtC,IAAK/C,IAAWA,EAAO9C,UAAUC,SAxcT,YAyctB,OAGF,MAAMtB,EAAS,IACV8M,EAAYI,kBAAkB/I,MAC9B2I,EAAYI,kBAAkBhG,OAE7B0M,EAAa1M,KAAKpI,aAAa,oBAEjC8U,IACF5T,EAAOmO,UAAW,GAGpBc,EAAS0E,kBAAkBxP,EAAQnE,GAE/B4T,GACF3E,EAAS5D,YAAYlH,GAAQ0M,GAAG+C,GAGlCxN,EAAMwD,kBAUVrC,EAAaQ,GAAGjL,SAxec,6BAkBF,sCAsdyCmS,EAAS4E,qBAE9EtM,EAAaQ,GAAG5F,OA3ea,4BA2egB,KAC3C,MAAM2R,EAAYpX,EAAeC,KAxdR,6BA0dzB,IAAK,IAAIuJ,EAAI,EAAGC,EAAM2N,EAAUjU,OAAQqG,EAAIC,EAAKD,IAC/C+I,EAAS0E,kBAAkBG,EAAU5N,GAAI+I,EAAS5D,YAAYyI,EAAU5N,OAW5E1D,EAAmByM,GC5iBnB,MAKMf,EAAU,CACd3B,QAAQ,EACRwH,OAAQ,IAGJtF,GAAc,CAClBlC,OAAQ,UACRwH,OAAQ,oBA0BV,MAAMC,WAAiBtJ,EACrBC,YAAY9N,EAASmD,GACnBkP,MAAMrS,GAENqK,KAAK+M,kBAAmB,EACxB/M,KAAKyI,QAAUzI,KAAK0I,WAAW5P,GAC/BkH,KAAKgN,cAAgBxX,EAAeC,KACjC,sCAAiCuK,KAAK0D,SAASuJ,qDACJjN,KAAK0D,SAASuJ,QAG5D,MAAMC,EAAa1X,EAAeC,KAnBT,+BAqBzB,IAAK,IAAIuJ,EAAI,EAAGC,EAAMiO,EAAWvU,OAAQqG,EAAIC,EAAKD,IAAK,CACrD,MAAMmO,EAAOD,EAAWlO,GAClBtJ,EAAWwC,EAAuBiV,GAClCC,EAAgB5X,EAAeC,KAAKC,GACvCY,OAAO+W,GAAaA,IAAcrN,KAAK0D,UAEzB,OAAbhO,GAAqB0X,EAAczU,SACrCqH,KAAKsN,UAAY5X,EACjBsK,KAAKgN,cAAcjW,KAAKoW,IAI5BnN,KAAKuN,QAAUvN,KAAKyI,QAAQoE,OAAS7M,KAAKwN,aAAe,KAEpDxN,KAAKyI,QAAQoE,QAChB7M,KAAKyN,0BAA0BzN,KAAK0D,SAAU1D,KAAKgN,eAGjDhN,KAAKyI,QAAQpD,QACfrF,KAAKqF,SAMS2B,qBAChB,OAAOA,EAGMrL,kBACb,MAjFS,WAsFX0J,SACMrF,KAAK0D,SAASvJ,UAAUC,SAlER,QAmElB4F,KAAK0N,OAEL1N,KAAK2N,OAITA,OACE,GAAI3N,KAAK+M,kBAAoB/M,KAAK0D,SAASvJ,UAAUC,SA1EjC,QA2ElB,OAGF,IAAIwT,EACAC,EAEA7N,KAAKuN,UACPK,EAAUpY,EAAeC,KA1EN,qBA0E6BuK,KAAKuN,SAClDjX,OAAO6W,GAC6B,iBAAxBnN,KAAKyI,QAAQoE,OACfM,EAAKvV,aAAa,oBAAsBoI,KAAKyI,QAAQoE,OAGvDM,EAAKhT,UAAUC,SAvFJ,aA0FC,IAAnBwT,EAAQjV,SACViV,EAAU,OAId,MAAME,EAAYtY,EAAeW,QAAQ6J,KAAKsN,WAC9C,GAAIM,EAAS,CACX,MAAMG,EAAiBH,EAAQnY,KAAK0X,GAAQW,IAAcX,GAG1D,GAFAU,EAAcE,EAAiBjB,GAAS3I,YAAY4J,GAAkB,KAElEF,GAAeA,EAAYd,iBAC7B,OAKJ,GADmB1M,EAAamB,QAAQxB,KAAK0D,SAhH7B,oBAiHD5B,iBACb,OAGE8L,GACFA,EAAQ1U,QAAQ8U,IACVF,IAAcE,GAChBlB,GAASmB,kBAAkBD,EAAY,QAGpCH,GACHhL,EAAKC,IAAIkL,EA1IF,cA0IwB,QAKrC,MAAME,EAAYlO,KAAKmO,gBAEvBnO,KAAK0D,SAASvJ,UAAUmJ,OA5HA,YA6HxBtD,KAAK0D,SAASvJ,UAAU2Q,IA5HE,cA8H1B9K,KAAK0D,SAAS0K,MAAMF,GAAa,EAE7BlO,KAAKgN,cAAcrU,QACrBqH,KAAKgN,cAAc9T,QAAQvD,IACzBA,EAAQwE,UAAUmJ,OAjIG,aAkIrB3N,EAAQ2P,aAAa,iBAAiB,KAI1CtF,KAAKqO,kBAAiB,GAEtB,MAYMC,EAAc,UADSJ,EAAU,GAAGrU,cAAgBqU,EAAU5M,MAAM,IAG1EtB,KAAKgE,eAdY,KACfhE,KAAK0D,SAASvJ,UAAUmJ,OA1IA,cA2IxBtD,KAAK0D,SAASvJ,UAAU2Q,IA5IF,WADJ,QA+IlB9K,KAAK0D,SAAS0K,MAAMF,GAAa,GAEjClO,KAAKqO,kBAAiB,GAEtBhO,EAAamB,QAAQxB,KAAK0D,SAxJX,sBA8Ja1D,KAAK0D,UAAU,GAC7C1D,KAAK0D,SAAS0K,MAAMF,GAAgBlO,KAAK0D,SAAS4K,GAAhB,KAGpCZ,OACE,GAAI1N,KAAK+M,mBAAqB/M,KAAK0D,SAASvJ,UAAUC,SA9JlC,QA+JlB,OAIF,GADmBiG,EAAamB,QAAQxB,KAAK0D,SAtK7B,oBAuKD5B,iBACb,OAGF,MAAMoM,EAAYlO,KAAKmO,gBAEvBnO,KAAK0D,SAAS0K,MAAMF,GAAgBlO,KAAK0D,SAAS8C,wBAAwB0H,GAAxC,KAElCrT,EAAOmF,KAAK0D,UAEZ1D,KAAK0D,SAASvJ,UAAU2Q,IA3KE,cA4K1B9K,KAAK0D,SAASvJ,UAAUmJ,OA7KA,WADJ,QAgLpB,MAAMiL,EAAqBvO,KAAKgN,cAAcrU,OAC9C,GAAI4V,EAAqB,EACvB,IAAK,IAAIvP,EAAI,EAAGA,EAAIuP,EAAoBvP,IAAK,CAC3C,MAAMwC,EAAUxB,KAAKgN,cAAchO,GAC7BmO,EAAOhV,EAAuBqJ,GAEhC2L,IAASA,EAAKhT,UAAUC,SAtLZ,UAuLdoH,EAAQrH,UAAU2Q,IApLC,aAqLnBtJ,EAAQ8D,aAAa,iBAAiB,IAK5CtF,KAAKqO,kBAAiB,GAStBrO,KAAK0D,SAAS0K,MAAMF,GAAa,GAEjClO,KAAKgE,eATY,KACfhE,KAAKqO,kBAAiB,GACtBrO,KAAK0D,SAASvJ,UAAUmJ,OA/LA,cAgMxBtD,KAAK0D,SAASvJ,UAAU2Q,IAjMF,YAkMtBzK,EAAamB,QAAQxB,KAAK0D,SAtMV,uBA2MY1D,KAAK0D,UAAU,GAG/C2K,iBAAiBG,GACfxO,KAAK+M,iBAAmByB,EAK1B9F,WAAW5P,GAOT,OANAA,EAAS,IACJkO,KACAlO,IAEEuM,OAASzE,QAAQ9H,EAAOuM,QAC/BzM,EA5OS,WA4OaE,EAAQyO,IACvBzO,EAGTqV,gBACE,OAAOnO,KAAK0D,SAASvJ,UAAUC,SAvNrB,SAAA,QACC,SAyNboT,aACE,IAAIX,OAAEA,GAAW7M,KAAKyI,QAEtBoE,EAASnU,EAAWmU,GAEpB,MAAMnX,EAAY,+CAA0CmX,MAY5D,OAVArX,EAAeC,KAAKC,EAAUmX,GAC3B3T,QAAQvD,IACP,MAAM8Y,EAAWtW,EAAuBxC,GAExCqK,KAAKyN,0BACHgB,EACA,CAAC9Y,MAIAkX,EAGTY,0BAA0B9X,EAAS+Y,GACjC,IAAK/Y,IAAY+Y,EAAa/V,OAC5B,OAGF,MAAMgW,EAAShZ,EAAQwE,UAAUC,SAxPb,QA0PpBsU,EAAaxV,QAAQiU,IACfwB,EACFxB,EAAKhT,UAAUmJ,OAzPM,aA2PrB6J,EAAKhT,UAAU2Q,IA3PM,aA8PvBqC,EAAK7H,aAAa,gBAAiBqJ,KAMfzK,yBAACvO,EAASmD,GAChC,IAAIkM,EAAO8H,GAAS3I,YAAYxO,GAChC,MAAM8S,EAAU,IACXzB,KACApB,EAAYI,kBAAkBrQ,MACX,iBAAXmD,GAAuBA,EAASA,EAAS,IAWtD,IARKkM,GAAQyD,EAAQpD,QAA4B,iBAAXvM,GAAuB,YAAYa,KAAKb,KAC5E2P,EAAQpD,QAAS,GAGdL,IACHA,EAAO,IAAI8H,GAASnX,EAAS8S,IAGT,iBAAX3P,EAAqB,CAC9B,QAA4B,IAAjBkM,EAAKlM,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CkM,EAAKlM,MAIaoL,uBAACpL,GACrB,OAAOkH,KAAK+E,MAAK,WACf+H,GAASmB,kBAAkBjO,KAAMlH,OAWvCuH,EAAaQ,GAAGjL,SA/Sc,6BAWD,+BAoSyC,SAAUsJ,IAEjD,MAAzBA,EAAMjC,OAAO8N,SAAoB7L,EAAMa,gBAAmD,MAAjCb,EAAMa,eAAegL,UAChF7L,EAAMwD,iBAGR,MAAMkM,EAAchJ,EAAYI,kBAAkBhG,MAC5CtK,EAAWwC,EAAuB8H,MACfxK,EAAeC,KAAKC,GAE5BwD,QAAQvD,IACvB,MAAMqP,EAAO8H,GAAS3I,YAAYxO,GAClC,IAAImD,EACAkM,GAEmB,OAAjBA,EAAKuI,SAAkD,iBAAvBqB,EAAY/B,SAC9C7H,EAAKyD,QAAQoE,OAAS+B,EAAY/B,OAClC7H,EAAKuI,QAAUvI,EAAKwI,cAGtB1U,EAAS,UAETA,EAAS8V,EAGX9B,GAASmB,kBAAkBtY,EAASmD,QAWxCwC,EAAmBwR,ICjYZ,IAAIrG,GAAM,MACNoI,GAAS,SACTC,GAAQ,QACRnI,GAAO,OAEPoI,GAAiB,CAACtI,GAAKoI,GAAQC,GAAOnI,IAOtCqI,GAAmCD,GAAeE,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAIpZ,OAAO,CAACqZ,EAAAA,SAAyBA,EAAAA,WAC3C,IACQC,GAA0B,GAAGtZ,OAAOiZ,GAAgB,CAX7C,SAWqDE,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAIpZ,OAAO,CAACqZ,EAAWA,EAAAA,SAAyBA,EAAAA,WACtD,IAaQE,GAAiB,CAXJ,aACN,OACK,YAEC,aACN,OACK,YAEE,cACN,QACK,cC7BT,SAASC,GAAY3Z,GAClC,OAAOA,GAAWA,EAAQ4Z,UAAY,IAAI9V,cAAgB,KCD7C,SAAS+V,GAAUC,GAChC,GAAY,MAARA,EACF,OAAOxU,OAGT,GAAwB,oBAApBwU,EAAKlW,WAAkC,CACzC,IAAImW,EAAgBD,EAAKC,cACzB,OAAOA,GAAgBA,EAAcC,aAAwB1U,OAG/D,OAAOwU,ECRT,SAASlX,GAAUkX,GAEjB,OAAOA,aADUD,GAAUC,GAAM1Z,SACI0Z,aAAgB1Z,QAGvD,SAAS6Z,GAAcH,GAErB,OAAOA,aADUD,GAAUC,GAAMI,aACIJ,aAAgBI,YAGvD,SAASC,GAAaL,GAEpB,MAA0B,oBAAf9U,aAKJ8U,aADUD,GAAUC,GAAM9U,YACI8U,aAAgB9U,YCyDvD,IAAAoV,GAAe,CACbrU,KAAM,cACNsU,SAAS,EACTC,MAAO,QACPpU,GA5EF,SAAqBqU,GACnB,IAAIC,EAAQD,EAAKC,MACjBnX,OAAOC,KAAKkX,EAAMC,UAAUlX,SAAQ,SAAUwC,GAC5C,IAAI0S,EAAQ+B,EAAME,OAAO3U,IAAS,GAC9BuK,EAAakK,EAAMlK,WAAWvK,IAAS,GACvC/F,EAAUwa,EAAMC,SAAS1U,GAExBkU,GAAcja,IAAa2Z,GAAY3Z,KAO5CqD,OAAOsX,OAAO3a,EAAQyY,MAAOA,GAC7BpV,OAAOC,KAAKgN,GAAY/M,SAAQ,SAAUwC,GACxC,IAAIrC,EAAQ4M,EAAWvK,IAET,IAAVrC,EACF1D,EAAQoQ,gBAAgBrK,GAExB/F,EAAQ2P,aAAa5J,GAAgB,IAAVrC,EAAiB,GAAKA,WAwDvDkX,OAlDF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MACdM,EAAgB,CAClBC,OAAQ,CACN7J,SAAUsJ,EAAMQ,QAAQC,SACxBjK,KAAM,IACNF,IAAK,IACLoK,OAAQ,KAEVC,MAAO,CACLjK,SAAU,YAEZkK,UAAW,IASb,OAPA/X,OAAOsX,OAAOH,EAAMC,SAASM,OAAOtC,MAAOqC,EAAcC,QACzDP,EAAME,OAASI,EAEXN,EAAMC,SAASU,OACjB9X,OAAOsX,OAAOH,EAAMC,SAASU,MAAM1C,MAAOqC,EAAcK,OAGnD,WACL9X,OAAOC,KAAKkX,EAAMC,UAAUlX,SAAQ,SAAUwC,GAC5C,IAAI/F,EAAUwa,EAAMC,SAAS1U,GACzBuK,EAAakK,EAAMlK,WAAWvK,IAAS,GAGvC0S,EAFkBpV,OAAOC,KAAKkX,EAAME,OAAOW,eAAetV,GAAQyU,EAAME,OAAO3U,GAAQ+U,EAAc/U,IAE7EuT,QAAO,SAAUb,EAAOjV,GAElD,OADAiV,EAAMjV,GAAY,GACXiV,IACN,IAEEwB,GAAcja,IAAa2Z,GAAY3Z,KAI5CqD,OAAOsX,OAAO3a,EAAQyY,MAAOA,GAC7BpV,OAAOC,KAAKgN,GAAY/M,SAAQ,SAAU+X,GACxCtb,EAAQoQ,gBAAgBkL,YAa9BC,SAAU,CAAC,kBCjFE,SAASC,GAAiBhC,GACvC,OAAOA,EAAUnX,MAAM,KAAK,GCFf,SAASwO,GAAsB7Q,GAC5C,IAAI4Q,EAAO5Q,EAAQ6Q,wBACnB,MAAO,CACL4K,MAAO7K,EAAK6K,MACZC,OAAQ9K,EAAK8K,OACb5K,IAAKF,EAAKE,IACVqI,MAAOvI,EAAKuI,MACZD,OAAQtI,EAAKsI,OACblI,KAAMJ,EAAKI,KACX2K,EAAG/K,EAAKI,KACR4K,EAAGhL,EAAKE,KCPG,SAAS+K,GAAc7b,GACpC,IAAI8b,EAAajL,GAAsB7Q,GAGnCyb,EAAQzb,EAAQ+b,YAChBL,EAAS1b,EAAQmF,aAUrB,OARIvD,KAAK0S,IAAIwH,EAAWL,MAAQA,IAAU,IACxCA,EAAQK,EAAWL,OAGjB7Z,KAAK0S,IAAIwH,EAAWJ,OAASA,IAAW,IAC1CA,EAASI,EAAWJ,QAGf,CACLC,EAAG3b,EAAQoR,WACXwK,EAAG5b,EAAQmR,UACXsK,MAAOA,EACPC,OAAQA,GCrBG,SAASjX,GAASyS,EAAQtW,GACvC,IAAIob,EAAWpb,EAAMkE,aAAelE,EAAMkE,cAE1C,GAAIoS,EAAOzS,SAAS7D,GAClB,OAAO,EAEJ,GAAIob,GAAY7B,GAAa6B,GAAW,CACzC,IAAIxa,EAAOZ,EAEX,EAAG,CACD,GAAIY,GAAQ0V,EAAO+E,WAAWza,GAC5B,OAAO,EAITA,EAAOA,EAAKR,YAAcQ,EAAK0a,WACxB1a,GAIb,OAAO,ECpBM,SAAS6C,GAAiBrE,GACvC,OAAO6Z,GAAU7Z,GAASqE,iBAAiBrE,GCD9B,SAASmc,GAAenc,GACrC,MAAO,CAAC,QAAS,KAAM,MAAM+H,QAAQ4R,GAAY3Z,KAAa,ECDjD,SAASoc,GAAmBpc,GAEzC,QAAS4C,GAAU5C,GAAWA,EAAQ+Z,cACtC/Z,EAAQC,WAAaqF,OAAOrF,UAAUC,gBCDzB,SAASmc,GAAcrc,GACpC,MAA6B,SAAzB2Z,GAAY3Z,GACPA,EAMPA,EAAQsc,cACRtc,EAAQgB,aACRmZ,GAAana,GAAWA,EAAQkc,KAAO,OAEvCE,GAAmBpc,GCRvB,SAASuc,GAAoBvc,GAC3B,OAAKia,GAAcja,IACoB,UAAvCqE,GAAiBrE,GAASkR,SAInBlR,EAAQwc,aAHN,KAwCI,SAASC,GAAgBzc,GAItC,IAHA,IAAIsF,EAASuU,GAAU7Z,GACnBwc,EAAeD,GAAoBvc,GAEhCwc,GAAgBL,GAAeK,IAA6D,WAA5CnY,GAAiBmY,GAActL,UACpFsL,EAAeD,GAAoBC,GAGrC,OAAIA,IAA+C,SAA9B7C,GAAY6C,IAA0D,SAA9B7C,GAAY6C,IAAwE,WAA5CnY,GAAiBmY,GAActL,UAC3H5L,EAGFkX,GA5CT,SAA4Bxc,GAC1B,IAAI0c,GAAsE,IAA1DxJ,UAAUyJ,UAAU7Y,cAAciE,QAAQ,WAG1D,IAFuD,IAA5CmL,UAAUyJ,UAAU5U,QAAQ,YAE3BkS,GAAcja,IAII,UAFXqE,GAAiBrE,GAEnBkR,SACb,OAAO,KAMX,IAFA,IAAI0L,EAAcP,GAAcrc,GAEzBia,GAAc2C,IAAgB,CAAC,OAAQ,QAAQ7U,QAAQ4R,GAAYiD,IAAgB,GAAG,CAC3F,IAAIC,EAAMxY,GAAiBuY,GAI3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0C,UAAhBF,EAAIG,UAAiF,IAA1D,CAAC,YAAa,eAAejV,QAAQ8U,EAAII,aAAsBP,GAAgC,WAAnBG,EAAII,YAA2BP,GAAaG,EAAIlc,QAAyB,SAAfkc,EAAIlc,OACjO,OAAOic,EAEPA,EAAcA,EAAY5b,WAI9B,OAAO,KAiBgBkc,CAAmBld,IAAYsF,EC9DzC,SAAS6X,GAAyB3D,GAC/C,MAAO,CAAC,MAAO,UAAUzR,QAAQyR,IAAc,EAAI,IAAM,ICDpD,IAAIvR,GAAMrG,KAAKqG,IACXC,GAAMtG,KAAKsG,IACXkV,GAAQxb,KAAKwb,MCDT,SAASC,GAAOnV,EAAKxE,EAAOuE,GACzC,OAAOqV,GAAQpV,EAAKqV,GAAQ7Z,EAAOuE,ICDtB,SAASuV,GAAmBC,GACzC,OAAOpa,OAAOsX,OAAO,GCDd,CACL7J,IAAK,EACLqI,MAAO,EACPD,OAAQ,EACRlI,KAAM,GDHuCyM,GEFlC,SAASC,GAAgBha,EAAOJ,GAC7C,OAAOA,EAAKgW,QAAO,SAAUqE,EAAS/Q,GAEpC,OADA+Q,EAAQ/Q,GAAOlJ,EACRia,IACN,ICwFL,IAAAC,GAAe,CACb7X,KAAM,QACNsU,SAAS,EACTC,MAAO,OACPpU,GA9EF,SAAeqU,GACb,IAAIsD,EAEArD,EAAQD,EAAKC,MACbzU,EAAOwU,EAAKxU,KACZiV,EAAUT,EAAKS,QACf8C,EAAetD,EAAMC,SAASU,MAC9B4C,EAAgBvD,EAAMwD,cAAcD,cACpCE,EAAgBzC,GAAiBhB,EAAMhB,WACvC0E,EAAOf,GAAyBc,GAEhC3U,EADa,CAAC0H,GAAMmI,IAAOpR,QAAQkW,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIN,EAxBgB,SAAyBU,EAAS3D,GAItD,OAAOgD,GAAsC,iBAH7CW,EAA6B,mBAAZA,EAAyBA,EAAQ9a,OAAOsX,OAAO,GAAIH,EAAM4D,MAAO,CAC/E5E,UAAWgB,EAAMhB,aACb2E,GACkDA,EAAUT,GAAgBS,EAAS/E,KAoBvEiF,CAAgBrD,EAAQmD,QAAS3D,GACjD8D,EAAYzC,GAAciC,GAC1BS,EAAmB,MAATL,EAAepN,GAAME,GAC/BwN,EAAmB,MAATN,EAAehF,GAASC,GAClCsF,EAAUjE,EAAM4D,MAAMhD,UAAU9R,GAAOkR,EAAM4D,MAAMhD,UAAU8C,GAAQH,EAAcG,GAAQ1D,EAAM4D,MAAMrD,OAAOzR,GAC9GoV,EAAYX,EAAcG,GAAQ1D,EAAM4D,MAAMhD,UAAU8C,GACxDS,EAAoBlC,GAAgBqB,GACpCc,EAAaD,EAA6B,MAATT,EAAeS,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBN,EAAU,EAAIC,EAAY,EAG9CxW,EAAMuV,EAAcc,GACpBtW,EAAM2W,EAAaN,EAAUhV,GAAOmU,EAAce,GAClDQ,EAASJ,EAAa,EAAIN,EAAUhV,GAAO,EAAIyV,EAC/CpO,EAAS0M,GAAOnV,EAAK8W,EAAQ/W,GAE7BgX,EAAWf,EACf1D,EAAMwD,cAAcjY,KAAS8X,EAAwB,IAA0BoB,GAAYtO,EAAQkN,EAAsBqB,aAAevO,EAASqO,EAAQnB,KA6CzJjD,OA1CF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MAEd2E,EADUtE,EAAMG,QACWhb,QAC3B8d,OAAoC,IAArBqB,EAA8B,sBAAwBA,EAErD,MAAhBrB,IAKwB,iBAAjBA,IACTA,EAAetD,EAAMC,SAASM,OAAOta,cAAcqd,MAahDrZ,GAAS+V,EAAMC,SAASM,OAAQ+C,KAQrCtD,EAAMC,SAASU,MAAQ2C,IAUvBvC,SAAU,CAAC,iBACX6D,iBAAkB,CAAC,oBC3FjBC,GAAa,CACfvO,IAAK,OACLqI,MAAO,OACPD,OAAQ,OACRlI,KAAM,QAgBD,SAASsO,GAAYzE,GAC1B,IAAI0E,EAEAxE,EAASF,EAAME,OACfyE,EAAa3E,EAAM2E,WACnBhG,EAAYqB,EAAMrB,UAClBiG,EAAU5E,EAAM4E,QAChBvO,EAAW2J,EAAM3J,SACjBwO,EAAkB7E,EAAM6E,gBACxBC,EAAW9E,EAAM8E,SACjBC,EAAe/E,EAAM+E,aAErBC,GAAyB,IAAjBD,EAvBd,SAA2BrF,GACzB,IAAIoB,EAAIpB,EAAKoB,EACTC,EAAIrB,EAAKqB,EAETkE,EADMxa,OACIya,kBAAoB,EAClC,MAAO,CACLpE,EAAGyB,GAAMA,GAAMzB,EAAImE,GAAOA,IAAQ,EAClClE,EAAGwB,GAAMA,GAAMxB,EAAIkE,GAAOA,IAAQ,GAgBAE,CAAkBP,GAAmC,mBAAjBG,EAA8BA,EAAaH,GAAWA,EAC1HQ,EAAUJ,EAAMlE,EAChBA,OAAgB,IAAZsE,EAAqB,EAAIA,EAC7BC,EAAUL,EAAMjE,EAChBA,OAAgB,IAAZsE,EAAqB,EAAIA,EAE7BC,EAAOV,EAAQpE,eAAe,KAC9B+E,EAAOX,EAAQpE,eAAe,KAC9BgF,EAAQrP,GACRsP,EAAQxP,GACRyP,EAAMjb,OAEV,GAAIqa,EAAU,CACZ,IAAInD,EAAeC,GAAgB1B,GAC/ByF,EAAa,eACbC,EAAY,cAEZjE,IAAiB3C,GAAUkB,IAGmB,WAA5C1W,GAFJmY,EAAeJ,GAAmBrB,IAEC7J,WACjCsP,EAAa,eACbC,EAAY,eAKhBjE,EAAeA,EAEXhD,IAAc1I,KAChBwP,EAAQpH,GAER0C,GAAKY,EAAagE,GAAchB,EAAW9D,OAC3CE,GAAK8D,EAAkB,GAAK,GAG1BlG,IAAcxI,KAChBqP,EAAQlH,GAERwC,GAAKa,EAAaiE,GAAajB,EAAW/D,MAC1CE,GAAK+D,EAAkB,GAAK,GAIhC,IAKMgB,EALFC,EAAetd,OAAOsX,OAAO,CAC/BzJ,SAAUA,GACTyO,GAAYN,IAEf,OAAIK,EAGKrc,OAAOsX,OAAO,GAAIgG,IAAeD,EAAiB,IAAmBJ,GAASF,EAAO,IAAM,GAAIM,EAAeL,GAASF,EAAO,IAAM,GAAIO,EAAe5D,WAAayD,EAAIR,kBAAoB,GAAK,EAAI,aAAepE,EAAI,OAASC,EAAI,MAAQ,eAAiBD,EAAI,OAASC,EAAI,SAAU8E,IAG3Rrd,OAAOsX,OAAO,GAAIgG,IAAepB,EAAkB,IAAoBe,GAASF,EAAOxE,EAAI,KAAO,GAAI2D,EAAgBc,GAASF,EAAOxE,EAAI,KAAO,GAAI4D,EAAgBzC,UAAY,GAAIyC,IAsD9L,IAAAqB,GAAe,CACb7a,KAAM,gBACNsU,SAAS,EACTC,MAAO,cACPpU,GAvDF,SAAuB2a,GACrB,IAAIrG,EAAQqG,EAAMrG,MACdQ,EAAU6F,EAAM7F,QAChB8F,EAAwB9F,EAAQ0E,gBAChCA,OAA4C,IAA1BoB,GAA0CA,EAC5DC,EAAoB/F,EAAQ2E,SAC5BA,OAAiC,IAAtBoB,GAAsCA,EACjDC,EAAwBhG,EAAQ4E,aAChCA,OAAyC,IAA1BoB,GAA0CA,EAYzDL,EAAe,CACjBnH,UAAWgC,GAAiBhB,EAAMhB,WAClCuB,OAAQP,EAAMC,SAASM,OACvByE,WAAYhF,EAAM4D,MAAMrD,OACxB2E,gBAAiBA,GAGsB,MAArClF,EAAMwD,cAAcD,gBACtBvD,EAAME,OAAOK,OAAS1X,OAAOsX,OAAO,GAAIH,EAAME,OAAOK,OAAQuE,GAAYjc,OAAOsX,OAAO,GAAIgG,EAAc,CACvGlB,QAASjF,EAAMwD,cAAcD,cAC7B7M,SAAUsJ,EAAMQ,QAAQC,SACxB0E,SAAUA,EACVC,aAAcA,OAIe,MAA7BpF,EAAMwD,cAAc7C,QACtBX,EAAME,OAAOS,MAAQ9X,OAAOsX,OAAO,GAAIH,EAAME,OAAOS,MAAOmE,GAAYjc,OAAOsX,OAAO,GAAIgG,EAAc,CACrGlB,QAASjF,EAAMwD,cAAc7C,MAC7BjK,SAAU,WACVyO,UAAU,EACVC,aAAcA,OAIlBpF,EAAMlK,WAAWyK,OAAS1X,OAAOsX,OAAO,GAAIH,EAAMlK,WAAWyK,OAAQ,CACnEkG,wBAAyBzG,EAAMhB,aAUjCnK,KAAM,ICvJJ6R,GAAU,CACZA,SAAS,GAsCXC,GAAe,CACbpb,KAAM,iBACNsU,SAAS,EACTC,MAAO,QACPpU,GAAI,aACJ0U,OAxCF,SAAgBL,GACd,IAAIC,EAAQD,EAAKC,MACbpN,EAAWmN,EAAKnN,SAChB4N,EAAUT,EAAKS,QACfoG,EAAkBpG,EAAQqG,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkBtG,EAAQuG,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7Chc,EAASuU,GAAUW,EAAMC,SAASM,QAClCyG,EAAgB,GAAGrhB,OAAOqa,EAAMgH,cAAcpG,UAAWZ,EAAMgH,cAAczG,QAYjF,OAVIsG,GACFG,EAAcje,SAAQ,SAAUke,GAC9BA,EAAalb,iBAAiB,SAAU6G,EAASsU,OAAQR,OAIzDK,GACFjc,EAAOiB,iBAAiB,SAAU6G,EAASsU,OAAQR,IAG9C,WACDG,GACFG,EAAcje,SAAQ,SAAUke,GAC9BA,EAAala,oBAAoB,SAAU6F,EAASsU,OAAQR,OAI5DK,GACFjc,EAAOiC,oBAAoB,SAAU6F,EAASsU,OAAQR,MAY1D7R,KAAM,IC/CJsS,GAAO,CACT3Q,KAAM,QACNmI,MAAO,OACPD,OAAQ,MACRpI,IAAK,UAEQ,SAAS8Q,GAAqBpI,GAC3C,OAAOA,EAAUhP,QAAQ,0BAA0B,SAAUqX,GAC3D,OAAOF,GAAKE,MCRhB,IAAIF,GAAO,CACTjN,MAAO,MACPK,IAAK,SAEQ,SAAS+M,GAA8BtI,GACpD,OAAOA,EAAUhP,QAAQ,cAAc,SAAUqX,GAC/C,OAAOF,GAAKE,MCLD,SAASE,GAAgBjI,GACtC,IAAIyG,EAAM1G,GAAUC,GAGpB,MAAO,CACL7I,WAHesP,EAAIyB,YAInBjR,UAHcwP,EAAI0B,aCDP,SAASC,GAAoBliB,GAQ1C,OAAO6Q,GAAsBuL,GAAmBpc,IAAUgR,KAAO+Q,GAAgB/hB,GAASiR,WCV7E,SAASkR,GAAeniB,GAErC,IAAIoiB,EAAoB/d,GAAiBrE,GACrCqiB,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6Bve,KAAKqe,EAAWE,EAAYD,GCGnD,SAASE,GAAkBxiB,EAAS0H,GACjD,IAAI+a,OAES,IAAT/a,IACFA,EAAO,IAGT,IAAI+Z,ECdS,SAASiB,EAAgB5I,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAa/R,QAAQ4R,GAAYG,KAAU,EAEvDA,EAAKC,cAAcxU,KAGxB0U,GAAcH,IAASqI,GAAerI,GACjCA,EAGF4I,EAAgBrG,GAAcvC,IDIlB4I,CAAgB1iB,GAC/B2iB,EAASlB,KAAqE,OAAlDgB,EAAwBziB,EAAQ+Z,oBAAyB,EAAS0I,EAAsBld,MACpHgb,EAAM1G,GAAU4H,GAChBna,EAASqb,EAAS,CAACpC,GAAKpgB,OAAOogB,EAAIqC,gBAAkB,GAAIT,GAAeV,GAAgBA,EAAe,IAAMA,EAC7GoB,EAAcnb,EAAKvH,OAAOmH,GAC9B,OAAOqb,EAASE,EAChBA,EAAY1iB,OAAOqiB,GAAkBnG,GAAc/U,KExBtC,SAASwb,GAAiBlS,GACvC,OAAOvN,OAAOsX,OAAO,GAAI/J,EAAM,CAC7BI,KAAMJ,EAAK+K,EACX7K,IAAKF,EAAKgL,EACVzC,MAAOvI,EAAK+K,EAAI/K,EAAK6K,MACrBvC,OAAQtI,EAAKgL,EAAIhL,EAAK8K,SCuB1B,SAASqH,GAA2B/iB,EAASgjB,GAC3C,M/BpBoB,a+BoBbA,EAA8BF,GC1BxB,SAAyB9iB,GACtC,IAAIugB,EAAM1G,GAAU7Z,GAChBijB,EAAO7G,GAAmBpc,GAC1B4iB,EAAiBrC,EAAIqC,eACrBnH,EAAQwH,EAAKnE,YACbpD,EAASuH,EAAKpE,aACdlD,EAAI,EACJC,EAAI,EAuBR,OAjBIgH,IACFnH,EAAQmH,EAAenH,MACvBC,EAASkH,EAAelH,OASnB,iCAAiC1X,KAAKkP,UAAUyJ,aACnDhB,EAAIiH,EAAexR,WACnBwK,EAAIgH,EAAezR,YAIhB,CACLsK,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EAAIuG,GAAoBliB,GAC3B4b,EAAGA,GDRiDsH,CAAgBljB,IAAYia,GAAc+I,GAdlG,SAAoChjB,GAClC,IAAI4Q,EAAOC,GAAsB7Q,GASjC,OARA4Q,EAAKE,IAAMF,EAAKE,IAAM9Q,EAAQmjB,UAC9BvS,EAAKI,KAAOJ,EAAKI,KAAOhR,EAAQojB,WAChCxS,EAAKsI,OAAStI,EAAKE,IAAM9Q,EAAQ6e,aACjCjO,EAAKuI,MAAQvI,EAAKI,KAAOhR,EAAQ8e,YACjClO,EAAK6K,MAAQzb,EAAQ8e,YACrBlO,EAAK8K,OAAS1b,EAAQ6e,aACtBjO,EAAK+K,EAAI/K,EAAKI,KACdJ,EAAKgL,EAAIhL,EAAKE,IACPF,EAI2GyS,CAA2BL,GAAkBF,GEtBlJ,SAAyB9iB,GACtC,IAAIyiB,EAEAQ,EAAO7G,GAAmBpc,GAC1BsjB,EAAYvB,GAAgB/hB,GAC5BuF,EAA0D,OAAlDkd,EAAwBziB,EAAQ+Z,oBAAyB,EAAS0I,EAAsBld,KAChGkW,EAAQxT,GAAIgb,EAAKM,YAAaN,EAAKnE,YAAavZ,EAAOA,EAAKge,YAAc,EAAGhe,EAAOA,EAAKuZ,YAAc,GACvGpD,EAASzT,GAAIgb,EAAKO,aAAcP,EAAKpE,aAActZ,EAAOA,EAAKie,aAAe,EAAGje,EAAOA,EAAKsZ,aAAe,GAC5GlD,GAAK2H,EAAUrS,WAAaiR,GAAoBliB,GAChD4b,GAAK0H,EAAUvS,UAMnB,MAJiD,QAA7C1M,GAAiBkB,GAAQ0d,GAAM1O,YACjCoH,GAAK1T,GAAIgb,EAAKnE,YAAavZ,EAAOA,EAAKuZ,YAAc,GAAKrD,GAGrD,CACLA,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EACHC,EAAGA,GFG2K6H,CAAgBrH,GAAmBpc,KG7BtM,SAAS0jB,GAAalK,GACnC,OAAOA,EAAUnX,MAAM,KAAK,GCGf,SAASshB,GAAepJ,GACrC,IAOIkF,EAPArE,EAAYb,EAAKa,UACjBpb,EAAUua,EAAKva,QACfwZ,EAAYe,EAAKf,UACjByE,EAAgBzE,EAAYgC,GAAiBhC,GAAa,KAC1DoK,EAAYpK,EAAYkK,GAAalK,GAAa,KAClDqK,EAAUzI,EAAUO,EAAIP,EAAUK,MAAQ,EAAIzb,EAAQyb,MAAQ,EAC9DqI,EAAU1I,EAAUQ,EAAIR,EAAUM,OAAS,EAAI1b,EAAQ0b,OAAS,EAGpE,OAAQuC,GACN,KAAKnN,GACH2O,EAAU,CACR9D,EAAGkI,EACHjI,EAAGR,EAAUQ,EAAI5b,EAAQ0b,QAE3B,MAEF,KAAKxC,GACHuG,EAAU,CACR9D,EAAGkI,EACHjI,EAAGR,EAAUQ,EAAIR,EAAUM,QAE7B,MAEF,KAAKvC,GACHsG,EAAU,CACR9D,EAAGP,EAAUO,EAAIP,EAAUK,MAC3BG,EAAGkI,GAEL,MAEF,KAAK9S,GACHyO,EAAU,CACR9D,EAAGP,EAAUO,EAAI3b,EAAQyb,MACzBG,EAAGkI,GAEL,MAEF,QACErE,EAAU,CACR9D,EAAGP,EAAUO,EACbC,EAAGR,EAAUQ,GAInB,IAAImI,EAAW9F,EAAgBd,GAAyBc,GAAiB,KAEzE,GAAgB,MAAZ8F,EAAkB,CACpB,IAAIza,EAAmB,MAAbya,EAAmB,SAAW,QAExC,OAAQH,GACN,InClDa,QmCmDXnE,EAAQsE,GAAYtE,EAAQsE,IAAa3I,EAAU9R,GAAO,EAAItJ,EAAQsJ,GAAO,GAC7E,MAEF,InCrDW,MmCsDTmW,EAAQsE,GAAYtE,EAAQsE,IAAa3I,EAAU9R,GAAO,EAAItJ,EAAQsJ,GAAO,IAOnF,OAAOmW,EC1DM,SAASuE,GAAexJ,EAAOQ,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAIiJ,EAAWjJ,EACXkJ,EAAqBD,EAASzK,UAC9BA,OAAmC,IAAvB0K,EAAgC1J,EAAMhB,UAAY0K,EAC9DC,EAAoBF,EAASG,SAC7BA,OAAiC,IAAtBD,EpCXY,kBoCWqCA,EAC5DE,EAAwBJ,EAASK,aACjCA,OAAyC,IAA1BD,EpCZC,WoCY6CA,EAC7DE,EAAwBN,EAASO,eACjCA,OAA2C,IAA1BD,EpCbH,SoCa+CA,EAC7DE,EAAuBR,EAASS,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxDE,EAAmBV,EAAS9F,QAC5BA,OAA+B,IAArBwG,EAA8B,EAAIA,EAC5ClH,EAAgBD,GAAsC,iBAAZW,EAAuBA,EAAUT,GAAgBS,EAAS/E,KACpGwL,EpCnBc,WoCmBDJ,EpClBI,YADH,SoCoBdK,EAAmBrK,EAAMC,SAASW,UAClCoE,EAAahF,EAAM4D,MAAMrD,OACzB/a,EAAUwa,EAAMC,SAASiK,EAAcE,EAAaJ,GACpDM,ELmBS,SAAyB9kB,EAASokB,EAAUE,GACzD,IAAIS,EAAmC,oBAAbX,EAlB5B,SAA4BpkB,GAC1B,IAAIglB,EAAkBxC,GAAkBnG,GAAcrc,IAElDilB,EADoB,CAAC,WAAY,SAASld,QAAQ1D,GAAiBrE,GAASkR,WAAa,GACnD+I,GAAcja,GAAWyc,GAAgBzc,GAAWA,EAE9F,OAAK4C,GAAUqiB,GAKRD,EAAgBrkB,QAAO,SAAUqiB,GACtC,OAAOpgB,GAAUogB,IAAmBve,GAASue,EAAgBiC,IAAmD,SAAhCtL,GAAYqJ,MALrF,GAYkDkC,CAAmBllB,GAAW,GAAGG,OAAOikB,GAC/FY,EAAkB,GAAG7kB,OAAO4kB,EAAqB,CAACT,IAClDa,EAAsBH,EAAgB,GACtCI,EAAeJ,EAAgB1L,QAAO,SAAU+L,EAASrC,GAC3D,IAAIpS,EAAOmS,GAA2B/iB,EAASgjB,GAK/C,OAJAqC,EAAQvU,IAAM7I,GAAI2I,EAAKE,IAAKuU,EAAQvU,KACpCuU,EAAQlM,MAAQjR,GAAI0I,EAAKuI,MAAOkM,EAAQlM,OACxCkM,EAAQnM,OAAShR,GAAI0I,EAAKsI,OAAQmM,EAAQnM,QAC1CmM,EAAQrU,KAAO/I,GAAI2I,EAAKI,KAAMqU,EAAQrU,MAC/BqU,IACNtC,GAA2B/iB,EAASmlB,IAKvC,OAJAC,EAAa3J,MAAQ2J,EAAajM,MAAQiM,EAAapU,KACvDoU,EAAa1J,OAAS0J,EAAalM,OAASkM,EAAatU,IACzDsU,EAAazJ,EAAIyJ,EAAapU,KAC9BoU,EAAaxJ,EAAIwJ,EAAatU,IACvBsU,EKnCkBE,CAAgB1iB,GAAU5C,GAAWA,EAAUA,EAAQulB,gBAAkBnJ,GAAmB5B,EAAMC,SAASM,QAASqJ,EAAUE,GACnJkB,EAAsB3U,GAAsBgU,GAC5C9G,EAAgB4F,GAAe,CACjCvI,UAAWoK,EACXxlB,QAASwf,EACTvE,SAAU,WACVzB,UAAWA,IAETiM,EAAmB3C,GAAiBzf,OAAOsX,OAAO,GAAI6E,EAAYzB,IAClE2H,EpChCc,WoCgCMlB,EAA4BiB,EAAmBD,EAGnEG,EAAkB,CACpB7U,IAAKgU,EAAmBhU,IAAM4U,EAAkB5U,IAAM2M,EAAc3M,IACpEoI,OAAQwM,EAAkBxM,OAAS4L,EAAmB5L,OAASuE,EAAcvE,OAC7ElI,KAAM8T,EAAmB9T,KAAO0U,EAAkB1U,KAAOyM,EAAczM,KACvEmI,MAAOuM,EAAkBvM,MAAQ2L,EAAmB3L,MAAQsE,EAActE,OAExEyM,EAAapL,EAAMwD,cAAcrN,OAErC,GpC3CkB,WoC2Cd6T,GAA6BoB,EAAY,CAC3C,IAAIjV,EAASiV,EAAWpM,GACxBnW,OAAOC,KAAKqiB,GAAiBpiB,SAAQ,SAAUqJ,GAC7C,IAAIiZ,EAAW,CAAC1M,GAAOD,IAAQnR,QAAQ6E,IAAQ,EAAI,GAAK,EACpDsR,EAAO,CAACpN,GAAKoI,IAAQnR,QAAQ6E,IAAQ,EAAI,IAAM,IACnD+Y,EAAgB/Y,IAAQ+D,EAAOuN,GAAQ2H,KAI3C,OAAOF,EC1DM,SAASG,GAAqBtL,EAAOQ,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAIiJ,EAAWjJ,EACXxB,EAAYyK,EAASzK,UACrB4K,EAAWH,EAASG,SACpBE,EAAeL,EAASK,aACxBnG,EAAU8F,EAAS9F,QACnB4H,EAAiB9B,EAAS8B,eAC1BC,EAAwB/B,EAASgC,sBACjCA,OAAkD,IAA1BD,EAAmCE,GAAgBF,EAC3EpC,EAAYF,GAAalK,GACzBC,EAAamK,EAAYmC,EAAiB1M,GAAsBA,GAAoB1Y,QAAO,SAAU6Y,GACvG,OAAOkK,GAAalK,KAAeoK,KAChCxK,GACD+M,EAAoB1M,EAAW9Y,QAAO,SAAU6Y,GAClD,OAAOyM,EAAsBle,QAAQyR,IAAc,KAGpB,IAA7B2M,EAAkBnjB,SACpBmjB,EAAoB1M,GAQtB,IAAI2M,EAAYD,EAAkB7M,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAawK,GAAexJ,EAAO,CACrChB,UAAWA,EACX4K,SAAUA,EACVE,aAAcA,EACdnG,QAASA,IACR3C,GAAiBhC,IACbD,IACN,IACH,OAAOlW,OAAOC,KAAK8iB,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,MC6FpC,IAAAC,GAAe,CACbzgB,KAAM,OACNsU,SAAS,EACTC,MAAO,OACPpU,GA5HF,SAAcqU,GACZ,IAAIC,EAAQD,EAAKC,MACbQ,EAAUT,EAAKS,QACfjV,EAAOwU,EAAKxU,KAEhB,IAAIyU,EAAMwD,cAAcjY,GAAM0gB,MAA9B,CAoCA,IAhCA,IAAIC,EAAoB1L,EAAQ+I,SAC5B4C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB5L,EAAQ6L,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8B/L,EAAQgM,mBACtC7I,EAAUnD,EAAQmD,QAClBiG,EAAWpJ,EAAQoJ,SACnBE,EAAetJ,EAAQsJ,aACvBI,EAAc1J,EAAQ0J,YACtBuC,EAAwBjM,EAAQ+K,eAChCA,OAA2C,IAA1BkB,GAA0CA,EAC3DhB,EAAwBjL,EAAQiL,sBAChCiB,EAAqB1M,EAAMQ,QAAQxB,UACnCyE,EAAgBzC,GAAiB0L,GAEjCF,EAAqBD,IADH9I,IAAkBiJ,GACqCnB,EAjC/E,SAAuCvM,GACrC,GtCLgB,SsCKZgC,GAAiBhC,GACnB,MAAO,GAGT,IAAI2N,EAAoBvF,GAAqBpI,GAC7C,MAAO,CAACsI,GAA8BtI,GAAY2N,EAAmBrF,GAA8BqF,IA2BwCC,CAA8BF,GAA3E,CAACtF,GAAqBsF,KAChHzN,EAAa,CAACyN,GAAoB/mB,OAAO6mB,GAAoB1N,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAIpZ,OtCvCG,SsCuCIqb,GAAiBhC,GAAsBsM,GAAqBtL,EAAO,CACnFhB,UAAWA,EACX4K,SAAUA,EACVE,aAAcA,EACdnG,QAASA,EACT4H,eAAgBA,EAChBE,sBAAuBA,IACpBzM,KACJ,IACC6N,EAAgB7M,EAAM4D,MAAMhD,UAC5BoE,EAAahF,EAAM4D,MAAMrD,OACzBuM,EAAY,IAAIra,IAChBsa,GAAqB,EACrBC,EAAwB/N,EAAW,GAE9BpQ,EAAI,EAAGA,EAAIoQ,EAAWzW,OAAQqG,IAAK,CAC1C,IAAImQ,EAAYC,EAAWpQ,GAEvBoe,EAAiBjM,GAAiBhC,GAElCkO,EtCzDW,UsCyDQhE,GAAalK,GAChCmO,EAAa,CAAC7W,GAAKoI,IAAQnR,QAAQ0f,IAAmB,EACtDne,EAAMqe,EAAa,QAAU,SAC7BtF,EAAW2B,GAAexJ,EAAO,CACnChB,UAAWA,EACX4K,SAAUA,EACVE,aAAcA,EACdI,YAAaA,EACbvG,QAASA,IAEPyJ,EAAoBD,EAAaD,EAAmBvO,GAAQnI,GAAO0W,EAAmBxO,GAASpI,GAE/FuW,EAAc/d,GAAOkW,EAAWlW,KAClCse,EAAoBhG,GAAqBgG,IAG3C,IAAIC,EAAmBjG,GAAqBgG,GACxCE,EAAS,GAUb,GARInB,GACFmB,EAAO1mB,KAAKihB,EAASoF,IAAmB,GAGtCX,GACFgB,EAAO1mB,KAAKihB,EAASuF,IAAsB,EAAGvF,EAASwF,IAAqB,GAG1EC,EAAOC,OAAM,SAAUC,GACzB,OAAOA,KACL,CACFR,EAAwBhO,EACxB+N,GAAqB,EACrB,MAGFD,EAAUna,IAAIqM,EAAWsO,GAG3B,GAAIP,EAqBF,IAnBA,IAEIU,EAAQ,SAAeC,GACzB,IAAIC,EAAmB1O,EAAW3Z,MAAK,SAAU0Z,GAC/C,IAAIsO,EAASR,EAAUxa,IAAI0M,GAE3B,GAAIsO,EACF,OAAOA,EAAOnc,MAAM,EAAGuc,GAAIH,OAAM,SAAUC,GACzC,OAAOA,QAKb,GAAIG,EAEF,OADAX,EAAwBW,EACjB,SAIFD,EAnBYnC,EAAiB,EAAI,EAmBZmC,EAAK,GAGpB,UAFFD,EAAMC,GADmBA,KAOpC1N,EAAMhB,YAAcgO,IACtBhN,EAAMwD,cAAcjY,GAAM0gB,OAAQ,EAClCjM,EAAMhB,UAAYgO,EAClBhN,EAAM4N,OAAQ,KAUhBhJ,iBAAkB,CAAC,UACnB/P,KAAM,CACJoX,OAAO,IC7IX,SAAS4B,GAAehG,EAAUzR,EAAM0X,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjB3M,EAAG,EACHC,EAAG,IAIA,CACL9K,IAAKuR,EAASvR,IAAMF,EAAK8K,OAAS4M,EAAiB1M,EACnDzC,MAAOkJ,EAASlJ,MAAQvI,EAAK6K,MAAQ6M,EAAiB3M,EACtDzC,OAAQmJ,EAASnJ,OAAStI,EAAK8K,OAAS4M,EAAiB1M,EACzD5K,KAAMqR,EAASrR,KAAOJ,EAAK6K,MAAQ6M,EAAiB3M,GAIxD,SAAS4M,GAAsBlG,GAC7B,MAAO,CAACvR,GAAKqI,GAAOD,GAAQlI,IAAMwX,MAAK,SAAUC,GAC/C,OAAOpG,EAASoG,IAAS,KAiC7B,IAAAC,GAAe,CACb3iB,KAAM,OACNsU,SAAS,EACTC,MAAO,OACP8E,iBAAkB,CAAC,mBACnBlZ,GAlCF,SAAcqU,GACZ,IAAIC,EAAQD,EAAKC,MACbzU,EAAOwU,EAAKxU,KACZshB,EAAgB7M,EAAM4D,MAAMhD,UAC5BoE,EAAahF,EAAM4D,MAAMrD,OACzBuN,EAAmB9N,EAAMwD,cAAc2K,gBACvCC,EAAoB5E,GAAexJ,EAAO,CAC5CgK,eAAgB,cAEdqE,EAAoB7E,GAAexJ,EAAO,CAC5CkK,aAAa,IAEXoE,EAA2BT,GAAeO,EAAmBvB,GAC7D0B,EAAsBV,GAAeQ,EAAmBrJ,EAAY8I,GACpEU,EAAoBT,GAAsBO,GAC1CG,EAAmBV,GAAsBQ,GAC7CvO,EAAMwD,cAAcjY,GAAQ,CAC1B+iB,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpBzO,EAAMlK,WAAWyK,OAAS1X,OAAOsX,OAAO,GAAIH,EAAMlK,WAAWyK,OAAQ,CACnEmO,+BAAgCF,EAChCG,sBAAuBF,MCH3BG,GAAe,CACbrjB,KAAM,SACNsU,SAAS,EACTC,MAAO,OACPiB,SAAU,CAAC,iBACXrV,GA5BF,SAAgB2U,GACd,IAAIL,EAAQK,EAAML,MACdQ,EAAUH,EAAMG,QAChBjV,EAAO8U,EAAM9U,KACbsjB,EAAkBrO,EAAQrK,OAC1BA,OAA6B,IAApB0Y,EAA6B,CAAC,EAAG,GAAKA,EAC/Cha,EAAOoK,GAAWH,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GA5BD,SAAiCA,EAAW4E,EAAOzN,GACxD,IAAIsN,EAAgBzC,GAAiBhC,GACjC8P,EAAiB,CAACtY,GAAMF,IAAK/I,QAAQkW,IAAkB,GAAK,EAAI,EAEhE1D,EAAyB,mBAAX5J,EAAwBA,EAAOtN,OAAOsX,OAAO,GAAIyD,EAAO,CACxE5E,UAAWA,KACP7I,EACF4Y,EAAWhP,EAAK,GAChBiP,EAAWjP,EAAK,GAIpB,OAFAgP,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAACtY,GAAMmI,IAAOpR,QAAQkW,IAAkB,EAAI,CACjDtC,EAAG6N,EACH5N,EAAG2N,GACD,CACF5N,EAAG4N,EACH3N,EAAG4N,GAWcC,CAAwBjQ,EAAWgB,EAAM4D,MAAOzN,GAC1D4I,IACN,IACCmQ,EAAwBra,EAAKmL,EAAMhB,WACnCmC,EAAI+N,EAAsB/N,EAC1BC,EAAI8N,EAAsB9N,EAEW,MAArCpB,EAAMwD,cAAcD,gBACtBvD,EAAMwD,cAAcD,cAAcpC,GAAKA,EACvCnB,EAAMwD,cAAcD,cAAcnC,GAAKA,GAGzCpB,EAAMwD,cAAcjY,GAAQsJ,ICxB9Bsa,GAAe,CACb5jB,KAAM,gBACNsU,SAAS,EACTC,MAAO,OACPpU,GApBF,SAAuBqU,GACrB,IAAIC,EAAQD,EAAKC,MACbzU,EAAOwU,EAAKxU,KAKhByU,EAAMwD,cAAcjY,GAAQ4d,GAAe,CACzCvI,UAAWZ,EAAM4D,MAAMhD,UACvBpb,QAASwa,EAAM4D,MAAMrD,OACrBE,SAAU,WACVzB,UAAWgB,EAAMhB,aAUnBnK,KAAM,IC6FRua,GAAe,CACb7jB,KAAM,kBACNsU,SAAS,EACTC,MAAO,OACPpU,GA5GF,SAAyBqU,GACvB,IAAIC,EAAQD,EAAKC,MACbQ,EAAUT,EAAKS,QACfjV,EAAOwU,EAAKxU,KACZ2gB,EAAoB1L,EAAQ+I,SAC5B4C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB5L,EAAQ6L,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrDxC,EAAWpJ,EAAQoJ,SACnBE,EAAetJ,EAAQsJ,aACvBI,EAAc1J,EAAQ0J,YACtBvG,EAAUnD,EAAQmD,QAClB0L,EAAkB7O,EAAQ8O,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwB/O,EAAQgP,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtD1H,EAAW2B,GAAexJ,EAAO,CACnC4J,SAAUA,EACVE,aAAcA,EACdnG,QAASA,EACTuG,YAAaA,IAEXzG,EAAgBzC,GAAiBhB,EAAMhB,WACvCoK,EAAYF,GAAalJ,EAAMhB,WAC/ByQ,GAAmBrG,EACnBG,EAAW5G,GAAyBc,GACpC4I,ECrCY,MDqCS9C,ECrCH,IAAM,IDsCxBhG,EAAgBvD,EAAMwD,cAAcD,cACpCsJ,EAAgB7M,EAAM4D,MAAMhD,UAC5BoE,EAAahF,EAAM4D,MAAMrD,OACzBmP,EAA4C,mBAAjBF,EAA8BA,EAAa3mB,OAAOsX,OAAO,GAAIH,EAAM4D,MAAO,CACvG5E,UAAWgB,EAAMhB,aACbwQ,EACF3a,EAAO,CACTsM,EAAG,EACHC,EAAG,GAGL,GAAKmC,EAAL,CAIA,GAAI4I,GAAiBG,EAAc,CACjC,IAAIqD,EAAwB,MAAbpG,EAAmBjT,GAAME,GACpCoZ,EAAuB,MAAbrG,EAAmB7K,GAASC,GACtC7P,EAAmB,MAAbya,EAAmB,SAAW,QACpCpT,EAASoN,EAAcgG,GACvB7b,EAAM6V,EAAcgG,GAAY1B,EAAS8H,GACzCliB,EAAM8V,EAAcgG,GAAY1B,EAAS+H,GACzCC,EAAWP,GAAUtK,EAAWlW,GAAO,EAAI,EAC3CghB,E1CxDW,U0CwDF1G,EAAsByD,EAAc/d,GAAOkW,EAAWlW,GAC/DihB,E1CzDW,U0CyDF3G,GAAuBpE,EAAWlW,IAAQ+d,EAAc/d,GAGjEwU,EAAetD,EAAMC,SAASU,MAC9BmD,EAAYwL,GAAUhM,EAAejC,GAAciC,GAAgB,CACrErC,MAAO,EACPC,OAAQ,GAEN8O,EAAqBhQ,EAAMwD,cAAc,oBAAsBxD,EAAMwD,cAAc,oBAAoBG,QxBtEtG,CACLrN,IAAK,EACLqI,MAAO,EACPD,OAAQ,EACRlI,KAAM,GwBmEFyZ,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAWtN,GAAO,EAAGgK,EAAc/d,GAAMgV,EAAUhV,IACnDshB,EAAYX,EAAkB5C,EAAc/d,GAAO,EAAI+gB,EAAWM,EAAWF,EAAkBP,EAAoBI,EAASK,EAAWF,EAAkBP,EACzJW,EAAYZ,GAAmB5C,EAAc/d,GAAO,EAAI+gB,EAAWM,EAAWD,EAAkBR,EAAoBK,EAASI,EAAWD,EAAkBR,EAC1JvL,EAAoBnE,EAAMC,SAASU,OAASsB,GAAgBjC,EAAMC,SAASU,OAC3E2P,EAAenM,EAAiC,MAAboF,EAAmBpF,EAAkBwE,WAAa,EAAIxE,EAAkByE,YAAc,EAAI,EAC7H2H,EAAsBvQ,EAAMwD,cAAcrN,OAAS6J,EAAMwD,cAAcrN,OAAO6J,EAAMhB,WAAWuK,GAAY,EAC3GiH,EAAYjN,EAAcgG,GAAY6G,EAAYG,EAAsBD,EACxEG,EAAYlN,EAAcgG,GAAY8G,EAAYE,EAEtD,GAAIpE,EAAe,CACjB,IAAIuE,EAAkB7N,GAAOyM,EAASvM,GAAQrV,EAAK8iB,GAAa9iB,EAAKyI,EAAQmZ,EAASxM,GAAQrV,EAAKgjB,GAAahjB,GAChH8V,EAAcgG,GAAYmH,EAC1B7b,EAAK0U,GAAYmH,EAAkBva,EAGrC,GAAImW,EAAc,CAChB,IAAIqE,EAAyB,MAAbpH,EAAmBjT,GAAME,GAErCoa,EAAwB,MAAbrH,EAAmB7K,GAASC,GAEvCkS,EAAUtN,EAAc8I,GAExByE,EAAOD,EAAUhJ,EAAS8I,GAE1BI,GAAOF,EAAUhJ,EAAS+I,GAE1BI,GAAmBnO,GAAOyM,EAASvM,GAAQ+N,EAAMN,GAAaM,EAAMD,EAASvB,EAASxM,GAAQiO,GAAMN,GAAaM,IAErHxN,EAAc8I,GAAW2E,GACzBnc,EAAKwX,GAAW2E,GAAmBH,GAIvC7Q,EAAMwD,cAAcjY,GAAQsJ,IAS5B+P,iBAAkB,CAAC,WEhHN,SAASqM,GAAiBC,EAAyBlP,EAAcmP,QAC9D,IAAZA,IACFA,GAAU,GAGZ,ICVoC7R,ECJO9Z,EFcvCE,EAAkBkc,GAAmBI,GACrC5L,EAAOC,GAAsB6a,GAC7BE,EAA0B3R,GAAcuC,GACxC6E,EAAS,CACXpQ,WAAY,EACZF,UAAW,GAET0O,EAAU,CACZ9D,EAAG,EACHC,EAAG,GAkBL,OAfIgQ,IAA4BA,IAA4BD,MACxB,SAA9BhS,GAAY6C,IAChB2F,GAAejiB,MACbmhB,GCzBgCvH,EDyBT0C,KCxBd3C,GAAUC,IAAUG,GAAcH,GCJxC,CACL7I,YAFyCjR,EDQb8Z,GCNR7I,WACpBF,UAAW/Q,EAAQ+Q,WDGZgR,GAAgBjI,ID0BnBG,GAAcuC,KAChBiD,EAAU5O,GAAsB2L,IACxBb,GAAKa,EAAa4G,WAC1B3D,EAAQ7D,GAAKY,EAAa2G,WACjBjjB,IACTuf,EAAQ9D,EAAIuG,GAAoBhiB,KAI7B,CACLyb,EAAG/K,EAAKI,KAAOqQ,EAAOpQ,WAAawO,EAAQ9D,EAC3CC,EAAGhL,EAAKE,IAAMuQ,EAAOtQ,UAAY0O,EAAQ7D,EACzCH,MAAO7K,EAAK6K,MACZC,OAAQ9K,EAAK8K,QG7BjB,IAAImQ,GAAkB,CACpBrS,UAAW,SACXsS,UAAW,GACX7Q,SAAU,YAGZ,SAAS8Q,KACP,IAAK,IAAIC,EAAOC,UAAUjpB,OAAQ8I,EAAO,IAAI2B,MAAMue,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/EpgB,EAAKogB,GAAQD,UAAUC,GAGzB,OAAQpgB,EAAK0c,MAAK,SAAUxoB,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQ6Q,0BAIhC,SAASsb,GAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCX,GAAkBW,EAC3E,OAAO,SAAsBpR,EAAWL,EAAQC,QAC9B,IAAZA,IACFA,EAAUyR,GAGZ,IC/C6BvmB,EAC3BwmB,ED8CElS,EAAQ,CACVhB,UAAW,SACXmT,iBAAkB,GAClB3R,QAAS3X,OAAOsX,OAAO,GAAIkR,GAAiBY,GAC5CzO,cAAe,GACfvD,SAAU,CACRW,UAAWA,EACXL,OAAQA,GAEVzK,WAAY,GACZoK,OAAQ,IAENkS,EAAmB,GACnBC,GAAc,EACdzf,EAAW,CACboN,MAAOA,EACPsS,WAAY,SAAoB9R,GAC9B+R,IACAvS,EAAMQ,QAAU3X,OAAOsX,OAAO,GAAI8R,EAAgBjS,EAAMQ,QAASA,GACjER,EAAMgH,cAAgB,CACpBpG,UAAWxY,GAAUwY,GAAaoH,GAAkBpH,GAAaA,EAAUmK,eAAiB/C,GAAkBpH,EAAUmK,gBAAkB,GAC1IxK,OAAQyH,GAAkBzH,IAI5B,IExE4B+Q,EAC9BkB,EFuEML,EGtCG,SAAwBb,GAErC,IAAIa,EAlCN,SAAeb,GACb,IAAImB,EAAM,IAAIhgB,IACVigB,EAAU,IAAIrkB,IACdskB,EAAS,GA0Bb,OAzBArB,EAAUvoB,SAAQ,SAAU6pB,GAC1BH,EAAI9f,IAAIigB,EAASrnB,KAAMqnB,MAkBzBtB,EAAUvoB,SAAQ,SAAU6pB,GACrBF,EAAQnjB,IAAIqjB,EAASrnB,OAhB5B,SAASsgB,EAAK+G,GACZF,EAAQ/X,IAAIiY,EAASrnB,MACN,GAAG5F,OAAOitB,EAAS7R,UAAY,GAAI6R,EAAShO,kBAAoB,IACtE7b,SAAQ,SAAU8pB,GACzB,IAAKH,EAAQnjB,IAAIsjB,GAAM,CACrB,IAAIC,EAAcL,EAAIngB,IAAIugB,GAEtBC,GACFjH,EAAKiH,OAIXH,EAAO/rB,KAAKgsB,GAMV/G,CAAK+G,MAGFD,EAKgBhZ,CAAM2X,GAE7B,OAAOpS,GAAeJ,QAAO,SAAUC,EAAKe,GAC1C,OAAOf,EAAIpZ,OAAOwsB,EAAiBhsB,QAAO,SAAUysB,GAClD,OAAOA,EAAS9S,QAAUA,QAE3B,IH8B0BiT,EExEKzB,EFwEsB,GAAG3rB,OAAOosB,EAAkB/R,EAAMQ,QAAQ8Q,WEvE9FkB,EAASlB,EAAUxS,QAAO,SAAU0T,EAAQQ,GAC9C,IAAIC,EAAWT,EAAOQ,EAAQznB,MAK9B,OAJAinB,EAAOQ,EAAQznB,MAAQ0nB,EAAWpqB,OAAOsX,OAAO,GAAI8S,EAAUD,EAAS,CACrExS,QAAS3X,OAAOsX,OAAO,GAAI8S,EAASzS,QAASwS,EAAQxS,SACrD3L,KAAMhM,OAAOsX,OAAO,GAAI8S,EAASpe,KAAMme,EAAQne,QAC5Cme,EACER,IACN,IAEI3pB,OAAOC,KAAK0pB,GAAQC,KAAI,SAAUrgB,GACvC,OAAOogB,EAAOpgB,QFsGV,OAvCA4N,EAAMmS,iBAAmBA,EAAiBhsB,QAAO,SAAU+sB,GACzD,OAAOA,EAAErT,WAqJbG,EAAMmS,iBAAiBppB,SAAQ,SAAUsc,GACvC,IAAI9Z,EAAO8Z,EAAM9Z,KACb4nB,EAAgB9N,EAAM7E,QACtBA,OAA4B,IAAlB2S,EAA2B,GAAKA,EAC1C/S,EAASiF,EAAMjF,OAEnB,GAAsB,mBAAXA,EAAuB,CAChC,IAAIgT,EAAYhT,EAAO,CACrBJ,MAAOA,EACPzU,KAAMA,EACNqH,SAAUA,EACV4N,QAASA,IAKX4R,EAAiBxrB,KAAKwsB,GAFT,kBA7HRxgB,EAASsU,UAOlBmM,YAAa,WACX,IAAIhB,EAAJ,CAIA,IAAIiB,EAAkBtT,EAAMC,SACxBW,EAAY0S,EAAgB1S,UAC5BL,EAAS+S,EAAgB/S,OAG7B,GAAKgR,GAAiB3Q,EAAWL,GAAjC,CASAP,EAAM4D,MAAQ,CACZhD,UAAWqQ,GAAiBrQ,EAAWqB,GAAgB1B,GAAoC,UAA3BP,EAAMQ,QAAQC,UAC9EF,OAAQc,GAAcd,IAOxBP,EAAM4N,OAAQ,EACd5N,EAAMhB,UAAYgB,EAAMQ,QAAQxB,UAKhCgB,EAAMmS,iBAAiBppB,SAAQ,SAAU6pB,GACvC,OAAO5S,EAAMwD,cAAcoP,EAASrnB,MAAQ1C,OAAOsX,OAAO,GAAIyS,EAAS/d,SAIzE,IAAK,IAAIvH,EAAQ,EAAGA,EAAQ0S,EAAMmS,iBAAiB3pB,OAAQ8E,IAUzD,IAAoB,IAAhB0S,EAAM4N,MAAV,CAMA,IAAI2F,EAAwBvT,EAAMmS,iBAAiB7kB,GAC/C5B,EAAK6nB,EAAsB7nB,GAC3B8nB,EAAyBD,EAAsB/S,QAC/CiJ,OAAsC,IAA3B+J,EAAoC,GAAKA,EACpDjoB,EAAOgoB,EAAsBhoB,KAEf,mBAAPG,IACTsU,EAAQtU,EAAG,CACTsU,MAAOA,EACPQ,QAASiJ,EACTle,KAAMA,EACNqH,SAAUA,KACNoN,QAjBNA,EAAM4N,OAAQ,EACdtgB,GAAS,KAsBf4Z,QCjM2Bxb,EDiMV,WACf,OAAO,IAAI+nB,SAAQ,SAAUC,GAC3B9gB,EAASygB,cACTK,EAAQ1T,OClMT,WAUL,OATKkS,IACHA,EAAU,IAAIuB,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBzB,OAAU0B,EACVF,EAAQhoB,YAKPwmB,ID2LL2B,QAAS,WACPtB,IACAF,GAAc,IAIlB,IAAKd,GAAiB3Q,EAAWL,GAK/B,OAAO3N,EAmCT,SAAS2f,IACPH,EAAiBrpB,SAAQ,SAAU2C,GACjC,OAAOA,OAET0mB,EAAmB,GAGrB,OAvCAxf,EAAS0f,WAAW9R,GAASmT,MAAK,SAAU3T,IACrCqS,GAAe7R,EAAQsT,eAC1BtT,EAAQsT,cAAc9T,MAqCnBpN,GAGJ,IAAImhB,GAA4BpC,KIzPnCoC,GAA4BpC,GAAgB,CAC9CI,iBAFqB,CAACpL,GAAgBpD,GAAeyQ,GAAeC,MCMlEF,GAA4BpC,GAAgB,CAC9CI,iBAFqB,CAACpL,GAAgBpD,GAAeyQ,GAAeC,GAAa9d,GAAQ+d,GAAM/F,GAAiBxN,GAAOpD,2KpDNvG,+BAEC,YACF,sBACY,2BACP,kBACF,mBACG,4DAQC,kBACN,iBACK,uBAEC,kBACN,iBACK,wBAEE,oBACN,mBACK,0JqDGxB,MAYM4W,GAAiB,IAAI5qB,OAAQ,4BAsB7B6qB,GAAgBnpB,IAAU,UAAY,YACtCopB,GAAmBppB,IAAU,YAAc,UAC3CqpB,GAAmBrpB,IAAU,aAAe,eAC5CspB,GAAsBtpB,IAAU,eAAiB,aACjDupB,GAAkBvpB,IAAU,aAAe,cAC3CwpB,GAAiBxpB,IAAU,cAAgB,aAE3C4L,GAAU,CACdV,OAAQ,CAAC,EAAG,GACZyT,SAAU,kBACVhJ,UAAW,SACX8T,QAAS,UACTC,aAAc,KACdC,WAAW,GAGPxd,GAAc,CAClBjB,OAAQ,0BACRyT,SAAU,mBACVhJ,UAAW,0BACX8T,QAAS,SACTC,aAAc,yBACdC,UAAW,oBASb,MAAMC,WAAiBxhB,EACrBC,YAAY9N,EAASmD,GACnBkP,MAAMrS,GAENqK,KAAKilB,QAAU,KACfjlB,KAAKyI,QAAUzI,KAAK0I,WAAW5P,GAC/BkH,KAAKklB,MAAQllB,KAAKmlB,kBAClBnlB,KAAKolB,UAAYplB,KAAKqlB,gBAEtBrlB,KAAKiJ,qBAKWjC,qBAChB,OAAOA,GAGaO,yBACpB,OAAOA,GAGM5L,kBACb,MAxFS,WA6FX0J,SACMnL,EAAW8F,KAAK0D,YAIH1D,KAAK0D,SAASvJ,UAAUC,SA3ErB,QA8ElB4F,KAAK0N,OAIP1N,KAAK2N,QAGPA,OACE,GAAIzT,EAAW8F,KAAK0D,WAAa1D,KAAKklB,MAAM/qB,UAAUC,SAtFlC,QAuFlB,OAGF,MAAMyS,EAASmY,GAASM,qBAAqBtlB,KAAK0D,UAC5C5D,EAAgB,CACpBA,cAAeE,KAAK0D,UAKtB,IAFkBrD,EAAamB,QAAQxB,KAAK0D,SAtG5B,mBAsGkD5D,GAEpDgC,iBAAd,CAKA,GAAI9B,KAAKolB,UACPxf,EAAYC,iBAAiB7F,KAAKklB,MAAO,SAAU,YAC9C,CACL,QAAsB,IAAXK,GACT,MAAM,IAAI3rB,UAAU,gEAGtB,IAAI4gB,EAAmBxa,KAAK0D,SAEG,WAA3B1D,KAAKyI,QAAQsI,UACfyJ,EAAmB3N,EACVtU,EAAUyH,KAAKyI,QAAQsI,WAChCyJ,EAAmB9hB,EAAWsH,KAAKyI,QAAQsI,WACA,iBAA3B/Q,KAAKyI,QAAQsI,YAC7ByJ,EAAmBxa,KAAKyI,QAAQsI,WAGlC,MAAM+T,EAAe9kB,KAAKwlB,mBACpBC,EAAkBX,EAAarD,UAAUhsB,KAAKstB,GAA8B,gBAAlBA,EAASrnB,OAA+C,IAArBqnB,EAAS/S,SAE5GhQ,KAAKilB,QAAUM,GAAoB/K,EAAkBxa,KAAKklB,MAAOJ,GAE7DW,GACF7f,EAAYC,iBAAiB7F,KAAKklB,MAAO,SAAU,UAQnD,iBAAkBtvB,SAASC,kBAC5BgX,EAAOhI,QA9Hc,gBA+HtB,GAAG/O,UAAUF,SAASsF,KAAK7E,UACxB6C,QAAQiU,GAAQ9M,EAAaQ,GAAGsM,EAAM,YAAavS,IAGxDoF,KAAK0D,SAASgiB,QACd1lB,KAAK0D,SAAS4B,aAAa,iBAAiB,GAE5CtF,KAAKklB,MAAM/qB,UAAUkL,OA9ID,QA+IpBrF,KAAK0D,SAASvJ,UAAUkL,OA/IJ,QAgJpBhF,EAAamB,QAAQxB,KAAK0D,SAtJT,oBAsJgC5D,IAGnD4N,OACE,GAAIxT,EAAW8F,KAAK0D,YAAc1D,KAAKklB,MAAM/qB,UAAUC,SApJnC,QAqJlB,OAGF,MAAM0F,EAAgB,CACpBA,cAAeE,KAAK0D,UAGtB1D,KAAK2lB,cAAc7lB,GAGrB8D,UACM5D,KAAKilB,SACPjlB,KAAKilB,QAAQjB,UAGfhc,MAAMpE,UAGRyT,SACErX,KAAKolB,UAAYplB,KAAKqlB,gBAClBrlB,KAAKilB,SACPjlB,KAAKilB,QAAQ5N,SAMjBpO,qBACE5I,EAAaQ,GAAGb,KAAK0D,SAtLJ,oBAsL2BxE,IAC1CA,EAAMwD,iBACN1C,KAAKqF,WAITsgB,cAAc7lB,GACMO,EAAamB,QAAQxB,KAAK0D,SAjM5B,mBAiMkD5D,GACpDgC,mBAMV,iBAAkBlM,SAASC,iBAC7B,GAAGC,UAAUF,SAASsF,KAAK7E,UACxB6C,QAAQiU,GAAQ9M,EAAaC,IAAI6M,EAAM,YAAavS,IAGrDoF,KAAKilB,SACPjlB,KAAKilB,QAAQjB,UAGfhkB,KAAKklB,MAAM/qB,UAAUmJ,OAxMD,QAyMpBtD,KAAK0D,SAASvJ,UAAUmJ,OAzMJ,QA0MpBtD,KAAK0D,SAAS4B,aAAa,gBAAiB,SAC5CM,EAAYE,oBAAoB9F,KAAKklB,MAAO,UAC5C7kB,EAAamB,QAAQxB,KAAK0D,SApNR,qBAoNgC5D,IAGpD4I,WAAW5P,GAST,GARAA,EAAS,IACJkH,KAAKyD,YAAYuD,WACjBpB,EAAYI,kBAAkBhG,KAAK0D,aACnC5K,GAGLF,EA7OS,WA6OaE,EAAQkH,KAAKyD,YAAY8D,aAEf,iBAArBzO,EAAOiY,YAA2BxY,EAAUO,EAAOiY,YACV,mBAA3CjY,EAAOiY,UAAUvK,sBAGxB,MAAM,IAAI5M,UAnPH,WAmPqBC,cAAP,kGAGvB,OAAOf,EAGTqsB,kBACE,OAAO3vB,EAAe2B,KAAK6I,KAAK0D,SA5Nd,kBA4NuC,GAG3DkiB,gBACE,MAAMC,EAAiB7lB,KAAK0D,SAAS/M,WAErC,GAAIkvB,EAAe1rB,UAAUC,SAvON,WAwOrB,OAAOuqB,GAGT,GAAIkB,EAAe1rB,UAAUC,SA1OJ,aA2OvB,OAAOwqB,GAIT,MAAMkB,EAAkF,QAA1E9rB,iBAAiBgG,KAAKklB,OAAOjrB,iBAAiB,iBAAiBhC,OAE7E,OAAI4tB,EAAe1rB,UAAUC,SAnPP,UAoPb0rB,EAAQtB,GAAmBD,GAG7BuB,EAAQpB,GAAsBD,GAGvCY,gBACE,OAA0D,OAAnDrlB,KAAK0D,SAASmB,QAAS,WAGhCkhB,aACE,MAAMzf,OAAEA,GAAWtG,KAAKyI,QAExB,MAAsB,iBAAXnC,EACFA,EAAOtO,MAAM,KAAK4qB,IAAIpd,GAAO7I,OAAO8O,SAASjG,EAAK,KAGrC,mBAAXc,EACF0f,GAAc1f,EAAO0f,EAAYhmB,KAAK0D,UAGxC4C,EAGTkf,mBACE,MAAMS,EAAwB,CAC5B9W,UAAWnP,KAAK4lB,gBAChBnE,UAAW,CAAC,CACV/lB,KAAM,kBACNiV,QAAS,CACPoJ,SAAU/Z,KAAKyI,QAAQsR,WAG3B,CACEre,KAAM,SACNiV,QAAS,CACPrK,OAAQtG,KAAK+lB,iBAanB,MAP6B,WAAzB/lB,KAAKyI,QAAQoc,UACfoB,EAAsBxE,UAAY,CAAC,CACjC/lB,KAAM,cACNsU,SAAS,KAIN,IACFiW,KACsC,mBAA9BjmB,KAAKyI,QAAQqc,aAA8B9kB,KAAKyI,QAAQqc,aAAamB,GAAyBjmB,KAAKyI,QAAQqc,cAI1HoB,iBAAgB3jB,IAAEA,EAAFtF,OAAOA,IACrB,MAAMkpB,EAAQ3wB,EAAeC,KApSF,8DAoS+BuK,KAAKklB,OAAO5uB,OAAOwD,GAExEqsB,EAAMxtB,QAMXyE,EAAqB+oB,EAAOlpB,EAnUT,cAmUiBsF,GAAyB4jB,EAAMruB,SAASmF,IAASyoB,QAK/DxhB,yBAACvO,EAASmD,GAChC,MAAMkM,EAAOggB,GAAS/f,oBAAoBtP,EAASmD,GAEnD,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBkM,EAAKlM,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CkM,EAAKlM,MAIaoL,uBAACpL,GACrB,OAAOkH,KAAK+E,MAAK,WACfigB,GAASoB,kBAAkBpmB,KAAMlH,MAIpBoL,kBAAChF,GAChB,GAAIA,IA1VmB,IA0VTA,EAAMyG,QAAiD,UAAfzG,EAAMqB,MA7VhD,QA6VoErB,EAAMqD,KACpF,OAGF,MAAM8jB,EAAU7wB,EAAeC,KA3UN,+BA6UzB,IAAK,IAAIuJ,EAAI,EAAGC,EAAMonB,EAAQ1tB,OAAQqG,EAAIC,EAAKD,IAAK,CAClD,MAAMsnB,EAAUtB,GAAS7gB,YAAYkiB,EAAQrnB,IAC7C,IAAKsnB,IAAyC,IAA9BA,EAAQ7d,QAAQsc,UAC9B,SAGF,IAAKuB,EAAQ5iB,SAASvJ,UAAUC,SAzVd,QA0VhB,SAGF,MAAM0F,EAAgB,CACpBA,cAAewmB,EAAQ5iB,UAGzB,GAAIxE,EAAO,CACT,MAAMqnB,EAAernB,EAAMqnB,eACrBC,EAAeD,EAAazuB,SAASwuB,EAAQpB,OACnD,GACEqB,EAAazuB,SAASwuB,EAAQ5iB,WACC,WAA9B4iB,EAAQ7d,QAAQsc,YAA2ByB,GACb,YAA9BF,EAAQ7d,QAAQsc,WAA2ByB,EAE5C,SAIF,GAAIF,EAAQpB,MAAM9qB,SAAS8E,EAAMjC,UAA4B,UAAfiC,EAAMqB,MA7X5C,QA6XgErB,EAAMqD,KAAoB,qCAAqC5I,KAAKuF,EAAMjC,OAAO8N,UACvJ,SAGiB,UAAf7L,EAAMqB,OACRT,EAAc2mB,WAAavnB,GAI/BonB,EAAQX,cAAc7lB,IAICoE,4BAACvO,GAC1B,OAAOwC,EAAuBxC,IAAYA,EAAQgB,WAGxBuN,6BAAChF,GAQ3B,GAAI,kBAAkBvF,KAAKuF,EAAMjC,OAAO8N,SAvZ1B,UAwZZ7L,EAAMqD,KAzZO,WAyZerD,EAAMqD,MArZjB,cAsZfrD,EAAMqD,KAvZO,YAuZmBrD,EAAMqD,KACtCrD,EAAMjC,OAAO4H,QAlYC,oBAmYfyf,GAAe3qB,KAAKuF,EAAMqD,KAC3B,OAGF,MAAMmkB,EAAW1mB,KAAK7F,UAAUC,SA9YZ,QAgZpB,IAAKssB,GAlaU,WAkaExnB,EAAMqD,IACrB,OAMF,GAHArD,EAAMwD,iBACNxD,EAAMynB,kBAEFzsB,EAAW8F,MACb,OAGF,MAAM4mB,EAAkB,IAAM5mB,KAAKxJ,QArZV,+BAqZ0CwJ,KAAOxK,EAAewB,KAAKgJ,KArZrE,+BAqZiG,GAE1H,MA/ae,WA+aXd,EAAMqD,KACRqkB,IAAkBlB,aAClBV,GAAS6B,cA9aM,YAkbb3nB,EAAMqD,KAjbS,cAiberD,EAAMqD,KACjCmkB,GACHE,IAAkBE,aAGpB9B,GAAS7gB,YAAYyiB,KAAmBV,gBAAgBhnB,SAIrDwnB,GA7bS,UA6bGxnB,EAAMqD,KACrByiB,GAAS6B,eAWfxmB,EAAaQ,GAAGjL,SA3bgB,+BASH,8BAkb2CovB,GAAS+B,uBACjF1mB,EAAaQ,GAAGjL,SA5bgB,+BAUV,iBAkb2CovB,GAAS+B,uBAC1E1mB,EAAaQ,GAAGjL,SA9bc,6BA8bkBovB,GAAS6B,YACzDxmB,EAAaQ,GAAGjL,SA7bc,6BA6bkBovB,GAAS6B,YACzDxmB,EAAaQ,GAAGjL,SAhcc,6BAUD,+BAsbyC,SAAUsJ,GAC9EA,EAAMwD,iBACNsiB,GAASoB,kBAAkBpmB,SAU7B1E,EAAmB0pB,ICjfnB,MAAMgC,GACJvjB,cACEzD,KAAK0D,SAAW9N,SAASsF,KAG3B+rB,WAEE,MAAMC,EAAgBtxB,SAASC,gBAAgB4e,YAC/C,OAAOld,KAAK0S,IAAIhP,OAAOksB,WAAaD,GAGtCxZ,OACE,MAAM0D,EAAQpR,KAAKinB,WACnBjnB,KAAKonB,mBAELpnB,KAAKqnB,sBAAsBrnB,KAAK0D,SAAU,eAAgB4jB,GAAmBA,EAAkBlW,GAE/FpR,KAAKqnB,sBApBsB,oDAoBwB,eAAgBC,GAAmBA,EAAkBlW,GACxGpR,KAAKqnB,sBApBuB,cAoBwB,cAAeC,GAAmBA,EAAkBlW,GAG1GgW,mBACEpnB,KAAKunB,sBAAsBvnB,KAAK0D,SAAU,YAC1C1D,KAAK0D,SAAS0K,MAAM4J,SAAW,SAGjCqP,sBAAsB3xB,EAAU8xB,EAAWhsB,GACzC,MAAMisB,EAAiBznB,KAAKinB,WAW5BjnB,KAAK0nB,2BAA2BhyB,EAVHC,IAC3B,GAAIA,IAAYqK,KAAK0D,UAAYzI,OAAOksB,WAAaxxB,EAAQ8e,YAAcgT,EACzE,OAGFznB,KAAKunB,sBAAsB5xB,EAAS6xB,GACpC,MAAMF,EAAkBrsB,OAAOjB,iBAAiBrE,GAAS6xB,GACzD7xB,EAAQyY,MAAMoZ,GAAgBhsB,EAASmB,OAAOC,WAAW0qB,IAA7B,OAMhCvJ,QACE/d,KAAK2nB,wBAAwB3nB,KAAK0D,SAAU,YAC5C1D,KAAK2nB,wBAAwB3nB,KAAK0D,SAAU,gBAC5C1D,KAAK2nB,wBA/CsB,oDA+C0B,gBACrD3nB,KAAK2nB,wBA/CuB,cA+C0B,eAGxDJ,sBAAsB5xB,EAAS6xB,GAC7B,MAAMI,EAAcjyB,EAAQyY,MAAMoZ,GAC9BI,GACFhiB,EAAYC,iBAAiBlQ,EAAS6xB,EAAWI,GAIrDD,wBAAwBjyB,EAAU8xB,GAWhCxnB,KAAK0nB,2BAA2BhyB,EAVHC,IAC3B,MAAM0D,EAAQuM,EAAYS,iBAAiB1Q,EAAS6xB,QAC/B,IAAVnuB,EACT1D,EAAQyY,MAAMyZ,eAAeL,IAE7B5hB,EAAYE,oBAAoBnQ,EAAS6xB,GACzC7xB,EAAQyY,MAAMoZ,GAAanuB,KAOjCquB,2BAA2BhyB,EAAUoyB,GAC/BvvB,EAAU7C,GACZoyB,EAASpyB,GAETF,EAAeC,KAAKC,EAAUsK,KAAK0D,UAAUxK,QAAQ4uB,GAIzDC,gBACE,OAAO/nB,KAAKinB,WAAa,GClF7B,MAAMjgB,GAAU,CACdlN,WAAW,EACXmK,YAAY,EACZO,YAAa,OACbwjB,cAAe,MAGXzgB,GAAc,CAClBzN,UAAW,UACXmK,WAAY,UACZO,YAAa,mBACbwjB,cAAe,mBASjB,MAAMC,GACJxkB,YAAY3K,GACVkH,KAAKyI,QAAUzI,KAAK0I,WAAW5P,GAC/BkH,KAAKkoB,aAAc,EACnBloB,KAAK0D,SAAW,KAGlBiK,KAAKnS,GACEwE,KAAKyI,QAAQ3O,WAKlBkG,KAAKmoB,UAEDnoB,KAAKyI,QAAQxE,YACfpJ,EAAOmF,KAAKooB,eAGdpoB,KAAKooB,cAAcjuB,UAAU2Q,IAvBT,QAyBpB9K,KAAKqoB,kBAAkB,KACrBlsB,EAAQX,MAbRW,EAAQX,GAiBZkS,KAAKlS,GACEwE,KAAKyI,QAAQ3O,WAKlBkG,KAAKooB,cAAcjuB,UAAUmJ,OApCT,QAsCpBtD,KAAKqoB,kBAAkB,KACrBroB,KAAK4D,UACLzH,EAAQX,MARRW,EAAQX,GAcZ4sB,cACE,IAAKpoB,KAAK0D,SAAU,CAClB,MAAM4kB,EAAW1yB,SAAS2yB,cAAc,OACxCD,EAASE,UAnDa,iBAoDlBxoB,KAAKyI,QAAQxE,YACfqkB,EAASnuB,UAAU2Q,IApDH,QAuDlB9K,KAAK0D,SAAW4kB,EAGlB,OAAOtoB,KAAK0D,SAGdgF,WAAW5P,GAST,OARAA,EAAS,IACJkO,MACmB,iBAAXlO,EAAsBA,EAAS,KAIrC0L,YAAc9L,EAAWI,EAAO0L,aACvC5L,EAvES,WAuEaE,EAAQyO,IACvBzO,EAGTqvB,UACMnoB,KAAKkoB,cAITloB,KAAKyI,QAAQjE,YAAYikB,YAAYzoB,KAAKooB,eAE1C/nB,EAAaQ,GAAGb,KAAKooB,cA7EA,wBA6EgC,KACnDjsB,EAAQ6D,KAAKyI,QAAQuf,iBAGvBhoB,KAAKkoB,aAAc,GAGrBtkB,UACO5D,KAAKkoB,cAIV7nB,EAAaC,IAAIN,KAAK0D,SAzFD,yBA2FrB1D,KAAK0D,SAASJ,SACdtD,KAAKkoB,aAAc,GAGrBG,kBAAkB7sB,GAChBY,EAAuBZ,EAAUwE,KAAKooB,cAAepoB,KAAKyI,QAAQxE,aChGtE,MAMM+C,GAAU,CACdshB,UAAU,EACVphB,UAAU,EACVwe,OAAO,GAGHne,GAAc,CAClB+gB,SAAU,mBACVphB,SAAU,UACVwe,MAAO,WAgCT,MAAMgD,WAAcllB,EAClBC,YAAY9N,EAASmD,GACnBkP,MAAMrS,GAENqK,KAAKyI,QAAUzI,KAAK0I,WAAW5P,GAC/BkH,KAAK2oB,QAAUnzB,EAAeW,QAhBV,gBAgBmC6J,KAAK0D,UAC5D1D,KAAK4oB,UAAY5oB,KAAK6oB,sBACtB7oB,KAAK8oB,UAAW,EAChB9oB,KAAK+oB,sBAAuB,EAC5B/oB,KAAK+M,kBAAmB,EACxB/M,KAAKgpB,WAAa,IAAIhC,GAKNhgB,qBAChB,OAAOA,GAGMrL,kBACb,MAnES,QAwEX0J,OAAOvF,GACL,OAAOE,KAAK8oB,SAAW9oB,KAAK0N,OAAS1N,KAAK2N,KAAK7N,GAGjD6N,KAAK7N,GACCE,KAAK8oB,UAAY9oB,KAAK+M,kBAIR1M,EAAamB,QAAQxB,KAAK0D,SA5D5B,gBA4DkD,CAChE5D,cAAAA,IAGYgC,mBAId9B,KAAK8oB,UAAW,EAEZ9oB,KAAKipB,gBACPjpB,KAAK+M,kBAAmB,GAG1B/M,KAAKgpB,WAAWtb,OAEhB9X,SAASsF,KAAKf,UAAU2Q,IAlEJ,cAoEpB9K,KAAKkpB,gBAELlpB,KAAKmpB,kBACLnpB,KAAKopB,kBAEL/oB,EAAaQ,GAAGb,KAAK0D,SA/EI,yBAcC,4BAiEiDxE,GAASc,KAAK0N,KAAKxO,IAE9FmB,EAAaQ,GAAGb,KAAK2oB,QA9EQ,6BA8E0B,KACrDtoB,EAAaS,IAAId,KAAK0D,SAhFG,2BAgF8BxE,IACjDA,EAAMjC,SAAW+C,KAAK0D,WACxB1D,KAAK+oB,sBAAuB,OAKlC/oB,KAAKqpB,cAAc,IAAMrpB,KAAKspB,aAAaxpB,KAG7C4N,KAAKxO,GAKH,GAJIA,GAAS,CAAC,IAAK,QAAQpH,SAASoH,EAAMjC,OAAO8N,UAC/C7L,EAAMwD,kBAGH1C,KAAK8oB,UAAY9oB,KAAK+M,iBACzB,OAKF,GAFkB1M,EAAamB,QAAQxB,KAAK0D,SA5G5B,iBA8GF5B,iBACZ,OAGF9B,KAAK8oB,UAAW,EAChB,MAAM7kB,EAAajE,KAAKipB,cAEpBhlB,IACFjE,KAAK+M,kBAAmB,GAG1B/M,KAAKmpB,kBACLnpB,KAAKopB,kBAEL/oB,EAAaC,IAAI1K,SAvHE,oBAyHnBoK,KAAK0D,SAASvJ,UAAUmJ,OA/GJ,QAiHpBjD,EAAaC,IAAIN,KAAK0D,SAzHG,0BA0HzBrD,EAAaC,IAAIN,KAAK2oB,QAvHO,8BAyH7B3oB,KAAKgE,eAAe,IAAMhE,KAAKupB,aAAcvpB,KAAK0D,SAAUO,GAG9DL,UACE,CAAC3I,OAAQ+E,KAAK2oB,SACXzvB,QAAQswB,GAAenpB,EAAaC,IAAIkpB,EAxJ5B,cA0JfxpB,KAAK4oB,UAAUhlB,UACfoE,MAAMpE,UAONvD,EAAaC,IAAI1K,SA7IE,oBAgJrB6zB,eACEzpB,KAAKkpB,gBAKPL,sBACE,OAAO,IAAIZ,GAAS,CAClBnuB,UAAW8G,QAAQZ,KAAKyI,QAAQ6f,UAChCrkB,WAAYjE,KAAKipB,gBAIrBvgB,WAAW5P,GAOT,OANAA,EAAS,IACJkO,MACApB,EAAYI,kBAAkBhG,KAAK0D,aAChB,iBAAX5K,EAAsBA,EAAS,IAE5CF,EA1LS,QA0LaE,EAAQyO,IACvBzO,EAGTwwB,aAAaxpB,GACX,MAAMmE,EAAajE,KAAKipB,cAClBS,EAAYl0B,EAAeW,QA3JT,cA2JsC6J,KAAK2oB,SAE9D3oB,KAAK0D,SAAS/M,YAAcqJ,KAAK0D,SAAS/M,WAAWC,WAAaC,KAAKC,cAE1ElB,SAASsF,KAAKutB,YAAYzoB,KAAK0D,UAGjC1D,KAAK0D,SAAS0K,MAAMyW,QAAU,QAC9B7kB,KAAK0D,SAASqC,gBAAgB,eAC9B/F,KAAK0D,SAAS4B,aAAa,cAAc,GACzCtF,KAAK0D,SAAS4B,aAAa,OAAQ,UACnCtF,KAAK0D,SAASgD,UAAY,EAEtBgjB,IACFA,EAAUhjB,UAAY,GAGpBzC,GACFpJ,EAAOmF,KAAK0D,UAGd1D,KAAK0D,SAASvJ,UAAU2Q,IApLJ,QAsLhB9K,KAAKyI,QAAQid,OACf1lB,KAAK2pB,gBAcP3pB,KAAKgE,eAXsB,KACrBhE,KAAKyI,QAAQid,OACf1lB,KAAK0D,SAASgiB,QAGhB1lB,KAAK+M,kBAAmB,EACxB1M,EAAamB,QAAQxB,KAAK0D,SA3MX,iBA2MkC,CAC/C5D,cAAAA,KAIoCE,KAAK2oB,QAAS1kB,GAGxD0lB,gBACEtpB,EAAaC,IAAI1K,SAnNE,oBAoNnByK,EAAaQ,GAAGjL,SApNG,mBAoNsBsJ,IACnCtJ,WAAasJ,EAAMjC,QACnB+C,KAAK0D,WAAaxE,EAAMjC,QACvB+C,KAAK0D,SAAStJ,SAAS8E,EAAMjC,SAChC+C,KAAK0D,SAASgiB,UAKpByD,kBACMnpB,KAAK8oB,SACPzoB,EAAaQ,GAAGb,KAAK0D,SA5NI,2BA4N6BxE,IAChDc,KAAKyI,QAAQvB,UAnPN,WAmPkBhI,EAAMqD,KACjCrD,EAAMwD,iBACN1C,KAAK0N,QACK1N,KAAKyI,QAAQvB,UAtPd,WAsP0BhI,EAAMqD,KACzCvC,KAAK4pB,+BAITvpB,EAAaC,IAAIN,KAAK0D,SArOG,4BAyO7B0lB,kBACMppB,KAAK8oB,SACPzoB,EAAaQ,GAAG5F,OA7OA,kBA6OsB,IAAM+E,KAAKkpB,iBAEjD7oB,EAAaC,IAAIrF,OA/OD,mBAmPpBsuB,aACEvpB,KAAK0D,SAAS0K,MAAMyW,QAAU,OAC9B7kB,KAAK0D,SAAS4B,aAAa,eAAe,GAC1CtF,KAAK0D,SAASqC,gBAAgB,cAC9B/F,KAAK0D,SAASqC,gBAAgB,QAC9B/F,KAAK+M,kBAAmB,EACxB/M,KAAK4oB,UAAUlb,KAAK,KAClB9X,SAASsF,KAAKf,UAAUmJ,OAnPN,cAoPlBtD,KAAK6pB,oBACL7pB,KAAKgpB,WAAWjL,QAChB1d,EAAamB,QAAQxB,KAAK0D,SAjQV,qBAqQpB2lB,cAAc7tB,GACZ6E,EAAaQ,GAAGb,KAAK0D,SAjQI,yBAiQ2BxE,IAC9Cc,KAAK+oB,qBACP/oB,KAAK+oB,sBAAuB,EAI1B7pB,EAAMjC,SAAWiC,EAAM4qB,iBAIG,IAA1B9pB,KAAKyI,QAAQ6f,SACftoB,KAAK0N,OAC8B,WAA1B1N,KAAKyI,QAAQ6f,UACtBtoB,KAAK4pB,gCAIT5pB,KAAK4oB,UAAUjb,KAAKnS,GAGtBytB,cACE,OAAOjpB,KAAK0D,SAASvJ,UAAUC,SA/QX,QAkRtBwvB,6BAEE,GADkBvpB,EAAamB,QAAQxB,KAAK0D,SAhSlB,0BAiSZ5B,iBACZ,OAGF,MAAM3H,UAAEA,EAAFgf,aAAaA,EAAb/K,MAA2BA,GAAUpO,KAAK0D,SAC1CqmB,EAAqB5Q,EAAevjB,SAASC,gBAAgB2e,cAG7DuV,GAA0C,WAApB3b,EAAM8J,WAA2B/d,EAAUC,SA1RjD,kBA8RjB2vB,IACH3b,EAAM8J,UAAY,UAGpB/d,EAAU2Q,IAlSY,gBAmStB9K,KAAKgE,eAAe,KAClB7J,EAAUmJ,OApSU,gBAqSfymB,GACH/pB,KAAKgE,eAAe,KAClBoK,EAAM8J,UAAY,IACjBlY,KAAK2oB,UAET3oB,KAAK2oB,SAER3oB,KAAK0D,SAASgiB,SAOhBwD,gBACE,MAAMa,EAAqB/pB,KAAK0D,SAASyV,aAAevjB,SAASC,gBAAgB2e,aAC3EiT,EAAiBznB,KAAKgpB,WAAW/B,WACjC+C,EAAoBvC,EAAiB,IAErCuC,GAAqBD,IAAuB3uB,KAAa4uB,IAAsBD,GAAsB3uB,OACzG4E,KAAK0D,SAAS0K,MAAM6b,YAAiBxC,EAAF,OAGhCuC,IAAsBD,IAAuB3uB,MAAc4uB,GAAqBD,GAAsB3uB,OACzG4E,KAAK0D,SAAS0K,MAAM8b,aAAkBzC,EAAF,MAIxCoC,oBACE7pB,KAAK0D,SAAS0K,MAAM6b,YAAc,GAClCjqB,KAAK0D,SAAS0K,MAAM8b,aAAe,GAKfhmB,uBAACpL,EAAQgH,GAC7B,OAAOE,KAAK+E,MAAK,WACf,MAAMC,EAAO0jB,GAAMzjB,oBAAoBjF,KAAMlH,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBkM,EAAKlM,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CkM,EAAKlM,GAAQgH,QAWnBO,EAAaQ,GAAGjL,SApWc,0BASD,4BA2VyC,SAAUsJ,GAC9E,MAAMjC,EAAS9E,EAAuB6H,MAElC,CAAC,IAAK,QAAQlI,SAASkI,KAAK+K,UAC9B7L,EAAMwD,iBAGRrC,EAAaS,IAAI7D,EAnXC,gBAmXmBktB,IAC/BA,EAAUroB,kBAKdzB,EAAaS,IAAI7D,EA1XC,kBA0XqB,KACjCnD,EAAUkG,OACZA,KAAK0lB,YAKEgD,GAAMzjB,oBAAoBhI,GAElCoI,OAAOrF,SAUd1E,EAAmBotB,IClanB,MAOM1hB,GAAU,CACdshB,UAAU,EACVphB,UAAU,EACV8P,QAAQ,GAGJzP,GAAc,CAClB+gB,SAAU,UACVphB,SAAU,UACV8P,OAAQ,WAwBV,MAAMoT,WAAkB5mB,EACtBC,YAAY9N,EAASmD,GACnBkP,MAAMrS,GAENqK,KAAKyI,QAAUzI,KAAK0I,WAAW5P,GAC/BkH,KAAK8oB,UAAW,EAChB9oB,KAAK4oB,UAAY5oB,KAAK6oB,sBACtB7oB,KAAKiJ,qBAKQtN,kBACb,MArDS,YAwDOqL,qBAChB,OAAOA,GAKT3B,OAAOvF,GACL,OAAOE,KAAK8oB,SAAW9oB,KAAK0N,OAAS1N,KAAK2N,KAAK7N,GAGjD6N,KAAK7N,GACCE,KAAK8oB,UAISzoB,EAAamB,QAAQxB,KAAK0D,SAjD5B,oBAiDkD,CAAE5D,cAAAA,IAEtDgC,mBAId9B,KAAK8oB,UAAW,EAChB9oB,KAAK0D,SAAS0K,MAAMic,WAAa,UAEjCrqB,KAAK4oB,UAAUjb,OAEV3N,KAAKyI,QAAQuO,UAChB,IAAIgQ,IAAkBtZ,OACtB1N,KAAKsqB,uBAAuBtqB,KAAK0D,WAGnC1D,KAAK0D,SAASqC,gBAAgB,eAC9B/F,KAAK0D,SAAS4B,aAAa,cAAc,GACzCtF,KAAK0D,SAAS4B,aAAa,OAAQ,UACnCtF,KAAK0D,SAASvJ,UAAU2Q,IAvEJ,QA6EpB9K,KAAKgE,eAJoB,KACvB3D,EAAamB,QAAQxB,KAAK0D,SAtEX,qBAsEkC,CAAE5D,cAAAA,KAGfE,KAAK0D,UAAU,IAGvDgK,OACO1N,KAAK8oB,WAIQzoB,EAAamB,QAAQxB,KAAK0D,SAhF5B,qBAkFF5B,mBAIdzB,EAAaC,IAAI1K,SApFE,wBAqFnBoK,KAAK0D,SAAS6mB,OACdvqB,KAAK8oB,UAAW,EAChB9oB,KAAK0D,SAASvJ,UAAUmJ,OA9FJ,QA+FpBtD,KAAK4oB,UAAUlb,OAef1N,KAAKgE,eAboB,KACvBhE,KAAK0D,SAAS4B,aAAa,eAAe,GAC1CtF,KAAK0D,SAASqC,gBAAgB,cAC9B/F,KAAK0D,SAASqC,gBAAgB,QAC9B/F,KAAK0D,SAAS0K,MAAMic,WAAa,SAE5BrqB,KAAKyI,QAAQuO,SAChB,IAAIgQ,IAAkBjJ,QAGxB1d,EAAamB,QAAQxB,KAAK0D,SArGV,wBAwGoB1D,KAAK0D,UAAU,KAGvDE,UACE5D,KAAK4oB,UAAUhlB,UACfoE,MAAMpE,UACNvD,EAAaC,IAAI1K,SA7GE,wBAkHrB8S,WAAW5P,GAOT,OANAA,EAAS,IACJkO,MACApB,EAAYI,kBAAkBhG,KAAK0D,aAChB,iBAAX5K,EAAsBA,EAAS,IAE5CF,EAlJS,YAkJaE,EAAQyO,IACvBzO,EAGT+vB,sBACE,OAAO,IAAIZ,GAAS,CAClBnuB,UAAWkG,KAAKyI,QAAQ6f,SACxBrkB,YAAY,EACZO,YAAaxE,KAAK0D,SAAS/M,WAC3BqxB,cAAe,IAAMhoB,KAAK0N,SAI9B4c,uBAAuB30B,GACrB0K,EAAaC,IAAI1K,SAtIE,wBAuInByK,EAAaQ,GAAGjL,SAvIG,uBAuIsBsJ,IACnCtJ,WAAasJ,EAAMjC,QACrBtH,IAAYuJ,EAAMjC,QACjBtH,EAAQyE,SAAS8E,EAAMjC,SACxBtH,EAAQ+vB,UAGZ/vB,EAAQ+vB,QAGVzc,qBACE5I,EAAaQ,GAAGb,KAAK0D,SAhJI,6BAGC,gCA6IiD,IAAM1D,KAAK0N,QAEtFrN,EAAaQ,GAAGb,KAAK0D,SAjJM,+BAiJ2BxE,IAChDc,KAAKyI,QAAQvB,UA1KJ,WA0KgBhI,EAAMqD,KACjCvC,KAAK0N,SAOWxJ,uBAACpL,GACrB,OAAOkH,KAAK+E,MAAK,WACf,MAAMC,EAAOolB,GAAUnlB,oBAAoBjF,KAAMlH,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBirB,IAAjB/e,EAAKlM,IAAyBA,EAAOf,WAAW,MAAmB,gBAAXe,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CkM,EAAKlM,GAAQkH,WAWnBK,EAAaQ,GAAGjL,SAnLc,8BAKD,gCA8KyC,SAAUsJ,GAC9E,MAAMjC,EAAS9E,EAAuB6H,MAMtC,GAJI,CAAC,IAAK,QAAQlI,SAASkI,KAAK+K,UAC9B7L,EAAMwD,iBAGJxI,EAAW8F,MACb,OAGFK,EAAaS,IAAI7D,EAhMG,sBAgMmB,KAEjCnD,EAAUkG,OACZA,KAAK0lB,UAKT,MAAM8E,EAAeh1B,EAAeW,QA7MhB,mBA8MhBq0B,GAAgBA,IAAiBvtB,GACnCmtB,GAAUjmB,YAAYqmB,GAAc9c,OAGzB0c,GAAUnlB,oBAAoBhI,GACtCoI,OAAOrF,SAGdK,EAAaQ,GAAG5F,OAtOa,6BAsOgB,IAC3CzF,EAAeC,KAvNK,mBAuNeyD,QAAQuxB,GAAML,GAAUnlB,oBAAoBwlB,GAAI9c,SASrFrS,EAAmB8uB,ICxQnB,MAAMM,GAAW,IAAIlsB,IAAI,CACvB,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUImsB,GAAmB,6DAOnBC,GAAmB,qIAEnBC,GAAmB,CAACC,EAAMC,KAC9B,MAAMC,EAAWF,EAAKvb,SAAS9V,cAE/B,GAAIsxB,EAAqBjzB,SAASkzB,GAChC,OAAIN,GAAShrB,IAAIsrB,IACRpqB,QAAQ+pB,GAAiBhxB,KAAKmxB,EAAKG,YAAcL,GAAiBjxB,KAAKmxB,EAAKG,YAMvF,MAAMC,EAASH,EAAqBz0B,OAAO60B,GAAaA,aAAqBzxB,QAG7E,IAAK,IAAIsF,EAAI,EAAGC,EAAMisB,EAAOvyB,OAAQqG,EAAIC,EAAKD,IAC5C,GAAIksB,EAAOlsB,GAAGrF,KAAKqxB,GACjB,OAAO,EAIX,OAAO,GAqCF,SAASI,GAAaC,EAAYC,EAAWC,GAClD,IAAKF,EAAW1yB,OACd,OAAO0yB,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAGpB,MACMG,GADY,IAAIvwB,OAAOwwB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgB3yB,OAAOC,KAAKqyB,GAC5Blb,EAAW,GAAGta,UAAU01B,EAAgBtwB,KAAKjF,iBAAiB,MAEpE,IAAK,IAAI+I,EAAI,EAAGC,EAAMmR,EAASzX,OAAQqG,EAAIC,EAAKD,IAAK,CACnD,MAAMyrB,EAAKra,EAASpR,GACd4sB,EAASnB,EAAGlb,SAAS9V,cAE3B,IAAKkyB,EAAc7zB,SAAS8zB,GAAS,CACnCnB,EAAGnnB,SAEH,SAGF,MAAMuoB,EAAgB,GAAG/1B,UAAU20B,EAAGxkB,YAChC6lB,EAAoB,GAAGh2B,OAAOw1B,EAAU,MAAQ,GAAIA,EAAUM,IAAW,IAE/EC,EAAc3yB,QAAQ4xB,IACfD,GAAiBC,EAAMgB,IAC1BrB,EAAG1kB,gBAAgB+kB,EAAKvb,YAK9B,OAAOic,EAAgBtwB,KAAK6wB,UC1F9B,MAIMC,GAAqB,IAAItyB,OAAQ,wBAA6B,KAC9DuyB,GAAwB,IAAIztB,IAAI,CAAC,WAAY,YAAa,eAE1D+I,GAAc,CAClB2kB,UAAW,UACXC,SAAU,SACVC,MAAO,4BACP5qB,QAAS,SACT6qB,MAAO,kBACPzT,KAAM,UACNljB,SAAU,mBACVyZ,UAAW,oBACX7I,OAAQ,0BACRwH,UAAW,2BACX6O,mBAAoB,QACpB5C,SAAU,mBACVuS,YAAa,oBACbC,SAAU,UACVhB,WAAY,kBACZD,UAAW,SACXxG,aAAc,0BAGV0H,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOvxB,IAAU,OAAS,QAC1BwxB,OAAQ,SACRC,KAAMzxB,IAAU,QAAU,QAGtB4L,GAAU,CACdklB,WAAW,EACXC,SAAU,+GAIV3qB,QAAS,cACT4qB,MAAO,GACPC,MAAO,EACPzT,MAAM,EACNljB,UAAU,EACVyZ,UAAW,MACX7I,OAAQ,CAAC,EAAG,GACZwH,WAAW,EACX6O,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C5C,SAAU,kBACVuS,YAAa,GACbC,UAAU,EACVhB,WAAY,KACZD,UDhC8B,CAE9BwB,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7B7Q,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/B8Q,KAAM,GACN7Q,EAAG,GACH8Q,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJ3uB,EAAG,GACH4uB,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,ICEJ1J,aAAc,MAGVxsB,GAAQ,CACZm2B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAuBf,MAAMC,WAAgB3rB,EACpBC,YAAY9N,EAASmD,GACnB,QAAsB,IAAXysB,GACT,MAAM,IAAI3rB,UAAU,+DAGtBoO,MAAMrS,GAGNqK,KAAKovB,YAAa,EAClBpvB,KAAKqvB,SAAW,EAChBrvB,KAAKsvB,YAAc,GACnBtvB,KAAKuvB,eAAiB,GACtBvvB,KAAKilB,QAAU,KAGfjlB,KAAKyI,QAAUzI,KAAK0I,WAAW5P,GAC/BkH,KAAKwvB,IAAM,KAEXxvB,KAAKyvB,gBAKWzoB,qBAChB,OAAOA,GAGMrL,kBACb,MAxHS,UA2HKrD,mBACd,OAAOA,GAGaiP,yBACpB,OAAOA,GAKTmoB,SACE1vB,KAAKovB,YAAa,EAGpBO,UACE3vB,KAAKovB,YAAa,EAGpBQ,gBACE5vB,KAAKovB,YAAcpvB,KAAKovB,WAG1B/pB,OAAOnG,GACL,GAAKc,KAAKovB,WAIV,GAAIlwB,EAAO,CACT,MAAMonB,EAAUtmB,KAAK6vB,6BAA6B3wB,GAElDonB,EAAQiJ,eAAezI,OAASR,EAAQiJ,eAAezI,MAEnDR,EAAQwJ,uBACVxJ,EAAQyJ,OAAO,KAAMzJ,GAErBA,EAAQ0J,OAAO,KAAM1J,OAElB,CACL,GAAItmB,KAAKiwB,gBAAgB91B,UAAUC,SAxFjB,QA0FhB,YADA4F,KAAKgwB,OAAO,KAAMhwB,MAIpBA,KAAK+vB,OAAO,KAAM/vB,OAItB4D,UACE+G,aAAa3K,KAAKqvB,UAElBhvB,EAAaC,IAAIN,KAAK0D,SAASmB,QAAS,UAAwB,gBAAiB7E,KAAKkwB,mBAElFlwB,KAAKwvB,KACPxvB,KAAKwvB,IAAIlsB,SAGPtD,KAAKilB,SACPjlB,KAAKilB,QAAQjB,UAGfhc,MAAMpE,UAGR+J,OACE,GAAoC,SAAhC3N,KAAK0D,SAAS0K,MAAMyW,QACtB,MAAM,IAAIxgB,MAAM,uCAGlB,IAAMrE,KAAKmwB,kBAAmBnwB,KAAKovB,WACjC,OAGF,MAAMjF,EAAY9pB,EAAamB,QAAQxB,KAAK0D,SAAU1D,KAAKyD,YAAYnL,MAAMq2B,MACvEyB,EAAa71B,EAAeyF,KAAK0D,UACjC2sB,EAA4B,OAAfD,EACjBpwB,KAAK0D,SAASgM,cAAc7Z,gBAAgBuE,SAAS4F,KAAK0D,UAC1D0sB,EAAWh2B,SAAS4F,KAAK0D,UAE3B,GAAIymB,EAAUroB,mBAAqBuuB,EACjC,OAGF,MAAMb,EAAMxvB,KAAKiwB,gBACXK,EAAQj5B,EAAO2I,KAAKyD,YAAY9H,MAEtC6zB,EAAIlqB,aAAa,KAAMgrB,GACvBtwB,KAAK0D,SAAS4B,aAAa,mBAAoBgrB,GAE/CtwB,KAAKuwB,aAEDvwB,KAAKyI,QAAQyjB,WACfsD,EAAIr1B,UAAU2Q,IA/II,QAkJpB,MAAMqE,EAA8C,mBAA3BnP,KAAKyI,QAAQ0G,UACpCnP,KAAKyI,QAAQ0G,UAAUjZ,KAAK8J,KAAMwvB,EAAKxvB,KAAK0D,UAC5C1D,KAAKyI,QAAQ0G,UAETqhB,EAAaxwB,KAAKywB,eAAethB,GACvCnP,KAAK0wB,oBAAoBF,GAEzB,MAAM1iB,UAAEA,GAAc9N,KAAKyI,QAC3B5F,EAAKC,IAAI0sB,EAAKxvB,KAAKyD,YAAYE,SAAU3D,MAEpCA,KAAK0D,SAASgM,cAAc7Z,gBAAgBuE,SAAS4F,KAAKwvB,OAC7D1hB,EAAU2a,YAAY+G,GACtBnvB,EAAamB,QAAQxB,KAAK0D,SAAU1D,KAAKyD,YAAYnL,MAAMu2B,WAGzD7uB,KAAKilB,QACPjlB,KAAKilB,QAAQ5N,SAEbrX,KAAKilB,QAAUM,GAAoBvlB,KAAK0D,SAAU8rB,EAAKxvB,KAAKwlB,iBAAiBgL,IAG/EhB,EAAIr1B,UAAU2Q,IArKM,QAuKpB,MAAMwhB,EAAkD,mBAA7BtsB,KAAKyI,QAAQ6jB,YAA6BtsB,KAAKyI,QAAQ6jB,cAAgBtsB,KAAKyI,QAAQ6jB,YAC3GA,GACFkD,EAAIr1B,UAAU2Q,OAAOwhB,EAAYt0B,MAAM,MAOrC,iBAAkBpC,SAASC,iBAC7B,GAAGC,UAAUF,SAASsF,KAAK7E,UAAU6C,QAAQvD,IAC3C0K,EAAaQ,GAAGlL,EAAS,YAAaiF,KAI1C,MAWMqJ,EAAajE,KAAKwvB,IAAIr1B,UAAUC,SAnMlB,QAoMpB4F,KAAKgE,eAZY,KACf,MAAM2sB,EAAiB3wB,KAAKsvB,YAE5BtvB,KAAKsvB,YAAc,KACnBjvB,EAAamB,QAAQxB,KAAK0D,SAAU1D,KAAKyD,YAAYnL,MAAMs2B,OAvLzC,QAyLd+B,GACF3wB,KAAKgwB,OAAO,KAAMhwB,OAKQA,KAAKwvB,IAAKvrB,GAG1CyJ,OACE,IAAK1N,KAAKilB,QACR,OAGF,MAAMuK,EAAMxvB,KAAKiwB,gBAqBjB,GADkB5vB,EAAamB,QAAQxB,KAAK0D,SAAU1D,KAAKyD,YAAYnL,MAAMm2B,MAC/D3sB,iBACZ,OAGF0tB,EAAIr1B,UAAUmJ,OAnOM,QAuOhB,iBAAkB1N,SAASC,iBAC7B,GAAGC,UAAUF,SAASsF,KAAK7E,UACxB6C,QAAQvD,GAAW0K,EAAaC,IAAI3K,EAAS,YAAaiF,IAG/DoF,KAAKuvB,eAAL,OAAqC,EACrCvvB,KAAKuvB,eAAL,OAAqC,EACrCvvB,KAAKuvB,eAAL,OAAqC,EAErC,MAAMtrB,EAAajE,KAAKwvB,IAAIr1B,UAAUC,SAlPlB,QAmPpB4F,KAAKgE,eAtCY,KACXhE,KAAK8vB,yBA1MU,SA8Mf9vB,KAAKsvB,aACPE,EAAIlsB,SAGNtD,KAAK4wB,iBACL5wB,KAAK0D,SAASqC,gBAAgB,oBAC9B1F,EAAamB,QAAQxB,KAAK0D,SAAU1D,KAAKyD,YAAYnL,MAAMo2B,QAEvD1uB,KAAKilB,UACPjlB,KAAKilB,QAAQjB,UACbhkB,KAAKilB,QAAU,QAuBWjlB,KAAKwvB,IAAKvrB,GACxCjE,KAAKsvB,YAAc,GAGrBjY,SACuB,OAAjBrX,KAAKilB,SACPjlB,KAAKilB,QAAQ5N,SAMjB8Y,gBACE,OAAOvvB,QAAQZ,KAAK6wB,YAGtBZ,gBACE,GAAIjwB,KAAKwvB,IACP,OAAOxvB,KAAKwvB,IAGd,MAAM75B,EAAUC,SAAS2yB,cAAc,OAIvC,OAHA5yB,EAAQo2B,UAAY/rB,KAAKyI,QAAQ0jB,SAEjCnsB,KAAKwvB,IAAM75B,EAAQU,SAAS,GACrB2J,KAAKwvB,IAGde,aACE,MAAMf,EAAMxvB,KAAKiwB,gBACjBjwB,KAAK8wB,kBAAkBt7B,EAAeW,QA1QX,iBA0Q2Cq5B,GAAMxvB,KAAK6wB,YACjFrB,EAAIr1B,UAAUmJ,OAlRM,OAEA,QAmRtBwtB,kBAAkBn7B,EAASo7B,GACzB,GAAgB,OAAZp7B,EAIJ,OAAI4C,EAAUw4B,IACZA,EAAUr4B,EAAWq4B,QAGjB/wB,KAAKyI,QAAQmQ,KACXmY,EAAQp6B,aAAehB,IACzBA,EAAQo2B,UAAY,GACpBp2B,EAAQ8yB,YAAYsI,IAGtBp7B,EAAQq7B,YAAcD,EAAQC,mBAM9BhxB,KAAKyI,QAAQmQ,MACX5Y,KAAKyI,QAAQ8jB,WACfwE,EAAU3F,GAAa2F,EAAS/wB,KAAKyI,QAAQ6iB,UAAWtrB,KAAKyI,QAAQ8iB,aAGvE51B,EAAQo2B,UAAYgF,GAEpBp7B,EAAQq7B,YAAcD,GAI1BF,WACE,IAAIzE,EAAQpsB,KAAK0D,SAAS9L,aAAa,0BAQvC,OANKw0B,IACHA,EAAsC,mBAAvBpsB,KAAKyI,QAAQ2jB,MAC1BpsB,KAAKyI,QAAQ2jB,MAAMl2B,KAAK8J,KAAK0D,UAC7B1D,KAAKyI,QAAQ2jB,OAGVA,EAGT6E,iBAAiBT,GACf,MAAmB,UAAfA,EACK,MAGU,SAAfA,EACK,QAGFA,EAKTX,6BAA6B3wB,EAAOonB,GAClC,MAAM4K,EAAUlxB,KAAKyD,YAAYE,SAQjC,OAPA2iB,EAAUA,GAAWzjB,EAAKJ,IAAIvD,EAAMa,eAAgBmxB,MAGlD5K,EAAU,IAAItmB,KAAKyD,YAAYvE,EAAMa,eAAgBC,KAAKmxB,sBAC1DtuB,EAAKC,IAAI5D,EAAMa,eAAgBmxB,EAAS5K,IAGnCA,EAGTP,aACE,MAAMzf,OAAEA,GAAWtG,KAAKyI,QAExB,MAAsB,iBAAXnC,EACFA,EAAOtO,MAAM,KAAK4qB,IAAIpd,GAAO7I,OAAO8O,SAASjG,EAAK,KAGrC,mBAAXc,EACF0f,GAAc1f,EAAO0f,EAAYhmB,KAAK0D,UAGxC4C,EAGTkf,iBAAiBgL,GACf,MAAMvK,EAAwB,CAC5B9W,UAAWqhB,EACX/O,UAAW,CACT,CACE/lB,KAAM,OACNiV,QAAS,CACPgM,mBAAoB3c,KAAKyI,QAAQkU,qBAGrC,CACEjhB,KAAM,SACNiV,QAAS,CACPrK,OAAQtG,KAAK+lB,eAGjB,CACErqB,KAAM,kBACNiV,QAAS,CACPoJ,SAAU/Z,KAAKyI,QAAQsR,WAG3B,CACEre,KAAM,QACNiV,QAAS,CACPhb,QAAU,IAAGqK,KAAKyD,YAAY9H,eAGlC,CACED,KAAM,WACNsU,SAAS,EACTC,MAAO,aACPpU,GAAImJ,GAAQhF,KAAKoxB,6BAA6BpsB,KAGlDif,cAAejf,IACTA,EAAK2L,QAAQxB,YAAcnK,EAAKmK,WAClCnP,KAAKoxB,6BAA6BpsB,KAKxC,MAAO,IACFihB,KACsC,mBAA9BjmB,KAAKyI,QAAQqc,aAA8B9kB,KAAKyI,QAAQqc,aAAamB,GAAyBjmB,KAAKyI,QAAQqc,cAI1H4L,oBAAoBF,GAClBxwB,KAAKiwB,gBAAgB91B,UAAU2Q,IAAK,cAAkB9K,KAAKixB,iBAAiBT,IAG9EC,eAAethB,GACb,OAAOqd,GAAcrd,EAAUtV,eAGjC41B,gBACmBzvB,KAAKyI,QAAQjH,QAAQxJ,MAAM,KAEnCkB,QAAQsI,IACf,GAAgB,UAAZA,EACFnB,EAAaQ,GAAGb,KAAK0D,SAAU1D,KAAKyD,YAAYnL,MAAMw2B,MAAO9uB,KAAKyI,QAAQ/S,SAAUwJ,GAASc,KAAKqF,OAAOnG,SACpG,GA3ZU,WA2ZNsC,EAA4B,CACrC,MAAM6vB,EA/ZQ,UA+ZE7vB,EACdxB,KAAKyD,YAAYnL,MAAM22B,WACvBjvB,KAAKyD,YAAYnL,MAAMy2B,QACnBuC,EAlaQ,UAkaG9vB,EACfxB,KAAKyD,YAAYnL,MAAM42B,WACvBlvB,KAAKyD,YAAYnL,MAAM02B,SAEzB3uB,EAAaQ,GAAGb,KAAK0D,SAAU2tB,EAASrxB,KAAKyI,QAAQ/S,SAAUwJ,GAASc,KAAK+vB,OAAO7wB,IACpFmB,EAAaQ,GAAGb,KAAK0D,SAAU4tB,EAAUtxB,KAAKyI,QAAQ/S,SAAUwJ,GAASc,KAAKgwB,OAAO9wB,OAIzFc,KAAKkwB,kBAAoB,KACnBlwB,KAAK0D,UACP1D,KAAK0N,QAITrN,EAAaQ,GAAGb,KAAK0D,SAASmB,QAAS,UAAwB,gBAAiB7E,KAAKkwB,mBAEjFlwB,KAAKyI,QAAQ/S,SACfsK,KAAKyI,QAAU,IACVzI,KAAKyI,QACRjH,QAAS,SACT9L,SAAU,IAGZsK,KAAKuxB,YAITA,YACE,MAAMnF,EAAQpsB,KAAK0D,SAAS9L,aAAa,SACnC45B,SAA2BxxB,KAAK0D,SAAS9L,aAAa,2BAExDw0B,GAA+B,WAAtBoF,KACXxxB,KAAK0D,SAAS4B,aAAa,yBAA0B8mB,GAAS,KAC1DA,GAAUpsB,KAAK0D,SAAS9L,aAAa,eAAkBoI,KAAK0D,SAASstB,aACvEhxB,KAAK0D,SAAS4B,aAAa,aAAc8mB,GAG3CpsB,KAAK0D,SAAS4B,aAAa,QAAS,KAIxCyqB,OAAO7wB,EAAOonB,GACZA,EAAUtmB,KAAK6vB,6BAA6B3wB,EAAOonB,GAE/CpnB,IACFonB,EAAQiJ,eACS,YAAfrwB,EAAMqB,KAhdQ,QADA,UAkdZ,GAGF+lB,EAAQ2J,gBAAgB91B,UAAUC,SA5dlB,SAEC,SA0d8CksB,EAAQgJ,YACzEhJ,EAAQgJ,YA3dW,QA+drB3kB,aAAa2b,EAAQ+I,UAErB/I,EAAQgJ,YAjea,OAmehBhJ,EAAQ7d,QAAQ4jB,OAAU/F,EAAQ7d,QAAQ4jB,MAAM1e,KAKrD2Y,EAAQ+I,SAAWlyB,WAAW,KAxeT,SAyefmpB,EAAQgJ,aACVhJ,EAAQ3Y,QAET2Y,EAAQ7d,QAAQ4jB,MAAM1e,MARvB2Y,EAAQ3Y,QAWZqiB,OAAO9wB,EAAOonB,GACZA,EAAUtmB,KAAK6vB,6BAA6B3wB,EAAOonB,GAE/CpnB,IACFonB,EAAQiJ,eACS,aAAfrwB,EAAMqB,KA9eQ,QADA,SAgfZ+lB,EAAQ5iB,SAAStJ,SAAS8E,EAAMY,gBAGlCwmB,EAAQwJ,yBAIZnlB,aAAa2b,EAAQ+I,UAErB/I,EAAQgJ,YA7fY,MA+ffhJ,EAAQ7d,QAAQ4jB,OAAU/F,EAAQ7d,QAAQ4jB,MAAM3e,KAKrD4Y,EAAQ+I,SAAWlyB,WAAW,KApgBV,QAqgBdmpB,EAAQgJ,aACVhJ,EAAQ5Y,QAET4Y,EAAQ7d,QAAQ4jB,MAAM3e,MARvB4Y,EAAQ5Y,QAWZoiB,uBACE,IAAK,MAAMtuB,KAAWxB,KAAKuvB,eACzB,GAAIvvB,KAAKuvB,eAAe/tB,GACtB,OAAO,EAIX,OAAO,EAGTkH,WAAW5P,GACT,MAAM24B,EAAiB7rB,EAAYI,kBAAkBhG,KAAK0D,UAqC1D,OAnCA1K,OAAOC,KAAKw4B,GAAgBv4B,QAAQw4B,IAC9BzF,GAAsBvsB,IAAIgyB,WACrBD,EAAeC,MAI1B54B,EAAS,IACJkH,KAAKyD,YAAYuD,WACjByqB,KACmB,iBAAX34B,GAAuBA,EAASA,EAAS,KAG/CgV,WAAiC,IAArBhV,EAAOgV,UAAsBlY,SAASsF,KAAOxC,EAAWI,EAAOgV,WAEtD,iBAAjBhV,EAAOuzB,QAChBvzB,EAAOuzB,MAAQ,CACb1e,KAAM7U,EAAOuzB,MACb3e,KAAM5U,EAAOuzB,QAIW,iBAAjBvzB,EAAOszB,QAChBtzB,EAAOszB,MAAQtzB,EAAOszB,MAAM7yB,YAGA,iBAAnBT,EAAOi4B,UAChBj4B,EAAOi4B,QAAUj4B,EAAOi4B,QAAQx3B,YAGlCX,EAjoBS,UAioBaE,EAAQkH,KAAKyD,YAAY8D,aAE3CzO,EAAOyzB,WACTzzB,EAAOqzB,SAAWf,GAAatyB,EAAOqzB,SAAUrzB,EAAOwyB,UAAWxyB,EAAOyyB,aAGpEzyB,EAGTq4B,qBACE,MAAMr4B,EAAS,GAEf,GAAIkH,KAAKyI,QACP,IAAK,MAAMlG,KAAOvC,KAAKyI,QACjBzI,KAAKyD,YAAYuD,QAAQzE,KAASvC,KAAKyI,QAAQlG,KACjDzJ,EAAOyJ,GAAOvC,KAAKyI,QAAQlG,IAKjC,OAAOzJ,EAGT83B,iBACE,MAAMpB,EAAMxvB,KAAKiwB,gBACX0B,EAAWnC,EAAI53B,aAAa,SAAS4B,MAAMwyB,IAChC,OAAb2F,GAAqBA,EAASh5B,OAAS,GACzCg5B,EAAS/O,IAAIgP,GAASA,EAAM35B,QACzBiB,QAAQ24B,GAAUrC,EAAIr1B,UAAUmJ,OAAOuuB,IAI9CT,6BAA6BpL,GAC3B,MAAM7V,MAAEA,GAAU6V,EAEb7V,IAILnQ,KAAKwvB,IAAMrf,EAAMC,SAASM,OAC1B1Q,KAAK4wB,iBACL5wB,KAAK0wB,oBAAoB1wB,KAAKywB,eAAetgB,EAAMhB,aAK/BjL,uBAACpL,GACrB,OAAOkH,KAAK+E,MAAK,WACf,MAAMC,EAAOmqB,GAAQlqB,oBAAoBjF,KAAMlH,GAE/C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBkM,EAAKlM,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CkM,EAAKlM,UAabwC,EAAmB6zB,ICvtBnB,MAIMnD,GAAqB,IAAItyB,OAAQ,wBAA6B,KAE9DsN,GAAU,IACXmoB,GAAQnoB,QACXmI,UAAW,QACX7I,OAAQ,CAAC,EAAG,GACZ9E,QAAS,QACTuvB,QAAS,GACT5E,SAAU,+IAON5kB,GAAc,IACf4nB,GAAQ5nB,YACXwpB,QAAS,6BAGLz4B,GAAQ,CACZm2B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAef,MAAM4C,WAAgB3C,GAGFnoB,qBAChB,OAAOA,GAGMrL,kBACb,MAzDS,UA4DKrD,mBACd,OAAOA,GAGaiP,yBACpB,OAAOA,GAKT4oB,gBACE,OAAOnwB,KAAK6wB,YAAc7wB,KAAK+xB,cAGjC9B,gBACE,OAAIjwB,KAAKwvB,MAITxvB,KAAKwvB,IAAMxnB,MAAMioB,gBAEZjwB,KAAK6wB,YACRr7B,EAAeW,QA1CE,kBA0CsB6J,KAAKwvB,KAAKlsB,SAG9CtD,KAAK+xB,eACRv8B,EAAeW,QA7CI,gBA6CsB6J,KAAKwvB,KAAKlsB,UAV5CtD,KAAKwvB,IAgBhBe,aACE,MAAMf,EAAMxvB,KAAKiwB,gBAGjBjwB,KAAK8wB,kBAAkBt7B,EAAeW,QAxDnB,kBAwD2Cq5B,GAAMxvB,KAAK6wB,YACzE,IAAIE,EAAU/wB,KAAK+xB,cACI,mBAAZhB,IACTA,EAAUA,EAAQ76B,KAAK8J,KAAK0D,WAG9B1D,KAAK8wB,kBAAkBt7B,EAAeW,QA7DjB,gBA6D2Cq5B,GAAMuB,GAEtEvB,EAAIr1B,UAAUmJ,OAnEM,OACA,QAuEtBotB,oBAAoBF,GAClBxwB,KAAKiwB,gBAAgB91B,UAAU2Q,IAAK,cAAkB9K,KAAKixB,iBAAiBT,IAG9EuB,cACE,OAAO/xB,KAAK0D,SAAS9L,aAAa,oBAAsBoI,KAAKyI,QAAQsoB,QAGvEH,iBACE,MAAMpB,EAAMxvB,KAAKiwB,gBACX0B,EAAWnC,EAAI53B,aAAa,SAAS4B,MAAMwyB,IAChC,OAAb2F,GAAqBA,EAASh5B,OAAS,GACzCg5B,EAAS/O,IAAIgP,GAASA,EAAM35B,QACzBiB,QAAQ24B,GAAUrC,EAAIr1B,UAAUmJ,OAAOuuB,IAMxB3tB,uBAACpL,GACrB,OAAOkH,KAAK+E,MAAK,WACf,MAAMC,EAAO8sB,GAAQ7sB,oBAAoBjF,KAAMlH,GAE/C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBkM,EAAKlM,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CkM,EAAKlM,UAabwC,EAAmBw2B,IC9InB,MAKM9qB,GAAU,CACdV,OAAQ,GACR0rB,OAAQ,OACR/0B,OAAQ,IAGJsK,GAAc,CAClBjB,OAAQ,SACR0rB,OAAQ,SACR/0B,OAAQ,oBA2BV,MAAMg1B,WAAkBzuB,EACtBC,YAAY9N,EAASmD,GACnBkP,MAAMrS,GACNqK,KAAKkyB,eAA2C,SAA1BlyB,KAAK0D,SAASqH,QAAqB9P,OAAS+E,KAAK0D,SACvE1D,KAAKyI,QAAUzI,KAAK0I,WAAW5P,GAC/BkH,KAAKsN,UAAa,GAAEtN,KAAKyI,QAAQxL,qBAAiC+C,KAAKyI,QAAQxL,4BAAkC+C,KAAKyI,QAAQxL,wBAC9H+C,KAAKmyB,SAAW,GAChBnyB,KAAKoyB,SAAW,GAChBpyB,KAAKqyB,cAAgB,KACrBryB,KAAKsyB,cAAgB,EAErBjyB,EAAaQ,GAAGb,KAAKkyB,eAlCH,sBAkCiC,IAAMlyB,KAAKuyB,YAE9DvyB,KAAKwyB,UACLxyB,KAAKuyB,WAKWvrB,qBAChB,OAAOA,GAGMrL,kBACb,MAjES,YAsEX62B,UACE,MAAMC,EAAazyB,KAAKkyB,iBAAmBlyB,KAAKkyB,eAAej3B,OAvC7C,SACE,WA0Cdy3B,EAAuC,SAAxB1yB,KAAKyI,QAAQupB,OAChCS,EACAzyB,KAAKyI,QAAQupB,OAETW,EA9Cc,aA8CDD,EACjB1yB,KAAK4yB,gBACL,EAEF5yB,KAAKmyB,SAAW,GAChBnyB,KAAKoyB,SAAW,GAChBpyB,KAAKsyB,cAAgBtyB,KAAK6yB,mBAEVr9B,EAAeC,KAAKuK,KAAKsN,WAEjCsV,IAAIjtB,IACV,MAAMm9B,EAAiB56B,EAAuBvC,GACxCsH,EAAS61B,EAAiBt9B,EAAeW,QAAQ28B,GAAkB,KAEzE,GAAI71B,EAAQ,CACV,MAAM81B,EAAY91B,EAAOuJ,wBACzB,GAAIusB,EAAU3hB,OAAS2hB,EAAU1hB,OAC/B,MAAO,CACLzL,EAAY8sB,GAAcz1B,GAAQwJ,IAAMksB,EACxCG,GAKN,OAAO,OAENx8B,OAAO08B,GAAQA,GACfhX,KAAK,CAACC,EAAGC,IAAMD,EAAE,GAAKC,EAAE,IACxBhjB,QAAQ85B,IACPhzB,KAAKmyB,SAASp7B,KAAKi8B,EAAK,IACxBhzB,KAAKoyB,SAASr7B,KAAKi8B,EAAK,MAI9BpvB,UACEvD,EAAaC,IAAIN,KAAKkyB,eAhHP,iBAiHflqB,MAAMpE,UAKR8E,WAAW5P,GAOT,GAA6B,iBAN7BA,EAAS,IACJkO,MACApB,EAAYI,kBAAkBhG,KAAK0D,aAChB,iBAAX5K,GAAuBA,EAASA,EAAS,KAGpCmE,QAAuB1E,EAAUO,EAAOmE,QAAS,CACjE,IAAIgQ,GAAEA,GAAOnU,EAAOmE,OACfgQ,IACHA,EAAK5V,EAlIA,aAmILyB,EAAOmE,OAAOgQ,GAAKA,GAGrBnU,EAAOmE,OAAU,IAAGgQ,EAKtB,OAFArU,EAzIS,YAyIaE,EAAQyO,IAEvBzO,EAGT85B,gBACE,OAAO5yB,KAAKkyB,iBAAmBj3B,OAC7B+E,KAAKkyB,eAAeta,YACpB5X,KAAKkyB,eAAexrB,UAGxBmsB,mBACE,OAAO7yB,KAAKkyB,eAAe/Y,cAAgB5hB,KAAKqG,IAC9ChI,SAASsF,KAAKie,aACdvjB,SAASC,gBAAgBsjB,cAI7B8Z,mBACE,OAAOjzB,KAAKkyB,iBAAmBj3B,OAC7BA,OAAOi4B,YACPlzB,KAAKkyB,eAAe1rB,wBAAwB6K,OAGhDkhB,WACE,MAAM7rB,EAAY1G,KAAK4yB,gBAAkB5yB,KAAKyI,QAAQnC,OAChD6S,EAAenZ,KAAK6yB,mBACpBM,EAAYnzB,KAAKyI,QAAQnC,OAAS6S,EAAenZ,KAAKizB,mBAM5D,GAJIjzB,KAAKsyB,gBAAkBnZ,GACzBnZ,KAAKwyB,UAGH9rB,GAAaysB,EAAjB,CACE,MAAMl2B,EAAS+C,KAAKoyB,SAASpyB,KAAKoyB,SAASz5B,OAAS,GAEhDqH,KAAKqyB,gBAAkBp1B,GACzB+C,KAAKozB,UAAUn2B,OAJnB,CAUA,GAAI+C,KAAKqyB,eAAiB3rB,EAAY1G,KAAKmyB,SAAS,IAAMnyB,KAAKmyB,SAAS,GAAK,EAG3E,OAFAnyB,KAAKqyB,cAAgB,UACrBryB,KAAKqzB,SAIP,IAAK,IAAIr0B,EAAIgB,KAAKmyB,SAASx5B,OAAQqG,KACVgB,KAAKqyB,gBAAkBryB,KAAKoyB,SAASpzB,IACxD0H,GAAa1G,KAAKmyB,SAASnzB,UACM,IAAzBgB,KAAKmyB,SAASnzB,EAAI,IAAsB0H,EAAY1G,KAAKmyB,SAASnzB,EAAI,KAGhFgB,KAAKozB,UAAUpzB,KAAKoyB,SAASpzB,KAKnCo0B,UAAUn2B,GACR+C,KAAKqyB,cAAgBp1B,EAErB+C,KAAKqzB,SAEL,MAAMC,EAAUtzB,KAAKsN,UAAUtV,MAAM,KAClC4qB,IAAIltB,GAAa,GAAEA,qBAA4BuH,OAAYvH,WAAkBuH,OAE1Es2B,EAAO/9B,EAAeW,QAAQm9B,EAAQE,KAAK,MAE7CD,EAAKp5B,UAAUC,SA1LU,kBA2L3B5E,EAAeW,QAlLY,mBAkLsBo9B,EAAK1uB,QAnLlC,cAoLjB1K,UAAU2Q,IA3LO,UA6LpByoB,EAAKp5B,UAAU2Q,IA7LK,YAgMpByoB,EAAKp5B,UAAU2Q,IAhMK,UAkMpBtV,EAAeiB,QAAQ88B,EA/LG,qBAgMvBr6B,QAAQu6B,IAGPj+B,EAAewB,KAAKy8B,EAAY,+BAC7Bv6B,QAAQ85B,GAAQA,EAAK74B,UAAU2Q,IAvMlB,WA0MhBtV,EAAewB,KAAKy8B,EArMH,aAsMdv6B,QAAQw6B,IACPl+B,EAAea,SAASq9B,EAxMX,aAyMVx6B,QAAQ85B,GAAQA,EAAK74B,UAAU2Q,IA7MtB,gBAkNtBzK,EAAamB,QAAQxB,KAAKkyB,eAvNN,wBAuNsC,CACxDpyB,cAAe7C,IAInBo2B,SACE79B,EAAeC,KAAKuK,KAAKsN,WACtBhX,OAAOmZ,GAAQA,EAAKtV,UAAUC,SAzNX,WA0NnBlB,QAAQuW,GAAQA,EAAKtV,UAAUmJ,OA1NZ,WA+NFY,uBAACpL,GACrB,OAAOkH,KAAK+E,MAAK,WACf,MAAMC,EAAOitB,GAAUhtB,oBAAoBjF,KAAMlH,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBkM,EAAKlM,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CkM,EAAKlM,UAWXuH,EAAaQ,GAAG5F,OAzPa,6BAyPgB,KAC3CzF,EAAeC,KArPS,0BAsPrByD,QAAQy6B,GAAO,IAAI1B,GAAU0B,MAUlCr4B,EAAmB22B,IC5PnB,MAAM2B,WAAYpwB,EAGD7H,kBACb,MAlCS,MAuCXgS,OACE,GAAK3N,KAAK0D,SAAS/M,YACjBqJ,KAAK0D,SAAS/M,WAAWC,WAAaC,KAAKC,cAC3CkJ,KAAK0D,SAASvJ,UAAUC,SA9BJ,UA+BpB,OAGF,IAAInD,EACJ,MAAMgG,EAAS9E,EAAuB6H,KAAK0D,UACrCmwB,EAAc7zB,KAAK0D,SAASmB,QA/BN,qBAiC5B,GAAIgvB,EAAa,CACf,MAAMC,EAAwC,OAAzBD,EAAYtkB,UAA8C,OAAzBskB,EAAYtkB,SAhC7C,wBADH,UAkClBtY,EAAWzB,EAAeC,KAAKq+B,EAAcD,GAC7C58B,EAAWA,EAASA,EAAS0B,OAAS,GAGxC,MAAMo7B,EAAY98B,EAChBoJ,EAAamB,QAAQvK,EApDP,cAoD6B,CACzC6I,cAAeE,KAAK0D,WAEtB,KAMF,GAJkBrD,EAAamB,QAAQxB,KAAK0D,SAvD5B,cAuDkD,CAChE5D,cAAe7I,IAGH6K,kBAAmC,OAAdiyB,GAAsBA,EAAUjyB,iBACjE,OAGF9B,KAAKozB,UAAUpzB,KAAK0D,SAAUmwB,GAE9B,MAAMG,EAAW,KACf3zB,EAAamB,QAAQvK,EAnEL,gBAmE6B,CAC3C6I,cAAeE,KAAK0D,WAEtBrD,EAAamB,QAAQxB,KAAK0D,SApEX,eAoEkC,CAC/C5D,cAAe7I,KAIfgG,EACF+C,KAAKozB,UAAUn2B,EAAQA,EAAOtG,WAAYq9B,GAE1CA,IAMJZ,UAAUz9B,EAASmY,EAAWtS,GAC5B,MAIMy4B,IAJiBnmB,GAAqC,OAAvBA,EAAUyB,UAA4C,OAAvBzB,EAAUyB,SAE5E/Z,EAAea,SAASyX,EA3EN,WA0ElBtY,EAAeC,KAzEM,wBAyEmBqY,IAGZ,GACxBU,EAAkBhT,GAAay4B,GAAUA,EAAO95B,UAAUC,SAnF5C,QAqFd45B,EAAW,IAAMh0B,KAAKk0B,oBAAoBv+B,EAASs+B,EAAQz4B,GAE7Dy4B,GAAUzlB,GACZylB,EAAO95B,UAAUmJ,OAvFC,QAwFlBtD,KAAKgE,eAAegwB,EAAUr+B,GAAS,IAEvCq+B,IAIJE,oBAAoBv+B,EAASs+B,EAAQz4B,GACnC,GAAIy4B,EAAQ,CACVA,EAAO95B,UAAUmJ,OAlGG,UAoGpB,MAAM6wB,EAAgB3+B,EAAeW,QA1FJ,kCA0F4C89B,EAAOt9B,YAEhFw9B,GACFA,EAAch6B,UAAUmJ,OAvGN,UA0GgB,QAAhC2wB,EAAOr8B,aAAa,SACtBq8B,EAAO3uB,aAAa,iBAAiB,GAIzC3P,EAAQwE,UAAU2Q,IA/GI,UAgHe,QAAjCnV,EAAQiC,aAAa,SACvBjC,EAAQ2P,aAAa,iBAAiB,GAGxCzK,EAAOlF,GAEHA,EAAQwE,UAAUC,SArHF,SAsHlBzE,EAAQwE,UAAU2Q,IArHA,QAwHpB,IAAI+B,EAASlX,EAAQgB,WAKrB,GAJIkW,GAA8B,OAApBA,EAAO0C,WACnB1C,EAASA,EAAOlW,YAGdkW,GAAUA,EAAO1S,UAAUC,SAhIF,iBAgIsC,CACjE,MAAMg6B,EAAkBz+B,EAAQkP,QA5HZ,aA8HhBuvB,GACF5+B,EAAeC,KA1HU,mBA0HqB2+B,GAC3Cl7B,QAAQm7B,GAAYA,EAASl6B,UAAU2Q,IApIxB,WAuIpBnV,EAAQ2P,aAAa,iBAAiB,GAGpC9J,GACFA,IAMkB0I,uBAACpL,GACrB,OAAOkH,KAAK+E,MAAK,WACf,MAAMC,EAAO4uB,GAAI3uB,oBAAoBjF,MAErC,GAAsB,iBAAXlH,EAAqB,CAC9B,QAA4B,IAAjBkM,EAAKlM,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CkM,EAAKlM,UAYbuH,EAAaQ,GAAGjL,SAzKc,wBAWD,4EA8JyC,SAAUsJ,GAC1E,CAAC,IAAK,QAAQpH,SAASkI,KAAK+K,UAC9B7L,EAAMwD,iBAGJxI,EAAW8F,OAIF4zB,GAAI3uB,oBAAoBjF,MAChC2N,UAUPrS,EAAmBs4B,ICvMnB,MAmBMrsB,GAAc,CAClB2kB,UAAW,UACXoI,SAAU,UACVjI,MAAO,UAGHrlB,GAAU,CACdklB,WAAW,EACXoI,UAAU,EACVjI,MAAO,KAWT,MAAMkI,WAAc/wB,EAClBC,YAAY9N,EAASmD,GACnBkP,MAAMrS,GAENqK,KAAKyI,QAAUzI,KAAK0I,WAAW5P,GAC/BkH,KAAKqvB,SAAW,KAChBrvB,KAAKw0B,sBAAuB,EAC5Bx0B,KAAKy0B,yBAA0B,EAC/Bz0B,KAAKyvB,gBAKeloB,yBACpB,OAAOA,GAGSP,qBAChB,OAAOA,GAGMrL,kBACb,MA7DS,QAkEXgS,OACoBtN,EAAamB,QAAQxB,KAAK0D,SAxD5B,iBA0DF5B,mBAId9B,KAAK00B,gBAED10B,KAAKyI,QAAQyjB,WACflsB,KAAK0D,SAASvJ,UAAU2Q,IA9DN,QA0EpB9K,KAAK0D,SAASvJ,UAAUmJ,OAzEJ,QA0EpBzI,EAAOmF,KAAK0D,UACZ1D,KAAK0D,SAASvJ,UAAU2Q,IAzED,WA2EvB9K,KAAKgE,eAbY,KACfhE,KAAK0D,SAASvJ,UAAUmJ,OA/DH,WAgErBtD,KAAK0D,SAASvJ,UAAU2Q,IAjEN,QAmElBzK,EAAamB,QAAQxB,KAAK0D,SAvEX,kBAyEf1D,KAAK20B,sBAOuB30B,KAAK0D,SAAU1D,KAAKyI,QAAQyjB,YAG5Dxe,OACO1N,KAAK0D,SAASvJ,UAAUC,SAhFT,UAoFFiG,EAAamB,QAAQxB,KAAK0D,SA3F5B,iBA6FF5B,mBASd9B,KAAK0D,SAASvJ,UAAUmJ,OA/FJ,QAgGpBtD,KAAKgE,eANY,KACfhE,KAAK0D,SAASvJ,UAAU2Q,IA5FN,QA6FlBzK,EAAamB,QAAQxB,KAAK0D,SAlGV,oBAsGY1D,KAAK0D,SAAU1D,KAAKyI,QAAQyjB,aAG5DtoB,UACE5D,KAAK00B,gBAED10B,KAAK0D,SAASvJ,UAAUC,SAtGR,SAuGlB4F,KAAK0D,SAASvJ,UAAUmJ,OAvGN,QA0GpB0E,MAAMpE,UAKR8E,WAAW5P,GAST,OARAA,EAAS,IACJkO,MACApB,EAAYI,kBAAkBhG,KAAK0D,aAChB,iBAAX5K,GAAuBA,EAASA,EAAS,IAGtDF,EAtIS,QAsIaE,EAAQkH,KAAKyD,YAAY8D,aAExCzO,EAGT67B,qBACO30B,KAAKyI,QAAQ6rB,WAIdt0B,KAAKw0B,sBAAwBx0B,KAAKy0B,0BAItCz0B,KAAKqvB,SAAWlyB,WAAW,KACzB6C,KAAK0N,QACJ1N,KAAKyI,QAAQ4jB,SAGlBuI,eAAe11B,EAAO21B,GACpB,OAAQ31B,EAAMqB,MACZ,IAAK,YACL,IAAK,WACHP,KAAKw0B,qBAAuBK,EAC5B,MACF,IAAK,UACL,IAAK,WACH70B,KAAKy0B,wBAA0BI,EAMnC,GAAIA,EAEF,YADA70B,KAAK00B,gBAIP,MAAM3oB,EAAc7M,EAAMY,cACtBE,KAAK0D,WAAaqI,GAAe/L,KAAK0D,SAAStJ,SAAS2R,IAI5D/L,KAAK20B,qBAGPlF,gBACEpvB,EAAaQ,GAAGb,KAAK0D,SAjLI,yBA2BC,4BAsJiD,IAAM1D,KAAK0N,QACtFrN,EAAaQ,GAAGb,KAAK0D,SAjLA,qBAiL2BxE,GAASc,KAAK40B,eAAe11B,GAAO,IACpFmB,EAAaQ,GAAGb,KAAK0D,SAjLD,oBAiL2BxE,GAASc,KAAK40B,eAAe11B,GAAO,IACnFmB,EAAaQ,GAAGb,KAAK0D,SAjLF,mBAiL2BxE,GAASc,KAAK40B,eAAe11B,GAAO,IAClFmB,EAAaQ,GAAGb,KAAK0D,SAjLD,oBAiL2BxE,GAASc,KAAK40B,eAAe11B,GAAO,IAGrFw1B,gBACE/pB,aAAa3K,KAAKqvB,UAClBrvB,KAAKqvB,SAAW,KAKInrB,uBAACpL,GACrB,OAAOkH,KAAK+E,MAAK,WACf,MAAMC,EAAOuvB,GAAMtvB,oBAAoBjF,KAAMlH,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBkM,EAAKlM,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CkM,EAAKlM,GAAQkH,kBAarB1E,EAAmBi5B,IC3NJ,CACbjwB,MAAAA,EACAc,OAAAA,EACA2C,SAAAA,EACA+E,SAAAA,GACAkY,SAAAA,GACA0D,MAAAA,GACA0B,UAAAA,GACA0H,QAAAA,GACAG,UAAAA,GACA2B,IAAAA,GACAW,MAAAA,GACApF,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst customEventsRegex = /^(mouseenter|mouseleave)/i\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, selector, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  let typeEvent = getTypeEvent(originalTypeEvent)\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (customEventsRegex.test(originalTypeEvent)) {\n    const wrapFn = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFn) {\n      delegationFn = wrapFn(delegationFn)\n    } else {\n      handler = wrapFn(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport {\n  executeAfterTransition,\n  getElement\n} from './util/index'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.2'\n\nclass BaseComponent {\n  constructor(element) {\n    element = getElement(element)\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    Object.getOwnPropertyNames(this).forEach(propertyName => {\n      this[propertyName] = null\n    })\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(element, this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASS_NAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(element), element, isAnimated)\n  }\n\n  _destroyElement(element) {\n    element.remove()\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toL<PERSON>er<PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  getNextActiveElement,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(direction)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    return getNextActiveElement(this._items, activeElement, isNext, this._config.wrap)\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    if (this._isSliding) {\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    const triggerSlidEvent = () => {\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const completeCallBack = () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(triggerSlidEvent, 0)\n      }\n\n      this._queueCallback(completeCallBack, activeElement, true)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      triggerSlidEvent()\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    const data = Carousel.getOrCreateInstance(element, config)\n\n    let { _config } = data\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Carousel.getInstance(target).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Carousel.getInstance(carousels[i]))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  getElementFromSelector,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${this._element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${this._element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Collapse.getInstance(tempActiveData) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.set(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    parent = getElement(parent)\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Collapse.getInstance(element)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Collapse.getInstance(element)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export default function getBoundingClientRect(element) {\n  var rect = element.getBoundingClientRect();\n  return {\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    x: rect.left,\n    y: rect.top\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;\n  var isIE = navigator.userAgent.indexOf('Trident') !== -1;\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport default function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport within from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "import { top, left, right, bottom } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(round(x * dpr) / dpr) || 0,\n    y: round(round(y * dpr) / dpr) || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets;\n\n  var _ref3 = roundOffsets === true ? roundOffsetsByDPR(offsets) : typeof roundOffsets === 'function' ? roundOffsets(offsets) : offsets,\n      _ref3$x = _ref3.x,\n      x = _ref3$x === void 0 ? 0 : _ref3$x,\n      _ref3$y = _ref3.y,\n      y = _ref3$y === void 0 ? 0 : _ref3$y;\n\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top) {\n      sideY = bottom; // $FlowFixMe[prop-missing]\n\n      y -= offsetParent[heightProp] - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left) {\n      sideX = right; // $FlowFixMe[prop-missing]\n\n      x -= offsetParent[widthProp] - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) < 2 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref4) {\n  var state = _ref4.state,\n      options = _ref4.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element) {\n  var rect = getBoundingClientRect(element);\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element)) : isHTMLElement(clippingParent) ? getInnerBoundingClientRect(clippingParent) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nexport default function getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var referenceElement = state.elements.reference;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary);\n  var referenceClientRect = getBoundingClientRect(referenceElement);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\";\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport within from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { max as mathMax, min as mathMin } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis || checkAltAxis) {\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = popperOffsets[mainAxis] + overflow[mainSide];\n    var max = popperOffsets[mainAxis] - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - tetherOffsetValue : minLen - arrowLen - arrowPaddingMin - tetherOffsetValue;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + tetherOffsetValue : maxLen + arrowLen + arrowPaddingMax + tetherOffsetValue;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = state.modifiersData.offset ? state.modifiersData.offset[state.placement][mainAxis] : 0;\n    var tetherMin = popperOffsets[mainAxis] + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = popperOffsets[mainAxis] + maxOffset - offsetModifierValue;\n\n    if (checkMainAxis) {\n      var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n      popperOffsets[mainAxis] = preventedOffset;\n      data[mainAxis] = preventedOffset - offset;\n    }\n\n    if (checkAltAxis) {\n      var _mainSide = mainAxis === 'x' ? top : left;\n\n      var _altSide = mainAxis === 'x' ? bottom : right;\n\n      var _offset = popperOffsets[altAxis];\n\n      var _min = _offset + overflow[_mainSide];\n\n      var _max = _offset - overflow[_altSide];\n\n      var _preventedOffset = within(tether ? mathMin(_min, tetherMin) : _min, _offset, tether ? mathMax(_max, tetherMax) : _max);\n\n      popperOffsets[altAxis] = _preventedOffset;\n      data[altAxis] = _preventedOffset - _offset;\n    }\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\"; // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement);\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(options) {\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  isDisabled,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  getNextActiveElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (isDisabled(this._element)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    if (isActive) {\n      this.hide()\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = getElement(this._config.reference)\n      } else if (typeof this._config.reference === 'object') {\n        referenceElement = this._config.reference\n      }\n\n      const popperConfig = this._getPopperConfig()\n      const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n      if (isDisplayStatic) {\n        Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n      }\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', noop))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      this.toggle()\n    })\n  }\n\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.off(elem, 'mouseover', noop))\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    const data = Dropdown.getOrCreateInstance(element, config)\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Dropdown.getInstance(toggles[i])\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      if (!context._element.classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      const relatedTarget = {\n        relatedTarget: context._element\n      }\n\n      if (event) {\n        const composedPath = event.composedPath()\n        const isMenuTarget = composedPath.includes(context._menu)\n        if (\n          composedPath.includes(context._element) ||\n          (context._config.autoClose === 'inside' && !isMenuTarget) ||\n          (context._config.autoClose === 'outside' && isMenuTarget)\n        ) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n        if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n          continue\n        }\n\n        if (event.type === 'click') {\n          relatedTarget.clickEvent = event\n        }\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (!isActive && event.key === ESCAPE_KEY) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const getToggleButton = () => this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n\n    if (event.key === ESCAPE_KEY) {\n      getToggleButton().focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY) {\n      if (!isActive) {\n        getToggleButton().click()\n      }\n\n      Dropdown.getInstance(getToggleButton())._selectMenuItem(event)\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.dropdownInterface(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, 'paddingRight', calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  }\n\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProp)\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  }\n\n  _saveInitialAttribute(element, styleProp) {\n    const actualValue = element.style[styleProp]\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined') {\n        element.style.removeProperty(styleProp)\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n    } else {\n      SelectorEngine.find(selector, this._element).forEach(callBack)\n    }\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = CLASS_NAME_BACKDROP\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.appendChild(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._isShown = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event && ['A', 'AREA'].includes(event.target.tagName)) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    this._queueCallback(() => this._hideModal(), this._element, isAnimated)\n  }\n\n  dispose() {\n    [window, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    this._backdrop.dispose()\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, isAnimated)\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _showBackdrop(callback) {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (this._ignoreBackdropClick) {\n        this._ignoreBackdropClick = false\n        return\n      }\n\n      if (event.target !== event.currentTarget) {\n        return\n      }\n\n      if (this._config.backdrop === true) {\n        this.hide()\n      } else if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n      }\n    })\n\n    this._backdrop.show(callback)\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const { classList, scrollHeight, style } = this._element\n    const isModalOverflowing = scrollHeight > document.documentElement.clientHeight\n\n    // return if the following background transition hasn't yet completed\n    if ((!isModalOverflowing && style.overflowY === 'hidden') || classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      style.overflowY = 'hidden'\n    }\n\n    classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        this._queueCallback(() => {\n          style.overflowY = ''\n        }, this._dialog)\n      }\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if ((!isBodyOverflowing && isModalOverflowing && !isRTL()) || (isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${scrollbarWidth}px`\n    }\n\n    if ((isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_SHOW = 'show'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"offcanvas\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n      this._enforceFocusOnElement(this._element)\n    }\n\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    super.dispose()\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: this._config.backdrop,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: () => this.hide()\n    })\n  }\n\n  _enforceFocusOnElement(element) {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n        element !== event.target &&\n        !element.contains(event.target)) {\n        element.focus()\n      }\n    })\n    element.focus()\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    Offcanvas.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () =>\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => Offcanvas.getOrCreateInstance(el).show())\n)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  findShadowRoot,\n  getElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this._config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.remove()\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    this.setContent()\n\n    if (this._config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const { container } = this._config\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.appendChild(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = typeof this._config.customClass === 'function' ? this._config.customClass() : this._config.customClass\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop)\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW) {\n        tip.remove()\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this._config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (isElement(content)) {\n      content = getElement(content)\n\n      // content is a DOM node or a jQuery\n      if (this._config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this._config.html) {\n      if (this._config.sanitize) {\n        content = sanitizeHtml(content, this._config.allowList, this._config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this._config.title === 'function' ?\n        this._config.title.call(this._element) :\n        this._config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.get(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n      Data.set(event.delegateTarget, dataKey, context)\n    }\n\n    return context\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this._config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context._config.delay || !context._config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context._config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context._config.delay || !context._config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context._config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this._config) {\n      for (const key in this._config) {\n        if (this.constructor.Default[key] !== this._config[key]) {\n          config[key] = this._config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    this.tip = super.getTipElement()\n\n    if (!this.getTitle()) {\n      SelectorEngine.findOne(SELECTOR_TITLE, this.tip).remove()\n    }\n\n    if (!this._getContent()) {\n      SelectorEngine.findOne(SELECTOR_CONTENT, this.tip).remove()\n    }\n\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this._config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  reflow\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE))) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      active.classList.remove(CLASS_NAME_SHOW)\n      this._queueCallback(complete, element, true)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    let parent = element.parentNode\n    if (parent && parent.nodeName === 'LI') {\n      parent = parent.parentNode\n    }\n\n    if (parent && parent.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE, dropdownElement)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  const data = Tab.getOrCreateInstance(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}
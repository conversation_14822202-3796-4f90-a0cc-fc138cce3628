{"root": true, "extends": ["plugin:import/errors", "plugin:import/warnings", "plugin:unicorn/recommended", "xo", "xo/browser"], "rules": {"capitalized-comments": "off", "indent": ["error", 2, {"MemberExpression": "off", "SwitchCase": 1}], "max-params": ["warn", 5], "multiline-ternary": ["error", "always-multiline"], "new-cap": ["error", {"properties": false}], "no-console": "error", "object-curly-spacing": ["error", "always"], "semi": ["error", "never"], "unicorn/consistent-function-scoping": "off", "unicorn/explicit-length-check": "off", "unicorn/no-array-callback-reference": "off", "unicorn/no-array-for-each": "off", "unicorn/no-for-loop": "off", "unicorn/no-null": "off", "unicorn/no-unused-properties": "error", "unicorn/no-useless-undefined": "off", "unicorn/prefer-dom-node-append": "off", "unicorn/prefer-dom-node-dataset": "off", "unicorn/prefer-dom-node-remove": "off", "unicorn/prefer-module": "off", "unicorn/prefer-query-selector": "off", "unicorn/prefer-spread": "off", "unicorn/prevent-abbreviations": "off"}}
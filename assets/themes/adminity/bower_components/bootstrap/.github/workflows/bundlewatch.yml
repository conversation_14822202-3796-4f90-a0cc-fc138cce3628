name: Bundlewatch

on:
  push:
  pull_request:

env:
  FORCE_COLOR: 2
  NODE: 14

jobs:
  bundlewatch:
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "${{ env.NODE }}"

      - name: Set up npm cache
        uses: actions/cache@v2
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ env.NODE }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-${{ env.NODE }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}

      - name: Install npm dependencies
        run: npm ci

      - name: Run dist
        run: npm run dist

      - name: Run bundlewatch
        run: npm run bundlewatch
        env:
          BUNDLEWATCH_GITHUB_TOKEN: "${{ secrets.BUNDLEWATCH_GITHUB_TOKEN }}"
          CI_BRANCH_BASE: main

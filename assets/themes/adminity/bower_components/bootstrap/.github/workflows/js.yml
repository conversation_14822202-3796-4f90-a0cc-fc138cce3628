name: JS Tests

on:
  push:
    branches-ignore:
      - "dependabot/**"
  pull_request:

env:
  FORCE_COLOR: 2

jobs:
  run:
    name: Node ${{ matrix.node }}
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        node: [12, 14, 16]

    steps:
      - name: Clone repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node }}

      - name: Set up npm cache
        uses: actions/cache@v2
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ matrix.node }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-${{ matrix.node }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}

      - name: Install npm dependencies
        run: npm ci

      - name: Run dist
        run: npm run js

      - name: Run JS tests
        run: npm run js-test

      - name: Run Coveralls
        uses: coverallsapp/github-action@v1.1.2
        if: matrix.node == 14
        with:
          github-token: "${{ secrets.GITHUB_TOKEN }}"
          path-to-lcov: "./js/coverage/lcov.info"

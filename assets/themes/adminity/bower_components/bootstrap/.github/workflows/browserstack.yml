name: BrowserStack

on:
  push:

env:
  FORCE_COLOR: 2
  NODE: 14

jobs:
  browserstack:
    runs-on: ubuntu-latest
    if: github.repository == 'twbs/bootstrap' && (!contains(github.event.commits[0].message, '[ci skip]') && !contains(github.event.commits[0].message, '[skip ci]'))
    timeout-minutes: 30

    steps:
      - name: Clone repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "${{ env.NODE }}"

      - name: Set up npm cache
        uses: actions/cache@v2
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ env.NODE }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-${{ env.NODE }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}

      - name: Install npm dependencies
        run: npm ci

      - name: Run dist
        run: npm run dist

      - name: Run BrowserStack tests
        run: npm run js-test-cloud
        env:
          BROWSER_STACK_ACCESS_KEY: "${{ secrets.BROWSER_STACK_ACCESS_KEY }}"
          BROWSER_STACK_USERNAME: "${{ secrets.BROWSER_STACK_USERNAME }}"

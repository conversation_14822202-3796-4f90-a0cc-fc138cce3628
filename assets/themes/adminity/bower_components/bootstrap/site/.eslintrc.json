{"root": true, "parserOptions": {"ecmaVersion": 5, "sourceType": "script"}, "extends": ["plugin:unicorn/recommended", "xo", "xo/browser"], "rules": {"capitalized-comments": "off", "indent": ["error", 2, {"MemberExpression": "off", "SwitchCase": 1}], "multiline-ternary": ["error", "always-multiline"], "no-new": "off", "no-var": "off", "object-curly-spacing": ["error", "always"], "object-shorthand": "off", "prefer-arrow-callback": "off", "prefer-destructuring": "off", "semi": ["error", "never"], "strict": "error", "unicorn/no-array-for-each": "off", "unicorn/no-for-loop": "off", "unicorn/no-null": "off", "unicorn/prefer-dom-node-append": "off", "unicorn/prefer-dom-node-dataset": "off", "unicorn/prefer-module": "off", "unicorn/prefer-query-selector": "off", "unicorn/prevent-abbreviations": "off"}}
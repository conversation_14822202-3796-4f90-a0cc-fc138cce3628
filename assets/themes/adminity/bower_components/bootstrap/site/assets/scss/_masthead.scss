.bd-masthead {
  padding: 3rem 0;
  background: linear-gradient(165deg, tint-color($bd-purple-light, 85%) 50%, $white 50%);

  h1 {
    @include font-size(4rem);
    line-height: 1;
  }

  p:not(.lead) {
    color: $gray-700;
  }

  .btn {
    padding: .8rem 2rem;
    font-weight: 600;
  }
  .lead {
    @include font-size(1.5rem);
    font-weight: 400;
    color: $gray-700;
  }
}

@include media-breakpoint-up(md) {
  .mw-md-75 { max-width: 75%; }
}

.masthead-followup-icon {
  padding: .75rem;
  background-image: linear-gradient(to bottom right, rgba(255, 255, 255, .2), rgba(255, 255, 255, .01));
  @include border-radius(.75rem);
  box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .1);
}

.masthead-followup-svg {
  filter: drop-shadow(0 1px 0 rgba(0, 0, 0, .125));
}

.bd-subnavbar {
  // The position and z-index are needed for the dropdown to stay on top of the content
  position: relative;
  z-index: $zindex-sticky;
  background-color: rgba($white, .95);
  box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .05), inset 0 -1px 0 rgba(0, 0, 0, .15);

  .dropdown-menu {
    @include font-size(.875rem);
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .05);
  }

  .dropdown-item.current {
    font-weight: 600;
    background-image: escape-svg($dropdown-active-icon);
    background-repeat: no-repeat;
    background-position: right $dropdown-item-padding-x top .6rem;
    background-size: .75rem .75rem;
  }

  @include media-breakpoint-up(md) {
    position: sticky;
    top: 0;
  }
}

.bd-search {
  position: relative;

  &::after {
    position: absolute;
    top: .4rem;
    right: .4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.5rem;
    padding-right: .25rem;
    padding-left: .25rem;
    @include font-size(.75rem);
    color: $gray-600;
    content: "Ctrl + /";
    border: $border-width solid $border-color;
    @include border-radius(.125rem);
  }

  @include media-breakpoint-down(md) {
    width: 100%;
  }

  .form-control {
    padding-right: 3.75rem;

    &:focus {
      border-color: $bd-purple-bright;
      box-shadow: 0 0 0 3px rgba($bd-purple-bright, .25);
    }
  }
}

.bd-sidebar-toggle {
  color: $text-muted;

  &:hover,
  &:focus {
    color: $bd-purple-bright;
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba($bd-purple-bright, .25);
  }

  .bi-collapse { display: none; }

  &:not(.collapsed) {
    .bi-expand { display: none; }
    .bi-collapse { display: inline-block; }
  }
}

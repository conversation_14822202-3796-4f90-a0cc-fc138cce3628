{{- /*
  Usage: `scss-docs name="name" file="file/_location.scss"`

    Prints everything between `// scss-docs-start "name"` and `// scss-docs-end "name"`
    comments in the docs.

    Optional parameters:
    strip-default: Remove the ` !default` flag from variable assignments - default: `true`
*/ -}}

{{- $name := .Get "name" -}}
{{- $file := .Get "file" -}}
{{- $strip_default := .Get "strip-default" | default "true" -}}

{{- $start := printf "// scss-docs-start %s\n" $name -}}
{{- $end := printf "// scss-docs-end %s" $name -}}
{{- $regex := printf "%s(.|\n)*%s" $start $end -}}

{{- $css := readFile $file -}}
{{- $match := findRE $regex $css 1 -}}

{{- if (eq (len $match) 0) -}}
  {{- errorf "Got no matches for %q in %q! (called in %q)" $name $file $.Page.Path -}}
{{- end -}}

{{- $remove_start := replace (index $match 0) $start "" -}}
{{- $result := replace $remove_start $end "" -}}

{{- if (ne $strip_default "false") -}}
  {{- $result = replace $result " !default" "" -}}
{{- end -}}

{{- highlight $result "scss" "" -}}

{{ define "main" }}
  {{ partial "docs-subnav" . }}

  <div class="container-xxl my-md-4 bd-layout">
    <aside class="bd-sidebar">
      {{ partial "docs-sidebar" . }}
    </aside>

    <main class="bd-main order-1">
      <div class="bd-intro ps-lg-4">
        <div class="d-md-flex flex-md-row-reverse align-items-center justify-content-between">
          <a class="btn btn-sm btn-bd-light mb-2 mb-md-0" href="{{ .Site.Params.repo }}/blob/main/site/content/{{ .Page.File.Path | replaceRE `\\` "/" }}" title="View and edit this file on GitHub" target="_blank" rel="noopener">View on GitHub</a>
          <h1 class="bd-title" id="content">{{ .Title | markdownify }}</h1>
        </div>
        <p class="bd-lead">{{ .Page.Params.Description | markdownify }}</p>
        {{ partial "ads" . }}
      </div>

      {{ if (eq .Page.Params.toc true) }}
        <div class="bd-toc mt-4 mb-5 my-md-0 ps-xl-3 mb-lg-5 text-muted">
          <strong class="d-block h6 my-2 pb-2 border-bottom">On this page</strong>
          {{ .TableOfContents }}
        </div>
      {{ end }}

      <div class="bd-content ps-lg-4">
        {{ if .Page.Params.sections }}
          <div class="row g-3">
            {{ range .Page.Params.sections }}
            <div class="col-md-6">
              <a class="d-block text-decoration-none" href="../{{ urlize .title }}/">
                <strong class="d-block h5 mb-0">{{ .title }}</strong>
                <span class="text-secondary">{{ .description }}</span>
              </a>
            </div>
            {{ end }}
          </div>
        {{ end }}

        {{ .Content }}
      </div>
    </main>
  </div>
{{ end }}
{{ define "footer" }}
  {{ range .Page.Params.extra_js -}}
    <script{{ with .async }} async{{ end }} src="{{ .src }}"></script>
  {{- end -}}
{{ end }}

<div class="container masthead-followup px-4 px-md-3">
  <section class="row mb-5 pb-md-4 align-items-center">
    <div class="col-md-5">
      <div class="masthead-followup-icon d-inline-block mb-2 text-white bg-success">
        {{ partial "icons/code.svg" (dict "width" "32" "height" "32") }}
      </div>
      <h2 class="display-5 fw-normal">Installation</h2>
      <p class="lead fw-normal">
        Install Bootstrap’s source Sass and JavaScript files via npm, Composer, or Meteor.
      </p>
      <p>Package managed installs don’t include documentation or our full build scripts. You can also <a href="https://github.com/twbs/bootstrap-npm-starter">use our npm template repo</a> to quickly generate a Bootstrap project via npm.</p>
      <a class="btn btn-lg btn-outline-primary mb-3" href="/docs/{{ .Site.Params.docs_version }}/getting-started/download/">Read installation docs</a>
    </div>
    <div class="col-md-7 ps-md-5">
      {{ highlight "npm install bootstrap" "sh" "" }}
      {{ highlight (printf ("gem install bootstrap -v %s") .Site.Params.current_ruby_version) "sh" "" }}
    </div>
  </section>

  <section class="row mb-5 pb-md-4 align-items-center">
    <div class="col-md-5">
      <div class="masthead-followup-icon d-inline-block mb-2 text-white bg-primary">
        {{ partial "icons/cloud-fill.svg" (dict "width" "32" "height" "32") }}
      </div>
      <h2 class="display-5 fw-normal">jsDelivr</h2>
      <p class="lead fw-normal">
        When you only need to include Bootstrap’s compiled CSS or JS, you can use <a href="https://www.jsdelivr.com/">jsDelivr</a>.
      </p>
      <p>
        See it in action with our simple <a href="/docs/{{ .Site.Params.docs_version }}/getting-started/introduction/#starter-template">starter template</a>, or <a href="/docs/{{ .Site.Params.docs_version }}/examples/">browse the examples</a> to jumpstart your next project. You can also choose to include Popper and our JS <a href="/docs/{{ .Site.Params.docs_version }}/getting-started/introduction/#separate">separately</a>.
      </p>
      <a class="btn btn-lg btn-outline-primary mb-3" href="/docs/{{ .Site.Params.docs_version }}/getting-started/introduction/">Explore the docs</a>
    </div>
    <div class="col-md-7 ps-md-5">
      {{ highlight (printf (`<!-- CSS only -->
<link href="%s" rel="stylesheet" integrity=%q crossorigin="anonymous">
`) .Site.Params.cdn.css (.Site.Params.cdn.css_hash | safeHTMLAttr)) "html" "" }}
      {{ highlight (printf (`<!-- JavaScript Bundle with Popper -->
<script src="%s" integrity=%q crossorigin="anonymous"></script>
`) .Site.Params.cdn.js_bundle (.Site.Params.cdn.js_bundle_hash | safeHTMLAttr)) "html" "" }}
    </div>
  </section>

  <section class="row mb-5 pb-md-4 align-items-center">
    <div class="col-md-5">
      <div class="masthead-followup-icon d-inline-block mb-2 text-white bd-bg-purple-bright">
        {{ partial "icons/circle-square.svg" (dict "width" "32" "height" "32") }}
      </div>
      <h2 class="display-5 fw-normal">Bootstrap Icons</h2>
      <p class="lead fw-normal">
        For the first time ever, Bootstrap has its own open source SVG icon library, designed to work best with our components and documentation.
      </p>
      <p>
        Bootstrap Icons are designed to work best with Bootstrap components, but they’ll work in any project. They’re SVGs, so they scale quickly and easily, can be implemented in several ways, and can be styled with CSS.
      </p>
      <a href="{{ .Site.Params.icons }}" class="btn btn-lg btn-outline-primary mb-3">Get Bootstrap Icons</a>
    </div>
    <div class="col-md-7 ps-md-5">
        <img class="img-fluid mt-3 mx-auto" srcset="/docs/{{ .Site.Params.docs_version }}/assets/img/bootstrap-icons.png,
                                                    /docs/{{ .Site.Params.docs_version }}/assets/img/<EMAIL> 2x"
                                            src="/docs/{{ .Site.Params.docs_version }}/assets/img/bootstrap-icons.png"
                                            alt="Bootstrap Icons" width="700" height="425" loading="lazy">
    </div>
  </section>

  <section class="row mb-5 pb-md-4 align-items-center">
    <div class="col-md-5">
      <div class="masthead-followup-icon d-inline-block mb-2 text-white bg-danger">
        {{ partial "icons/droplet-fill.svg" (dict "width" "32" "height" "32") }}
      </div>
      <h2 class="display-5 fw-normal">Official Themes</h2>
      <p class="lead fw-normal">
        Take Bootstrap to the next level with premium themes from the <a href="{{ .Site.Params.themes }}">official Bootstrap Themes marketplace</a>.
      </p>
      <p>
        Themes are built on Bootstrap as their own extended frameworks, rich with new components and plugins, documentation, and powerful build tools.
      </p>
      <a href="{{ .Site.Params.themes }}" class="btn btn-lg btn-outline-primary mb-3">Browse themes</a>
    </div>
    <div class="col-md-7 ps-md-5">
        <img class="img-fluid mt-3 mx-auto" srcset="/docs/{{ .Site.Params.docs_version }}/assets/img/bootstrap-themes.png,
                                                    /docs/{{ .Site.Params.docs_version }}/assets/img/<EMAIL> 2x"
                                            src="/docs/{{ .Site.Params.docs_version }}/assets/img/bootstrap-themes.png"
                                            alt="Bootstrap Themes" width="700" height="500" loading="lazy">
    </div>
  </section>
</div>

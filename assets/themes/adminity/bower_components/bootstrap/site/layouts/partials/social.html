{{ "<!-- Twitter -->" | safeHTML }}
<meta name="twitter:card" content="{{ if .IsHome }}summary_large_image{{ else }}summary{{ end }}">
<meta name="twitter:site" content="@{{ .Site.Params.twitter }}">
<meta name="twitter:creator" content="@{{ .Site.Params.twitter }}">
<meta name="twitter:title" content="{{ .Title | markdownify }}">
<meta name="twitter:description" content="{{ .Page.Params.description | default .Site.Params.description | markdownify }}">
<meta name="twitter:image" content="{{ if .IsHome }}{{ .Site.Params.social_logo_path | absURL }}{{ else }}{{ .Site.Params.social_image_path | absURL }}{{ end }}">

{{ "<!-- Facebook -->" | safeHTML }}
<meta property="og:url" content="{{ .Permalink }}">
<meta property="og:title" content="{{ .Title | markdownify }}">
<meta property="og:description" content="{{ .Page.Params.description | default .Site.Params.description | markdownify }}">
<meta property="og:type" content="{{ if .IsPage }}article{{ else }}website{{ end }}">
<meta property="og:image" content="{{ .Site.Params.social_image_path | absURL }}">
<meta property="og:image:type" content="image/png">
<meta property="og:image:width" content="1000">
<meta property="og:image:height" content="500">

<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="description" content="{{ .Page.Params.description | default .Site.Params.description | markdownify }}">
<meta name="author" content="{{ .Site.Params.authors }}">
<meta name="generator" content="Hugo {{ hugo.Version }}">

<meta name="docsearch:language" content="en">
<meta name="docsearch:version" content="{{ .Site.Params.docs_version }}">

<title>{{ if .IsHome }}{{ .Site.Title | markdownify }} · {{ .Site.Params.description | markdownify }}{{ else }}{{ .Title | markdownify }} · {{ .Site.Title | markdownify }} v{{ .Site.Params.docs_version }}{{ end }}</title>

<link rel="canonical" href="{{ .Permalink }}">

{{ with .Params.robots -}}
<meta name="robots" content="{{ . }}">
{{- end }}

{{ partial "stylesheet" . }}
{{ partial "favicons" . }}
{{ partial "social" . }}
{{ partial "analytics" . }}

<nav class="collapse bd-links" id="bd-docs-nav" aria-label="Docs navigation">
  {{- $url := split .Permalink "/" -}}
  {{- $page_slug := index $url (sub (len $url) 2) -}}

  <ul class="list-unstyled mb-0 py-3 pt-md-1">
  {{- range $group := .Site.Data.sidebar -}}
    {{- $link := $group.title -}}
    {{- $link_slug := $link | urlize -}}

    {{- if $group.pages -}}
      {{- $link = index $group.pages 0 -}}
      {{- $link_slug = $link.title | urlize -}}
    {{- end -}}

    {{- $group_slug := $group.title | urlize -}}
    {{- $is_active_group := eq $.Page.Params.group $group_slug -}}

    {{- if $group.pages }}
      <li class="mb-1">
        <button class="btn d-inline-flex align-items-center rounded{{ if not $is_active_group }} collapsed{{ end }}" data-bs-toggle="collapse" data-bs-target="#{{ $group_slug }}-collapse" aria-expanded="{{ $is_active_group }}"{{ if $is_active_group }} aria-current="true"{{ end }}>
          {{ $group.title }}
        </button>

        <div class="collapse{{ if $is_active_group }} show{{ end }}" id="{{ $group_slug }}-collapse">
          <ul class="list-unstyled fw-normal pb-1 small">
            {{- range $doc := $group.pages -}}
              {{- $doc_slug := $doc.title | urlize -}}
              {{- $is_active := and $is_active_group (eq $page_slug $doc_slug) -}}
              {{- $href := printf "/docs/%s/%s/%s/" $.Site.Params.docs_version $group_slug $doc_slug }}
              <li><a href="{{ $href }}" class="d-inline-flex align-items-center rounded{{ if $is_active }} active{{ end }}"{{ if $is_active }} aria-current="page"{{ end }}>{{ $doc.title }}</a></li>
            {{- end }}
          </ul>
        </div>
      </li>
    {{- else }}
      <li class="my-3 mx-4 border-top"></li>
      <li>
        <a href="/docs/{{ $.Site.Params.docs_version }}/{{ $group_slug }}/" class="d-inline-flex align-items-center rounded{{ if $is_active_group }} active{{ end }}"{{ if $is_active_group }} aria-current="page"{{ end }}>
          {{ $group.title }}
        </a>
      </li>
    {{- end }}
  {{- end }}
  </ul>
</nav>

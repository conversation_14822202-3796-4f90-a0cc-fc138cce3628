---
layout: docs
title: Brand guidelines
description: Documentation and examples for Bootstrap's logo and brand usage guidelines.
group: about
toc: true
---

Have a need for Bootstrap's brand resources? Great! We have only a few guidelines we follow, and in turn ask you to follow as well.

## Logo

When referencing Bootstrap, use our logo mark. Do not modify our logos in any way. Do not use Bootstrap's branding for your own open or closed source projects. **Do not use the Twitter name or logo** in association with Bootstrap.

<div class="bd-brand-item px-2 py-5 mb-3 bg-light rounded-lg">
  <img class="d-block img-fluid mx-auto" src="/docs/{{< param docs_version >}}/assets/brand/bootstrap-logo.svg" alt="Bootstrap" width="256" height="204">
</div>

Our logo mark is also available in black and white. All rules for our primary logo apply to these as well.

<div class="bd-brand-logos d-sm-flex text-center bg-light rounded-lg overflow-hidden w-100 mb-3">
  <div class="bd-brand-item w-100 px-2 py-5">
    <img src="/docs/{{< param docs_version >}}/assets/brand/bootstrap-logo-black.svg" alt="Bootstrap" width="128" height="102" loading="lazy">
  </div>
  <div class="bd-brand-item w-100 px-2 py-5 inverse">
    <img src="/docs/{{< param docs_version >}}/assets/brand/bootstrap-logo-white.svg" alt="Bootstrap" width="128" height="102" loading="lazy">
  </div>
</div>

## Name

Bootstrap should always be referred to as just **Bootstrap**. No Twitter before it and no capital _s_.

<div class="bd-brand-logos d-sm-flex text-center bg-light rounded-lg overflow-hidden w-100 mb-3">
  <div class="bd-brand-item w-100 p-3">
    <div class="h3">Bootstrap</div>
    <strong class="text-success">Correct</strong>
  </div>
  <div class="bd-brand-item w-100 p-3">
    <div class="h3 text-muted">BootStrap</div>
    <strong class="text-danger">Incorrect</strong>
  </div>
  <div class="bd-brand-item w-100 p-3">
    <div class="h3 text-muted">Twitter Bootstrap</div>
    <strong class="text-danger">Incorrect</strong>
  </div>
</div>

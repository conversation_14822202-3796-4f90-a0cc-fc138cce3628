{"version": 3, "file": "popover.js", "sources": ["../src/util/index.js", "../src/popover.js"], "sourcesContent": ["import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    this.tip = super.getTipElement()\n\n    if (!this.getTitle()) {\n      SelectorEngine.findOne(SELECTOR_TITLE, this.tip).remove()\n    }\n\n    if (!this._getContent()) {\n      SelectorEngine.findOne(SELECTOR_CONTENT, this.tip).remove()\n    }\n\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this._config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "document", "body", "hasAttribute", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "length", "addEventListener", "for<PERSON>ach", "push", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "DATA_KEY", "EVENT_KEY", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "RegExp", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "placement", "offset", "trigger", "content", "template", "DefaultType", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "isWithContent", "getTitle", "_getContent", "getTipElement", "tip", "SelectorEngine", "findOne", "remove", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "call", "_element", "classList", "_addAttachmentClass", "attachment", "add", "updateAttachment", "getAttribute", "_config", "_cleanTipClass", "tabClass", "match", "map", "token", "trim", "tClass", "config", "each", "data", "getOrCreateInstance", "TypeError"], "mappings": ";;;;;;;;;;;;;;;;EAiMA,MAAMA,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAACE,QAAQ,CAACC,IAAT,CAAcC,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOJ,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMK,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAIL,QAAQ,CAACM,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAACI,MAA/B,EAAuC;EACrCP,MAAAA,QAAQ,CAACQ,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDL,QAAAA,yBAAyB,CAACM,OAA1B,CAAkCJ,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACO,IAA1B,CAA+BL,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAiBA,MAAMM,kBAAkB,GAAGC,MAAM,IAAI;EACnCR,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMS,CAAC,GAAGhB,SAAS,EAAnB;EACA;;EACA,QAAIgB,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;EC9NA;EACA;EACA;EACA;EACA;EACA;EAMA;EACA;EACA;EACA;EACA;;EAEA,MAAMH,IAAI,GAAG,SAAb;EACA,MAAMM,QAAQ,GAAG,YAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAME,YAAY,GAAG,YAArB;EACA,MAAMC,kBAAkB,GAAG,IAAIC,MAAJ,CAAY,UAASF,YAAa,MAAlC,EAAyC,GAAzC,CAA3B;EAEA,MAAMG,OAAO,GAAG,EACd,GAAGC,2BAAO,CAACD,OADG;EAEdE,EAAAA,SAAS,EAAE,OAFG;EAGdC,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAHM;EAIdC,EAAAA,OAAO,EAAE,OAJK;EAKdC,EAAAA,OAAO,EAAE,EALK;EAMdC,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,kCAFF,GAGE,kCAHF,GAIA;EAVI,CAAhB;EAaA,MAAMC,WAAW,GAAG,EAClB,GAAGN,2BAAO,CAACM,WADO;EAElBF,EAAAA,OAAO,EAAE;EAFS,CAApB;EAKA,MAAMG,KAAK,GAAG;EACZC,EAAAA,IAAI,EAAG,OAAMb,SAAU,EADX;EAEZc,EAAAA,MAAM,EAAG,SAAQd,SAAU,EAFf;EAGZe,EAAAA,IAAI,EAAG,OAAMf,SAAU,EAHX;EAIZgB,EAAAA,KAAK,EAAG,QAAOhB,SAAU,EAJb;EAKZiB,EAAAA,QAAQ,EAAG,WAAUjB,SAAU,EALnB;EAMZkB,EAAAA,KAAK,EAAG,QAAOlB,SAAU,EANb;EAOZmB,EAAAA,OAAO,EAAG,UAASnB,SAAU,EAPjB;EAQZoB,EAAAA,QAAQ,EAAG,WAAUpB,SAAU,EARnB;EASZqB,EAAAA,UAAU,EAAG,aAAYrB,SAAU,EATvB;EAUZsB,EAAAA,UAAU,EAAG,aAAYtB,SAAU;EAVvB,CAAd;EAaA,MAAMuB,eAAe,GAAG,MAAxB;EACA,MAAMC,eAAe,GAAG,MAAxB;EAEA,MAAMC,cAAc,GAAG,iBAAvB;EACA,MAAMC,gBAAgB,GAAG,eAAzB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,OAAN,SAAsBtB,2BAAtB,CAA8B;EAC5B;EAEkB,aAAPD,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEc,aAAJX,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD;;EAEe,aAALmB,KAAK,GAAG;EACjB,WAAOA,KAAP;EACD;;EAEqB,aAAXD,WAAW,GAAG;EACvB,WAAOA,WAAP;EACD,GAjB2B;;;EAqB5BiB,EAAAA,aAAa,GAAG;EACd,WAAO,KAAKC,QAAL,MAAmB,KAAKC,WAAL,EAA1B;EACD;;EAEDC,EAAAA,aAAa,GAAG;EACd,QAAI,KAAKC,GAAT,EAAc;EACZ,aAAO,KAAKA,GAAZ;EACD;;EAED,SAAKA,GAAL,GAAW,MAAMD,aAAN,EAAX;;EAEA,QAAI,CAAC,KAAKF,QAAL,EAAL,EAAsB;EACpBI,MAAAA,kCAAc,CAACC,OAAf,CAAuBT,cAAvB,EAAuC,KAAKO,GAA5C,EAAiDG,MAAjD;EACD;;EAED,QAAI,CAAC,KAAKL,WAAL,EAAL,EAAyB;EACvBG,MAAAA,kCAAc,CAACC,OAAf,CAAuBR,gBAAvB,EAAyC,KAAKM,GAA9C,EAAmDG,MAAnD;EACD;;EAED,WAAO,KAAKH,GAAZ;EACD;;EAEDI,EAAAA,UAAU,GAAG;EACX,UAAMJ,GAAG,GAAG,KAAKD,aAAL,EAAZ,CADW;;EAIX,SAAKM,iBAAL,CAAuBJ,kCAAc,CAACC,OAAf,CAAuBT,cAAvB,EAAuCO,GAAvC,CAAvB,EAAoE,KAAKH,QAAL,EAApE;;EACA,QAAIpB,OAAO,GAAG,KAAKqB,WAAL,EAAd;;EACA,QAAI,OAAOrB,OAAP,KAAmB,UAAvB,EAAmC;EACjCA,MAAAA,OAAO,GAAGA,OAAO,CAAC6B,IAAR,CAAa,KAAKC,QAAlB,CAAV;EACD;;EAED,SAAKF,iBAAL,CAAuBJ,kCAAc,CAACC,OAAf,CAAuBR,gBAAvB,EAAyCM,GAAzC,CAAvB,EAAsEvB,OAAtE;EAEAuB,IAAAA,GAAG,CAACQ,SAAJ,CAAcL,MAAd,CAAqBZ,eAArB,EAAsCC,eAAtC;EACD,GAxD2B;;;EA4D5BiB,EAAAA,mBAAmB,CAACC,UAAD,EAAa;EAC9B,SAAKX,aAAL,GAAqBS,SAArB,CAA+BG,GAA/B,CAAoC,GAAE1C,YAAa,IAAG,KAAK2C,gBAAL,CAAsBF,UAAtB,CAAkC,EAAxF;EACD;;EAEDZ,EAAAA,WAAW,GAAG;EACZ,WAAO,KAAKS,QAAL,CAAcM,YAAd,CAA2B,iBAA3B,KAAiD,KAAKC,OAAL,CAAarC,OAArE;EACD;;EAEDsC,EAAAA,cAAc,GAAG;EACf,UAAMf,GAAG,GAAG,KAAKD,aAAL,EAAZ;EACA,UAAMiB,QAAQ,GAAGhB,GAAG,CAACa,YAAJ,CAAiB,OAAjB,EAA0BI,KAA1B,CAAgC/C,kBAAhC,CAAjB;;EACA,QAAI8C,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC/D,MAAT,GAAkB,CAA3C,EAA8C;EAC5C+D,MAAAA,QAAQ,CAACE,GAAT,CAAaC,KAAK,IAAIA,KAAK,CAACC,IAAN,EAAtB,EACGjE,OADH,CACWkE,MAAM,IAAIrB,GAAG,CAACQ,SAAJ,CAAcL,MAAd,CAAqBkB,MAArB,CADrB;EAED;EACF,GA3E2B;;;EA+EN,SAAfzD,eAAe,CAAC0D,MAAD,EAAS;EAC7B,WAAO,KAAKC,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAG7B,OAAO,CAAC8B,mBAAR,CAA4B,IAA5B,EAAkCH,MAAlC,CAAb;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOE,IAAI,CAACF,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAII,SAAJ,CAAe,oBAAmBJ,MAAO,GAAzC,CAAN;EACD;;EAEDE,QAAAA,IAAI,CAACF,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;EA3F2B;EA8F9B;EACA;EACA;EACA;EACA;EACA;;;EAEAjE,kBAAkB,CAACsC,OAAD,CAAlB;;;;;;;;"}
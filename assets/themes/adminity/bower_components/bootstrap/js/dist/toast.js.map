{"version": 3, "file": "toast.js", "sources": ["../src/util/index.js", "../src/toast.js"], "sourcesContent": ["import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n"], "names": ["toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "isElement", "j<PERSON>y", "nodeType", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "reflow", "element", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "document", "body", "hasAttribute", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "length", "addEventListener", "push", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "DATA_KEY", "EVENT_KEY", "EVENT_CLICK_DISMISS", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSIN", "EVENT_FOCUSOUT", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "CLASS_NAME_FADE", "CLASS_NAME_HIDE", "CLASS_NAME_SHOW", "CLASS_NAME_SHOWING", "DefaultType", "animation", "autohide", "delay", "<PERSON><PERSON><PERSON>", "SELECTOR_DATA_DISMISS", "Toast", "BaseComponent", "constructor", "_config", "_getConfig", "_timeout", "_hasMouseInteraction", "_hasKeyboardInteraction", "_setListeners", "show", "showEvent", "EventHandler", "trigger", "_element", "defaultPrevented", "_clearTimeout", "classList", "add", "complete", "remove", "_maybeScheduleHide", "_queueCallback", "hide", "contains", "hideEvent", "dispose", "Manipulator", "getDataAttributes", "setTimeout", "_onInteraction", "event", "isInteracting", "type", "nextElement", "relatedTarget", "on", "clearTimeout", "each", "data", "getOrCreateInstance"], "mappings": ";;;;;;;;;;;;;;;;;EAcA,MAAMA,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;;EA0FA,MAAMC,SAAS,GAAGN,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAACO,MAAX,KAAsB,WAA1B,EAAuC;EACrCP,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACQ,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAwBA,MAAMC,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAIZ,SAAS,CAACY,KAAD,CAAlB,GAA4B,SAA5B,GAAwCnB,MAAM,CAACmB,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EA+DA,MAAMO,MAAM,GAAGC,OAAO,IAAIA,OAAO,CAACC,YAAlC;;EAEA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAACE,QAAQ,CAACC,IAAT,CAAcC,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOJ,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMK,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAIL,QAAQ,CAACM,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAACI,MAA/B,EAAuC;EACrCP,MAAAA,QAAQ,CAACQ,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDL,QAAAA,yBAAyB,CAAClB,OAA1B,CAAkCoB,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACM,IAA1B,CAA+BJ,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAiBA,MAAMK,kBAAkB,GAAGC,MAAM,IAAI;EACnCP,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMQ,CAAC,GAAGf,SAAS,EAAnB;EACA;;EACA,QAAIe,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;EC9NA;EACA;EACA;EACA;EACA;EACA;EAWA;EACA;EACA;EACA;EACA;;EAEA,MAAMH,IAAI,GAAG,OAAb;EACA,MAAMM,QAAQ,GAAG,UAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EAEA,MAAME,mBAAmB,GAAI,gBAAeD,SAAU,EAAtD;EACA,MAAME,eAAe,GAAI,YAAWF,SAAU,EAA9C;EACA,MAAMG,cAAc,GAAI,WAAUH,SAAU,EAA5C;EACA,MAAMI,aAAa,GAAI,UAASJ,SAAU,EAA1C;EACA,MAAMK,cAAc,GAAI,WAAUL,SAAU,EAA5C;EACA,MAAMM,UAAU,GAAI,OAAMN,SAAU,EAApC;EACA,MAAMO,YAAY,GAAI,SAAQP,SAAU,EAAxC;EACA,MAAMQ,UAAU,GAAI,OAAMR,SAAU,EAApC;EACA,MAAMS,WAAW,GAAI,QAAOT,SAAU,EAAtC;EAEA,MAAMU,eAAe,GAAG,MAAxB;EACA,MAAMC,eAAe,GAAG,MAAxB;EACA,MAAMC,eAAe,GAAG,MAAxB;EACA,MAAMC,kBAAkB,GAAG,SAA3B;EAEA,MAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,MAAMC,OAAO,GAAG;EACdH,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA,MAAME,qBAAqB,GAAG,2BAA9B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,KAAN,SAAoBC,iCAApB,CAAkC;EAChCC,EAAAA,WAAW,CAAChD,OAAD,EAAUd,MAAV,EAAkB;EAC3B,UAAMc,OAAN;EAEA,SAAKiD,OAAL,GAAe,KAAKC,UAAL,CAAgBhE,MAAhB,CAAf;EACA,SAAKiE,QAAL,GAAgB,IAAhB;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAKC,uBAAL,GAA+B,KAA/B;;EACA,SAAKC,aAAL;EACD,GAT+B;;;EAaV,aAAXd,WAAW,GAAG;EACvB,WAAOA,WAAP;EACD;;EAEiB,aAAPI,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEc,aAAJzB,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD,GAvB+B;;;EA2BhCoC,EAAAA,IAAI,GAAG;EACL,UAAMC,SAAS,GAAGC,gCAAY,CAACC,OAAb,CAAqB,KAAKC,QAA1B,EAAoCzB,UAApC,CAAlB;;EAEA,QAAIsB,SAAS,CAACI,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKC,aAAL;;EAEA,QAAI,KAAKZ,OAAL,CAAaR,SAAjB,EAA4B;EAC1B,WAAKkB,QAAL,CAAcG,SAAd,CAAwBC,GAAxB,CAA4B3B,eAA5B;EACD;;EAED,UAAM4B,QAAQ,GAAG,MAAM;EACrB,WAAKL,QAAL,CAAcG,SAAd,CAAwBG,MAAxB,CAA+B1B,kBAA/B;;EACA,WAAKoB,QAAL,CAAcG,SAAd,CAAwBC,GAAxB,CAA4BzB,eAA5B;;EAEAmB,MAAAA,gCAAY,CAACC,OAAb,CAAqB,KAAKC,QAA1B,EAAoCxB,WAApC;;EAEA,WAAK+B,kBAAL;EACD,KAPD;;EASA,SAAKP,QAAL,CAAcG,SAAd,CAAwBG,MAAxB,CAA+B5B,eAA/B;;EACAtC,IAAAA,MAAM,CAAC,KAAK4D,QAAN,CAAN;;EACA,SAAKA,QAAL,CAAcG,SAAd,CAAwBC,GAAxB,CAA4BxB,kBAA5B;;EAEA,SAAK4B,cAAL,CAAoBH,QAApB,EAA8B,KAAKL,QAAnC,EAA6C,KAAKV,OAAL,CAAaR,SAA1D;EACD;;EAED2B,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKT,QAAL,CAAcG,SAAd,CAAwBO,QAAxB,CAAiC/B,eAAjC,CAAL,EAAwD;EACtD;EACD;;EAED,UAAMgC,SAAS,GAAGb,gCAAY,CAACC,OAAb,CAAqB,KAAKC,QAA1B,EAAoC3B,UAApC,CAAlB;;EAEA,QAAIsC,SAAS,CAACV,gBAAd,EAAgC;EAC9B;EACD;;EAED,UAAMI,QAAQ,GAAG,MAAM;EACrB,WAAKL,QAAL,CAAcG,SAAd,CAAwBC,GAAxB,CAA4B1B,eAA5B;;EACAoB,MAAAA,gCAAY,CAACC,OAAb,CAAqB,KAAKC,QAA1B,EAAoC1B,YAApC;EACD,KAHD;;EAKA,SAAK0B,QAAL,CAAcG,SAAd,CAAwBG,MAAxB,CAA+B3B,eAA/B;;EACA,SAAK6B,cAAL,CAAoBH,QAApB,EAA8B,KAAKL,QAAnC,EAA6C,KAAKV,OAAL,CAAaR,SAA1D;EACD;;EAED8B,EAAAA,OAAO,GAAG;EACR,SAAKV,aAAL;;EAEA,QAAI,KAAKF,QAAL,CAAcG,SAAd,CAAwBO,QAAxB,CAAiC/B,eAAjC,CAAJ,EAAuD;EACrD,WAAKqB,QAAL,CAAcG,SAAd,CAAwBG,MAAxB,CAA+B3B,eAA/B;EACD;;EAED,UAAMiC,OAAN;EACD,GApF+B;;;EAwFhCrB,EAAAA,UAAU,CAAChE,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG0D,OADI;EAEP,SAAG4B,+BAAW,CAACC,iBAAZ,CAA8B,KAAKd,QAAnC,CAFI;EAGP,UAAI,OAAOzE,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHO,KAAT;EAMAF,IAAAA,eAAe,CAACmC,IAAD,EAAOjC,MAAP,EAAe,KAAK8D,WAAL,CAAiBR,WAAhC,CAAf;EAEA,WAAOtD,MAAP;EACD;;EAEDgF,EAAAA,kBAAkB,GAAG;EACnB,QAAI,CAAC,KAAKjB,OAAL,CAAaP,QAAlB,EAA4B;EAC1B;EACD;;EAED,QAAI,KAAKU,oBAAL,IAA6B,KAAKC,uBAAtC,EAA+D;EAC7D;EACD;;EAED,SAAKF,QAAL,GAAgBuB,UAAU,CAAC,MAAM;EAC/B,WAAKN,IAAL;EACD,KAFyB,EAEvB,KAAKnB,OAAL,CAAaN,KAFU,CAA1B;EAGD;;EAEDgC,EAAAA,cAAc,CAACC,KAAD,EAAQC,aAAR,EAAuB;EACnC,YAAQD,KAAK,CAACE,IAAd;EACE,WAAK,WAAL;EACA,WAAK,UAAL;EACE,aAAK1B,oBAAL,GAA4ByB,aAA5B;EACA;;EACF,WAAK,SAAL;EACA,WAAK,UAAL;EACE,aAAKxB,uBAAL,GAA+BwB,aAA/B;EACA;EARJ;;EAaA,QAAIA,aAAJ,EAAmB;EACjB,WAAKhB,aAAL;;EACA;EACD;;EAED,UAAMkB,WAAW,GAAGH,KAAK,CAACI,aAA1B;;EACA,QAAI,KAAKrB,QAAL,KAAkBoB,WAAlB,IAAiC,KAAKpB,QAAL,CAAcU,QAAd,CAAuBU,WAAvB,CAArC,EAA0E;EACxE;EACD;;EAED,SAAKb,kBAAL;EACD;;EAEDZ,EAAAA,aAAa,GAAG;EACdG,IAAAA,gCAAY,CAACwB,EAAb,CAAgB,KAAKtB,QAArB,EAA+BhC,mBAA/B,EAAoDkB,qBAApD,EAA2E,MAAM,KAAKuB,IAAL,EAAjF;EACAX,IAAAA,gCAAY,CAACwB,EAAb,CAAgB,KAAKtB,QAArB,EAA+B/B,eAA/B,EAAgDgD,KAAK,IAAI,KAAKD,cAAL,CAAoBC,KAApB,EAA2B,IAA3B,CAAzD;EACAnB,IAAAA,gCAAY,CAACwB,EAAb,CAAgB,KAAKtB,QAArB,EAA+B9B,cAA/B,EAA+C+C,KAAK,IAAI,KAAKD,cAAL,CAAoBC,KAApB,EAA2B,KAA3B,CAAxD;EACAnB,IAAAA,gCAAY,CAACwB,EAAb,CAAgB,KAAKtB,QAArB,EAA+B7B,aAA/B,EAA8C8C,KAAK,IAAI,KAAKD,cAAL,CAAoBC,KAApB,EAA2B,IAA3B,CAAvD;EACAnB,IAAAA,gCAAY,CAACwB,EAAb,CAAgB,KAAKtB,QAArB,EAA+B5B,cAA/B,EAA+C6C,KAAK,IAAI,KAAKD,cAAL,CAAoBC,KAApB,EAA2B,KAA3B,CAAxD;EACD;;EAEDf,EAAAA,aAAa,GAAG;EACdqB,IAAAA,YAAY,CAAC,KAAK/B,QAAN,CAAZ;EACA,SAAKA,QAAL,GAAgB,IAAhB;EACD,GAxJ+B;;;EA4JV,SAAf7B,eAAe,CAACpC,MAAD,EAAS;EAC7B,WAAO,KAAKiG,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGtC,KAAK,CAACuC,mBAAN,CAA0B,IAA1B,EAAgCnG,MAAhC,CAAb;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOkG,IAAI,CAAClG,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAEDkG,QAAAA,IAAI,CAAClG,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAVM,CAAP;EAWD;;EAxK+B;EA2KlC;EACA;EACA;EACA;EACA;EACA;;;EAEA6B,kBAAkB,CAAC+B,KAAD,CAAlB;;;;;;;;"}
{"version": 3, "file": "tab.js", "sources": ["../src/util/index.js", "../src/tab.js"], "sourcesContent": ["import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  reflow\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE))) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      active.classList.remove(CLASS_NAME_SHOW)\n      this._queueCallback(complete, element, true)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    let parent = element.parentNode\n    if (parent && parent.nodeName === 'LI') {\n      parent = parent.parentNode\n    }\n\n    if (parent && parent.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE, dropdownElement)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  const data = Tab.getOrCreateInstance(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n"], "names": ["getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getElementFromSelector", "document", "querySelector", "isDisabled", "nodeType", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "length", "addEventListener", "for<PERSON>ach", "push", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK_DATA_API", "CLASS_NAME_DROPDOWN_MENU", "CLASS_NAME_ACTIVE", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "SELECTOR_DROPDOWN", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_UL", "SELECTOR_DATA_TOGGLE", "SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "BaseComponent", "show", "_element", "parentNode", "previous", "target", "listElement", "closest", "itemSelector", "nodeName", "SelectorEngine", "find", "hideEvent", "EventHandler", "trigger", "relatedTarget", "showEvent", "defaultPrevented", "_activate", "complete", "container", "activeElements", "children", "active", "isTransitioning", "_transitionComplete", "remove", "_queueCallback", "dropdown<PERSON><PERSON>d", "findOne", "setAttribute", "add", "parent", "dropdownElement", "dropdown", "config", "each", "data", "getOrCreateInstance", "TypeError", "on", "event", "tagName", "preventDefault"], "mappings": ";;;;;;;;;;;;;;;;;EAoCA,MAAMA,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAmCA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EA+EA,MAAMU,UAAU,GAAGX,OAAO,IAAI;EAC5B,MAAI,CAACA,OAAD,IAAYA,OAAO,CAACY,QAAR,KAAqBC,IAAI,CAACC,YAA1C,EAAwD;EACtD,WAAO,IAAP;EACD;;EAED,MAAId,OAAO,CAACe,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;EAC1C,WAAO,IAAP;EACD;;EAED,MAAI,OAAOhB,OAAO,CAACiB,QAAf,KAA4B,WAAhC,EAA6C;EAC3C,WAAOjB,OAAO,CAACiB,QAAf;EACD;;EAED,SAAOjB,OAAO,CAACkB,YAAR,CAAqB,UAArB,KAAoClB,OAAO,CAACE,YAAR,CAAqB,UAArB,MAAqC,OAAhF;EACD,CAdD;;EAyCA,MAAMiB,MAAM,GAAGnB,OAAO,IAAIA,OAAO,CAACoB,YAAlC;;EAEA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAACb,QAAQ,CAACe,IAAT,CAAcN,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOI,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMG,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAIlB,QAAQ,CAACmB,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAACI,MAA/B,EAAuC;EACrCpB,MAAAA,QAAQ,CAACqB,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDL,QAAAA,yBAAyB,CAACM,OAA1B,CAAkCJ,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACO,IAA1B,CAA+BL,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAiBA,MAAMM,kBAAkB,GAAGC,MAAM,IAAI;EACnCR,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMS,CAAC,GAAGd,SAAS,EAAnB;EACA;;EACA,QAAIc,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;EC9NA;EACA;EACA;EACA;EACA;EACA;EAYA;EACA;EACA;EACA;EACA;;EAEA,MAAMH,IAAI,GAAG,KAAb;EACA,MAAMM,QAAQ,GAAG,QAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAME,YAAY,GAAG,WAArB;EAEA,MAAMC,UAAU,GAAI,OAAMF,SAAU,EAApC;EACA,MAAMG,YAAY,GAAI,SAAQH,SAAU,EAAxC;EACA,MAAMI,UAAU,GAAI,OAAMJ,SAAU,EAApC;EACA,MAAMK,WAAW,GAAI,QAAOL,SAAU,EAAtC;EACA,MAAMM,oBAAoB,GAAI,QAAON,SAAU,GAAEC,YAAa,EAA9D;EAEA,MAAMM,wBAAwB,GAAG,eAAjC;EACA,MAAMC,iBAAiB,GAAG,QAA1B;EACA,MAAMC,eAAe,GAAG,MAAxB;EACA,MAAMC,eAAe,GAAG,MAAxB;EAEA,MAAMC,iBAAiB,GAAG,WAA1B;EACA,MAAMC,uBAAuB,GAAG,mBAAhC;EACA,MAAMC,eAAe,GAAG,SAAxB;EACA,MAAMC,kBAAkB,GAAG,uBAA3B;EACA,MAAMC,oBAAoB,GAAG,0EAA7B;EACA,MAAMC,wBAAwB,GAAG,kBAAjC;EACA,MAAMC,8BAA8B,GAAG,iCAAvC;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,GAAN,SAAkBC,iCAAlB,CAAgC;EAC9B;EAEe,aAAJ1B,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD,GAL6B;;;EAS9B2B,EAAAA,IAAI,GAAG;EACL,QAAK,KAAKC,QAAL,CAAcC,UAAd,IACH,KAAKD,QAAL,CAAcC,UAAd,CAAyBtD,QAAzB,KAAsCC,IAAI,CAACC,YADxC,IAEH,KAAKmD,QAAL,CAAclD,SAAd,CAAwBC,QAAxB,CAAiCoC,iBAAjC,CAFF,EAEwD;EACtD;EACD;;EAED,QAAIe,QAAJ;EACA,UAAMC,MAAM,GAAG5D,sBAAsB,CAAC,KAAKyD,QAAN,CAArC;;EACA,UAAMI,WAAW,GAAG,KAAKJ,QAAL,CAAcK,OAAd,CAAsBd,uBAAtB,CAApB;;EAEA,QAAIa,WAAJ,EAAiB;EACf,YAAME,YAAY,GAAGF,WAAW,CAACG,QAAZ,KAAyB,IAAzB,IAAiCH,WAAW,CAACG,QAAZ,KAAyB,IAA1D,GAAiEd,kBAAjE,GAAsFD,eAA3G;EACAU,MAAAA,QAAQ,GAAGM,kCAAc,CAACC,IAAf,CAAoBH,YAApB,EAAkCF,WAAlC,CAAX;EACAF,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAACtC,MAAT,GAAkB,CAAnB,CAAnB;EACD;;EAED,UAAM8C,SAAS,GAAGR,QAAQ,GACxBS,gCAAY,CAACC,OAAb,CAAqBV,QAArB,EAA+BrB,UAA/B,EAA2C;EACzCgC,MAAAA,aAAa,EAAE,KAAKb;EADqB,KAA3C,CADwB,GAIxB,IAJF;EAMA,UAAMc,SAAS,GAAGH,gCAAY,CAACC,OAAb,CAAqB,KAAKZ,QAA1B,EAAoCjB,UAApC,EAAgD;EAChE8B,MAAAA,aAAa,EAAEX;EADiD,KAAhD,CAAlB;;EAIA,QAAIY,SAAS,CAACC,gBAAV,IAA+BL,SAAS,KAAK,IAAd,IAAsBA,SAAS,CAACK,gBAAnE,EAAsF;EACpF;EACD;;EAED,SAAKC,SAAL,CAAe,KAAKhB,QAApB,EAA8BI,WAA9B;;EAEA,UAAMa,QAAQ,GAAG,MAAM;EACrBN,MAAAA,gCAAY,CAACC,OAAb,CAAqBV,QAArB,EAA+BpB,YAA/B,EAA6C;EAC3C+B,QAAAA,aAAa,EAAE,KAAKb;EADuB,OAA7C;EAGAW,MAAAA,gCAAY,CAACC,OAAb,CAAqB,KAAKZ,QAA1B,EAAoChB,WAApC,EAAiD;EAC/C6B,QAAAA,aAAa,EAAEX;EADgC,OAAjD;EAGD,KAPD;;EASA,QAAIC,MAAJ,EAAY;EACV,WAAKa,SAAL,CAAeb,MAAf,EAAuBA,MAAM,CAACF,UAA9B,EAA0CgB,QAA1C;EACD,KAFD,MAEO;EACLA,MAAAA,QAAQ;EACT;EACF,GAxD6B;;;EA4D9BD,EAAAA,SAAS,CAACjF,OAAD,EAAUmF,SAAV,EAAqBxD,QAArB,EAA+B;EACtC,UAAMyD,cAAc,GAAGD,SAAS,KAAKA,SAAS,CAACX,QAAV,KAAuB,IAAvB,IAA+BW,SAAS,CAACX,QAAV,KAAuB,IAA3D,CAAT,GACrBC,kCAAc,CAACC,IAAf,CAAoBhB,kBAApB,EAAwCyB,SAAxC,CADqB,GAErBV,kCAAc,CAACY,QAAf,CAAwBF,SAAxB,EAAmC1B,eAAnC,CAFF;EAIA,UAAM6B,MAAM,GAAGF,cAAc,CAAC,CAAD,CAA7B;EACA,UAAMG,eAAe,GAAG5D,QAAQ,IAAK2D,MAAM,IAAIA,MAAM,CAACvE,SAAP,CAAiBC,QAAjB,CAA0BqC,eAA1B,CAA/C;;EAEA,UAAM6B,QAAQ,GAAG,MAAM,KAAKM,mBAAL,CAAyBxF,OAAzB,EAAkCsF,MAAlC,EAA0C3D,QAA1C,CAAvB;;EAEA,QAAI2D,MAAM,IAAIC,eAAd,EAA+B;EAC7BD,MAAAA,MAAM,CAACvE,SAAP,CAAiB0E,MAAjB,CAAwBnC,eAAxB;;EACA,WAAKoC,cAAL,CAAoBR,QAApB,EAA8BlF,OAA9B,EAAuC,IAAvC;EACD,KAHD,MAGO;EACLkF,MAAAA,QAAQ;EACT;EACF;;EAEDM,EAAAA,mBAAmB,CAACxF,OAAD,EAAUsF,MAAV,EAAkB3D,QAAlB,EAA4B;EAC7C,QAAI2D,MAAJ,EAAY;EACVA,MAAAA,MAAM,CAACvE,SAAP,CAAiB0E,MAAjB,CAAwBrC,iBAAxB;EAEA,YAAMuC,aAAa,GAAGlB,kCAAc,CAACmB,OAAf,CAAuB/B,8BAAvB,EAAuDyB,MAAM,CAACpB,UAA9D,CAAtB;;EAEA,UAAIyB,aAAJ,EAAmB;EACjBA,QAAAA,aAAa,CAAC5E,SAAd,CAAwB0E,MAAxB,CAA+BrC,iBAA/B;EACD;;EAED,UAAIkC,MAAM,CAACpF,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;EACzCoF,QAAAA,MAAM,CAACO,YAAP,CAAoB,eAApB,EAAqC,KAArC;EACD;EACF;;EAED7F,IAAAA,OAAO,CAACe,SAAR,CAAkB+E,GAAlB,CAAsB1C,iBAAtB;;EACA,QAAIpD,OAAO,CAACE,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;EAC1CF,MAAAA,OAAO,CAAC6F,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAED1E,IAAAA,MAAM,CAACnB,OAAD,CAAN;;EAEA,QAAIA,OAAO,CAACe,SAAR,CAAkBC,QAAlB,CAA2BqC,eAA3B,CAAJ,EAAiD;EAC/CrD,MAAAA,OAAO,CAACe,SAAR,CAAkB+E,GAAlB,CAAsBxC,eAAtB;EACD;;EAED,QAAIyC,MAAM,GAAG/F,OAAO,CAACkE,UAArB;;EACA,QAAI6B,MAAM,IAAIA,MAAM,CAACvB,QAAP,KAAoB,IAAlC,EAAwC;EACtCuB,MAAAA,MAAM,GAAGA,MAAM,CAAC7B,UAAhB;EACD;;EAED,QAAI6B,MAAM,IAAIA,MAAM,CAAChF,SAAP,CAAiBC,QAAjB,CAA0BmC,wBAA1B,CAAd,EAAmE;EACjE,YAAM6C,eAAe,GAAGhG,OAAO,CAACsE,OAAR,CAAgBf,iBAAhB,CAAxB;;EAEA,UAAIyC,eAAJ,EAAqB;EACnBvB,QAAAA,kCAAc,CAACC,IAAf,CAAoBd,wBAApB,EAA8CoC,eAA9C,EACGjE,OADH,CACWkE,QAAQ,IAAIA,QAAQ,CAAClF,SAAT,CAAmB+E,GAAnB,CAAuB1C,iBAAvB,CADvB;EAED;;EAEDpD,MAAAA,OAAO,CAAC6F,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAED,QAAIlE,QAAJ,EAAc;EACZA,MAAAA,QAAQ;EACT;EACF,GA3H6B;;;EA+HR,SAAfa,eAAe,CAAC0D,MAAD,EAAS;EAC7B,WAAO,KAAKC,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGtC,GAAG,CAACuC,mBAAJ,CAAwB,IAAxB,CAAb;;EAEA,UAAI,OAAOH,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOE,IAAI,CAACF,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAII,SAAJ,CAAe,oBAAmBJ,MAAO,GAAzC,CAAN;EACD;;EAEDE,QAAAA,IAAI,CAACF,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;EA3I6B;EA8IhC;EACA;EACA;EACA;EACA;;;AAEAtB,kCAAY,CAAC2B,EAAb,CAAgB9F,QAAhB,EAA0ByC,oBAA1B,EAAgDS,oBAAhD,EAAsE,UAAU6C,KAAV,EAAiB;EACrF,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcpG,QAAd,CAAuB,KAAKqG,OAA5B,CAAJ,EAA0C;EACxCD,IAAAA,KAAK,CAACE,cAAN;EACD;;EAED,MAAI/F,UAAU,CAAC,IAAD,CAAd,EAAsB;EACpB;EACD;;EAED,QAAMyF,IAAI,GAAGtC,GAAG,CAACuC,mBAAJ,CAAwB,IAAxB,CAAb;EACAD,EAAAA,IAAI,CAACpC,IAAL;EACD,CAXD;EAaA;EACA;EACA;EACA;EACA;EACA;;EAEA/B,kBAAkB,CAAC6B,GAAD,CAAlB;;;;;;;;"}
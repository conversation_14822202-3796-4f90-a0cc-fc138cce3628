{"version": 3, "file": "dropdown.js", "sources": ["../src/util/index.js", "../src/dropdown.js"], "sourcesContent": ["import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  isDisabled,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  getNextActiveElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (isDisabled(this._element)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    if (isActive) {\n      this.hide()\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = getElement(this._config.reference)\n      } else if (typeof this._config.reference === 'object') {\n        referenceElement = this._config.reference\n      }\n\n      const popperConfig = this._getPopperConfig()\n      const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n      if (isDisplayStatic) {\n        Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n      }\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', noop))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      this.toggle()\n    })\n  }\n\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.off(elem, 'mouseover', noop))\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    const data = Dropdown.getOrCreateInstance(element, config)\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Dropdown.getInstance(toggles[i])\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      if (!context._element.classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      const relatedTarget = {\n        relatedTarget: context._element\n      }\n\n      if (event) {\n        const composedPath = event.composedPath()\n        const isMenuTarget = composedPath.includes(context._menu)\n        if (\n          composedPath.includes(context._element) ||\n          (context._config.autoClose === 'inside' && !isMenuTarget) ||\n          (context._config.autoClose === 'outside' && isMenuTarget)\n        ) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n        if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n          continue\n        }\n\n        if (event.type === 'click') {\n          relatedTarget.clickEvent = event\n        }\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (!isActive && event.key === ESCAPE_KEY) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const getToggleButton = () => this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n\n    if (event.key === ESCAPE_KEY) {\n      getToggleButton().focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY) {\n      if (!isActive) {\n        getToggleButton().click()\n      }\n\n      Dropdown.getInstance(getToggleButton())._selectMenuItem(event)\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.dropdownInterface(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n"], "names": ["toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getElementFromSelector", "document", "querySelector", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "SelectorEngine", "findOne", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "getClientRects", "getComputedStyle", "getPropertyValue", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "noop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "isRTL", "documentElement", "dir", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "index", "indexOf", "listLength", "Math", "max", "min", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_SHOW", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_NAVBAR", "SELECTOR_DATA_TOGGLE", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "<PERSON><PERSON><PERSON>", "offset", "boundary", "reference", "display", "popperConfig", "autoClose", "DefaultType", "Dropdown", "BaseComponent", "constructor", "_popper", "_config", "_getConfig", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "_addEventListeners", "toggle", "_element", "isActive", "hide", "show", "parent", "getParentFromElement", "relatedTarget", "showEvent", "EventHandler", "trigger", "defaultPrevented", "Manipulator", "setDataAttribute", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "isDisplayStatic", "modifiers", "find", "modifier", "enabled", "createPopper", "closest", "concat", "children", "elem", "on", "focus", "setAttribute", "_completeHide", "dispose", "destroy", "update", "event", "preventDefault", "hideEvent", "off", "remove", "removeDataAttribute", "getDataAttributes", "getBoundingClientRect", "next", "_getPlacement", "parentDropdown", "parentNode", "isEnd", "_getOffset", "map", "val", "Number", "parseInt", "popperData", "defaultBsPopperConfig", "placement", "options", "_selectMenuItem", "key", "target", "items", "filter", "dropdownInterface", "data", "getOrCreateInstance", "each", "clearMenus", "button", "type", "toggles", "i", "len", "context", "getInstance", "<PERSON><PERSON><PERSON>", "isMenuTarget", "tagName", "clickEvent", "dataApiKeydownHandler", "stopPropagation", "getToggleButton", "matches", "prev", "click"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAcA,MAAMA,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;;EAsBA,MAAMC,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAmCA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAiCA,MAAMU,SAAS,GAAGlB,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAACmB,MAAX,KAAsB,WAA1B,EAAuC;EACrCnB,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACoB,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAYA,MAAMC,UAAU,GAAGrB,GAAG,IAAI;EACxB,MAAIkB,SAAS,CAAClB,GAAD,CAAb,EAAoB;EAAE;EACpB,WAAOA,GAAG,CAACmB,MAAJ,GAAanB,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;EACD;;EAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACsB,MAAJ,GAAa,CAA5C,EAA+C;EAC7C,WAAOC,kCAAc,CAACC,OAAf,CAAuBxB,GAAvB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVD;;EAYA,MAAMyB,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAIhB,SAAS,CAACgB,KAAD,CAAlB,GAA4B,SAA5B,GAAwCnC,MAAM,CAACmC,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EAcA,MAAMO,SAAS,GAAGjC,OAAO,IAAI;EAC3B,MAAI,CAACW,SAAS,CAACX,OAAD,CAAV,IAAuBA,OAAO,CAACkC,cAAR,GAAyBnB,MAAzB,KAAoC,CAA/D,EAAkE;EAChE,WAAO,KAAP;EACD;;EAED,SAAOoB,gBAAgB,CAACnC,OAAD,CAAhB,CAA0BoC,gBAA1B,CAA2C,YAA3C,MAA6D,SAApE;EACD,CAND;;EAQA,MAAMC,UAAU,GAAGrC,OAAO,IAAI;EAC5B,MAAI,CAACA,OAAD,IAAYA,OAAO,CAACa,QAAR,KAAqByB,IAAI,CAACC,YAA1C,EAAwD;EACtD,WAAO,IAAP;EACD;;EAED,MAAIvC,OAAO,CAACwC,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;EAC1C,WAAO,IAAP;EACD;;EAED,MAAI,OAAOzC,OAAO,CAAC0C,QAAf,KAA4B,WAAhC,EAA6C;EAC3C,WAAO1C,OAAO,CAAC0C,QAAf;EACD;;EAED,SAAO1C,OAAO,CAAC2C,YAAR,CAAqB,UAArB,KAAoC3C,OAAO,CAACE,YAAR,CAAqB,UAArB,MAAqC,OAAhF;EACD,CAdD;;EAuCA,MAAM0C,IAAI,GAAG,MAAM,EAAnB;;EAIA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAACrC,QAAQ,CAACuC,IAAT,CAAcL,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOG,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMG,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAI1C,QAAQ,CAAC2C,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAAClC,MAA/B,EAAuC;EACrCN,MAAAA,QAAQ,CAAC4C,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDJ,QAAAA,yBAAyB,CAACzB,OAA1B,CAAkC2B,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACK,IAA1B,CAA+BH,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAeA,MAAMI,KAAK,GAAG,MAAM9C,QAAQ,CAAC+C,eAAT,CAAyBC,GAAzB,KAAiC,KAArD;;EAEA,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;EACnCT,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMU,CAAC,GAAGf,SAAS,EAAnB;EACA;;EACA,QAAIe,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;EAoDA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,MAAMG,oBAAoB,GAAG,CAACC,IAAD,EAAOC,aAAP,EAAsBC,aAAtB,EAAqCC,cAArC,KAAwD;EACnF,MAAIC,KAAK,GAAGJ,IAAI,CAACK,OAAL,CAAaJ,aAAb,CAAZ,CADmF;;EAInF,MAAIG,KAAK,KAAK,CAAC,CAAf,EAAkB;EAChB,WAAOJ,IAAI,CAAC,CAACE,aAAD,IAAkBC,cAAlB,GAAmCH,IAAI,CAACtD,MAAL,GAAc,CAAjD,GAAqD,CAAtD,CAAX;EACD;;EAED,QAAM4D,UAAU,GAAGN,IAAI,CAACtD,MAAxB;EAEA0D,EAAAA,KAAK,IAAIF,aAAa,GAAG,CAAH,GAAO,CAAC,CAA9B;;EAEA,MAAIC,cAAJ,EAAoB;EAClBC,IAAAA,KAAK,GAAG,CAACA,KAAK,GAAGE,UAAT,IAAuBA,UAA/B;EACD;;EAED,SAAON,IAAI,CAACO,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAASL,KAAT,EAAgBE,UAAU,GAAG,CAA7B,CAAZ,CAAD,CAAX;EACD,CAjBD;;EC3RA;EACA;EACA;EACA;EACA;EACA;EAqBA;EACA;EACA;EACA;EACA;;EAEA,MAAMb,IAAI,GAAG,UAAb;EACA,MAAMiB,QAAQ,GAAG,aAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAME,YAAY,GAAG,WAArB;EAEA,MAAMC,UAAU,GAAG,QAAnB;EACA,MAAMC,SAAS,GAAG,OAAlB;EACA,MAAMC,OAAO,GAAG,KAAhB;EACA,MAAMC,YAAY,GAAG,SAArB;EACA,MAAMC,cAAc,GAAG,WAAvB;EACA,MAAMC,kBAAkB,GAAG,CAA3B;;EAEA,MAAMC,cAAc,GAAG,IAAI3D,MAAJ,CAAY,GAAEwD,YAAa,IAAGC,cAAe,IAAGJ,UAAW,EAA3D,CAAvB;EAEA,MAAMO,UAAU,GAAI,OAAMT,SAAU,EAApC;EACA,MAAMU,YAAY,GAAI,SAAQV,SAAU,EAAxC;EACA,MAAMW,UAAU,GAAI,OAAMX,SAAU,EAApC;EACA,MAAMY,WAAW,GAAI,QAAOZ,SAAU,EAAtC;EACA,MAAMa,WAAW,GAAI,QAAOb,SAAU,EAAtC;EACA,MAAMc,oBAAoB,GAAI,QAAOd,SAAU,GAAEC,YAAa,EAA9D;EACA,MAAMc,sBAAsB,GAAI,UAASf,SAAU,GAAEC,YAAa,EAAlE;EACA,MAAMe,oBAAoB,GAAI,QAAOhB,SAAU,GAAEC,YAAa,EAA9D;EAEA,MAAMgB,eAAe,GAAG,MAAxB;EACA,MAAMC,iBAAiB,GAAG,QAA1B;EACA,MAAMC,kBAAkB,GAAG,SAA3B;EACA,MAAMC,oBAAoB,GAAG,WAA7B;EACA,MAAMC,iBAAiB,GAAG,QAA1B;EAEA,MAAMC,oBAAoB,GAAG,6BAA7B;EACA,MAAMC,aAAa,GAAG,gBAAtB;EACA,MAAMC,mBAAmB,GAAG,aAA5B;EACA,MAAMC,sBAAsB,GAAG,6DAA/B;EAEA,MAAMC,aAAa,GAAGnD,KAAK,KAAK,SAAL,GAAiB,WAA5C;EACA,MAAMoD,gBAAgB,GAAGpD,KAAK,KAAK,WAAL,GAAmB,SAAjD;EACA,MAAMqD,gBAAgB,GAAGrD,KAAK,KAAK,YAAL,GAAoB,cAAlD;EACA,MAAMsD,mBAAmB,GAAGtD,KAAK,KAAK,cAAL,GAAsB,YAAvD;EACA,MAAMuD,eAAe,GAAGvD,KAAK,KAAK,YAAL,GAAoB,aAAjD;EACA,MAAMwD,cAAc,GAAGxD,KAAK,KAAK,aAAL,GAAqB,YAAjD;EAEA,MAAMyD,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CADM;EAEdC,EAAAA,QAAQ,EAAE,iBAFI;EAGdC,EAAAA,SAAS,EAAE,QAHG;EAIdC,EAAAA,OAAO,EAAE,SAJK;EAKdC,EAAAA,YAAY,EAAE,IALA;EAMdC,EAAAA,SAAS,EAAE;EANG,CAAhB;EASA,MAAMC,WAAW,GAAG;EAClBN,EAAAA,MAAM,EAAE,yBADU;EAElBC,EAAAA,QAAQ,EAAE,kBAFQ;EAGlBC,EAAAA,SAAS,EAAE,yBAHO;EAIlBC,EAAAA,OAAO,EAAE,QAJS;EAKlBC,EAAAA,YAAY,EAAE,wBALI;EAMlBC,EAAAA,SAAS,EAAE;EANO,CAApB;EASA;EACA;EACA;EACA;EACA;;EAEA,MAAME,QAAN,SAAuBC,iCAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAAC1H,OAAD,EAAUoB,MAAV,EAAkB;EAC3B,UAAMpB,OAAN;EAEA,SAAK2H,OAAL,GAAe,IAAf;EACA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgBzG,MAAhB,CAAf;EACA,SAAK0G,KAAL,GAAa,KAAKC,eAAL,EAAb;EACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EAEA,SAAKC,kBAAL;EACD,GAVkC;;;EAcjB,aAAPlB,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEqB,aAAXO,WAAW,GAAG;EACvB,WAAOA,WAAP;EACD;;EAEc,aAAJzD,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD,GAxBkC;;;EA4BnCqE,EAAAA,MAAM,GAAG;EACP,QAAI9F,UAAU,CAAC,KAAK+F,QAAN,CAAd,EAA+B;EAC7B;EACD;;EAED,UAAMC,QAAQ,GAAG,KAAKD,QAAL,CAAc5F,SAAd,CAAwBC,QAAxB,CAAiCwD,eAAjC,CAAjB;;EAEA,QAAIoC,QAAJ,EAAc;EACZ,WAAKC,IAAL;EACA;EACD;;EAED,SAAKC,IAAL;EACD;;EAEDA,EAAAA,IAAI,GAAG;EACL,QAAIlG,UAAU,CAAC,KAAK+F,QAAN,CAAV,IAA6B,KAAKN,KAAL,CAAWtF,SAAX,CAAqBC,QAArB,CAA8BwD,eAA9B,CAAjC,EAAiF;EAC/E;EACD;;EAED,UAAMuC,MAAM,GAAGhB,QAAQ,CAACiB,oBAAT,CAA8B,KAAKL,QAAnC,CAAf;EACA,UAAMM,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKN;EADA,KAAtB;EAIA,UAAMO,SAAS,GAAGC,gCAAY,CAACC,OAAb,CAAqB,KAAKT,QAA1B,EAAoCzC,UAApC,EAAgD+C,aAAhD,CAAlB;;EAEA,QAAIC,SAAS,CAACG,gBAAd,EAAgC;EAC9B;EACD,KAdI;;;EAiBL,QAAI,KAAKd,SAAT,EAAoB;EAClBe,MAAAA,+BAAW,CAACC,gBAAZ,CAA6B,KAAKlB,KAAlC,EAAyC,QAAzC,EAAmD,MAAnD;EACD,KAFD,MAEO;EACL,UAAI,OAAOmB,iBAAP,KAAkB,WAAtB,EAAmC;EACjC,cAAM,IAAIlH,SAAJ,CAAc,+DAAd,CAAN;EACD;;EAED,UAAImH,gBAAgB,GAAG,KAAKd,QAA5B;;EAEA,UAAI,KAAKR,OAAL,CAAaT,SAAb,KAA2B,QAA/B,EAAyC;EACvC+B,QAAAA,gBAAgB,GAAGV,MAAnB;EACD,OAFD,MAEO,IAAI7H,SAAS,CAAC,KAAKiH,OAAL,CAAaT,SAAd,CAAb,EAAuC;EAC5C+B,QAAAA,gBAAgB,GAAGpI,UAAU,CAAC,KAAK8G,OAAL,CAAaT,SAAd,CAA7B;EACD,OAFM,MAEA,IAAI,OAAO,KAAKS,OAAL,CAAaT,SAApB,KAAkC,QAAtC,EAAgD;EACrD+B,QAAAA,gBAAgB,GAAG,KAAKtB,OAAL,CAAaT,SAAhC;EACD;;EAED,YAAME,YAAY,GAAG,KAAK8B,gBAAL,EAArB;;EACA,YAAMC,eAAe,GAAG/B,YAAY,CAACgC,SAAb,CAAuBC,IAAvB,CAA4BC,QAAQ,IAAIA,QAAQ,CAAC1F,IAAT,KAAkB,aAAlB,IAAmC0F,QAAQ,CAACC,OAAT,KAAqB,KAAhG,CAAxB;EAEA,WAAK7B,OAAL,GAAesB,iBAAM,CAACQ,YAAP,CAAoBP,gBAApB,EAAsC,KAAKpB,KAA3C,EAAkDT,YAAlD,CAAf;;EAEA,UAAI+B,eAAJ,EAAqB;EACnBL,QAAAA,+BAAW,CAACC,gBAAZ,CAA6B,KAAKlB,KAAlC,EAAyC,QAAzC,EAAmD,QAAnD;EACD;EACF,KA1CI;EA6CL;EACA;EACA;;;EACA,QAAI,kBAAkBrH,QAAQ,CAAC+C,eAA3B,IACF,CAACgF,MAAM,CAACkB,OAAP,CAAelD,mBAAf,CADH,EACwC;EACtC,SAAGmD,MAAH,CAAU,GAAGlJ,QAAQ,CAACuC,IAAT,CAAc4G,QAA3B,EACGpI,OADH,CACWqI,IAAI,IAAIjB,gCAAY,CAACkB,EAAb,CAAgBD,IAAhB,EAAsB,WAAtB,EAAmCjH,IAAnC,CADnB;EAED;;EAED,SAAKwF,QAAL,CAAc2B,KAAd;;EACA,SAAK3B,QAAL,CAAc4B,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;EAEA,SAAKlC,KAAL,CAAWtF,SAAX,CAAqB2F,MAArB,CAA4BlC,eAA5B;;EACA,SAAKmC,QAAL,CAAc5F,SAAd,CAAwB2F,MAAxB,CAA+BlC,eAA/B;;EACA2C,IAAAA,gCAAY,CAACC,OAAb,CAAqB,KAAKT,QAA1B,EAAoCxC,WAApC,EAAiD8C,aAAjD;EACD;;EAEDJ,EAAAA,IAAI,GAAG;EACL,QAAIjG,UAAU,CAAC,KAAK+F,QAAN,CAAV,IAA6B,CAAC,KAAKN,KAAL,CAAWtF,SAAX,CAAqBC,QAArB,CAA8BwD,eAA9B,CAAlC,EAAkF;EAChF;EACD;;EAED,UAAMyC,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKN;EADA,KAAtB;;EAIA,SAAK6B,aAAL,CAAmBvB,aAAnB;EACD;;EAEDwB,EAAAA,OAAO,GAAG;EACR,QAAI,KAAKvC,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAawC,OAAb;EACD;;EAED,UAAMD,OAAN;EACD;;EAEDE,EAAAA,MAAM,GAAG;EACP,SAAKpC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EACA,QAAI,KAAKN,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAayC,MAAb;EACD;EACF,GAlIkC;;;EAsInClC,EAAAA,kBAAkB,GAAG;EACnBU,IAAAA,gCAAY,CAACkB,EAAb,CAAgB,KAAK1B,QAArB,EAA+BvC,WAA/B,EAA4CwE,KAAK,IAAI;EACnDA,MAAAA,KAAK,CAACC,cAAN;EACA,WAAKnC,MAAL;EACD,KAHD;EAID;;EAED8B,EAAAA,aAAa,CAACvB,aAAD,EAAgB;EAC3B,UAAM6B,SAAS,GAAG3B,gCAAY,CAACC,OAAb,CAAqB,KAAKT,QAA1B,EAAoC3C,UAApC,EAAgDiD,aAAhD,CAAlB;;EACA,QAAI6B,SAAS,CAACzB,gBAAd,EAAgC;EAC9B;EACD,KAJ0B;EAO3B;;;EACA,QAAI,kBAAkBrI,QAAQ,CAAC+C,eAA/B,EAAgD;EAC9C,SAAGmG,MAAH,CAAU,GAAGlJ,QAAQ,CAACuC,IAAT,CAAc4G,QAA3B,EACGpI,OADH,CACWqI,IAAI,IAAIjB,gCAAY,CAAC4B,GAAb,CAAiBX,IAAjB,EAAuB,WAAvB,EAAoCjH,IAApC,CADnB;EAED;;EAED,QAAI,KAAK+E,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAawC,OAAb;EACD;;EAED,SAAKrC,KAAL,CAAWtF,SAAX,CAAqBiI,MAArB,CAA4BxE,eAA5B;;EACA,SAAKmC,QAAL,CAAc5F,SAAd,CAAwBiI,MAAxB,CAA+BxE,eAA/B;;EACA,SAAKmC,QAAL,CAAc4B,YAAd,CAA2B,eAA3B,EAA4C,OAA5C;;EACAjB,IAAAA,+BAAW,CAAC2B,mBAAZ,CAAgC,KAAK5C,KAArC,EAA4C,QAA5C;EACAc,IAAAA,gCAAY,CAACC,OAAb,CAAqB,KAAKT,QAA1B,EAAoC1C,YAApC,EAAkDgD,aAAlD;EACD;;EAEDb,EAAAA,UAAU,CAACzG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG,KAAKsG,WAAL,CAAiBV,OADb;EAEP,SAAG+B,+BAAW,CAAC4B,iBAAZ,CAA8B,KAAKvC,QAAnC,CAFI;EAGP,SAAGhH;EAHI,KAAT;EAMAF,IAAAA,eAAe,CAAC4C,IAAD,EAAO1C,MAAP,EAAe,KAAKsG,WAAL,CAAiBH,WAAhC,CAAf;;EAEA,QAAI,OAAOnG,MAAM,CAAC+F,SAAd,KAA4B,QAA5B,IAAwC,CAACxG,SAAS,CAACS,MAAM,CAAC+F,SAAR,CAAlD,IACF,OAAO/F,MAAM,CAAC+F,SAAP,CAAiByD,qBAAxB,KAAkD,UADpD,EAEE;EACA;EACA,YAAM,IAAI7I,SAAJ,CAAe,GAAE+B,IAAI,CAAC9B,WAAL,EAAmB,gGAApC,CAAN;EACD;;EAED,WAAOZ,MAAP;EACD;;EAED2G,EAAAA,eAAe,GAAG;EAChB,WAAO/G,kCAAc,CAAC6J,IAAf,CAAoB,KAAKzC,QAAzB,EAAmC7B,aAAnC,EAAkD,CAAlD,CAAP;EACD;;EAEDuE,EAAAA,aAAa,GAAG;EACd,UAAMC,cAAc,GAAG,KAAK3C,QAAL,CAAc4C,UAArC;;EAEA,QAAID,cAAc,CAACvI,SAAf,CAAyBC,QAAzB,CAAkC0D,kBAAlC,CAAJ,EAA2D;EACzD,aAAOW,eAAP;EACD;;EAED,QAAIiE,cAAc,CAACvI,SAAf,CAAyBC,QAAzB,CAAkC2D,oBAAlC,CAAJ,EAA6D;EAC3D,aAAOW,cAAP;EACD,KATa;;;EAYd,UAAMkE,KAAK,GAAG9I,gBAAgB,CAAC,KAAK2F,KAAN,CAAhB,CAA6B1F,gBAA7B,CAA8C,eAA9C,EAA+D7B,IAA/D,OAA0E,KAAxF;;EAEA,QAAIwK,cAAc,CAACvI,SAAf,CAAyBC,QAAzB,CAAkCyD,iBAAlC,CAAJ,EAA0D;EACxD,aAAO+E,KAAK,GAAGtE,gBAAH,GAAsBD,aAAlC;EACD;;EAED,WAAOuE,KAAK,GAAGpE,mBAAH,GAAyBD,gBAArC;EACD;;EAEDqB,EAAAA,aAAa,GAAG;EACd,WAAO,KAAKG,QAAL,CAAcsB,OAAd,CAAuB,IAAGrD,iBAAkB,EAA5C,MAAmD,IAA1D;EACD;;EAED6E,EAAAA,UAAU,GAAG;EACX,UAAM;EAAEjE,MAAAA;EAAF,QAAa,KAAKW,OAAxB;;EAEA,QAAI,OAAOX,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,aAAOA,MAAM,CAAC3G,KAAP,CAAa,GAAb,EAAkB6K,GAAlB,CAAsBC,GAAG,IAAIC,MAAM,CAACC,QAAP,CAAgBF,GAAhB,EAAqB,EAArB,CAA7B,CAAP;EACD;;EAED,QAAI,OAAOnE,MAAP,KAAkB,UAAtB,EAAkC;EAChC,aAAOsE,UAAU,IAAItE,MAAM,CAACsE,UAAD,EAAa,KAAKnD,QAAlB,CAA3B;EACD;;EAED,WAAOnB,MAAP;EACD;;EAEDkC,EAAAA,gBAAgB,GAAG;EACjB,UAAMqC,qBAAqB,GAAG;EAC5BC,MAAAA,SAAS,EAAE,KAAKX,aAAL,EADiB;EAE5BzB,MAAAA,SAAS,EAAE,CAAC;EACVxF,QAAAA,IAAI,EAAE,iBADI;EAEV6H,QAAAA,OAAO,EAAE;EACPxE,UAAAA,QAAQ,EAAE,KAAKU,OAAL,CAAaV;EADhB;EAFC,OAAD,EAMX;EACErD,QAAAA,IAAI,EAAE,QADR;EAEE6H,QAAAA,OAAO,EAAE;EACPzE,UAAAA,MAAM,EAAE,KAAKiE,UAAL;EADD;EAFX,OANW;EAFiB,KAA9B,CADiB;;EAkBjB,QAAI,KAAKtD,OAAL,CAAaR,OAAb,KAAyB,QAA7B,EAAuC;EACrCoE,MAAAA,qBAAqB,CAACnC,SAAtB,GAAkC,CAAC;EACjCxF,QAAAA,IAAI,EAAE,aAD2B;EAEjC2F,QAAAA,OAAO,EAAE;EAFwB,OAAD,CAAlC;EAID;;EAED,WAAO,EACL,GAAGgC,qBADE;EAEL,UAAI,OAAO,KAAK5D,OAAL,CAAaP,YAApB,KAAqC,UAArC,GAAkD,KAAKO,OAAL,CAAaP,YAAb,CAA0BmE,qBAA1B,CAAlD,GAAqG,KAAK5D,OAAL,CAAaP,YAAtH;EAFK,KAAP;EAID;;EAEDsE,EAAAA,eAAe,CAAC;EAAEC,IAAAA,GAAF;EAAOC,IAAAA;EAAP,GAAD,EAAkB;EAC/B,UAAMC,KAAK,GAAG9K,kCAAc,CAACsI,IAAf,CAAoB7C,sBAApB,EAA4C,KAAKqB,KAAjD,EAAwDiE,MAAxD,CAA+D9J,SAA/D,CAAd;;EAEA,QAAI,CAAC6J,KAAK,CAAC/K,MAAX,EAAmB;EACjB;EACD,KAL8B;EAQ/B;;;EACAqD,IAAAA,oBAAoB,CAAC0H,KAAD,EAAQD,MAAR,EAAgBD,GAAG,KAAKtG,cAAxB,EAAwC,CAACwG,KAAK,CAAC1L,QAAN,CAAeyL,MAAf,CAAzC,CAApB,CAAqF9B,KAArF;EACD,GA5QkC;;;EAgRX,SAAjBiC,iBAAiB,CAAChM,OAAD,EAAUoB,MAAV,EAAkB;EACxC,UAAM6K,IAAI,GAAGzE,QAAQ,CAAC0E,mBAAT,CAA6BlM,OAA7B,EAAsCoB,MAAtC,CAAb;;EAEA,QAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAO6K,IAAI,CAAC7K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED6K,MAAAA,IAAI,CAAC7K,MAAD,CAAJ;EACD;EACF;;EAEqB,SAAf6C,eAAe,CAAC7C,MAAD,EAAS;EAC7B,WAAO,KAAK+K,IAAL,CAAU,YAAY;EAC3B3E,MAAAA,QAAQ,CAACwE,iBAAT,CAA2B,IAA3B,EAAiC5K,MAAjC;EACD,KAFM,CAAP;EAGD;;EAEgB,SAAVgL,UAAU,CAAC/B,KAAD,EAAQ;EACvB,QAAIA,KAAK,KAAKA,KAAK,CAACgC,MAAN,KAAiB9G,kBAAjB,IAAwC8E,KAAK,CAACiC,IAAN,KAAe,OAAf,IAA0BjC,KAAK,CAACuB,GAAN,KAAcxG,OAArF,CAAT,EAAyG;EACvG;EACD;;EAED,UAAMmH,OAAO,GAAGvL,kCAAc,CAACsI,IAAf,CAAoBhD,oBAApB,CAAhB;;EAEA,SAAK,IAAIkG,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,OAAO,CAACxL,MAA9B,EAAsCyL,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;EAClD,YAAME,OAAO,GAAGlF,QAAQ,CAACmF,WAAT,CAAqBJ,OAAO,CAACC,CAAD,CAA5B,CAAhB;;EACA,UAAI,CAACE,OAAD,IAAYA,OAAO,CAAC9E,OAAR,CAAgBN,SAAhB,KAA8B,KAA9C,EAAqD;EACnD;EACD;;EAED,UAAI,CAACoF,OAAO,CAACtE,QAAR,CAAiB5F,SAAjB,CAA2BC,QAA3B,CAAoCwD,eAApC,CAAL,EAA2D;EACzD;EACD;;EAED,YAAMyC,aAAa,GAAG;EACpBA,QAAAA,aAAa,EAAEgE,OAAO,CAACtE;EADH,OAAtB;;EAIA,UAAIiC,KAAJ,EAAW;EACT,cAAMuC,YAAY,GAAGvC,KAAK,CAACuC,YAAN,EAArB;EACA,cAAMC,YAAY,GAAGD,YAAY,CAACxM,QAAb,CAAsBsM,OAAO,CAAC5E,KAA9B,CAArB;;EACA,YACE8E,YAAY,CAACxM,QAAb,CAAsBsM,OAAO,CAACtE,QAA9B,KACCsE,OAAO,CAAC9E,OAAR,CAAgBN,SAAhB,KAA8B,QAA9B,IAA0C,CAACuF,YAD5C,IAECH,OAAO,CAAC9E,OAAR,CAAgBN,SAAhB,KAA8B,SAA9B,IAA2CuF,YAH9C,EAIE;EACA;EACD,SATQ;;;EAYT,YAAIH,OAAO,CAAC5E,KAAR,CAAcrF,QAAd,CAAuB4H,KAAK,CAACwB,MAA7B,MAA0CxB,KAAK,CAACiC,IAAN,KAAe,OAAf,IAA0BjC,KAAK,CAACuB,GAAN,KAAcxG,OAAzC,IAAqD,qCAAqCtD,IAArC,CAA0CuI,KAAK,CAACwB,MAAN,CAAaiB,OAAvD,CAA9F,CAAJ,EAAoK;EAClK;EACD;;EAED,YAAIzC,KAAK,CAACiC,IAAN,KAAe,OAAnB,EAA4B;EAC1B5D,UAAAA,aAAa,CAACqE,UAAd,GAA2B1C,KAA3B;EACD;EACF;;EAEDqC,MAAAA,OAAO,CAACzC,aAAR,CAAsBvB,aAAtB;EACD;EACF;;EAE0B,SAApBD,oBAAoB,CAACzI,OAAD,EAAU;EACnC,WAAOQ,sBAAsB,CAACR,OAAD,CAAtB,IAAmCA,OAAO,CAACgL,UAAlD;EACD;;EAE2B,SAArBgC,qBAAqB,CAAC3C,KAAD,EAAQ;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,QAAI,kBAAkBvI,IAAlB,CAAuBuI,KAAK,CAACwB,MAAN,CAAaiB,OAApC,IACFzC,KAAK,CAACuB,GAAN,KAAczG,SAAd,IAA4BkF,KAAK,CAACuB,GAAN,KAAc1G,UAAd,KAC1BmF,KAAK,CAACuB,GAAN,KAActG,cAAd,IAAgC+E,KAAK,CAACuB,GAAN,KAAcvG,YAA/C,IACCgF,KAAK,CAACwB,MAAN,CAAanC,OAAb,CAAqBnD,aAArB,CAF0B,CAD1B,GAIF,CAACf,cAAc,CAAC1D,IAAf,CAAoBuI,KAAK,CAACuB,GAA1B,CAJH,EAImC;EACjC;EACD;;EAED,UAAMvD,QAAQ,GAAG,KAAK7F,SAAL,CAAeC,QAAf,CAAwBwD,eAAxB,CAAjB;;EAEA,QAAI,CAACoC,QAAD,IAAagC,KAAK,CAACuB,GAAN,KAAc1G,UAA/B,EAA2C;EACzC;EACD;;EAEDmF,IAAAA,KAAK,CAACC,cAAN;EACAD,IAAAA,KAAK,CAAC4C,eAAN;;EAEA,QAAI5K,UAAU,CAAC,IAAD,CAAd,EAAsB;EACpB;EACD;;EAED,UAAM6K,eAAe,GAAG,MAAM,KAAKC,OAAL,CAAa7G,oBAAb,IAAqC,IAArC,GAA4CtF,kCAAc,CAACoM,IAAf,CAAoB,IAApB,EAA0B9G,oBAA1B,EAAgD,CAAhD,CAA1E;;EAEA,QAAI+D,KAAK,CAACuB,GAAN,KAAc1G,UAAlB,EAA8B;EAC5BgI,MAAAA,eAAe,GAAGnD,KAAlB;EACAvC,MAAAA,QAAQ,CAAC4E,UAAT;EACA;EACD;;EAED,QAAI/B,KAAK,CAACuB,GAAN,KAAcvG,YAAd,IAA8BgF,KAAK,CAACuB,GAAN,KAActG,cAAhD,EAAgE;EAC9D,UAAI,CAAC+C,QAAL,EAAe;EACb6E,QAAAA,eAAe,GAAGG,KAAlB;EACD;;EAED7F,MAAAA,QAAQ,CAACmF,WAAT,CAAqBO,eAAe,EAApC,EAAwCvB,eAAxC,CAAwDtB,KAAxD;;EACA;EACD;;EAED,QAAI,CAAChC,QAAD,IAAagC,KAAK,CAACuB,GAAN,KAAczG,SAA/B,EAA0C;EACxCqC,MAAAA,QAAQ,CAAC4E,UAAT;EACD;EACF;;EArYkC;EAwYrC;EACA;EACA;EACA;EACA;;;AAEAxD,kCAAY,CAACkB,EAAb,CAAgBrJ,QAAhB,EAA0BsF,sBAA1B,EAAkDO,oBAAlD,EAAwEkB,QAAQ,CAACwF,qBAAjF;AACApE,kCAAY,CAACkB,EAAb,CAAgBrJ,QAAhB,EAA0BsF,sBAA1B,EAAkDQ,aAAlD,EAAiEiB,QAAQ,CAACwF,qBAA1E;AACApE,kCAAY,CAACkB,EAAb,CAAgBrJ,QAAhB,EAA0BqF,oBAA1B,EAAgD0B,QAAQ,CAAC4E,UAAzD;AACAxD,kCAAY,CAACkB,EAAb,CAAgBrJ,QAAhB,EAA0BuF,oBAA1B,EAAgDwB,QAAQ,CAAC4E,UAAzD;AACAxD,kCAAY,CAACkB,EAAb,CAAgBrJ,QAAhB,EAA0BqF,oBAA1B,EAAgDQ,oBAAhD,EAAsE,UAAU+D,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAACC,cAAN;EACA9C,EAAAA,QAAQ,CAACwE,iBAAT,CAA2B,IAA3B;EACD,CAHD;EAKA;EACA;EACA;EACA;EACA;EACA;;EAEAtI,kBAAkB,CAAC8D,QAAD,CAAlB;;;;;;;;"}
{"version": 3, "file": "tooltip.js", "sources": ["../src/util/index.js", "../src/util/sanitizer.js", "../src/tooltip.js"], "sourcesContent": ["import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  findShadowRoot,\n  getElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this._config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.remove()\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    this.setContent()\n\n    if (this._config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const { container } = this._config\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.appendChild(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = typeof this._config.customClass === 'function' ? this._config.customClass() : this._config.customClass\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop)\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW) {\n        tip.remove()\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this._config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (isElement(content)) {\n      content = getElement(content)\n\n      // content is a DOM node or a jQuery\n      if (this._config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this._config.html) {\n      if (this._config.sanitize) {\n        content = sanitizeHtml(content, this._config.allowList, this._config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this._config.title === 'function' ?\n        this._config.title.call(this._element) :\n        this._config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.get(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n      Data.set(event.delegateTarget, dataKey, context)\n    }\n\n    return context\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this._config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context._config.delay || !context._config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context._config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context._config.delay || !context._config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context._config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this._config) {\n      for (const key in this._config) {\n        if (this.constructor.Default[key] !== this._config[key]) {\n          config[key] = this._config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n"], "names": ["MAX_UID", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "SelectorEngine", "findOne", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "findShadowRoot", "element", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "parentNode", "noop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "hasAttribute", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "isRTL", "dir", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "uriAttrs", "Set", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "includes", "has", "Boolean", "nodeValue", "regExp", "filter", "attrRegex", "i", "len", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "concat", "querySelectorAll", "el", "el<PERSON>ame", "remove", "attributeList", "attributes", "allowedAttributes", "removeAttribute", "innerHTML", "DATA_KEY", "EVENT_KEY", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "DefaultType", "animation", "template", "title", "trigger", "delay", "html", "selector", "placement", "offset", "container", "fallbackPlacements", "boundary", "customClass", "sanitize", "popperConfig", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "<PERSON><PERSON><PERSON>", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_FADE", "CLASS_NAME_MODAL", "CLASS_NAME_SHOW", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "BaseComponent", "constructor", "<PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "_popper", "_config", "_getConfig", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "toggle", "event", "context", "_initializeOnDelegatedTarget", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "classList", "contains", "dispose", "clearTimeout", "EventHandler", "off", "_element", "closest", "_hideModalHandler", "destroy", "show", "style", "display", "Error", "isWithContent", "showEvent", "shadowRoot", "isInTheDom", "ownerDocument", "defaultPrevented", "tipId", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "add", "attachment", "_getAttachment", "_addAttachmentClass", "Data", "set", "append<PERSON><PERSON><PERSON>", "update", "createPopper", "_getPopperConfig", "split", "children", "on", "complete", "prevHoverState", "isAnimated", "_queueCallback", "hide", "_cleanTipClass", "hideEvent", "getTitle", "createElement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "getAttribute", "updateAttachment", "dataKey", "get", "<PERSON><PERSON><PERSON><PERSON>", "_getDelegateConfig", "_getOffset", "map", "val", "Number", "parseInt", "popperData", "defaultBsPopperConfig", "modifiers", "options", "enabled", "phase", "data", "_handlePopperPlacementChange", "onFirstUpdate", "triggers", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "type", "setTimeout", "relatedTarget", "dataAttributes", "Manipulator", "getDataAttributes", "dataAttr", "key", "tabClass", "token", "trim", "tClass", "state", "popper", "each", "getOrCreateInstance"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMA,OAAO,GAAG,OAAhB;;EAKA,MAAMC,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;EAQA;EACA;EACA;EACA;EACA;;;EAEA,MAAMC,MAAM,GAAGC,MAAM,IAAI;EACvB,KAAG;EACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBZ,OAA3B,CAAV;EACD,GAFD,QAESa,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT;;EAIA,SAAOA,MAAP;EACD,CAND;;EA4EA,MAAMM,SAAS,GAAGb,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAACc,MAAX,KAAsB,WAA1B,EAAuC;EACrCd,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACe,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAYA,MAAMC,UAAU,GAAGhB,GAAG,IAAI;EACxB,MAAIa,SAAS,CAACb,GAAD,CAAb,EAAoB;EAAE;EACpB,WAAOA,GAAG,CAACc,MAAJ,GAAad,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;EACD;;EAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACiB,MAAJ,GAAa,CAA5C,EAA+C;EAC7C,WAAOC,kCAAc,CAACC,OAAf,CAAuBnB,GAAvB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVD;;EAYA,MAAMoB,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAIhB,SAAS,CAACgB,KAAD,CAAlB,GAA4B,SAA5B,GAAwC9B,MAAM,CAAC8B,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EAsCA,MAAMO,cAAc,GAAGC,OAAO,IAAI;EAChC,MAAI,CAACzB,QAAQ,CAAC0B,eAAT,CAAyBC,YAA9B,EAA4C;EAC1C,WAAO,IAAP;EACD,GAH+B;;;EAMhC,MAAI,OAAOF,OAAO,CAACG,WAAf,KAA+B,UAAnC,EAA+C;EAC7C,UAAMC,IAAI,GAAGJ,OAAO,CAACG,WAAR,EAAb;EACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;EACD;;EAED,MAAIJ,OAAO,YAAYK,UAAvB,EAAmC;EACjC,WAAOL,OAAP;EACD,GAb+B;;;EAgBhC,MAAI,CAACA,OAAO,CAACM,UAAb,EAAyB;EACvB,WAAO,IAAP;EACD;;EAED,SAAOP,cAAc,CAACC,OAAO,CAACM,UAAT,CAArB;EACD,CArBD;;EAuBA,MAAMC,IAAI,GAAG,MAAM,EAAnB;;EAIA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAAClC,QAAQ,CAACoC,IAAT,CAAcC,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOH,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMI,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAIxC,QAAQ,CAACyC,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAAChC,MAA/B,EAAuC;EACrCN,MAAAA,QAAQ,CAAC0C,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDJ,QAAAA,yBAAyB,CAACvB,OAA1B,CAAkCyB,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACK,IAA1B,CAA+BH,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAeA,MAAMI,KAAK,GAAG,MAAM5C,QAAQ,CAAC0B,eAAT,CAAyBmB,GAAzB,KAAiC,KAArD;;EAEA,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;EACnCR,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMS,CAAC,GAAGf,SAAS,EAAnB;EACA;;EACA,QAAIe,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;EC9NA;EACA;EACA;EACA;EACA;EACA;EAEA,MAAMG,QAAQ,GAAG,IAAIC,GAAJ,CAAQ,CACvB,YADuB,EAEvB,MAFuB,EAGvB,MAHuB,EAIvB,UAJuB,EAKvB,UALuB,EAMvB,QANuB,EAOvB,KAPuB,EAQvB,YARuB,CAAR,CAAjB;EAWA,MAAMC,sBAAsB,GAAG,gBAA/B;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,gBAAgB,GAAG,4DAAzB;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,gBAAgB,GAAG,oIAAzB;;EAEA,MAAMC,gBAAgB,GAAG,CAACC,IAAD,EAAOC,oBAAP,KAAgC;EACvD,QAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcvE,WAAd,EAAjB;;EAEA,MAAIqE,oBAAoB,CAACG,QAArB,CAA8BF,QAA9B,CAAJ,EAA6C;EAC3C,QAAIR,QAAQ,CAACW,GAAT,CAAaH,QAAb,CAAJ,EAA4B;EAC1B,aAAOI,OAAO,CAACT,gBAAgB,CAACtC,IAAjB,CAAsByC,IAAI,CAACO,SAA3B,KAAyCT,gBAAgB,CAACvC,IAAjB,CAAsByC,IAAI,CAACO,SAA3B,CAA1C,CAAd;EACD;;EAED,WAAO,IAAP;EACD;;EAED,QAAMC,MAAM,GAAGP,oBAAoB,CAACQ,MAArB,CAA4BC,SAAS,IAAIA,SAAS,YAAYpD,MAA9D,CAAf,CAXuD;;EAcvD,OAAK,IAAIqD,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGJ,MAAM,CAAChE,MAA7B,EAAqCmE,CAAC,GAAGC,GAAzC,EAA8CD,CAAC,EAA/C,EAAmD;EACjD,QAAIH,MAAM,CAACG,CAAD,CAAN,CAAUpD,IAAV,CAAe2C,QAAf,CAAJ,EAA8B;EAC5B,aAAO,IAAP;EACD;EACF;;EAED,SAAO,KAAP;EACD,CArBD;;EAuBO,MAAMW,gBAAgB,GAAG;EAC9B;EACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCjB,sBAAvC,CAFyB;EAG9BkB,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;EAI9BC,EAAAA,IAAI,EAAE,EAJwB;EAK9BC,EAAAA,CAAC,EAAE,EAL2B;EAM9BC,EAAAA,EAAE,EAAE,EAN0B;EAO9BC,EAAAA,GAAG,EAAE,EAPyB;EAQ9BC,EAAAA,IAAI,EAAE,EARwB;EAS9BC,EAAAA,GAAG,EAAE,EATyB;EAU9BC,EAAAA,EAAE,EAAE,EAV0B;EAW9BC,EAAAA,EAAE,EAAE,EAX0B;EAY9BC,EAAAA,EAAE,EAAE,EAZ0B;EAa9BC,EAAAA,EAAE,EAAE,EAb0B;EAc9BC,EAAAA,EAAE,EAAE,EAd0B;EAe9BC,EAAAA,EAAE,EAAE,EAf0B;EAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;EAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;EAkB9BjB,EAAAA,CAAC,EAAE,EAlB2B;EAmB9BkB,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;EAoB9BC,EAAAA,EAAE,EAAE,EApB0B;EAqB9BC,EAAAA,EAAE,EAAE,EArB0B;EAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;EAuB9BC,EAAAA,GAAG,EAAE,EAvByB;EAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;EAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;EA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;EA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;EA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;EA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;EA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;EA+B9BC,EAAAA,EAAE,EAAE;EA/B0B,CAAzB;EAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;EAC9D,MAAI,CAACF,UAAU,CAACnG,MAAhB,EAAwB;EACtB,WAAOmG,UAAP;EACD;;EAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;EAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;EACD;;EAED,QAAMG,SAAS,GAAG,IAAIzE,MAAM,CAAC0E,SAAX,EAAlB;EACA,QAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;EACA,QAAMO,aAAa,GAAGnG,MAAM,CAACC,IAAP,CAAY4F,SAAZ,CAAtB;EACA,QAAMO,QAAQ,GAAG,GAAGC,MAAH,CAAU,GAAGJ,eAAe,CAAC1E,IAAhB,CAAqB+E,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;EAEA,OAAK,IAAI1C,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGuC,QAAQ,CAAC3G,MAA/B,EAAuCmE,CAAC,GAAGC,GAA3C,EAAgDD,CAAC,EAAjD,EAAqD;EACnD,UAAM2C,EAAE,GAAGH,QAAQ,CAACxC,CAAD,CAAnB;EACA,UAAM4C,MAAM,GAAGD,EAAE,CAACnD,QAAH,CAAYvE,WAAZ,EAAf;;EAEA,QAAI,CAACsH,aAAa,CAAC9C,QAAd,CAAuBmD,MAAvB,CAAL,EAAqC;EACnCD,MAAAA,EAAE,CAACE,MAAH;EAEA;EACD;;EAED,UAAMC,aAAa,GAAG,GAAGL,MAAH,CAAU,GAAGE,EAAE,CAACI,UAAhB,CAAtB;EACA,UAAMC,iBAAiB,GAAG,GAAGP,MAAH,CAAUR,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACW,MAAD,CAAT,IAAqB,EAArD,CAA1B;EAEAE,IAAAA,aAAa,CAACxG,OAAd,CAAsB+C,IAAI,IAAI;EAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAO2D,iBAAP,CAArB,EAAgD;EAC9CL,QAAAA,EAAE,CAACM,eAAH,CAAmB5D,IAAI,CAACG,QAAxB;EACD;EACF,KAJD;EAKD;;EAED,SAAO6C,eAAe,CAAC1E,IAAhB,CAAqBuF,SAA5B;EACD;;EC9HD;EACA;EACA;EACA;EACA;EACA;EAwBA;EACA;EACA;EACA;EACA;;EAEA,MAAMzE,IAAI,GAAG,SAAb;EACA,MAAM0E,QAAQ,GAAG,YAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAME,YAAY,GAAG,YAArB;EACA,MAAMC,kBAAkB,GAAG,IAAI3G,MAAJ,CAAY,UAAS0G,YAAa,MAAlC,EAAyC,GAAzC,CAA3B;EACA,MAAME,qBAAqB,GAAG,IAAIvE,GAAJ,CAAQ,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAAR,CAA9B;EAEA,MAAMwE,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,QAFQ;EAGlBC,EAAAA,KAAK,EAAE,2BAHW;EAIlBC,EAAAA,OAAO,EAAE,QAJS;EAKlBC,EAAAA,KAAK,EAAE,iBALW;EAMlBC,EAAAA,IAAI,EAAE,SANY;EAOlBC,EAAAA,QAAQ,EAAE,kBAPQ;EAQlBC,EAAAA,SAAS,EAAE,mBARO;EASlBC,EAAAA,MAAM,EAAE,yBATU;EAUlBC,EAAAA,SAAS,EAAE,0BAVO;EAWlBC,EAAAA,kBAAkB,EAAE,OAXF;EAYlBC,EAAAA,QAAQ,EAAE,kBAZQ;EAalBC,EAAAA,WAAW,EAAE,mBAbK;EAclBC,EAAAA,QAAQ,EAAE,SAdQ;EAelBpC,EAAAA,UAAU,EAAE,iBAfM;EAgBlBD,EAAAA,SAAS,EAAE,QAhBO;EAiBlBsC,EAAAA,YAAY,EAAE;EAjBI,CAApB;EAoBA,MAAMC,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAE,MADc;EAEpBC,EAAAA,GAAG,EAAE,KAFe;EAGpBC,EAAAA,KAAK,EAAExG,KAAK,KAAK,MAAL,GAAc,OAHN;EAIpByG,EAAAA,MAAM,EAAE,QAJY;EAKpBC,EAAAA,IAAI,EAAE1G,KAAK,KAAK,OAAL,GAAe;EALN,CAAtB;EAQA,MAAM2G,OAAO,GAAG;EACdrB,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,mCAFF,GAGA,QALI;EAMdE,EAAAA,OAAO,EAAE,aANK;EAOdD,EAAAA,KAAK,EAAE,EAPO;EAQdE,EAAAA,KAAK,EAAE,CARO;EASdC,EAAAA,IAAI,EAAE,KATQ;EAUdC,EAAAA,QAAQ,EAAE,KAVI;EAWdC,EAAAA,SAAS,EAAE,KAXG;EAYdC,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAZM;EAadC,EAAAA,SAAS,EAAE,KAbG;EAcdC,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAdN;EAedC,EAAAA,QAAQ,EAAE,iBAfI;EAgBdC,EAAAA,WAAW,EAAE,EAhBC;EAiBdC,EAAAA,QAAQ,EAAE,IAjBI;EAkBdpC,EAAAA,UAAU,EAAE,IAlBE;EAmBdD,EAAAA,SAAS,EAAE/B,gBAnBG;EAoBdqE,EAAAA,YAAY,EAAE;EApBA,CAAhB;EAuBA,MAAMQ,KAAK,GAAG;EACZC,EAAAA,IAAI,EAAG,OAAM5B,SAAU,EADX;EAEZ6B,EAAAA,MAAM,EAAG,SAAQ7B,SAAU,EAFf;EAGZ8B,EAAAA,IAAI,EAAG,OAAM9B,SAAU,EAHX;EAIZ+B,EAAAA,KAAK,EAAG,QAAO/B,SAAU,EAJb;EAKZgC,EAAAA,QAAQ,EAAG,WAAUhC,SAAU,EALnB;EAMZiC,EAAAA,KAAK,EAAG,QAAOjC,SAAU,EANb;EAOZkC,EAAAA,OAAO,EAAG,UAASlC,SAAU,EAPjB;EAQZmC,EAAAA,QAAQ,EAAG,WAAUnC,SAAU,EARnB;EASZoC,EAAAA,UAAU,EAAG,aAAYpC,SAAU,EATvB;EAUZqC,EAAAA,UAAU,EAAG,aAAYrC,SAAU;EAVvB,CAAd;EAaA,MAAMsC,eAAe,GAAG,MAAxB;EACA,MAAMC,gBAAgB,GAAG,OAAzB;EACA,MAAMC,eAAe,GAAG,MAAxB;EAEA,MAAMC,gBAAgB,GAAG,MAAzB;EACA,MAAMC,eAAe,GAAG,KAAxB;EAEA,MAAMC,sBAAsB,GAAG,gBAA/B;EAEA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,cAAc,GAAG,QAAvB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,OAAN,SAAsBC,iCAAtB,CAAoC;EAClCC,EAAAA,WAAW,CAACtJ,OAAD,EAAUd,MAAV,EAAkB;EAC3B,QAAI,OAAOqK,iBAAP,KAAkB,WAAtB,EAAmC;EACjC,YAAM,IAAI1J,SAAJ,CAAc,8DAAd,CAAN;EACD;;EAED,UAAMG,OAAN,EAL2B;;EAQ3B,SAAKwJ,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,CAAhB;EACA,SAAKC,WAAL,GAAmB,EAAnB;EACA,SAAKC,cAAL,GAAsB,EAAtB;EACA,SAAKC,OAAL,GAAe,IAAf,CAZ2B;;EAe3B,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgB5K,MAAhB,CAAf;EACA,SAAK6K,GAAL,GAAW,IAAX;;EAEA,SAAKC,aAAL;EACD,GApBiC;;;EAwBhB,aAAPlC,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEc,aAAJrG,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD;;EAEe,aAALsG,KAAK,GAAG;EACjB,WAAOA,KAAP;EACD;;EAEqB,aAAXvB,WAAW,GAAG;EACvB,WAAOA,WAAP;EACD,GAtCiC;;;EA0ClCyD,EAAAA,MAAM,GAAG;EACP,SAAKT,UAAL,GAAkB,IAAlB;EACD;;EAEDU,EAAAA,OAAO,GAAG;EACR,SAAKV,UAAL,GAAkB,KAAlB;EACD;;EAEDW,EAAAA,aAAa,GAAG;EACd,SAAKX,UAAL,GAAkB,CAAC,KAAKA,UAAxB;EACD;;EAEDY,EAAAA,MAAM,CAACC,KAAD,EAAQ;EACZ,QAAI,CAAC,KAAKb,UAAV,EAAsB;EACpB;EACD;;EAED,QAAIa,KAAJ,EAAW;EACT,YAAMC,OAAO,GAAG,KAAKC,4BAAL,CAAkCF,KAAlC,CAAhB;;EAEAC,MAAAA,OAAO,CAACX,cAAR,CAAuBa,KAAvB,GAA+B,CAACF,OAAO,CAACX,cAAR,CAAuBa,KAAvD;;EAEA,UAAIF,OAAO,CAACG,oBAAR,EAAJ,EAAoC;EAClCH,QAAAA,OAAO,CAACI,MAAR,CAAe,IAAf,EAAqBJ,OAArB;EACD,OAFD,MAEO;EACLA,QAAAA,OAAO,CAACK,MAAR,CAAe,IAAf,EAAqBL,OAArB;EACD;EACF,KAVD,MAUO;EACL,UAAI,KAAKM,aAAL,GAAqBC,SAArB,CAA+BC,QAA/B,CAAwClC,eAAxC,CAAJ,EAA8D;EAC5D,aAAK+B,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;EACA;EACD;;EAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF;;EAEDK,EAAAA,OAAO,GAAG;EACRC,IAAAA,YAAY,CAAC,KAAKvB,QAAN,CAAZ;EAEAwB,IAAAA,gCAAY,CAACC,GAAb,CAAiB,KAAKC,QAAL,CAAcC,OAAd,CAAuB,IAAGzC,gBAAiB,EAA3C,CAAjB,EAAgE,eAAhE,EAAiF,KAAK0C,iBAAtF;;EAEA,QAAI,KAAKtB,GAAT,EAAc;EACZ,WAAKA,GAAL,CAASlE,MAAT;EACD;;EAED,QAAI,KAAK+D,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAa0B,OAAb;EACD;;EAED,UAAMP,OAAN;EACD;;EAEDQ,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKJ,QAAL,CAAcK,KAAd,CAAoBC,OAApB,KAAgC,MAApC,EAA4C;EAC1C,YAAM,IAAIC,KAAJ,CAAU,qCAAV,CAAN;EACD;;EAED,QAAI,EAAE,KAAKC,aAAL,MAAwB,KAAKnC,UAA/B,CAAJ,EAAgD;EAC9C;EACD;;EAED,UAAMoC,SAAS,GAAGX,gCAAY,CAACrE,OAAb,CAAqB,KAAKuE,QAA1B,EAAoC,KAAK7B,WAAL,CAAiBvB,KAAjB,CAAuBG,IAA3D,CAAlB;EACA,UAAM2D,UAAU,GAAG9L,cAAc,CAAC,KAAKoL,QAAN,CAAjC;EACA,UAAMW,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAKV,QAAL,CAAcY,aAAd,CAA4B9L,eAA5B,CAA4C6K,QAA5C,CAAqD,KAAKK,QAA1D,CADiB,GAEjBU,UAAU,CAACf,QAAX,CAAoB,KAAKK,QAAzB,CAFF;;EAIA,QAAIS,SAAS,CAACI,gBAAV,IAA8B,CAACF,UAAnC,EAA+C;EAC7C;EACD;;EAED,UAAM/B,GAAG,GAAG,KAAKa,aAAL,EAAZ;EACA,UAAMqB,KAAK,GAAG/N,MAAM,CAAC,KAAKoL,WAAL,CAAiB7H,IAAlB,CAApB;EAEAsI,IAAAA,GAAG,CAACmC,YAAJ,CAAiB,IAAjB,EAAuBD,KAAvB;;EACA,SAAKd,QAAL,CAAce,YAAd,CAA2B,kBAA3B,EAA+CD,KAA/C;;EAEA,SAAKE,UAAL;;EAEA,QAAI,KAAKtC,OAAL,CAAapD,SAAjB,EAA4B;EAC1BsD,MAAAA,GAAG,CAACc,SAAJ,CAAcuB,GAAd,CAAkB1D,eAAlB;EACD;;EAED,UAAM1B,SAAS,GAAG,OAAO,KAAK6C,OAAL,CAAa7C,SAApB,KAAkC,UAAlC,GAChB,KAAK6C,OAAL,CAAa7C,SAAb,CAAuBjJ,IAAvB,CAA4B,IAA5B,EAAkCgM,GAAlC,EAAuC,KAAKoB,QAA5C,CADgB,GAEhB,KAAKtB,OAAL,CAAa7C,SAFf;;EAIA,UAAMqF,UAAU,GAAG,KAAKC,cAAL,CAAoBtF,SAApB,CAAnB;;EACA,SAAKuF,mBAAL,CAAyBF,UAAzB;;EAEA,UAAM;EAAEnF,MAAAA;EAAF,QAAgB,KAAK2C,OAA3B;EACA2C,IAAAA,wBAAI,CAACC,GAAL,CAAS1C,GAAT,EAAc,KAAKT,WAAL,CAAiBnD,QAA/B,EAAyC,IAAzC;;EAEA,QAAI,CAAC,KAAKgF,QAAL,CAAcY,aAAd,CAA4B9L,eAA5B,CAA4C6K,QAA5C,CAAqD,KAAKf,GAA1D,CAAL,EAAqE;EACnE7C,MAAAA,SAAS,CAACwF,WAAV,CAAsB3C,GAAtB;EACAkB,MAAAA,gCAAY,CAACrE,OAAb,CAAqB,KAAKuE,QAA1B,EAAoC,KAAK7B,WAAL,CAAiBvB,KAAjB,CAAuBK,QAA3D;EACD;;EAED,QAAI,KAAKwB,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAa+C,MAAb;EACD,KAFD,MAEO;EACL,WAAK/C,OAAL,GAAeL,iBAAM,CAACqD,YAAP,CAAoB,KAAKzB,QAAzB,EAAmCpB,GAAnC,EAAwC,KAAK8C,gBAAL,CAAsBR,UAAtB,CAAxC,CAAf;EACD;;EAEDtC,IAAAA,GAAG,CAACc,SAAJ,CAAcuB,GAAd,CAAkBxD,eAAlB;EAEA,UAAMvB,WAAW,GAAG,OAAO,KAAKwC,OAAL,CAAaxC,WAApB,KAAoC,UAApC,GAAiD,KAAKwC,OAAL,CAAaxC,WAAb,EAAjD,GAA8E,KAAKwC,OAAL,CAAaxC,WAA/G;;EACA,QAAIA,WAAJ,EAAiB;EACf0C,MAAAA,GAAG,CAACc,SAAJ,CAAcuB,GAAd,CAAkB,GAAG/E,WAAW,CAACyF,KAAZ,CAAkB,GAAlB,CAArB;EACD,KAzDI;EA4DL;EACA;EACA;;;EACA,QAAI,kBAAkBvO,QAAQ,CAAC0B,eAA/B,EAAgD;EAC9C,SAAGwF,MAAH,CAAU,GAAGlH,QAAQ,CAACoC,IAAT,CAAcoM,QAA3B,EAAqCzN,OAArC,CAA6CU,OAAO,IAAI;EACtDiL,QAAAA,gCAAY,CAAC+B,EAAb,CAAgBhN,OAAhB,EAAyB,WAAzB,EAAsCO,IAAtC;EACD,OAFD;EAGD;;EAED,UAAM0M,QAAQ,GAAG,MAAM;EACrB,YAAMC,cAAc,GAAG,KAAKxD,WAA5B;EAEA,WAAKA,WAAL,GAAmB,IAAnB;EACAuB,MAAAA,gCAAY,CAACrE,OAAb,CAAqB,KAAKuE,QAA1B,EAAoC,KAAK7B,WAAL,CAAiBvB,KAAjB,CAAuBI,KAA3D;;EAEA,UAAI+E,cAAc,KAAKpE,eAAvB,EAAwC;EACtC,aAAK6B,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF,KATD;;EAWA,UAAMwC,UAAU,GAAG,KAAKpD,GAAL,CAASc,SAAT,CAAmBC,QAAnB,CAA4BpC,eAA5B,CAAnB;;EACA,SAAK0E,cAAL,CAAoBH,QAApB,EAA8B,KAAKlD,GAAnC,EAAwCoD,UAAxC;EACD;;EAEDE,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKzD,OAAV,EAAmB;EACjB;EACD;;EAED,UAAMG,GAAG,GAAG,KAAKa,aAAL,EAAZ;;EACA,UAAMqC,QAAQ,GAAG,MAAM;EACrB,UAAI,KAAKxC,oBAAL,EAAJ,EAAiC;EAC/B;EACD;;EAED,UAAI,KAAKf,WAAL,KAAqBb,gBAAzB,EAA2C;EACzCkB,QAAAA,GAAG,CAAClE,MAAJ;EACD;;EAED,WAAKyH,cAAL;;EACA,WAAKnC,QAAL,CAAclF,eAAd,CAA8B,kBAA9B;;EACAgF,MAAAA,gCAAY,CAACrE,OAAb,CAAqB,KAAKuE,QAA1B,EAAoC,KAAK7B,WAAL,CAAiBvB,KAAjB,CAAuBE,MAA3D;;EAEA,UAAI,KAAK2B,OAAT,EAAkB;EAChB,aAAKA,OAAL,CAAa0B,OAAb;;EACA,aAAK1B,OAAL,GAAe,IAAf;EACD;EACF,KAjBD;;EAmBA,UAAM2D,SAAS,GAAGtC,gCAAY,CAACrE,OAAb,CAAqB,KAAKuE,QAA1B,EAAoC,KAAK7B,WAAL,CAAiBvB,KAAjB,CAAuBC,IAA3D,CAAlB;;EACA,QAAIuF,SAAS,CAACvB,gBAAd,EAAgC;EAC9B;EACD;;EAEDjC,IAAAA,GAAG,CAACc,SAAJ,CAAchF,MAAd,CAAqB+C,eAArB,EA9BK;EAiCL;;EACA,QAAI,kBAAkBrK,QAAQ,CAAC0B,eAA/B,EAAgD;EAC9C,SAAGwF,MAAH,CAAU,GAAGlH,QAAQ,CAACoC,IAAT,CAAcoM,QAA3B,EACGzN,OADH,CACWU,OAAO,IAAIiL,gCAAY,CAACC,GAAb,CAAiBlL,OAAjB,EAA0B,WAA1B,EAAuCO,IAAvC,CADtB;EAED;;EAED,SAAKoJ,cAAL,CAAoBT,aAApB,IAAqC,KAArC;EACA,SAAKS,cAAL,CAAoBV,aAApB,IAAqC,KAArC;EACA,SAAKU,cAAL,CAAoBX,aAApB,IAAqC,KAArC;EAEA,UAAMmE,UAAU,GAAG,KAAKpD,GAAL,CAASc,SAAT,CAAmBC,QAAnB,CAA4BpC,eAA5B,CAAnB;;EACA,SAAK0E,cAAL,CAAoBH,QAApB,EAA8B,KAAKlD,GAAnC,EAAwCoD,UAAxC;;EACA,SAAKzD,WAAL,GAAmB,EAAnB;EACD;;EAEDiD,EAAAA,MAAM,GAAG;EACP,QAAI,KAAK/C,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAa+C,MAAb;EACD;EACF,GAvOiC;;;EA2OlChB,EAAAA,aAAa,GAAG;EACd,WAAOhJ,OAAO,CAAC,KAAK6K,QAAL,EAAD,CAAd;EACD;;EAED5C,EAAAA,aAAa,GAAG;EACd,QAAI,KAAKb,GAAT,EAAc;EACZ,aAAO,KAAKA,GAAZ;EACD;;EAED,UAAM/J,OAAO,GAAGzB,QAAQ,CAACkP,aAAT,CAAuB,KAAvB,CAAhB;EACAzN,IAAAA,OAAO,CAACkG,SAAR,GAAoB,KAAK2D,OAAL,CAAanD,QAAjC;EAEA,SAAKqD,GAAL,GAAW/J,OAAO,CAAC+M,QAAR,CAAiB,CAAjB,CAAX;EACA,WAAO,KAAKhD,GAAZ;EACD;;EAEDoC,EAAAA,UAAU,GAAG;EACX,UAAMpC,GAAG,GAAG,KAAKa,aAAL,EAAZ;EACA,SAAK8C,iBAAL,CAAuB5O,kCAAc,CAACC,OAAf,CAAuBgK,sBAAvB,EAA+CgB,GAA/C,CAAvB,EAA4E,KAAKyD,QAAL,EAA5E;EACAzD,IAAAA,GAAG,CAACc,SAAJ,CAAchF,MAAd,CAAqB6C,eAArB,EAAsCE,eAAtC;EACD;;EAED8E,EAAAA,iBAAiB,CAAC1N,OAAD,EAAU2N,OAAV,EAAmB;EAClC,QAAI3N,OAAO,KAAK,IAAhB,EAAsB;EACpB;EACD;;EAED,QAAIvB,SAAS,CAACkP,OAAD,CAAb,EAAwB;EACtBA,MAAAA,OAAO,GAAG/O,UAAU,CAAC+O,OAAD,CAApB,CADsB;;EAItB,UAAI,KAAK9D,OAAL,CAAa/C,IAAjB,EAAuB;EACrB,YAAI6G,OAAO,CAACrN,UAAR,KAAuBN,OAA3B,EAAoC;EAClCA,UAAAA,OAAO,CAACkG,SAAR,GAAoB,EAApB;EACAlG,UAAAA,OAAO,CAAC0M,WAAR,CAAoBiB,OAApB;EACD;EACF,OALD,MAKO;EACL3N,QAAAA,OAAO,CAAC4N,WAAR,GAAsBD,OAAO,CAACC,WAA9B;EACD;;EAED;EACD;;EAED,QAAI,KAAK/D,OAAL,CAAa/C,IAAjB,EAAuB;EACrB,UAAI,KAAK+C,OAAL,CAAavC,QAAjB,EAA2B;EACzBqG,QAAAA,OAAO,GAAG5I,YAAY,CAAC4I,OAAD,EAAU,KAAK9D,OAAL,CAAa5E,SAAvB,EAAkC,KAAK4E,OAAL,CAAa3E,UAA/C,CAAtB;EACD;;EAEDlF,MAAAA,OAAO,CAACkG,SAAR,GAAoByH,OAApB;EACD,KAND,MAMO;EACL3N,MAAAA,OAAO,CAAC4N,WAAR,GAAsBD,OAAtB;EACD;EACF;;EAEDH,EAAAA,QAAQ,GAAG;EACT,QAAI7G,KAAK,GAAG,KAAKwE,QAAL,CAAc0C,YAAd,CAA2B,wBAA3B,CAAZ;;EAEA,QAAI,CAAClH,KAAL,EAAY;EACVA,MAAAA,KAAK,GAAG,OAAO,KAAKkD,OAAL,CAAalD,KAApB,KAA8B,UAA9B,GACN,KAAKkD,OAAL,CAAalD,KAAb,CAAmB5I,IAAnB,CAAwB,KAAKoN,QAA7B,CADM,GAEN,KAAKtB,OAAL,CAAalD,KAFf;EAGD;;EAED,WAAOA,KAAP;EACD;;EAEDmH,EAAAA,gBAAgB,CAACzB,UAAD,EAAa;EAC3B,QAAIA,UAAU,KAAK,OAAnB,EAA4B;EAC1B,aAAO,KAAP;EACD;;EAED,QAAIA,UAAU,KAAK,MAAnB,EAA2B;EACzB,aAAO,OAAP;EACD;;EAED,WAAOA,UAAP;EACD,GAvTiC;;;EA2TlC9B,EAAAA,4BAA4B,CAACF,KAAD,EAAQC,OAAR,EAAiB;EAC3C,UAAMyD,OAAO,GAAG,KAAKzE,WAAL,CAAiBnD,QAAjC;EACAmE,IAAAA,OAAO,GAAGA,OAAO,IAAIkC,wBAAI,CAACwB,GAAL,CAAS3D,KAAK,CAAC4D,cAAf,EAA+BF,OAA/B,CAArB;;EAEA,QAAI,CAACzD,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAKhB,WAAT,CAAqBe,KAAK,CAAC4D,cAA3B,EAA2C,KAAKC,kBAAL,EAA3C,CAAV;EACA1B,MAAAA,wBAAI,CAACC,GAAL,CAASpC,KAAK,CAAC4D,cAAf,EAA+BF,OAA/B,EAAwCzD,OAAxC;EACD;;EAED,WAAOA,OAAP;EACD;;EAED6D,EAAAA,UAAU,GAAG;EACX,UAAM;EAAElH,MAAAA;EAAF,QAAa,KAAK4C,OAAxB;;EAEA,QAAI,OAAO5C,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,aAAOA,MAAM,CAAC6F,KAAP,CAAa,GAAb,EAAkBsB,GAAlB,CAAsBC,GAAG,IAAIC,MAAM,CAACC,QAAP,CAAgBF,GAAhB,EAAqB,EAArB,CAA7B,CAAP;EACD;;EAED,QAAI,OAAOpH,MAAP,KAAkB,UAAtB,EAAkC;EAChC,aAAOuH,UAAU,IAAIvH,MAAM,CAACuH,UAAD,EAAa,KAAKrD,QAAlB,CAA3B;EACD;;EAED,WAAOlE,MAAP;EACD;;EAED4F,EAAAA,gBAAgB,CAACR,UAAD,EAAa;EAC3B,UAAMoC,qBAAqB,GAAG;EAC5BzH,MAAAA,SAAS,EAAEqF,UADiB;EAE5BqC,MAAAA,SAAS,EAAE,CACT;EACElN,QAAAA,IAAI,EAAE,MADR;EAEEmN,QAAAA,OAAO,EAAE;EACPxH,UAAAA,kBAAkB,EAAE,KAAK0C,OAAL,CAAa1C;EAD1B;EAFX,OADS,EAOT;EACE3F,QAAAA,IAAI,EAAE,QADR;EAEEmN,QAAAA,OAAO,EAAE;EACP1H,UAAAA,MAAM,EAAE,KAAKkH,UAAL;EADD;EAFX,OAPS,EAaT;EACE3M,QAAAA,IAAI,EAAE,iBADR;EAEEmN,QAAAA,OAAO,EAAE;EACPvH,UAAAA,QAAQ,EAAE,KAAKyC,OAAL,CAAazC;EADhB;EAFX,OAbS,EAmBT;EACE5F,QAAAA,IAAI,EAAE,OADR;EAEEmN,QAAAA,OAAO,EAAE;EACP3O,UAAAA,OAAO,EAAG,IAAG,KAAKsJ,WAAL,CAAiB7H,IAAK;EAD5B;EAFX,OAnBS,EAyBT;EACED,QAAAA,IAAI,EAAE,UADR;EAEEoN,QAAAA,OAAO,EAAE,IAFX;EAGEC,QAAAA,KAAK,EAAE,YAHT;EAIElN,QAAAA,EAAE,EAAEmN,IAAI,IAAI,KAAKC,4BAAL,CAAkCD,IAAlC;EAJd,OAzBS,CAFiB;EAkC5BE,MAAAA,aAAa,EAAEF,IAAI,IAAI;EACrB,YAAIA,IAAI,CAACH,OAAL,CAAa3H,SAAb,KAA2B8H,IAAI,CAAC9H,SAApC,EAA+C;EAC7C,eAAK+H,4BAAL,CAAkCD,IAAlC;EACD;EACF;EAtC2B,KAA9B;EAyCA,WAAO,EACL,GAAGL,qBADE;EAEL,UAAI,OAAO,KAAK5E,OAAL,CAAatC,YAApB,KAAqC,UAArC,GAAkD,KAAKsC,OAAL,CAAatC,YAAb,CAA0BkH,qBAA1B,CAAlD,GAAqG,KAAK5E,OAAL,CAAatC,YAAtH;EAFK,KAAP;EAID;;EAEDgF,EAAAA,mBAAmB,CAACF,UAAD,EAAa;EAC9B,SAAKzB,aAAL,GAAqBC,SAArB,CAA+BuB,GAA/B,CAAoC,GAAE/F,YAAa,IAAG,KAAKyH,gBAAL,CAAsBzB,UAAtB,CAAkC,EAAxF;EACD;;EAEDC,EAAAA,cAAc,CAACtF,SAAD,EAAY;EACxB,WAAOQ,aAAa,CAACR,SAAS,CAAClH,WAAV,EAAD,CAApB;EACD;;EAEDkK,EAAAA,aAAa,GAAG;EACd,UAAMiF,QAAQ,GAAG,KAAKpF,OAAL,CAAajD,OAAb,CAAqBkG,KAArB,CAA2B,GAA3B,CAAjB;;EAEAmC,IAAAA,QAAQ,CAAC3P,OAAT,CAAiBsH,OAAO,IAAI;EAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;EACvBqE,QAAAA,gCAAY,CAAC+B,EAAb,CAAgB,KAAK7B,QAArB,EAA+B,KAAK7B,WAAL,CAAiBvB,KAAjB,CAAuBM,KAAtD,EAA6D,KAAKwB,OAAL,CAAa9C,QAA1E,EAAoFsD,KAAK,IAAI,KAAKD,MAAL,CAAYC,KAAZ,CAA7F;EACD,OAFD,MAEO,IAAIzD,OAAO,KAAKuC,cAAhB,EAAgC;EACrC,cAAM+F,OAAO,GAAGtI,OAAO,KAAKoC,aAAZ,GACd,KAAKM,WAAL,CAAiBvB,KAAjB,CAAuBS,UADT,GAEd,KAAKc,WAAL,CAAiBvB,KAAjB,CAAuBO,OAFzB;EAGA,cAAM6G,QAAQ,GAAGvI,OAAO,KAAKoC,aAAZ,GACf,KAAKM,WAAL,CAAiBvB,KAAjB,CAAuBU,UADR,GAEf,KAAKa,WAAL,CAAiBvB,KAAjB,CAAuBQ,QAFzB;EAIA0C,QAAAA,gCAAY,CAAC+B,EAAb,CAAgB,KAAK7B,QAArB,EAA+B+D,OAA/B,EAAwC,KAAKrF,OAAL,CAAa9C,QAArD,EAA+DsD,KAAK,IAAI,KAAKK,MAAL,CAAYL,KAAZ,CAAxE;EACAY,QAAAA,gCAAY,CAAC+B,EAAb,CAAgB,KAAK7B,QAArB,EAA+BgE,QAA/B,EAAyC,KAAKtF,OAAL,CAAa9C,QAAtD,EAAgEsD,KAAK,IAAI,KAAKM,MAAL,CAAYN,KAAZ,CAAzE;EACD;EACF,KAdD;;EAgBA,SAAKgB,iBAAL,GAAyB,MAAM;EAC7B,UAAI,KAAKF,QAAT,EAAmB;EACjB,aAAKkC,IAAL;EACD;EACF,KAJD;;EAMApC,IAAAA,gCAAY,CAAC+B,EAAb,CAAgB,KAAK7B,QAAL,CAAcC,OAAd,CAAuB,IAAGzC,gBAAiB,EAA3C,CAAhB,EAA+D,eAA/D,EAAgF,KAAK0C,iBAArF;;EAEA,QAAI,KAAKxB,OAAL,CAAa9C,QAAjB,EAA2B;EACzB,WAAK8C,OAAL,GAAe,EACb,GAAG,KAAKA,OADK;EAEbjD,QAAAA,OAAO,EAAE,QAFI;EAGbG,QAAAA,QAAQ,EAAE;EAHG,OAAf;EAKD,KAND,MAMO;EACL,WAAKqI,SAAL;EACD;EACF;;EAEDA,EAAAA,SAAS,GAAG;EACV,UAAMzI,KAAK,GAAG,KAAKwE,QAAL,CAAc0C,YAAd,CAA2B,OAA3B,CAAd;;EACA,UAAMwB,iBAAiB,GAAG,OAAO,KAAKlE,QAAL,CAAc0C,YAAd,CAA2B,wBAA3B,CAAjC;;EAEA,QAAIlH,KAAK,IAAI0I,iBAAiB,KAAK,QAAnC,EAA6C;EAC3C,WAAKlE,QAAL,CAAce,YAAd,CAA2B,wBAA3B,EAAqDvF,KAAK,IAAI,EAA9D;;EACA,UAAIA,KAAK,IAAI,CAAC,KAAKwE,QAAL,CAAc0C,YAAd,CAA2B,YAA3B,CAAV,IAAsD,CAAC,KAAK1C,QAAL,CAAcyC,WAAzE,EAAsF;EACpF,aAAKzC,QAAL,CAAce,YAAd,CAA2B,YAA3B,EAAyCvF,KAAzC;EACD;;EAED,WAAKwE,QAAL,CAAce,YAAd,CAA2B,OAA3B,EAAoC,EAApC;EACD;EACF;;EAEDxB,EAAAA,MAAM,CAACL,KAAD,EAAQC,OAAR,EAAiB;EACrBA,IAAAA,OAAO,GAAG,KAAKC,4BAAL,CAAkCF,KAAlC,EAAyCC,OAAzC,CAAV;;EAEA,QAAID,KAAJ,EAAW;EACTC,MAAAA,OAAO,CAACX,cAAR,CACEU,KAAK,CAACiF,IAAN,KAAe,SAAf,GAA2BrG,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;EAGD;;EAED,QAAIsB,OAAO,CAACM,aAAR,GAAwBC,SAAxB,CAAkCC,QAAlC,CAA2ClC,eAA3C,KAA+D0B,OAAO,CAACZ,WAAR,KAAwBb,gBAA3F,EAA6G;EAC3GyB,MAAAA,OAAO,CAACZ,WAAR,GAAsBb,gBAAtB;EACA;EACD;;EAEDmC,IAAAA,YAAY,CAACV,OAAO,CAACb,QAAT,CAAZ;EAEAa,IAAAA,OAAO,CAACZ,WAAR,GAAsBb,gBAAtB;;EAEA,QAAI,CAACyB,OAAO,CAACT,OAAR,CAAgBhD,KAAjB,IAA0B,CAACyD,OAAO,CAACT,OAAR,CAAgBhD,KAAhB,CAAsB0E,IAArD,EAA2D;EACzDjB,MAAAA,OAAO,CAACiB,IAAR;EACA;EACD;;EAEDjB,IAAAA,OAAO,CAACb,QAAR,GAAmB8F,UAAU,CAAC,MAAM;EAClC,UAAIjF,OAAO,CAACZ,WAAR,KAAwBb,gBAA5B,EAA8C;EAC5CyB,QAAAA,OAAO,CAACiB,IAAR;EACD;EACF,KAJ4B,EAI1BjB,OAAO,CAACT,OAAR,CAAgBhD,KAAhB,CAAsB0E,IAJI,CAA7B;EAKD;;EAEDZ,EAAAA,MAAM,CAACN,KAAD,EAAQC,OAAR,EAAiB;EACrBA,IAAAA,OAAO,GAAG,KAAKC,4BAAL,CAAkCF,KAAlC,EAAyCC,OAAzC,CAAV;;EAEA,QAAID,KAAJ,EAAW;EACTC,MAAAA,OAAO,CAACX,cAAR,CACEU,KAAK,CAACiF,IAAN,KAAe,UAAf,GAA4BrG,aAA5B,GAA4CD,aAD9C,IAEIsB,OAAO,CAACa,QAAR,CAAiBL,QAAjB,CAA0BT,KAAK,CAACmF,aAAhC,CAFJ;EAGD;;EAED,QAAIlF,OAAO,CAACG,oBAAR,EAAJ,EAAoC;EAClC;EACD;;EAEDO,IAAAA,YAAY,CAACV,OAAO,CAACb,QAAT,CAAZ;EAEAa,IAAAA,OAAO,CAACZ,WAAR,GAAsBZ,eAAtB;;EAEA,QAAI,CAACwB,OAAO,CAACT,OAAR,CAAgBhD,KAAjB,IAA0B,CAACyD,OAAO,CAACT,OAAR,CAAgBhD,KAAhB,CAAsBwG,IAArD,EAA2D;EACzD/C,MAAAA,OAAO,CAAC+C,IAAR;EACA;EACD;;EAED/C,IAAAA,OAAO,CAACb,QAAR,GAAmB8F,UAAU,CAAC,MAAM;EAClC,UAAIjF,OAAO,CAACZ,WAAR,KAAwBZ,eAA5B,EAA6C;EAC3CwB,QAAAA,OAAO,CAAC+C,IAAR;EACD;EACF,KAJ4B,EAI1B/C,OAAO,CAACT,OAAR,CAAgBhD,KAAhB,CAAsBwG,IAJI,CAA7B;EAKD;;EAED5C,EAAAA,oBAAoB,GAAG;EACrB,SAAK,MAAM7D,OAAX,IAAsB,KAAK+C,cAA3B,EAA2C;EACzC,UAAI,KAAKA,cAAL,CAAoB/C,OAApB,CAAJ,EAAkC;EAChC,eAAO,IAAP;EACD;EACF;;EAED,WAAO,KAAP;EACD;;EAEDkD,EAAAA,UAAU,CAAC5K,MAAD,EAAS;EACjB,UAAMuQ,cAAc,GAAGC,+BAAW,CAACC,iBAAZ,CAA8B,KAAKxE,QAAnC,CAAvB;EAEA/L,IAAAA,MAAM,CAACC,IAAP,CAAYoQ,cAAZ,EAA4BnQ,OAA5B,CAAoCsQ,QAAQ,IAAI;EAC9C,UAAIrJ,qBAAqB,CAAC7D,GAAtB,CAA0BkN,QAA1B,CAAJ,EAAyC;EACvC,eAAOH,cAAc,CAACG,QAAD,CAArB;EACD;EACF,KAJD;EAMA1Q,IAAAA,MAAM,GAAG,EACP,GAAG,KAAKoK,WAAL,CAAiBxB,OADb;EAEP,SAAG2H,cAFI;EAGP,UAAI,OAAOvQ,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHO,KAAT;EAMAA,IAAAA,MAAM,CAACgI,SAAP,GAAmBhI,MAAM,CAACgI,SAAP,KAAqB,KAArB,GAA6B3I,QAAQ,CAACoC,IAAtC,GAA6C/B,UAAU,CAACM,MAAM,CAACgI,SAAR,CAA1E;;EAEA,QAAI,OAAOhI,MAAM,CAAC2H,KAAd,KAAwB,QAA5B,EAAsC;EACpC3H,MAAAA,MAAM,CAAC2H,KAAP,GAAe;EACb0E,QAAAA,IAAI,EAAErM,MAAM,CAAC2H,KADA;EAEbwG,QAAAA,IAAI,EAAEnO,MAAM,CAAC2H;EAFA,OAAf;EAID;;EAED,QAAI,OAAO3H,MAAM,CAACyH,KAAd,KAAwB,QAA5B,EAAsC;EACpCzH,MAAAA,MAAM,CAACyH,KAAP,GAAezH,MAAM,CAACyH,KAAP,CAAa7I,QAAb,EAAf;EACD;;EAED,QAAI,OAAOoB,MAAM,CAACyO,OAAd,KAA0B,QAA9B,EAAwC;EACtCzO,MAAAA,MAAM,CAACyO,OAAP,GAAiBzO,MAAM,CAACyO,OAAP,CAAe7P,QAAf,EAAjB;EACD;;EAEDkB,IAAAA,eAAe,CAACyC,IAAD,EAAOvC,MAAP,EAAe,KAAKoK,WAAL,CAAiB9C,WAAhC,CAAf;;EAEA,QAAItH,MAAM,CAACoI,QAAX,EAAqB;EACnBpI,MAAAA,MAAM,CAACwH,QAAP,GAAkB3B,YAAY,CAAC7F,MAAM,CAACwH,QAAR,EAAkBxH,MAAM,CAAC+F,SAAzB,EAAoC/F,MAAM,CAACgG,UAA3C,CAA9B;EACD;;EAED,WAAOhG,MAAP;EACD;;EAEDgP,EAAAA,kBAAkB,GAAG;EACnB,UAAMhP,MAAM,GAAG,EAAf;;EAEA,QAAI,KAAK2K,OAAT,EAAkB;EAChB,WAAK,MAAMgG,GAAX,IAAkB,KAAKhG,OAAvB,EAAgC;EAC9B,YAAI,KAAKP,WAAL,CAAiBxB,OAAjB,CAAyB+H,GAAzB,MAAkC,KAAKhG,OAAL,CAAagG,GAAb,CAAtC,EAAyD;EACvD3Q,UAAAA,MAAM,CAAC2Q,GAAD,CAAN,GAAc,KAAKhG,OAAL,CAAagG,GAAb,CAAd;EACD;EACF;EACF;;EAED,WAAO3Q,MAAP;EACD;;EAEDoO,EAAAA,cAAc,GAAG;EACf,UAAMvD,GAAG,GAAG,KAAKa,aAAL,EAAZ;EACA,UAAMkF,QAAQ,GAAG/F,GAAG,CAAC8D,YAAJ,CAAiB,OAAjB,EAA0B7P,KAA1B,CAAgCsI,kBAAhC,CAAjB;;EACA,QAAIwJ,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACjR,MAAT,GAAkB,CAA3C,EAA8C;EAC5CiR,MAAAA,QAAQ,CAAC1B,GAAT,CAAa2B,KAAK,IAAIA,KAAK,CAACC,IAAN,EAAtB,EACG1Q,OADH,CACW2Q,MAAM,IAAIlG,GAAG,CAACc,SAAJ,CAAchF,MAAd,CAAqBoK,MAArB,CADrB;EAED;EACF;;EAEDlB,EAAAA,4BAA4B,CAACP,UAAD,EAAa;EACvC,UAAM;EAAE0B,MAAAA;EAAF,QAAY1B,UAAlB;;EAEA,QAAI,CAAC0B,KAAL,EAAY;EACV;EACD;;EAED,SAAKnG,GAAL,GAAWmG,KAAK,CAAC1K,QAAN,CAAe2K,MAA1B;;EACA,SAAK7C,cAAL;;EACA,SAAKf,mBAAL,CAAyB,KAAKD,cAAL,CAAoB4D,KAAK,CAAClJ,SAA1B,CAAzB;EACD,GAhlBiC;;;EAolBZ,SAAfpF,eAAe,CAAC1C,MAAD,EAAS;EAC7B,WAAO,KAAKkR,IAAL,CAAU,YAAY;EAC3B,YAAMtB,IAAI,GAAG1F,OAAO,CAACiH,mBAAR,CAA4B,IAA5B,EAAkCnR,MAAlC,CAAb;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO4P,IAAI,CAAC5P,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED4P,QAAAA,IAAI,CAAC5P,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;EAhmBiC;EAmmBpC;EACA;EACA;EACA;EACA;EACA;;;EAEAmC,kBAAkB,CAAC+H,OAAD,CAAlB;;;;;;;;"}
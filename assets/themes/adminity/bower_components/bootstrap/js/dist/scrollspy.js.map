{"version": 3, "file": "scrollspy.js", "sources": ["../src/util/index.js", "../src/scrollspy.js"], "sourcesContent": ["import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n"], "names": ["MAX_UID", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "querySelector", "isElement", "j<PERSON>y", "nodeType", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "hasAttribute", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "length", "addEventListener", "push", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "<PERSON><PERSON><PERSON>", "offset", "method", "target", "DefaultType", "EVENT_ACTIVATE", "EVENT_SCROLL", "EVENT_LOAD_DATA_API", "CLASS_NAME_DROPDOWN_ITEM", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "BaseComponent", "constructor", "_scrollElement", "_element", "tagName", "_config", "_getConfig", "_selector", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "EventHandler", "on", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "SelectorEngine", "find", "map", "targetSelector", "findOne", "targetBCR", "getBoundingClientRect", "width", "height", "Manipulator", "top", "filter", "item", "sort", "a", "b", "dispose", "off", "getDataAttributes", "id", "pageYOffset", "scrollTop", "scrollHeight", "max", "documentElement", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "i", "isActiveTarget", "queries", "link", "join", "classList", "contains", "closest", "add", "parents", "listGroup", "prev", "navItem", "children", "trigger", "relatedTarget", "node", "remove", "each", "data", "getOrCreateInstance", "spy"], "mappings": ";;;;;;;;;;;;;;;;;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMA,OAAO,GAAG,OAAhB;;EAKA,MAAMC,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;EAQA;EACA;EACA;EACA;EACA;;;EAEA,MAAMC,MAAM,GAAGC,MAAM,IAAI;EACvB,KAAG;EACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBZ,OAA3B,CAAV;EACD,GAFD,QAESa,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT;;EAIA,SAAOA,MAAP;EACD,CAND;;EAQA,MAAMM,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAyBA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;;EAEA,MAAIC,QAAJ,EAAc;EACZ,WAAOJ,QAAQ,CAACY,aAAT,CAAuBR,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EA2CA,MAAMS,SAAS,GAAGxB,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAACyB,MAAX,KAAsB,WAA1B,EAAuC;EACrCzB,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAAC0B,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAwBA,MAAMC,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAIZ,SAAS,CAACY,KAAD,CAAlB,GAA4B,SAA5B,GAAwCrC,MAAM,CAACqC,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EAiEA,MAAMO,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAAChC,QAAQ,CAACkC,IAAT,CAAcC,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOH,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMI,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAItC,QAAQ,CAACuC,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAACI,MAA/B,EAAuC;EACrCxC,MAAAA,QAAQ,CAACyC,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDL,QAAAA,yBAAyB,CAACd,OAA1B,CAAkCgB,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACM,IAA1B,CAA+BJ,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAiBA,MAAMK,kBAAkB,GAAGC,MAAM,IAAI;EACnCP,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMQ,CAAC,GAAGd,SAAS,EAAnB;EACA;;EACA,QAAIc,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;EC9NA;EACA;EACA;EACA;EACA;EACA;EAcA;EACA;EACA;EACA;EACA;;EAEA,MAAMH,IAAI,GAAG,WAAb;EACA,MAAMM,QAAQ,GAAG,cAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAME,YAAY,GAAG,WAArB;EAEA,MAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,EADM;EAEdC,EAAAA,MAAM,EAAE,MAFM;EAGdC,EAAAA,MAAM,EAAE;EAHM,CAAhB;EAMA,MAAMC,WAAW,GAAG;EAClBH,EAAAA,MAAM,EAAE,QADU;EAElBC,EAAAA,MAAM,EAAE,QAFU;EAGlBC,EAAAA,MAAM,EAAE;EAHU,CAApB;EAMA,MAAME,cAAc,GAAI,WAAUP,SAAU,EAA5C;EACA,MAAMQ,YAAY,GAAI,SAAQR,SAAU,EAAxC;EACA,MAAMS,mBAAmB,GAAI,OAAMT,SAAU,GAAEC,YAAa,EAA5D;EAEA,MAAMS,wBAAwB,GAAG,eAAjC;EACA,MAAMC,iBAAiB,GAAG,QAA1B;EAEA,MAAMC,iBAAiB,GAAG,wBAA1B;EACA,MAAMC,uBAAuB,GAAG,mBAAhC;EACA,MAAMC,kBAAkB,GAAG,WAA3B;EACA,MAAMC,kBAAkB,GAAG,WAA3B;EACA,MAAMC,mBAAmB,GAAG,kBAA5B;EACA,MAAMC,iBAAiB,GAAG,WAA1B;EACA,MAAMC,wBAAwB,GAAG,kBAAjC;EAEA,MAAMC,aAAa,GAAG,QAAtB;EACA,MAAMC,eAAe,GAAG,UAAxB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,SAAN,SAAwBC,iCAAxB,CAAsC;EACpCC,EAAAA,WAAW,CAAC1E,OAAD,EAAUe,MAAV,EAAkB;EAC3B,UAAMf,OAAN;EACA,SAAK2E,cAAL,GAAsB,KAAKC,QAAL,CAAcC,OAAd,KAA0B,MAA1B,GAAmC/C,MAAnC,GAA4C,KAAK8C,QAAvE;EACA,SAAKE,OAAL,GAAe,KAAKC,UAAL,CAAgBhE,MAAhB,CAAf;EACA,SAAKiE,SAAL,GAAkB,GAAE,KAAKF,OAAL,CAAatB,MAAO,IAAGS,kBAAmB,KAAI,KAAKa,OAAL,CAAatB,MAAO,IAAGW,mBAAoB,KAAI,KAAKW,OAAL,CAAatB,MAAO,KAAIK,wBAAyB,EAAlK;EACA,SAAKoB,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,CAArB;EAEAC,IAAAA,gCAAY,CAACC,EAAb,CAAgB,KAAKX,cAArB,EAAqChB,YAArC,EAAmD,MAAM,KAAK4B,QAAL,EAAzD;EAEA,SAAKC,OAAL;;EACA,SAAKD,QAAL;EACD,GAfmC;;;EAmBlB,aAAPlC,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEc,aAAJT,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD,GAzBmC;;;EA6BpC4C,EAAAA,OAAO,GAAG;EACR,UAAMC,UAAU,GAAG,KAAKd,cAAL,KAAwB,KAAKA,cAAL,CAAoB7C,MAA5C,GACjBwC,aADiB,GAEjBC,eAFF;EAIA,UAAMmB,YAAY,GAAG,KAAKZ,OAAL,CAAavB,MAAb,KAAwB,MAAxB,GACnBkC,UADmB,GAEnB,KAAKX,OAAL,CAAavB,MAFf;EAIA,UAAMoC,UAAU,GAAGD,YAAY,KAAKnB,eAAjB,GACjB,KAAKqB,aAAL,EADiB,GAEjB,CAFF;EAIA,SAAKX,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKE,aAAL,GAAqB,KAAKS,gBAAL,EAArB;EAEA,UAAMC,OAAO,GAAGC,kCAAc,CAACC,IAAf,CAAoB,KAAKhB,SAAzB,CAAhB;EAEAc,IAAAA,OAAO,CAACG,GAAR,CAAYjG,OAAO,IAAI;EACrB,YAAMkG,cAAc,GAAG1F,sBAAsB,CAACR,OAAD,CAA7C;EACA,YAAMwD,MAAM,GAAG0C,cAAc,GAAGH,kCAAc,CAACI,OAAf,CAAuBD,cAAvB,CAAH,GAA4C,IAAzE;;EAEA,UAAI1C,MAAJ,EAAY;EACV,cAAM4C,SAAS,GAAG5C,MAAM,CAAC6C,qBAAP,EAAlB;;EACA,YAAID,SAAS,CAACE,KAAV,IAAmBF,SAAS,CAACG,MAAjC,EAAyC;EACvC,iBAAO,CACLC,+BAAW,CAACd,YAAD,CAAX,CAA0BlC,MAA1B,EAAkCiD,GAAlC,GAAwCd,UADnC,EAELO,cAFK,CAAP;EAID;EACF;;EAED,aAAO,IAAP;EACD,KAfD,EAgBGQ,MAhBH,CAgBUC,IAAI,IAAIA,IAhBlB,EAiBGC,IAjBH,CAiBQ,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAC,CAAC,CAAD,CAjB1B,EAkBG3F,OAlBH,CAkBWwF,IAAI,IAAI;EACf,WAAK1B,QAAL,CAAc1C,IAAd,CAAmBoE,IAAI,CAAC,CAAD,CAAvB;;EACA,WAAKzB,QAAL,CAAc3C,IAAd,CAAmBoE,IAAI,CAAC,CAAD,CAAvB;EACD,KArBH;EAsBD;;EAEDI,EAAAA,OAAO,GAAG;EACR1B,IAAAA,gCAAY,CAAC2B,GAAb,CAAiB,KAAKrC,cAAtB,EAAsCxB,SAAtC;EACA,UAAM4D,OAAN;EACD,GA3EmC;;;EA+EpChC,EAAAA,UAAU,CAAChE,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGsC,OADI;EAEP,SAAGmD,+BAAW,CAACS,iBAAZ,CAA8B,KAAKrC,QAAnC,CAFI;EAGP,UAAI,OAAO7D,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHO,KAAT;;EAMA,QAAI,OAAOA,MAAM,CAACyC,MAAd,KAAyB,QAAzB,IAAqC9C,SAAS,CAACK,MAAM,CAACyC,MAAR,CAAlD,EAAmE;EACjE,UAAI;EAAE0D,QAAAA;EAAF,UAASnG,MAAM,CAACyC,MAApB;;EACA,UAAI,CAAC0D,EAAL,EAAS;EACPA,QAAAA,EAAE,GAAG1H,MAAM,CAACoD,IAAD,CAAX;EACA7B,QAAAA,MAAM,CAACyC,MAAP,CAAc0D,EAAd,GAAmBA,EAAnB;EACD;;EAEDnG,MAAAA,MAAM,CAACyC,MAAP,GAAiB,IAAG0D,EAAG,EAAvB;EACD;;EAEDrG,IAAAA,eAAe,CAAC+B,IAAD,EAAO7B,MAAP,EAAe0C,WAAf,CAAf;EAEA,WAAO1C,MAAP;EACD;;EAED6E,EAAAA,aAAa,GAAG;EACd,WAAO,KAAKjB,cAAL,KAAwB7C,MAAxB,GACL,KAAK6C,cAAL,CAAoBwC,WADf,GAEL,KAAKxC,cAAL,CAAoByC,SAFtB;EAGD;;EAEDvB,EAAAA,gBAAgB,GAAG;EACjB,WAAO,KAAKlB,cAAL,CAAoB0C,YAApB,IAAoC3H,IAAI,CAAC4H,GAAL,CACzCzH,QAAQ,CAACkC,IAAT,CAAcsF,YAD2B,EAEzCxH,QAAQ,CAAC0H,eAAT,CAAyBF,YAFgB,CAA3C;EAID;;EAEDG,EAAAA,gBAAgB,GAAG;EACjB,WAAO,KAAK7C,cAAL,KAAwB7C,MAAxB,GACLA,MAAM,CAAC2F,WADF,GAEL,KAAK9C,cAAL,CAAoB0B,qBAApB,GAA4CE,MAF9C;EAGD;;EAEDhB,EAAAA,QAAQ,GAAG;EACT,UAAM6B,SAAS,GAAG,KAAKxB,aAAL,KAAuB,KAAKd,OAAL,CAAaxB,MAAtD;;EACA,UAAM+D,YAAY,GAAG,KAAKxB,gBAAL,EAArB;;EACA,UAAM6B,SAAS,GAAG,KAAK5C,OAAL,CAAaxB,MAAb,GAAsB+D,YAAtB,GAAqC,KAAKG,gBAAL,EAAvD;;EAEA,QAAI,KAAKpC,aAAL,KAAuBiC,YAA3B,EAAyC;EACvC,WAAK7B,OAAL;EACD;;EAED,QAAI4B,SAAS,IAAIM,SAAjB,EAA4B;EAC1B,YAAMlE,MAAM,GAAG,KAAK0B,QAAL,CAAc,KAAKA,QAAL,CAAc7C,MAAd,GAAuB,CAArC,CAAf;;EAEA,UAAI,KAAK8C,aAAL,KAAuB3B,MAA3B,EAAmC;EACjC,aAAKmE,SAAL,CAAenE,MAAf;EACD;;EAED;EACD;;EAED,QAAI,KAAK2B,aAAL,IAAsBiC,SAAS,GAAG,KAAKnC,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;EAC9E,WAAKE,aAAL,GAAqB,IAArB;;EACA,WAAKyC,MAAL;;EACA;EACD;;EAED,SAAK,IAAIC,CAAC,GAAG,KAAK5C,QAAL,CAAc5C,MAA3B,EAAmCwF,CAAC,EAApC,GAAyC;EACvC,YAAMC,cAAc,GAAG,KAAK3C,aAAL,KAAuB,KAAKD,QAAL,CAAc2C,CAAd,CAAvB,IACnBT,SAAS,IAAI,KAAKnC,QAAL,CAAc4C,CAAd,CADM,KAElB,OAAO,KAAK5C,QAAL,CAAc4C,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IAA+CT,SAAS,GAAG,KAAKnC,QAAL,CAAc4C,CAAC,GAAG,CAAlB,CAFzC,CAAvB;;EAIA,UAAIC,cAAJ,EAAoB;EAClB,aAAKH,SAAL,CAAe,KAAKzC,QAAL,CAAc2C,CAAd,CAAf;EACD;EACF;EACF;;EAEDF,EAAAA,SAAS,CAACnE,MAAD,EAAS;EAChB,SAAK2B,aAAL,GAAqB3B,MAArB;;EAEA,SAAKoE,MAAL;;EAEA,UAAMG,OAAO,GAAG,KAAK/C,SAAL,CAAe1E,KAAf,CAAqB,GAArB,EACb2F,GADa,CACThG,QAAQ,IAAK,GAAEA,QAAS,oBAAmBuD,MAAO,MAAKvD,QAAS,UAASuD,MAAO,IADvE,CAAhB;;EAGA,UAAMwE,IAAI,GAAGjC,kCAAc,CAACI,OAAf,CAAuB4B,OAAO,CAACE,IAAR,CAAa,GAAb,CAAvB,CAAb;;EAEA,QAAID,IAAI,CAACE,SAAL,CAAeC,QAAf,CAAwBtE,wBAAxB,CAAJ,EAAuD;EACrDkC,MAAAA,kCAAc,CAACI,OAAf,CAAuB9B,wBAAvB,EAAiD2D,IAAI,CAACI,OAAL,CAAahE,iBAAb,CAAjD,EACG8D,SADH,CACaG,GADb,CACiBvE,iBADjB;EAGAkE,MAAAA,IAAI,CAACE,SAAL,CAAeG,GAAf,CAAmBvE,iBAAnB;EACD,KALD,MAKO;EACL;EACAkE,MAAAA,IAAI,CAACE,SAAL,CAAeG,GAAf,CAAmBvE,iBAAnB;EAEAiC,MAAAA,kCAAc,CAACuC,OAAf,CAAuBN,IAAvB,EAA6BhE,uBAA7B,EACG7C,OADH,CACWoH,SAAS,IAAI;EACpB;EACA;EACAxC,QAAAA,kCAAc,CAACyC,IAAf,CAAoBD,SAApB,EAAgC,GAAEtE,kBAAmB,KAAIE,mBAAoB,EAA7E,EACGhD,OADH,CACWwF,IAAI,IAAIA,IAAI,CAACuB,SAAL,CAAeG,GAAf,CAAmBvE,iBAAnB,CADnB,EAHoB;;EAOpBiC,QAAAA,kCAAc,CAACyC,IAAf,CAAoBD,SAApB,EAA+BrE,kBAA/B,EACG/C,OADH,CACWsH,OAAO,IAAI;EAClB1C,UAAAA,kCAAc,CAAC2C,QAAf,CAAwBD,OAAxB,EAAiCxE,kBAAjC,EACG9C,OADH,CACWwF,IAAI,IAAIA,IAAI,CAACuB,SAAL,CAAeG,GAAf,CAAmBvE,iBAAnB,CADnB;EAED,SAJH;EAKD,OAbH;EAcD;;EAEDuB,IAAAA,gCAAY,CAACsD,OAAb,CAAqB,KAAKhE,cAA1B,EAA0CjB,cAA1C,EAA0D;EACxDkF,MAAAA,aAAa,EAAEpF;EADyC,KAA1D;EAGD;;EAEDoE,EAAAA,MAAM,GAAG;EACP7B,IAAAA,kCAAc,CAACC,IAAf,CAAoB,KAAKhB,SAAzB,EACG0B,MADH,CACUmC,IAAI,IAAIA,IAAI,CAACX,SAAL,CAAeC,QAAf,CAAwBrE,iBAAxB,CADlB,EAEG3C,OAFH,CAEW0H,IAAI,IAAIA,IAAI,CAACX,SAAL,CAAeY,MAAf,CAAsBhF,iBAAtB,CAFnB;EAGD,GAxMmC;;;EA4Md,SAAff,eAAe,CAAChC,MAAD,EAAS;EAC7B,WAAO,KAAKgI,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGxE,SAAS,CAACyE,mBAAV,CAA8B,IAA9B,EAAoClI,MAApC,CAAb;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B;EACD;;EAED,UAAI,OAAOiI,IAAI,CAACjI,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAEDiI,MAAAA,IAAI,CAACjI,MAAD,CAAJ;EACD,KAZM,CAAP;EAaD;;EA1NmC;EA6NtC;EACA;EACA;EACA;EACA;;;AAEAsE,kCAAY,CAACC,EAAb,CAAgBxD,MAAhB,EAAwB8B,mBAAxB,EAA6C,MAAM;EACjDmC,EAAAA,kCAAc,CAACC,IAAf,CAAoBjC,iBAApB,EACG5C,OADH,CACW+H,GAAG,IAAI,IAAI1E,SAAJ,CAAc0E,GAAd,CADlB;EAED,CAHD;EAKA;EACA;EACA;EACA;EACA;EACA;;EAEA1G,kBAAkB,CAACgC,SAAD,CAAlB;;;;;;;;"}
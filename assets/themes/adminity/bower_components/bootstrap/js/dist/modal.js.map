{"version": 3, "file": "modal.js", "sources": ["../src/util/index.js", "../src/util/scrollbar.js", "../src/util/backdrop.js", "../src/modal.js"], "sourcesContent": ["import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, 'paddingRight', calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  }\n\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProp)\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  }\n\n  _saveInitialAttribute(element, styleProp) {\n    const actualValue = element.style[styleProp]\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined') {\n        element.style.removeProperty(styleProp)\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n    } else {\n      SelectorEngine.find(selector, this._element).forEach(callBack)\n    }\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = CLASS_NAME_BACKDROP\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.appendChild(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._isShown = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event && ['A', 'AREA'].includes(event.target.tagName)) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    this._queueCallback(() => this._hideModal(), this._element, isAnimated)\n  }\n\n  dispose() {\n    [window, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    this._backdrop.dispose()\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, isAnimated)\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _showBackdrop(callback) {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (this._ignoreBackdropClick) {\n        this._ignoreBackdropClick = false\n        return\n      }\n\n      if (event.target !== event.currentTarget) {\n        return\n      }\n\n      if (this._config.backdrop === true) {\n        this.hide()\n      } else if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n      }\n    })\n\n    this._backdrop.show(callback)\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const { classList, scrollHeight, style } = this._element\n    const isModalOverflowing = scrollHeight > document.documentElement.clientHeight\n\n    // return if the following background transition hasn't yet completed\n    if ((!isModalOverflowing && style.overflowY === 'hidden') || classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      style.overflowY = 'hidden'\n    }\n\n    classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        this._queueCallback(() => {\n          style.overflowY = ''\n        }, this._dialog)\n      }\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if ((!isBodyOverflowing && isModalOverflowing && !isRTL()) || (isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${scrollbarWidth}px`\n    }\n\n    if ((isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n"], "names": ["MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getElementFromSelector", "document", "querySelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "SelectorEngine", "findOne", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "getClientRects", "getPropertyValue", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "isRTL", "documentElement", "dir", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "durationPadding", "emulatedDuration", "called", "handler", "target", "removeEventListener", "setTimeout", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "ScrollBarHelper", "constructor", "_element", "getWidth", "documentWidth", "clientWidth", "Math", "abs", "innerWidth", "hide", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_saveInitialAttribute", "style", "overflow", "styleProp", "scrollbarWidth", "manipulationCallBack", "_applyManipulationCallback", "reset", "_resetElementAttributes", "actualValue", "Manipulator", "setDataAttribute", "getDataAttribute", "removeProperty", "removeDataAttribute", "callBack", "find", "isOverflowing", "<PERSON><PERSON><PERSON>", "isAnimated", "rootElement", "clickCallback", "DefaultType", "CLASS_NAME_BACKDROP", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "EVENT_MOUSEDOWN", "Backdrop", "_config", "_getConfig", "_isAppended", "show", "_append", "_getElement", "classList", "add", "_emulateAnimation", "remove", "dispose", "backdrop", "createElement", "className", "append<PERSON><PERSON><PERSON>", "EventHandler", "on", "off", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "ESCAPE_KEY", "keyboard", "focus", "EVENT_HIDE", "EVENT_HIDE_PREVENTED", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_CLICK_DATA_API", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_TOGGLE", "SELECTOR_DATA_DISMISS", "Modal", "BaseComponent", "_dialog", "_backdrop", "_initializeBackDrop", "_isShown", "_ignoreBackdropClick", "_isTransitioning", "_scrollBar", "toggle", "relatedTarget", "showEvent", "trigger", "defaultPrevented", "_isAnimated", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "event", "one", "_showBackdrop", "_showElement", "tagName", "preventDefault", "hideEvent", "_queueCallback", "_hideModal", "htmlElement", "handleUpdate", "Boolean", "getDataAttributes", "modalBody", "parentNode", "Node", "ELEMENT_NODE", "display", "removeAttribute", "setAttribute", "scrollTop", "_enforceFocus", "transitionComplete", "contains", "key", "_triggerBackdropTransition", "_resetAdjustments", "currentTarget", "scrollHeight", "isModalOverflowing", "clientHeight", "overflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "each", "data", "getOrCreateInstance"], "mappings": ";;;;;;;;;;;;;;;;;;EAUA,MAAMA,uBAAuB,GAAG,IAAhC;EACA,MAAMC,cAAc,GAAG,eAAvB;;EAGA,MAAMC,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;;EAsBA,MAAMC,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAmCA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAMA,MAAMU,gCAAgC,GAAGX,OAAO,IAAI;EAClD,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,CAAP;EACD,GAHiD;;;EAMlD,MAAI;EAAEY,IAAAA,kBAAF;EAAsBC,IAAAA;EAAtB,MAA0CC,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,CAA9C;EAEA,QAAMgB,uBAAuB,GAAGC,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,CAAhC;EACA,QAAMO,oBAAoB,GAAGF,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAA7B,CATkD;;EAYlD,MAAI,CAACG,uBAAD,IAA4B,CAACG,oBAAjC,EAAuD;EACrD,WAAO,CAAP;EACD,GAdiD;;;EAiBlDP,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACN,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;EACAO,EAAAA,eAAe,GAAGA,eAAe,CAACP,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;EAEA,SAAO,CAACW,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,IAAwCK,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAAzC,IAA+EvB,uBAAtF;EACD,CArBD;;EAuBA,MAAM8B,oBAAoB,GAAGpB,OAAO,IAAI;EACtCA,EAAAA,OAAO,CAACqB,aAAR,CAAsB,IAAIC,KAAJ,CAAU/B,cAAV,CAAtB;EACD,CAFD;;EAIA,MAAMgC,SAAS,GAAG9B,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAAC+B,MAAX,KAAsB,WAA1B,EAAuC;EACrC/B,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACgC,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAYA,MAAMC,UAAU,GAAGjC,GAAG,IAAI;EACxB,MAAI8B,SAAS,CAAC9B,GAAD,CAAb,EAAoB;EAAE;EACpB,WAAOA,GAAG,CAAC+B,MAAJ,GAAa/B,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;EACD;;EAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACkC,MAAJ,GAAa,CAA5C,EAA+C;EAC7C,WAAOC,kCAAc,CAACC,OAAf,CAAuBpC,GAAvB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVD;;EAYA,MAAMqC,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAIhB,SAAS,CAACgB,KAAD,CAAlB,GAA4B,SAA5B,GAAwC/C,MAAM,CAAC+C,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EAcA,MAAMO,SAAS,GAAG7C,OAAO,IAAI;EAC3B,MAAI,CAACuB,SAAS,CAACvB,OAAD,CAAV,IAAuBA,OAAO,CAAC8C,cAAR,GAAyBnB,MAAzB,KAAoC,CAA/D,EAAkE;EAChE,WAAO,KAAP;EACD;;EAED,SAAOZ,gBAAgB,CAACf,OAAD,CAAhB,CAA0B+C,gBAA1B,CAA2C,YAA3C,MAA6D,SAApE;EACD,CAND;;EAiDA,MAAMC,MAAM,GAAGhD,OAAO,IAAIA,OAAO,CAACiD,YAAlC;;EAEA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAarC,MAAnB;;EAEA,MAAIqC,MAAM,IAAI,CAAC1C,QAAQ,CAAC2C,IAAT,CAAcC,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOF,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMG,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAI/C,QAAQ,CAACgD,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAAC3B,MAA/B,EAAuC;EACrClB,MAAAA,QAAQ,CAACiD,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDJ,QAAAA,yBAAyB,CAAClB,OAA1B,CAAkCoB,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACK,IAA1B,CAA+BH,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAeA,MAAMI,KAAK,GAAG,MAAMnD,QAAQ,CAACoD,eAAT,CAAyBC,GAAzB,KAAiC,KAArD;;EAEA,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;EACnCT,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMU,CAAC,GAAGf,SAAS,EAAnB;EACA;;EACA,QAAIe,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;EAiBA,MAAMG,OAAO,GAAGjB,QAAQ,IAAI;EAC1B,MAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;EAClCA,IAAAA,QAAQ;EACT;EACF,CAJD;;EAMA,MAAMkB,sBAAsB,GAAG,CAAClB,QAAD,EAAWmB,iBAAX,EAA8BC,iBAAiB,GAAG,IAAlD,KAA2D;EACxF,MAAI,CAACA,iBAAL,EAAwB;EACtBH,IAAAA,OAAO,CAACjB,QAAD,CAAP;EACA;EACD;;EAED,QAAMqB,eAAe,GAAG,CAAxB;EACA,QAAMC,gBAAgB,GAAGnE,gCAAgC,CAACgE,iBAAD,CAAhC,GAAsDE,eAA/E;EAEA,MAAIE,MAAM,GAAG,KAAb;;EAEA,QAAMC,OAAO,GAAG,CAAC;EAAEC,IAAAA;EAAF,GAAD,KAAgB;EAC9B,QAAIA,MAAM,KAAKN,iBAAf,EAAkC;EAChC;EACD;;EAEDI,IAAAA,MAAM,GAAG,IAAT;EACAJ,IAAAA,iBAAiB,CAACO,mBAAlB,CAAsC3F,cAAtC,EAAsDyF,OAAtD;EACAP,IAAAA,OAAO,CAACjB,QAAD,CAAP;EACD,GARD;;EAUAmB,EAAAA,iBAAiB,CAACjB,gBAAlB,CAAmCnE,cAAnC,EAAmDyF,OAAnD;EACAG,EAAAA,UAAU,CAAC,MAAM;EACf,QAAI,CAACJ,MAAL,EAAa;EACX3D,MAAAA,oBAAoB,CAACuD,iBAAD,CAApB;EACD;EACF,GAJS,EAIPG,gBAJO,CAAV;EAKD,CA3BD;;ECrPA;EACA;EACA;EACA;EACA;EACA;EAMA,MAAMM,sBAAsB,GAAG,mDAA/B;EACA,MAAMC,uBAAuB,GAAG,aAAhC;;EAEA,MAAMC,eAAN,CAAsB;EACpBC,EAAAA,WAAW,GAAG;EACZ,SAAKC,QAAL,GAAgB/E,QAAQ,CAAC2C,IAAzB;EACD;;EAEDqC,EAAAA,QAAQ,GAAG;EACT;EACA,UAAMC,aAAa,GAAGjF,QAAQ,CAACoD,eAAT,CAAyB8B,WAA/C;EACA,WAAOC,IAAI,CAACC,GAAL,CAAS/E,MAAM,CAACgF,UAAP,GAAoBJ,aAA7B,CAAP;EACD;;EAEDK,EAAAA,IAAI,GAAG;EACL,UAAMC,KAAK,GAAG,KAAKP,QAAL,EAAd;;EACA,SAAKQ,gBAAL,GAFK;;;EAIL,SAAKC,qBAAL,CAA2B,KAAKV,QAAhC,EAA0C,cAA1C,EAA0DW,eAAe,IAAIA,eAAe,GAAGH,KAA/F,EAJK;;;EAML,SAAKE,qBAAL,CAA2Bd,sBAA3B,EAAmD,cAAnD,EAAmEe,eAAe,IAAIA,eAAe,GAAGH,KAAxG;;EACA,SAAKE,qBAAL,CAA2Bb,uBAA3B,EAAoD,aAApD,EAAmEc,eAAe,IAAIA,eAAe,GAAGH,KAAxG;EACD;;EAEDC,EAAAA,gBAAgB,GAAG;EACjB,SAAKG,qBAAL,CAA2B,KAAKZ,QAAhC,EAA0C,UAA1C;;EACA,SAAKA,QAAL,CAAca,KAAd,CAAoBC,QAApB,GAA+B,QAA/B;EACD;;EAEDJ,EAAAA,qBAAqB,CAACjG,QAAD,EAAWsG,SAAX,EAAsB/C,QAAtB,EAAgC;EACnD,UAAMgD,cAAc,GAAG,KAAKf,QAAL,EAAvB;;EACA,UAAMgB,oBAAoB,GAAGzG,OAAO,IAAI;EACtC,UAAIA,OAAO,KAAK,KAAKwF,QAAjB,IAA6B1E,MAAM,CAACgF,UAAP,GAAoB9F,OAAO,CAAC2F,WAAR,GAAsBa,cAA3E,EAA2F;EACzF;EACD;;EAED,WAAKJ,qBAAL,CAA2BpG,OAA3B,EAAoCuG,SAApC;;EACA,YAAMJ,eAAe,GAAGrF,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,EAAiCuG,SAAjC,CAAxB;EACAvG,MAAAA,OAAO,CAACqG,KAAR,CAAcE,SAAd,IAA4B,GAAE/C,QAAQ,CAACvC,MAAM,CAACC,UAAP,CAAkBiF,eAAlB,CAAD,CAAqC,IAA3E;EACD,KARD;;EAUA,SAAKO,0BAAL,CAAgCzG,QAAhC,EAA0CwG,oBAA1C;EACD;;EAEDE,EAAAA,KAAK,GAAG;EACN,SAAKC,uBAAL,CAA6B,KAAKpB,QAAlC,EAA4C,UAA5C;;EACA,SAAKoB,uBAAL,CAA6B,KAAKpB,QAAlC,EAA4C,cAA5C;;EACA,SAAKoB,uBAAL,CAA6BxB,sBAA7B,EAAqD,cAArD;;EACA,SAAKwB,uBAAL,CAA6BvB,uBAA7B,EAAsD,aAAtD;EACD;;EAEDe,EAAAA,qBAAqB,CAACpG,OAAD,EAAUuG,SAAV,EAAqB;EACxC,UAAMM,WAAW,GAAG7G,OAAO,CAACqG,KAAR,CAAcE,SAAd,CAApB;;EACA,QAAIM,WAAJ,EAAiB;EACfC,MAAAA,+BAAW,CAACC,gBAAZ,CAA6B/G,OAA7B,EAAsCuG,SAAtC,EAAiDM,WAAjD;EACD;EACF;;EAEDD,EAAAA,uBAAuB,CAAC3G,QAAD,EAAWsG,SAAX,EAAsB;EAC3C,UAAME,oBAAoB,GAAGzG,OAAO,IAAI;EACtC,YAAMuC,KAAK,GAAGuE,+BAAW,CAACE,gBAAZ,CAA6BhH,OAA7B,EAAsCuG,SAAtC,CAAd;;EACA,UAAI,OAAOhE,KAAP,KAAiB,WAArB,EAAkC;EAChCvC,QAAAA,OAAO,CAACqG,KAAR,CAAcY,cAAd,CAA6BV,SAA7B;EACD,OAFD,MAEO;EACLO,QAAAA,+BAAW,CAACI,mBAAZ,CAAgClH,OAAhC,EAAyCuG,SAAzC;EACAvG,QAAAA,OAAO,CAACqG,KAAR,CAAcE,SAAd,IAA2BhE,KAA3B;EACD;EACF,KARD;;EAUA,SAAKmE,0BAAL,CAAgCzG,QAAhC,EAA0CwG,oBAA1C;EACD;;EAEDC,EAAAA,0BAA0B,CAACzG,QAAD,EAAWkH,QAAX,EAAqB;EAC7C,QAAI5F,SAAS,CAACtB,QAAD,CAAb,EAAyB;EACvBkH,MAAAA,QAAQ,CAAClH,QAAD,CAAR;EACD,KAFD,MAEO;EACL2B,MAAAA,kCAAc,CAACwF,IAAf,CAAoBnH,QAApB,EAA8B,KAAKuF,QAAnC,EAA6CpD,OAA7C,CAAqD+E,QAArD;EACD;EACF;;EAEDE,EAAAA,aAAa,GAAG;EACd,WAAO,KAAK5B,QAAL,KAAkB,CAAzB;EACD;;EA/EmB;;ECdtB;EACA;EACA;EACA;EACA;EACA;EAKA,MAAM6B,SAAO,GAAG;EACdzE,EAAAA,SAAS,EAAE,IADG;EACG;EACjB0E,EAAAA,UAAU,EAAE,KAFE;EAGdC,EAAAA,WAAW,EAAE,MAHC;EAGO;EACrBC,EAAAA,aAAa,EAAE;EAJD,CAAhB;EAOA,MAAMC,aAAW,GAAG;EAClB7E,EAAAA,SAAS,EAAE,SADO;EAElB0E,EAAAA,UAAU,EAAE,SAFM;EAGlBC,EAAAA,WAAW,EAAE,kBAHK;EAIlBC,EAAAA,aAAa,EAAE;EAJG,CAApB;EAMA,MAAMtD,MAAI,GAAG,UAAb;EACA,MAAMwD,mBAAmB,GAAG,gBAA5B;EACA,MAAMC,iBAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EAEA,MAAMC,eAAe,GAAI,gBAAe3D,MAAK,EAA7C;;EAEA,MAAM4D,QAAN,CAAe;EACbxC,EAAAA,WAAW,CAACvD,MAAD,EAAS;EAClB,SAAKgG,OAAL,GAAe,KAAKC,UAAL,CAAgBjG,MAAhB,CAAf;EACA,SAAKkG,WAAL,GAAmB,KAAnB;EACA,SAAK1C,QAAL,GAAgB,IAAhB;EACD;;EAED2C,EAAAA,IAAI,CAAC3E,QAAD,EAAW;EACb,QAAI,CAAC,KAAKwE,OAAL,CAAanF,SAAlB,EAA6B;EAC3B4B,MAAAA,OAAO,CAACjB,QAAD,CAAP;EACA;EACD;;EAED,SAAK4E,OAAL;;EAEA,QAAI,KAAKJ,OAAL,CAAaT,UAAjB,EAA6B;EAC3BvE,MAAAA,MAAM,CAAC,KAAKqF,WAAL,EAAD,CAAN;EACD;;EAED,SAAKA,WAAL,GAAmBC,SAAnB,CAA6BC,GAA7B,CAAiCV,iBAAjC;;EAEA,SAAKW,iBAAL,CAAuB,MAAM;EAC3B/D,MAAAA,OAAO,CAACjB,QAAD,CAAP;EACD,KAFD;EAGD;;EAEDuC,EAAAA,IAAI,CAACvC,QAAD,EAAW;EACb,QAAI,CAAC,KAAKwE,OAAL,CAAanF,SAAlB,EAA6B;EAC3B4B,MAAAA,OAAO,CAACjB,QAAD,CAAP;EACA;EACD;;EAED,SAAK6E,WAAL,GAAmBC,SAAnB,CAA6BG,MAA7B,CAAoCZ,iBAApC;;EAEA,SAAKW,iBAAL,CAAuB,MAAM;EAC3B,WAAKE,OAAL;EACAjE,MAAAA,OAAO,CAACjB,QAAD,CAAP;EACD,KAHD;EAID,GAtCY;;;EA0Cb6E,EAAAA,WAAW,GAAG;EACZ,QAAI,CAAC,KAAK7C,QAAV,EAAoB;EAClB,YAAMmD,QAAQ,GAAGlI,QAAQ,CAACmI,aAAT,CAAuB,KAAvB,CAAjB;EACAD,MAAAA,QAAQ,CAACE,SAAT,GAAqBlB,mBAArB;;EACA,UAAI,KAAKK,OAAL,CAAaT,UAAjB,EAA6B;EAC3BoB,QAAAA,QAAQ,CAACL,SAAT,CAAmBC,GAAnB,CAAuBX,iBAAvB;EACD;;EAED,WAAKpC,QAAL,GAAgBmD,QAAhB;EACD;;EAED,WAAO,KAAKnD,QAAZ;EACD;;EAEDyC,EAAAA,UAAU,CAACjG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGsF,SADI;EAEP,UAAI,OAAOtF,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAFO,KAAT,CADiB;;EAOjBA,IAAAA,MAAM,CAACwF,WAAP,GAAqB9F,UAAU,CAACM,MAAM,CAACwF,WAAR,CAA/B;EACA1F,IAAAA,eAAe,CAACqC,MAAD,EAAOnC,MAAP,EAAe0F,aAAf,CAAf;EACA,WAAO1F,MAAP;EACD;;EAEDoG,EAAAA,OAAO,GAAG;EACR,QAAI,KAAKF,WAAT,EAAsB;EACpB;EACD;;EAED,SAAKF,OAAL,CAAaR,WAAb,CAAyBsB,WAAzB,CAAqC,KAAKT,WAAL,EAArC;;EAEAU,IAAAA,gCAAY,CAACC,EAAb,CAAgB,KAAKX,WAAL,EAAhB,EAAoCP,eAApC,EAAqD,MAAM;EACzDrD,MAAAA,OAAO,CAAC,KAAKuD,OAAL,CAAaP,aAAd,CAAP;EACD,KAFD;EAIA,SAAKS,WAAL,GAAmB,IAAnB;EACD;;EAEDQ,EAAAA,OAAO,GAAG;EACR,QAAI,CAAC,KAAKR,WAAV,EAAuB;EACrB;EACD;;EAEDa,IAAAA,gCAAY,CAACE,GAAb,CAAiB,KAAKzD,QAAtB,EAAgCsC,eAAhC;;EAEA,SAAKtC,QAAL,CAAciD,MAAd;;EACA,SAAKP,WAAL,GAAmB,KAAnB;EACD;;EAEDM,EAAAA,iBAAiB,CAAChF,QAAD,EAAW;EAC1BkB,IAAAA,sBAAsB,CAAClB,QAAD,EAAW,KAAK6E,WAAL,EAAX,EAA+B,KAAKL,OAAL,CAAaT,UAA5C,CAAtB;EACD;;EA/FY;;EC9Bf;EACA;EACA;EACA;EACA;EACA;EAiBA;EACA;EACA;EACA;EACA;;EAEA,MAAMpD,IAAI,GAAG,OAAb;EACA,MAAM+E,QAAQ,GAAG,UAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAME,YAAY,GAAG,WAArB;EACA,MAAMC,UAAU,GAAG,QAAnB;EAEA,MAAM/B,OAAO,GAAG;EACdqB,EAAAA,QAAQ,EAAE,IADI;EAEdW,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA,MAAM7B,WAAW,GAAG;EAClBiB,EAAAA,QAAQ,EAAE,kBADQ;EAElBW,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,MAAMC,UAAU,GAAI,OAAML,SAAU,EAApC;EACA,MAAMM,oBAAoB,GAAI,gBAAeN,SAAU,EAAvD;EACA,MAAMO,YAAY,GAAI,SAAQP,SAAU,EAAxC;EACA,MAAMQ,UAAU,GAAI,OAAMR,SAAU,EAApC;EACA,MAAMS,WAAW,GAAI,QAAOT,SAAU,EAAtC;EACA,MAAMU,aAAa,GAAI,UAASV,SAAU,EAA1C;EACA,MAAMW,YAAY,GAAI,SAAQX,SAAU,EAAxC;EACA,MAAMY,mBAAmB,GAAI,gBAAeZ,SAAU,EAAtD;EACA,MAAMa,qBAAqB,GAAI,kBAAiBb,SAAU,EAA1D;EACA,MAAMc,qBAAqB,GAAI,kBAAiBd,SAAU,EAA1D;EACA,MAAMe,uBAAuB,GAAI,oBAAmBf,SAAU,EAA9D;EACA,MAAMgB,oBAAoB,GAAI,QAAOhB,SAAU,GAAEC,YAAa,EAA9D;EAEA,MAAMgB,eAAe,GAAG,YAAxB;EACA,MAAMxC,eAAe,GAAG,MAAxB;EACA,MAAMC,eAAe,GAAG,MAAxB;EACA,MAAMwC,iBAAiB,GAAG,cAA1B;EAEA,MAAMC,eAAe,GAAG,eAAxB;EACA,MAAMC,mBAAmB,GAAG,aAA5B;EACA,MAAMC,oBAAoB,GAAG,0BAA7B;EACA,MAAMC,qBAAqB,GAAG,2BAA9B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,KAAN,SAAoBC,iCAApB,CAAkC;EAChCpF,EAAAA,WAAW,CAACvF,OAAD,EAAUgC,MAAV,EAAkB;EAC3B,UAAMhC,OAAN;EAEA,SAAKgI,OAAL,GAAe,KAAKC,UAAL,CAAgBjG,MAAhB,CAAf;EACA,SAAK4I,OAAL,GAAehJ,kCAAc,CAACC,OAAf,CAAuByI,eAAvB,EAAwC,KAAK9E,QAA7C,CAAf;EACA,SAAKqF,SAAL,GAAiB,KAAKC,mBAAL,EAAjB;EACA,SAAKC,QAAL,GAAgB,KAAhB;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAKC,gBAAL,GAAwB,KAAxB;EACA,SAAKC,UAAL,GAAkB,IAAI5F,eAAJ,EAAlB;EACD,GAX+B;;;EAed,aAAPgC,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEc,aAAJnD,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD,GArB+B;;;EAyBhCgH,EAAAA,MAAM,CAACC,aAAD,EAAgB;EACpB,WAAO,KAAKL,QAAL,GAAgB,KAAKhF,IAAL,EAAhB,GAA8B,KAAKoC,IAAL,CAAUiD,aAAV,CAArC;EACD;;EAEDjD,EAAAA,IAAI,CAACiD,aAAD,EAAgB;EAClB,QAAI,KAAKL,QAAL,IAAiB,KAAKE,gBAA1B,EAA4C;EAC1C;EACD;;EAED,UAAMI,SAAS,GAAGtC,gCAAY,CAACuC,OAAb,CAAqB,KAAK9F,QAA1B,EAAoCmE,UAApC,EAAgD;EAChEyB,MAAAA;EADgE,KAAhD,CAAlB;;EAIA,QAAIC,SAAS,CAACE,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKR,QAAL,GAAgB,IAAhB;;EAEA,QAAI,KAAKS,WAAL,EAAJ,EAAwB;EACtB,WAAKP,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKC,UAAL,CAAgBnF,IAAhB;;EAEAtF,IAAAA,QAAQ,CAAC2C,IAAT,CAAckF,SAAd,CAAwBC,GAAxB,CAA4B6B,eAA5B;;EAEA,SAAKqB,aAAL;;EAEA,SAAKC,eAAL;;EACA,SAAKC,eAAL;;EAEA5C,IAAAA,gCAAY,CAACC,EAAb,CAAgB,KAAKxD,QAArB,EAA+BuE,mBAA/B,EAAoDU,qBAApD,EAA2EmB,KAAK,IAAI,KAAK7F,IAAL,CAAU6F,KAAV,CAApF;EAEA7C,IAAAA,gCAAY,CAACC,EAAb,CAAgB,KAAK4B,OAArB,EAA8BV,uBAA9B,EAAuD,MAAM;EAC3DnB,MAAAA,gCAAY,CAAC8C,GAAb,CAAiB,KAAKrG,QAAtB,EAAgCyE,qBAAhC,EAAuD2B,KAAK,IAAI;EAC9D,YAAIA,KAAK,CAAC3G,MAAN,KAAiB,KAAKO,QAA1B,EAAoC;EAClC,eAAKwF,oBAAL,GAA4B,IAA5B;EACD;EACF,OAJD;EAKD,KAND;;EAQA,SAAKc,aAAL,CAAmB,MAAM,KAAKC,YAAL,CAAkBX,aAAlB,CAAzB;EACD;;EAEDrF,EAAAA,IAAI,CAAC6F,KAAD,EAAQ;EACV,QAAIA,KAAK,IAAI,CAAC,GAAD,EAAM,MAAN,EAAcxL,QAAd,CAAuBwL,KAAK,CAAC3G,MAAN,CAAa+G,OAApC,CAAb,EAA2D;EACzDJ,MAAAA,KAAK,CAACK,cAAN;EACD;;EAED,QAAI,CAAC,KAAKlB,QAAN,IAAkB,KAAKE,gBAA3B,EAA6C;EAC3C;EACD;;EAED,UAAMiB,SAAS,GAAGnD,gCAAY,CAACuC,OAAb,CAAqB,KAAK9F,QAA1B,EAAoCgE,UAApC,CAAlB;;EAEA,QAAI0C,SAAS,CAACX,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKR,QAAL,GAAgB,KAAhB;;EACA,UAAMxD,UAAU,GAAG,KAAKiE,WAAL,EAAnB;;EAEA,QAAIjE,UAAJ,EAAgB;EACd,WAAK0D,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKS,eAAL;;EACA,SAAKC,eAAL;;EAEA5C,IAAAA,gCAAY,CAACE,GAAb,CAAiBxI,QAAjB,EAA2BoJ,aAA3B;;EAEA,SAAKrE,QAAL,CAAc8C,SAAd,CAAwBG,MAAxB,CAA+BZ,eAA/B;;EAEAkB,IAAAA,gCAAY,CAACE,GAAb,CAAiB,KAAKzD,QAAtB,EAAgCuE,mBAAhC;EACAhB,IAAAA,gCAAY,CAACE,GAAb,CAAiB,KAAK2B,OAAtB,EAA+BV,uBAA/B;;EAEA,SAAKiC,cAAL,CAAoB,MAAM,KAAKC,UAAL,EAA1B,EAA6C,KAAK5G,QAAlD,EAA4D+B,UAA5D;EACD;;EAEDmB,EAAAA,OAAO,GAAG;EACR,KAAC5H,MAAD,EAAS,KAAK8J,OAAd,EACGxI,OADH,CACWiK,WAAW,IAAItD,gCAAY,CAACE,GAAb,CAAiBoD,WAAjB,EAA8BlD,SAA9B,CAD1B;;EAGA,SAAK0B,SAAL,CAAenC,OAAf;;EACA,UAAMA,OAAN;EAEA;EACJ;EACA;EACA;EACA;;EACIK,IAAAA,gCAAY,CAACE,GAAb,CAAiBxI,QAAjB,EAA2BoJ,aAA3B;EACD;;EAEDyC,EAAAA,YAAY,GAAG;EACb,SAAKb,aAAL;EACD,GA1H+B;;;EA8HhCX,EAAAA,mBAAmB,GAAG;EACpB,WAAO,IAAI/C,QAAJ,CAAa;EAClBlF,MAAAA,SAAS,EAAE0J,OAAO,CAAC,KAAKvE,OAAL,CAAaW,QAAd,CADA;EACyB;EAC3CpB,MAAAA,UAAU,EAAE,KAAKiE,WAAL;EAFM,KAAb,CAAP;EAID;;EAEDvD,EAAAA,UAAU,CAACjG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGsF,OADI;EAEP,SAAGR,+BAAW,CAAC0F,iBAAZ,CAA8B,KAAKhH,QAAnC,CAFI;EAGP,UAAI,OAAOxD,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAHO,KAAT;EAKAF,IAAAA,eAAe,CAACqC,IAAD,EAAOnC,MAAP,EAAe0F,WAAf,CAAf;EACA,WAAO1F,MAAP;EACD;;EAED+J,EAAAA,YAAY,CAACX,aAAD,EAAgB;EAC1B,UAAM7D,UAAU,GAAG,KAAKiE,WAAL,EAAnB;;EACA,UAAMiB,SAAS,GAAG7K,kCAAc,CAACC,OAAf,CAAuB0I,mBAAvB,EAA4C,KAAKK,OAAjD,CAAlB;;EAEA,QAAI,CAAC,KAAKpF,QAAL,CAAckH,UAAf,IAA6B,KAAKlH,QAAL,CAAckH,UAAd,CAAyBjL,QAAzB,KAAsCkL,IAAI,CAACC,YAA5E,EAA0F;EACxF;EACAnM,MAAAA,QAAQ,CAAC2C,IAAT,CAAc0F,WAAd,CAA0B,KAAKtD,QAA/B;EACD;;EAED,SAAKA,QAAL,CAAca,KAAd,CAAoBwG,OAApB,GAA8B,OAA9B;;EACA,SAAKrH,QAAL,CAAcsH,eAAd,CAA8B,aAA9B;;EACA,SAAKtH,QAAL,CAAcuH,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAKvH,QAAL,CAAcuH,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EACA,SAAKvH,QAAL,CAAcwH,SAAd,GAA0B,CAA1B;;EAEA,QAAIP,SAAJ,EAAe;EACbA,MAAAA,SAAS,CAACO,SAAV,GAAsB,CAAtB;EACD;;EAED,QAAIzF,UAAJ,EAAgB;EACdvE,MAAAA,MAAM,CAAC,KAAKwC,QAAN,CAAN;EACD;;EAED,SAAKA,QAAL,CAAc8C,SAAd,CAAwBC,GAAxB,CAA4BV,eAA5B;;EAEA,QAAI,KAAKG,OAAL,CAAauB,KAAjB,EAAwB;EACtB,WAAK0D,aAAL;EACD;;EAED,UAAMC,kBAAkB,GAAG,MAAM;EAC/B,UAAI,KAAKlF,OAAL,CAAauB,KAAjB,EAAwB;EACtB,aAAK/D,QAAL,CAAc+D,KAAd;EACD;;EAED,WAAK0B,gBAAL,GAAwB,KAAxB;EACAlC,MAAAA,gCAAY,CAACuC,OAAb,CAAqB,KAAK9F,QAA1B,EAAoCoE,WAApC,EAAiD;EAC/CwB,QAAAA;EAD+C,OAAjD;EAGD,KATD;;EAWA,SAAKe,cAAL,CAAoBe,kBAApB,EAAwC,KAAKtC,OAA7C,EAAsDrD,UAAtD;EACD;;EAED0F,EAAAA,aAAa,GAAG;EACdlE,IAAAA,gCAAY,CAACE,GAAb,CAAiBxI,QAAjB,EAA2BoJ,aAA3B,EADc;;EAEdd,IAAAA,gCAAY,CAACC,EAAb,CAAgBvI,QAAhB,EAA0BoJ,aAA1B,EAAyC+B,KAAK,IAAI;EAChD,UAAInL,QAAQ,KAAKmL,KAAK,CAAC3G,MAAnB,IACA,KAAKO,QAAL,KAAkBoG,KAAK,CAAC3G,MADxB,IAEA,CAAC,KAAKO,QAAL,CAAc2H,QAAd,CAAuBvB,KAAK,CAAC3G,MAA7B,CAFL,EAE2C;EACzC,aAAKO,QAAL,CAAc+D,KAAd;EACD;EACF,KAND;EAOD;;EAEDmC,EAAAA,eAAe,GAAG;EAChB,QAAI,KAAKX,QAAT,EAAmB;EACjBhC,MAAAA,gCAAY,CAACC,EAAb,CAAgB,KAAKxD,QAArB,EAA+BwE,qBAA/B,EAAsD4B,KAAK,IAAI;EAC7D,YAAI,KAAK5D,OAAL,CAAasB,QAAb,IAAyBsC,KAAK,CAACwB,GAAN,KAAc/D,UAA3C,EAAuD;EACrDuC,UAAAA,KAAK,CAACK,cAAN;EACA,eAAKlG,IAAL;EACD,SAHD,MAGO,IAAI,CAAC,KAAKiC,OAAL,CAAasB,QAAd,IAA0BsC,KAAK,CAACwB,GAAN,KAAc/D,UAA5C,EAAwD;EAC7D,eAAKgE,0BAAL;EACD;EACF,OAPD;EAQD,KATD,MASO;EACLtE,MAAAA,gCAAY,CAACE,GAAb,CAAiB,KAAKzD,QAAtB,EAAgCwE,qBAAhC;EACD;EACF;;EAED2B,EAAAA,eAAe,GAAG;EAChB,QAAI,KAAKZ,QAAT,EAAmB;EACjBhC,MAAAA,gCAAY,CAACC,EAAb,CAAgBlI,MAAhB,EAAwBgJ,YAAxB,EAAsC,MAAM,KAAK2B,aAAL,EAA5C;EACD,KAFD,MAEO;EACL1C,MAAAA,gCAAY,CAACE,GAAb,CAAiBnI,MAAjB,EAAyBgJ,YAAzB;EACD;EACF;;EAEDsC,EAAAA,UAAU,GAAG;EACX,SAAK5G,QAAL,CAAca,KAAd,CAAoBwG,OAApB,GAA8B,MAA9B;;EACA,SAAKrH,QAAL,CAAcuH,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,SAAKvH,QAAL,CAAcsH,eAAd,CAA8B,YAA9B;;EACA,SAAKtH,QAAL,CAAcsH,eAAd,CAA8B,MAA9B;;EACA,SAAK7B,gBAAL,GAAwB,KAAxB;;EACA,SAAKJ,SAAL,CAAe9E,IAAf,CAAoB,MAAM;EACxBtF,MAAAA,QAAQ,CAAC2C,IAAT,CAAckF,SAAd,CAAwBG,MAAxB,CAA+B2B,eAA/B;;EACA,WAAKkD,iBAAL;;EACA,WAAKpC,UAAL,CAAgBvE,KAAhB;;EACAoC,MAAAA,gCAAY,CAACuC,OAAb,CAAqB,KAAK9F,QAA1B,EAAoCkE,YAApC;EACD,KALD;EAMD;;EAEDoC,EAAAA,aAAa,CAACtI,QAAD,EAAW;EACtBuF,IAAAA,gCAAY,CAACC,EAAb,CAAgB,KAAKxD,QAArB,EAA+BuE,mBAA/B,EAAoD6B,KAAK,IAAI;EAC3D,UAAI,KAAKZ,oBAAT,EAA+B;EAC7B,aAAKA,oBAAL,GAA4B,KAA5B;EACA;EACD;;EAED,UAAIY,KAAK,CAAC3G,MAAN,KAAiB2G,KAAK,CAAC2B,aAA3B,EAA0C;EACxC;EACD;;EAED,UAAI,KAAKvF,OAAL,CAAaW,QAAb,KAA0B,IAA9B,EAAoC;EAClC,aAAK5C,IAAL;EACD,OAFD,MAEO,IAAI,KAAKiC,OAAL,CAAaW,QAAb,KAA0B,QAA9B,EAAwC;EAC7C,aAAK0E,0BAAL;EACD;EACF,KAfD;;EAiBA,SAAKxC,SAAL,CAAe1C,IAAf,CAAoB3E,QAApB;EACD;;EAEDgI,EAAAA,WAAW,GAAG;EACZ,WAAO,KAAKhG,QAAL,CAAc8C,SAAd,CAAwB6E,QAAxB,CAAiCvF,eAAjC,CAAP;EACD;;EAEDyF,EAAAA,0BAA0B,GAAG;EAC3B,UAAMnB,SAAS,GAAGnD,gCAAY,CAACuC,OAAb,CAAqB,KAAK9F,QAA1B,EAAoCiE,oBAApC,CAAlB;;EACA,QAAIyC,SAAS,CAACX,gBAAd,EAAgC;EAC9B;EACD;;EAED,UAAM;EAAEjD,MAAAA,SAAF;EAAakF,MAAAA,YAAb;EAA2BnH,MAAAA;EAA3B,QAAqC,KAAKb,QAAhD;EACA,UAAMiI,kBAAkB,GAAGD,YAAY,GAAG/M,QAAQ,CAACoD,eAAT,CAAyB6J,YAAnE,CAP2B;;EAU3B,QAAK,CAACD,kBAAD,IAAuBpH,KAAK,CAACsH,SAAN,KAAoB,QAA5C,IAAyDrF,SAAS,CAAC6E,QAAV,CAAmB9C,iBAAnB,CAA7D,EAAoG;EAClG;EACD;;EAED,QAAI,CAACoD,kBAAL,EAAyB;EACvBpH,MAAAA,KAAK,CAACsH,SAAN,GAAkB,QAAlB;EACD;;EAEDrF,IAAAA,SAAS,CAACC,GAAV,CAAc8B,iBAAd;;EACA,SAAK8B,cAAL,CAAoB,MAAM;EACxB7D,MAAAA,SAAS,CAACG,MAAV,CAAiB4B,iBAAjB;;EACA,UAAI,CAACoD,kBAAL,EAAyB;EACvB,aAAKtB,cAAL,CAAoB,MAAM;EACxB9F,UAAAA,KAAK,CAACsH,SAAN,GAAkB,EAAlB;EACD,SAFD,EAEG,KAAK/C,OAFR;EAGD;EACF,KAPD,EAOG,KAAKA,OAPR;;EASA,SAAKpF,QAAL,CAAc+D,KAAd;EACD,GAhS+B;EAmShC;EACA;;;EAEAkC,EAAAA,aAAa,GAAG;EACd,UAAMgC,kBAAkB,GAAG,KAAKjI,QAAL,CAAcgI,YAAd,GAA6B/M,QAAQ,CAACoD,eAAT,CAAyB6J,YAAjF;;EACA,UAAMlH,cAAc,GAAG,KAAK0E,UAAL,CAAgBzF,QAAhB,EAAvB;;EACA,UAAMmI,iBAAiB,GAAGpH,cAAc,GAAG,CAA3C;;EAEA,QAAK,CAACoH,iBAAD,IAAsBH,kBAAtB,IAA4C,CAAC7J,KAAK,EAAnD,IAA2DgK,iBAAiB,IAAI,CAACH,kBAAtB,IAA4C7J,KAAK,EAAhH,EAAqH;EACnH,WAAK4B,QAAL,CAAca,KAAd,CAAoBwH,WAApB,GAAmC,GAAErH,cAAe,IAApD;EACD;;EAED,QAAKoH,iBAAiB,IAAI,CAACH,kBAAtB,IAA4C,CAAC7J,KAAK,EAAnD,IAA2D,CAACgK,iBAAD,IAAsBH,kBAAtB,IAA4C7J,KAAK,EAAhH,EAAqH;EACnH,WAAK4B,QAAL,CAAca,KAAd,CAAoByH,YAApB,GAAoC,GAAEtH,cAAe,IAArD;EACD;EACF;;EAED8G,EAAAA,iBAAiB,GAAG;EAClB,SAAK9H,QAAL,CAAca,KAAd,CAAoBwH,WAApB,GAAkC,EAAlC;EACA,SAAKrI,QAAL,CAAca,KAAd,CAAoByH,YAApB,GAAmC,EAAnC;EACD,GAvT+B;;;EA2TV,SAAfxJ,eAAe,CAACtC,MAAD,EAASoJ,aAAT,EAAwB;EAC5C,WAAO,KAAK2C,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGtD,KAAK,CAACuD,mBAAN,CAA0B,IAA1B,EAAgCjM,MAAhC,CAAb;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B;EACD;;EAED,UAAI,OAAOgM,IAAI,CAAChM,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAEDgM,MAAAA,IAAI,CAAChM,MAAD,CAAJ,CAAaoJ,aAAb;EACD,KAZM,CAAP;EAaD;;EAzU+B;EA4UlC;EACA;EACA;EACA;EACA;;;AAEArC,kCAAY,CAACC,EAAb,CAAgBvI,QAAhB,EAA0B0J,oBAA1B,EAAgDK,oBAAhD,EAAsE,UAAUoB,KAAV,EAAiB;EACrF,QAAM3G,MAAM,GAAGzE,sBAAsB,CAAC,IAAD,CAArC;;EAEA,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcJ,QAAd,CAAuB,KAAK4L,OAA5B,CAAJ,EAA0C;EACxCJ,IAAAA,KAAK,CAACK,cAAN;EACD;;EAEDlD,EAAAA,gCAAY,CAAC8C,GAAb,CAAiB5G,MAAjB,EAAyB0E,UAAzB,EAAqC0B,SAAS,IAAI;EAChD,QAAIA,SAAS,CAACE,gBAAd,EAAgC;EAC9B;EACA;EACD;;EAEDxC,IAAAA,gCAAY,CAAC8C,GAAb,CAAiB5G,MAAjB,EAAyByE,YAAzB,EAAuC,MAAM;EAC3C,UAAI7G,SAAS,CAAC,IAAD,CAAb,EAAqB;EACnB,aAAK0G,KAAL;EACD;EACF,KAJD;EAKD,GAXD;EAaA,QAAMyE,IAAI,GAAGtD,KAAK,CAACuD,mBAAN,CAA0BhJ,MAA1B,CAAb;EAEA+I,EAAAA,IAAI,CAAC7C,MAAL,CAAY,IAAZ;EACD,CAvBD;EAyBA;EACA;EACA;EACA;EACA;EACA;;EAEApH,kBAAkB,CAAC2G,KAAD,CAAlB;;;;;;;;"}
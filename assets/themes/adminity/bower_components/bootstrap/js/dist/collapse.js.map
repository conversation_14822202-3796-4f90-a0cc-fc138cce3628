{"version": 3, "file": "collapse.js", "sources": ["../src/util/index.js", "../src/collapse.js"], "sourcesContent": ["import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  getElementFromSelector,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${this._element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${this._element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Collapse.getInstance(tempActiveData) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.set(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    parent = getElement(parent)\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Collapse.getInstance(element)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Collapse.getInstance(element)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n"], "names": ["toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "document", "querySelector", "getElementFromSelector", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "SelectorEngine", "findOne", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "hasAttribute", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "<PERSON><PERSON><PERSON>", "toggle", "parent", "DefaultType", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_CLICK_DATA_API", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "SELECTOR_DATA_TOGGLE", "Collapse", "BaseComponent", "constructor", "_isTransitioning", "_config", "_getConfig", "_triggerArray", "find", "_element", "id", "toggleList", "i", "len", "elem", "filterElement", "filter", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "classList", "contains", "hide", "show", "actives", "activesData", "container", "tempActiveData", "getInstance", "startEvent", "EventHandler", "trigger", "defaultPrevented", "elemActive", "collapseInterface", "Data", "set", "dimension", "_getDimension", "remove", "add", "style", "setAttribute", "setTransitioning", "complete", "capitalizedDimension", "slice", "scrollSize", "_queueCallback", "getBoundingClientRect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "Boolean", "selected", "trigger<PERSON><PERSON>y", "isOpen", "data", "Manipulator", "getDataAttributes", "each", "on", "event", "target", "tagName", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "triggerData", "selectorElements"], "mappings": ";;;;;;;;;;;;;;;;;;;EAcA,MAAMA,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;;EAsBA,MAAMC,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAyBA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;;EAEA,MAAIC,QAAJ,EAAc;EACZ,WAAOQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMU,sBAAsB,GAAGX,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAiCA,MAAMW,SAAS,GAAGnB,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAACoB,MAAX,KAAsB,WAA1B,EAAuC;EACrCpB,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACqB,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAYA,MAAMC,UAAU,GAAGtB,GAAG,IAAI;EACxB,MAAImB,SAAS,CAACnB,GAAD,CAAb,EAAoB;EAAE;EACpB,WAAOA,GAAG,CAACoB,MAAJ,GAAapB,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;EACD;;EAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACuB,MAAJ,GAAa,CAA5C,EAA+C;EAC7C,WAAOC,kCAAc,CAACC,OAAf,CAAuBzB,GAAvB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVD;;EAYA,MAAM0B,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAIhB,SAAS,CAACgB,KAAD,CAAlB,GAA4B,SAA5B,GAAwCpC,MAAM,CAACoC,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EA+DA,MAAMO,MAAM,GAAGlC,OAAO,IAAIA,OAAO,CAACmC,YAAlC;;EAEA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAAC5B,QAAQ,CAAC8B,IAAT,CAAcC,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOH,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMI,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAIlC,QAAQ,CAACmC,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAACzB,MAA/B,EAAuC;EACrCP,MAAAA,QAAQ,CAACoC,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDJ,QAAAA,yBAAyB,CAAChB,OAA1B,CAAkCkB,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACK,IAA1B,CAA+BH,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAiBA,MAAMI,kBAAkB,GAAGC,MAAM,IAAI;EACnCN,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMO,CAAC,GAAGb,SAAS,EAAnB;EACA;;EACA,QAAIa,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;EC9NA;EACA;EACA;EACA;EACA;EACA;EAgBA;EACA;EACA;EACA;EACA;;EAEA,MAAMH,IAAI,GAAG,UAAb;EACA,MAAMM,QAAQ,GAAG,aAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAME,YAAY,GAAG,WAArB;EAEA,MAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,IADM;EAEdC,EAAAA,MAAM,EAAE;EAFM,CAAhB;EAKA,MAAMC,WAAW,GAAG;EAClBF,EAAAA,MAAM,EAAE,SADU;EAElBC,EAAAA,MAAM,EAAE;EAFU,CAApB;EAKA,MAAME,UAAU,GAAI,OAAMN,SAAU,EAApC;EACA,MAAMO,WAAW,GAAI,QAAOP,SAAU,EAAtC;EACA,MAAMQ,UAAU,GAAI,OAAMR,SAAU,EAApC;EACA,MAAMS,YAAY,GAAI,SAAQT,SAAU,EAAxC;EACA,MAAMU,oBAAoB,GAAI,QAAOV,SAAU,GAAEC,YAAa,EAA9D;EAEA,MAAMU,eAAe,GAAG,MAAxB;EACA,MAAMC,mBAAmB,GAAG,UAA5B;EACA,MAAMC,qBAAqB,GAAG,YAA9B;EACA,MAAMC,oBAAoB,GAAG,WAA7B;EAEA,MAAMC,KAAK,GAAG,OAAd;EACA,MAAMC,MAAM,GAAG,QAAf;EAEA,MAAMC,gBAAgB,GAAG,oBAAzB;EACA,MAAMC,oBAAoB,GAAG,6BAA7B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,QAAN,SAAuBC,iCAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAAC/E,OAAD,EAAUqB,MAAV,EAAkB;EAC3B,UAAMrB,OAAN;EAEA,SAAKgF,gBAAL,GAAwB,KAAxB;EACA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgB7D,MAAhB,CAAf;EACA,SAAK8D,aAAL,GAAqBlE,kCAAc,CAACmE,IAAf,CAClB,GAAER,oBAAqB,WAAU,KAAKS,QAAL,CAAcC,EAAG,KAAnD,GACC,GAAEV,oBAAqB,qBAAoB,KAAKS,QAAL,CAAcC,EAAG,IAF1C,CAArB;EAKA,UAAMC,UAAU,GAAGtE,kCAAc,CAACmE,IAAf,CAAoBR,oBAApB,CAAnB;;EAEA,SAAK,IAAIY,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,UAAU,CAACvE,MAAjC,EAAyCwE,CAAC,GAAGC,GAA7C,EAAkDD,CAAC,EAAnD,EAAuD;EACrD,YAAME,IAAI,GAAGH,UAAU,CAACC,CAAD,CAAvB;EACA,YAAMvF,QAAQ,GAAGO,sBAAsB,CAACkF,IAAD,CAAvC;EACA,YAAMC,aAAa,GAAG1E,kCAAc,CAACmE,IAAf,CAAoBnF,QAApB,EACnB2F,MADmB,CACZC,SAAS,IAAIA,SAAS,KAAK,KAAKR,QADpB,CAAtB;;EAGA,UAAIpF,QAAQ,KAAK,IAAb,IAAqB0F,aAAa,CAAC3E,MAAvC,EAA+C;EAC7C,aAAK8E,SAAL,GAAiB7F,QAAjB;;EACA,aAAKkF,aAAL,CAAmBrC,IAAnB,CAAwB4C,IAAxB;EACD;EACF;;EAED,SAAKK,OAAL,GAAe,KAAKd,OAAL,CAAanB,MAAb,GAAsB,KAAKkC,UAAL,EAAtB,GAA0C,IAAzD;;EAEA,QAAI,CAAC,KAAKf,OAAL,CAAanB,MAAlB,EAA0B;EACxB,WAAKmC,yBAAL,CAA+B,KAAKZ,QAApC,EAA8C,KAAKF,aAAnD;EACD;;EAED,QAAI,KAAKF,OAAL,CAAapB,MAAjB,EAAyB;EACvB,WAAKA,MAAL;EACD;EACF,GAlCkC;;;EAsCjB,aAAPD,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEc,aAAJT,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD,GA5CkC;;;EAgDnCU,EAAAA,MAAM,GAAG;EACP,QAAI,KAAKwB,QAAL,CAAca,SAAd,CAAwBC,QAAxB,CAAiC9B,eAAjC,CAAJ,EAAuD;EACrD,WAAK+B,IAAL;EACD,KAFD,MAEO;EACL,WAAKC,IAAL;EACD;EACF;;EAEDA,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKrB,gBAAL,IAAyB,KAAKK,QAAL,CAAca,SAAd,CAAwBC,QAAxB,CAAiC9B,eAAjC,CAA7B,EAAgF;EAC9E;EACD;;EAED,QAAIiC,OAAJ;EACA,QAAIC,WAAJ;;EAEA,QAAI,KAAKR,OAAT,EAAkB;EAChBO,MAAAA,OAAO,GAAGrF,kCAAc,CAACmE,IAAf,CAAoBT,gBAApB,EAAsC,KAAKoB,OAA3C,EACPH,MADO,CACAF,IAAI,IAAI;EACd,YAAI,OAAO,KAAKT,OAAL,CAAanB,MAApB,KAA+B,QAAnC,EAA6C;EAC3C,iBAAO4B,IAAI,CAACxF,YAAL,CAAkB,gBAAlB,MAAwC,KAAK+E,OAAL,CAAanB,MAA5D;EACD;;EAED,eAAO4B,IAAI,CAACQ,SAAL,CAAeC,QAAf,CAAwB7B,mBAAxB,CAAP;EACD,OAPO,CAAV;;EASA,UAAIgC,OAAO,CAACtF,MAAR,KAAmB,CAAvB,EAA0B;EACxBsF,QAAAA,OAAO,GAAG,IAAV;EACD;EACF;;EAED,UAAME,SAAS,GAAGvF,kCAAc,CAACC,OAAf,CAAuB,KAAK4E,SAA5B,CAAlB;;EACA,QAAIQ,OAAJ,EAAa;EACX,YAAMG,cAAc,GAAGH,OAAO,CAAClB,IAAR,CAAaM,IAAI,IAAIc,SAAS,KAAKd,IAAnC,CAAvB;EACAa,MAAAA,WAAW,GAAGE,cAAc,GAAG5B,QAAQ,CAAC6B,WAAT,CAAqBD,cAArB,CAAH,GAA0C,IAAtE;;EAEA,UAAIF,WAAW,IAAIA,WAAW,CAACvB,gBAA/B,EAAiD;EAC/C;EACD;EACF;;EAED,UAAM2B,UAAU,GAAGC,gCAAY,CAACC,OAAb,CAAqB,KAAKxB,QAA1B,EAAoCrB,UAApC,CAAnB;;EACA,QAAI2C,UAAU,CAACG,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAIR,OAAJ,EAAa;EACXA,MAAAA,OAAO,CAAC7E,OAAR,CAAgBsF,UAAU,IAAI;EAC5B,YAAIP,SAAS,KAAKO,UAAlB,EAA8B;EAC5BlC,UAAAA,QAAQ,CAACmC,iBAAT,CAA2BD,UAA3B,EAAuC,MAAvC;EACD;;EAED,YAAI,CAACR,WAAL,EAAkB;EAChBU,UAAAA,wBAAI,CAACC,GAAL,CAASH,UAAT,EAAqBtD,QAArB,EAA+B,IAA/B;EACD;EACF,OARD;EASD;;EAED,UAAM0D,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAK/B,QAAL,CAAca,SAAd,CAAwBmB,MAAxB,CAA+B/C,mBAA/B;;EACA,SAAKe,QAAL,CAAca,SAAd,CAAwBoB,GAAxB,CAA4B/C,qBAA5B;;EAEA,SAAKc,QAAL,CAAckC,KAAd,CAAoBJ,SAApB,IAAiC,CAAjC;;EAEA,QAAI,KAAKhC,aAAL,CAAmBnE,MAAvB,EAA+B;EAC7B,WAAKmE,aAAL,CAAmB1D,OAAnB,CAA2BzB,OAAO,IAAI;EACpCA,QAAAA,OAAO,CAACkG,SAAR,CAAkBmB,MAAlB,CAAyB7C,oBAAzB;EACAxE,QAAAA,OAAO,CAACwH,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD,OAHD;EAID;;EAED,SAAKC,gBAAL,CAAsB,IAAtB;;EAEA,UAAMC,QAAQ,GAAG,MAAM;EACrB,WAAKrC,QAAL,CAAca,SAAd,CAAwBmB,MAAxB,CAA+B9C,qBAA/B;;EACA,WAAKc,QAAL,CAAca,SAAd,CAAwBoB,GAAxB,CAA4BhD,mBAA5B,EAAiDD,eAAjD;;EAEA,WAAKgB,QAAL,CAAckC,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC;EAEA,WAAKM,gBAAL,CAAsB,KAAtB;EAEAb,MAAAA,gCAAY,CAACC,OAAb,CAAqB,KAAKxB,QAA1B,EAAoCpB,WAApC;EACD,KATD;;EAWA,UAAM0D,oBAAoB,GAAGR,SAAS,CAAC,CAAD,CAAT,CAAalF,WAAb,KAA6BkF,SAAS,CAACS,KAAV,CAAgB,CAAhB,CAA1D;EACA,UAAMC,UAAU,GAAI,SAAQF,oBAAqB,EAAjD;;EAEA,SAAKG,cAAL,CAAoBJ,QAApB,EAA8B,KAAKrC,QAAnC,EAA6C,IAA7C;;EACA,SAAKA,QAAL,CAAckC,KAAd,CAAoBJ,SAApB,IAAkC,GAAE,KAAK9B,QAAL,CAAcwC,UAAd,CAA0B,IAA9D;EACD;;EAEDzB,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKpB,gBAAL,IAAyB,CAAC,KAAKK,QAAL,CAAca,SAAd,CAAwBC,QAAxB,CAAiC9B,eAAjC,CAA9B,EAAiF;EAC/E;EACD;;EAED,UAAMsC,UAAU,GAAGC,gCAAY,CAACC,OAAb,CAAqB,KAAKxB,QAA1B,EAAoCnB,UAApC,CAAnB;;EACA,QAAIyC,UAAU,CAACG,gBAAf,EAAiC;EAC/B;EACD;;EAED,UAAMK,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAK/B,QAAL,CAAckC,KAAd,CAAoBJ,SAApB,IAAkC,GAAE,KAAK9B,QAAL,CAAc0C,qBAAd,GAAsCZ,SAAtC,CAAiD,IAArF;EAEAjF,IAAAA,MAAM,CAAC,KAAKmD,QAAN,CAAN;;EAEA,SAAKA,QAAL,CAAca,SAAd,CAAwBoB,GAAxB,CAA4B/C,qBAA5B;;EACA,SAAKc,QAAL,CAAca,SAAd,CAAwBmB,MAAxB,CAA+B/C,mBAA/B,EAAoDD,eAApD;;EAEA,UAAM2D,kBAAkB,GAAG,KAAK7C,aAAL,CAAmBnE,MAA9C;;EACA,QAAIgH,kBAAkB,GAAG,CAAzB,EAA4B;EAC1B,WAAK,IAAIxC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwC,kBAApB,EAAwCxC,CAAC,EAAzC,EAA6C;EAC3C,cAAMqB,OAAO,GAAG,KAAK1B,aAAL,CAAmBK,CAAnB,CAAhB;EACA,cAAME,IAAI,GAAG/E,sBAAsB,CAACkG,OAAD,CAAnC;;EAEA,YAAInB,IAAI,IAAI,CAACA,IAAI,CAACQ,SAAL,CAAeC,QAAf,CAAwB9B,eAAxB,CAAb,EAAuD;EACrDwC,UAAAA,OAAO,CAACX,SAAR,CAAkBoB,GAAlB,CAAsB9C,oBAAtB;EACAqC,UAAAA,OAAO,CAACW,YAAR,CAAqB,eAArB,EAAsC,KAAtC;EACD;EACF;EACF;;EAED,SAAKC,gBAAL,CAAsB,IAAtB;;EAEA,UAAMC,QAAQ,GAAG,MAAM;EACrB,WAAKD,gBAAL,CAAsB,KAAtB;;EACA,WAAKpC,QAAL,CAAca,SAAd,CAAwBmB,MAAxB,CAA+B9C,qBAA/B;;EACA,WAAKc,QAAL,CAAca,SAAd,CAAwBoB,GAAxB,CAA4BhD,mBAA5B;;EACAsC,MAAAA,gCAAY,CAACC,OAAb,CAAqB,KAAKxB,QAA1B,EAAoClB,YAApC;EACD,KALD;;EAOA,SAAKkB,QAAL,CAAckC,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC;;EAEA,SAAKW,cAAL,CAAoBJ,QAApB,EAA8B,KAAKrC,QAAnC,EAA6C,IAA7C;EACD;;EAEDoC,EAAAA,gBAAgB,CAACQ,eAAD,EAAkB;EAChC,SAAKjD,gBAAL,GAAwBiD,eAAxB;EACD,GA5LkC;;;EAgMnC/C,EAAAA,UAAU,CAAC7D,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGuC,OADI;EAEP,SAAGvC;EAFI,KAAT;EAIAA,IAAAA,MAAM,CAACwC,MAAP,GAAgBqE,OAAO,CAAC7G,MAAM,CAACwC,MAAR,CAAvB,CALiB;;EAMjB1C,IAAAA,eAAe,CAACgC,IAAD,EAAO9B,MAAP,EAAe0C,WAAf,CAAf;EACA,WAAO1C,MAAP;EACD;;EAED+F,EAAAA,aAAa,GAAG;EACd,WAAO,KAAK/B,QAAL,CAAca,SAAd,CAAwBC,QAAxB,CAAiC1B,KAAjC,IAA0CA,KAA1C,GAAkDC,MAAzD;EACD;;EAEDsB,EAAAA,UAAU,GAAG;EACX,QAAI;EAAElC,MAAAA;EAAF,QAAa,KAAKmB,OAAtB;EAEAnB,IAAAA,MAAM,GAAG/C,UAAU,CAAC+C,MAAD,CAAnB;EAEA,UAAM7D,QAAQ,GAAI,GAAE2E,oBAAqB,oBAAmBd,MAAO,IAAnE;EAEA7C,IAAAA,kCAAc,CAACmE,IAAf,CAAoBnF,QAApB,EAA8B6D,MAA9B,EACGrC,OADH,CACWzB,OAAO,IAAI;EAClB,YAAMmI,QAAQ,GAAGxH,sBAAsB,CAACX,OAAD,CAAvC;;EAEA,WAAKiG,yBAAL,CACEkC,QADF,EAEE,CAACnI,OAAD,CAFF;EAID,KARH;EAUA,WAAO8D,MAAP;EACD;;EAEDmC,EAAAA,yBAAyB,CAACjG,OAAD,EAAUoI,YAAV,EAAwB;EAC/C,QAAI,CAACpI,OAAD,IAAY,CAACoI,YAAY,CAACpH,MAA9B,EAAsC;EACpC;EACD;;EAED,UAAMqH,MAAM,GAAGrI,OAAO,CAACkG,SAAR,CAAkBC,QAAlB,CAA2B9B,eAA3B,CAAf;EAEA+D,IAAAA,YAAY,CAAC3G,OAAb,CAAqBiE,IAAI,IAAI;EAC3B,UAAI2C,MAAJ,EAAY;EACV3C,QAAAA,IAAI,CAACQ,SAAL,CAAemB,MAAf,CAAsB7C,oBAAtB;EACD,OAFD,MAEO;EACLkB,QAAAA,IAAI,CAACQ,SAAL,CAAeoB,GAAf,CAAmB9C,oBAAnB;EACD;;EAEDkB,MAAAA,IAAI,CAAC8B,YAAL,CAAkB,eAAlB,EAAmCa,MAAnC;EACD,KARD;EASD,GAlPkC;;;EAsPX,SAAjBrB,iBAAiB,CAAChH,OAAD,EAAUqB,MAAV,EAAkB;EACxC,QAAIiH,IAAI,GAAGzD,QAAQ,CAAC6B,WAAT,CAAqB1G,OAArB,CAAX;EACA,UAAMiF,OAAO,GAAG,EACd,GAAGrB,OADW;EAEd,SAAG2E,+BAAW,CAACC,iBAAZ,CAA8BxI,OAA9B,CAFW;EAGd,UAAI,OAAOqB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHc,KAAhB;;EAMA,QAAI,CAACiH,IAAD,IAASrD,OAAO,CAACpB,MAAjB,IAA2B,OAAOxC,MAAP,KAAkB,QAA7C,IAAyD,YAAYU,IAAZ,CAAiBV,MAAjB,CAA7D,EAAuF;EACrF4D,MAAAA,OAAO,CAACpB,MAAR,GAAiB,KAAjB;EACD;;EAED,QAAI,CAACyE,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIzD,QAAJ,CAAa7E,OAAb,EAAsBiF,OAAtB,CAAP;EACD;;EAED,QAAI,OAAO5D,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAOiH,IAAI,CAACjH,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAEDiH,MAAAA,IAAI,CAACjH,MAAD,CAAJ;EACD;EACF;;EAEqB,SAAfiC,eAAe,CAACjC,MAAD,EAAS;EAC7B,WAAO,KAAKoH,IAAL,CAAU,YAAY;EAC3B5D,MAAAA,QAAQ,CAACmC,iBAAT,CAA2B,IAA3B,EAAiC3F,MAAjC;EACD,KAFM,CAAP;EAGD;;EAnRkC;EAsRrC;EACA;EACA;EACA;EACA;;;AAEAuF,kCAAY,CAAC8B,EAAb,CAAgBjI,QAAhB,EAA0B2D,oBAA1B,EAAgDQ,oBAAhD,EAAsE,UAAU+D,KAAV,EAAiB;EACrF;EACA,MAAIA,KAAK,CAACC,MAAN,CAAaC,OAAb,KAAyB,GAAzB,IAAiCF,KAAK,CAACG,cAAN,IAAwBH,KAAK,CAACG,cAAN,CAAqBD,OAArB,KAAiC,GAA9F,EAAoG;EAClGF,IAAAA,KAAK,CAACI,cAAN;EACD;;EAED,QAAMC,WAAW,GAAGT,+BAAW,CAACC,iBAAZ,CAA8B,IAA9B,CAApB;EACA,QAAMvI,QAAQ,GAAGO,sBAAsB,CAAC,IAAD,CAAvC;EACA,QAAMyI,gBAAgB,GAAGhI,kCAAc,CAACmE,IAAf,CAAoBnF,QAApB,CAAzB;EAEAgJ,EAAAA,gBAAgB,CAACxH,OAAjB,CAAyBzB,OAAO,IAAI;EAClC,UAAMsI,IAAI,GAAGzD,QAAQ,CAAC6B,WAAT,CAAqB1G,OAArB,CAAb;EACA,QAAIqB,MAAJ;;EACA,QAAIiH,IAAJ,EAAU;EACR;EACA,UAAIA,IAAI,CAACvC,OAAL,KAAiB,IAAjB,IAAyB,OAAOiD,WAAW,CAAClF,MAAnB,KAA8B,QAA3D,EAAqE;EACnEwE,QAAAA,IAAI,CAACrD,OAAL,CAAanB,MAAb,GAAsBkF,WAAW,CAAClF,MAAlC;EACAwE,QAAAA,IAAI,CAACvC,OAAL,GAAeuC,IAAI,CAACtC,UAAL,EAAf;EACD;;EAED3E,MAAAA,MAAM,GAAG,QAAT;EACD,KARD,MAQO;EACLA,MAAAA,MAAM,GAAG2H,WAAT;EACD;;EAEDnE,IAAAA,QAAQ,CAACmC,iBAAT,CAA2BhH,OAA3B,EAAoCqB,MAApC;EACD,GAhBD;EAiBD,CA3BD;EA6BA;EACA;EACA;EACA;EACA;EACA;;EAEA0B,kBAAkB,CAAC8B,QAAD,CAAlB;;;;;;;;"}
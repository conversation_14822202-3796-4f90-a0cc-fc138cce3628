{"version": 3, "file": "offcanvas.js", "sources": ["../src/util/index.js", "../src/util/scrollbar.js", "../src/util/backdrop.js", "../src/offcanvas.js"], "sourcesContent": ["import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, 'paddingRight', calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  }\n\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProp)\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  }\n\n  _saveInitialAttribute(element, styleProp) {\n    const actualValue = element.style[styleProp]\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined') {\n        element.style.removeProperty(styleProp)\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n    } else {\n      SelectorEngine.find(selector, this._element).forEach(callBack)\n    }\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = CLASS_NAME_BACKDROP\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.appendChild(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\nimport Backdrop from './util/backdrop'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_SHOW = 'show'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"offcanvas\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n      this._enforceFocusOnElement(this._element)\n    }\n\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    super.dispose()\n    EventHandler.off(document, EVENT_FOCUSIN)\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: this._config.backdrop,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: () => this.hide()\n    })\n  }\n\n  _enforceFocusOnElement(element) {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n        element !== event.target &&\n        !element.contains(event.target)) {\n        element.focus()\n      }\n    })\n    element.focus()\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    Offcanvas.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () =>\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => Offcanvas.getOrCreateInstance(el).show())\n)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n"], "names": ["MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getElementFromSelector", "document", "querySelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "SelectorEngine", "findOne", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "getClientRects", "getPropertyValue", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "durationPadding", "emulatedDuration", "called", "handler", "target", "removeEventListener", "setTimeout", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "ScrollBarHelper", "constructor", "_element", "getWidth", "documentWidth", "documentElement", "clientWidth", "Math", "abs", "innerWidth", "hide", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_saveInitialAttribute", "style", "overflow", "styleProp", "scrollbarWidth", "manipulationCallBack", "_applyManipulationCallback", "reset", "_resetElementAttributes", "actualValue", "Manipulator", "setDataAttribute", "getDataAttribute", "removeProperty", "removeDataAttribute", "callBack", "find", "isOverflowing", "<PERSON><PERSON><PERSON>", "isAnimated", "rootElement", "clickCallback", "DefaultType", "CLASS_NAME_BACKDROP", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "EVENT_MOUSEDOWN", "Backdrop", "_config", "_getConfig", "_isAppended", "show", "_append", "_getElement", "add", "_emulateAnimation", "remove", "dispose", "backdrop", "createElement", "className", "append<PERSON><PERSON><PERSON>", "EventHandler", "on", "off", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "EVENT_LOAD_DATA_API", "ESCAPE_KEY", "keyboard", "scroll", "OPEN_SELECTOR", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_FOCUSIN", "EVENT_CLICK_DATA_API", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "SELECTOR_DATA_DISMISS", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON><PERSON>", "BaseComponent", "_isShown", "_backdrop", "_initializeBackDrop", "_addEventListeners", "toggle", "relatedTarget", "showEvent", "trigger", "defaultPrevented", "visibility", "_enforceFocusOnElement", "removeAttribute", "setAttribute", "completeCallBack", "_queueCallback", "hideEvent", "blur", "completeCallback", "getDataAttributes", "parentNode", "event", "focus", "key", "each", "data", "getOrCreateInstance", "tagName", "preventDefault", "one", "allReadyOpen", "getInstance", "el"], "mappings": ";;;;;;;;;;;;;;;;;;EAUA,MAAMA,uBAAuB,GAAG,IAAhC;EACA,MAAMC,cAAc,GAAG,eAAvB;;EAGA,MAAMC,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;;EAsBA,MAAMC,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAI,IAAGA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAuB,EAAtC;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAmCA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGQ,QAAQ,CAACC,aAAT,CAAuBT,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAMA,MAAMU,gCAAgC,GAAGX,OAAO,IAAI;EAClD,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,CAAP;EACD,GAHiD;;;EAMlD,MAAI;EAAEY,IAAAA,kBAAF;EAAsBC,IAAAA;EAAtB,MAA0CC,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,CAA9C;EAEA,QAAMgB,uBAAuB,GAAGC,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,CAAhC;EACA,QAAMO,oBAAoB,GAAGF,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAA7B,CATkD;;EAYlD,MAAI,CAACG,uBAAD,IAA4B,CAACG,oBAAjC,EAAuD;EACrD,WAAO,CAAP;EACD,GAdiD;;;EAiBlDP,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACN,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;EACAO,EAAAA,eAAe,GAAGA,eAAe,CAACP,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;EAEA,SAAO,CAACW,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,IAAwCK,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAAzC,IAA+EvB,uBAAtF;EACD,CArBD;;EAuBA,MAAM8B,oBAAoB,GAAGpB,OAAO,IAAI;EACtCA,EAAAA,OAAO,CAACqB,aAAR,CAAsB,IAAIC,KAAJ,CAAU/B,cAAV,CAAtB;EACD,CAFD;;EAIA,MAAMgC,SAAS,GAAG9B,GAAG,IAAI;EACvB,MAAI,CAACA,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;EACnC,WAAO,KAAP;EACD;;EAED,MAAI,OAAOA,GAAG,CAAC+B,MAAX,KAAsB,WAA1B,EAAuC;EACrC/B,IAAAA,GAAG,GAAGA,GAAG,CAAC,CAAD,CAAT;EACD;;EAED,SAAO,OAAOA,GAAG,CAACgC,QAAX,KAAwB,WAA/B;EACD,CAVD;;EAYA,MAAMC,UAAU,GAAGjC,GAAG,IAAI;EACxB,MAAI8B,SAAS,CAAC9B,GAAD,CAAb,EAAoB;EAAE;EACpB,WAAOA,GAAG,CAAC+B,MAAJ,GAAa/B,GAAG,CAAC,CAAD,CAAhB,GAAsBA,GAA7B;EACD;;EAED,MAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,CAACkC,MAAJ,GAAa,CAA5C,EAA+C;EAC7C,WAAOC,kCAAc,CAACC,OAAf,CAAuBpC,GAAvB,CAAP;EACD;;EAED,SAAO,IAAP;EACD,CAVD;;EAYA,MAAMqC,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAIhB,SAAS,CAACgB,KAAD,CAAlB,GAA4B,SAA5B,GAAwC/C,MAAM,CAAC+C,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,aAAYP,QAAS,oBAAmBG,SAAU,wBAAuBF,aAAc,IADlH,CAAN;EAGD;EACF,GAVD;EAWD,CAZD;;EAcA,MAAMO,SAAS,GAAG7C,OAAO,IAAI;EAC3B,MAAI,CAACuB,SAAS,CAACvB,OAAD,CAAV,IAAuBA,OAAO,CAAC8C,cAAR,GAAyBnB,MAAzB,KAAoC,CAA/D,EAAkE;EAChE,WAAO,KAAP;EACD;;EAED,SAAOZ,gBAAgB,CAACf,OAAD,CAAhB,CAA0B+C,gBAA1B,CAA2C,YAA3C,MAA6D,SAApE;EACD,CAND;;EAQA,MAAMC,UAAU,GAAGhD,OAAO,IAAI;EAC5B,MAAI,CAACA,OAAD,IAAYA,OAAO,CAACyB,QAAR,KAAqBwB,IAAI,CAACC,YAA1C,EAAwD;EACtD,WAAO,IAAP;EACD;;EAED,MAAIlD,OAAO,CAACmD,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;EAC1C,WAAO,IAAP;EACD;;EAED,MAAI,OAAOpD,OAAO,CAACqD,QAAf,KAA4B,WAAhC,EAA6C;EAC3C,WAAOrD,OAAO,CAACqD,QAAf;EACD;;EAED,SAAOrD,OAAO,CAACsD,YAAR,CAAqB,UAArB,KAAoCtD,OAAO,CAACE,YAAR,CAAqB,UAArB,MAAqC,OAAhF;EACD,CAdD;;EAyCA,MAAMqD,MAAM,GAAGvD,OAAO,IAAIA,OAAO,CAACwD,YAAlC;;EAEA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAa5C,MAAnB;;EAEA,MAAI4C,MAAM,IAAI,CAACjD,QAAQ,CAACkD,IAAT,CAAcL,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOI,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAME,yBAAyB,GAAG,EAAlC;;EAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAIrD,QAAQ,CAACsD,UAAT,KAAwB,SAA5B,EAAuC;EACrC;EACA,QAAI,CAACH,yBAAyB,CAACjC,MAA/B,EAAuC;EACrClB,MAAAA,QAAQ,CAACuD,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;EAClDJ,QAAAA,yBAAyB,CAACxB,OAA1B,CAAkC0B,QAAQ,IAAIA,QAAQ,EAAtD;EACD,OAFD;EAGD;;EAEDF,IAAAA,yBAAyB,CAACK,IAA1B,CAA+BH,QAA/B;EACD,GATD,MASO;EACLA,IAAAA,QAAQ;EACT;EACF,CAbD;;EAiBA,MAAMI,kBAAkB,GAAGC,MAAM,IAAI;EACnCN,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMO,CAAC,GAAGX,SAAS,EAAnB;EACA;;EACA,QAAIW,CAAJ,EAAO;EACL,YAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB;EACA,YAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B;EACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaF,MAAM,CAACM,eAApB;EACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWK,WAAX,GAAyBP,MAAzB;;EACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,EAAWM,UAAX,GAAwB,MAAM;EAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb;EACA,eAAOJ,MAAM,CAACM,eAAd;EACD,OAHD;EAID;EACF,GAbiB,CAAlB;EAcD,CAfD;;EAiBA,MAAMG,OAAO,GAAGd,QAAQ,IAAI;EAC1B,MAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;EAClCA,IAAAA,QAAQ;EACT;EACF,CAJD;;EAMA,MAAMe,sBAAsB,GAAG,CAACf,QAAD,EAAWgB,iBAAX,EAA8BC,iBAAiB,GAAG,IAAlD,KAA2D;EACxF,MAAI,CAACA,iBAAL,EAAwB;EACtBH,IAAAA,OAAO,CAACd,QAAD,CAAP;EACA;EACD;;EAED,QAAMkB,eAAe,GAAG,CAAxB;EACA,QAAMC,gBAAgB,GAAGtE,gCAAgC,CAACmE,iBAAD,CAAhC,GAAsDE,eAA/E;EAEA,MAAIE,MAAM,GAAG,KAAb;;EAEA,QAAMC,OAAO,GAAG,CAAC;EAAEC,IAAAA;EAAF,GAAD,KAAgB;EAC9B,QAAIA,MAAM,KAAKN,iBAAf,EAAkC;EAChC;EACD;;EAEDI,IAAAA,MAAM,GAAG,IAAT;EACAJ,IAAAA,iBAAiB,CAACO,mBAAlB,CAAsC9F,cAAtC,EAAsD4F,OAAtD;EACAP,IAAAA,OAAO,CAACd,QAAD,CAAP;EACD,GARD;;EAUAgB,EAAAA,iBAAiB,CAACd,gBAAlB,CAAmCzE,cAAnC,EAAmD4F,OAAnD;EACAG,EAAAA,UAAU,CAAC,MAAM;EACf,QAAI,CAACJ,MAAL,EAAa;EACX9D,MAAAA,oBAAoB,CAAC0D,iBAAD,CAApB;EACD;EACF,GAJS,EAIPG,gBAJO,CAAV;EAKD,CA3BD;;ECrPA;EACA;EACA;EACA;EACA;EACA;EAMA,MAAMM,sBAAsB,GAAG,mDAA/B;EACA,MAAMC,uBAAuB,GAAG,aAAhC;;EAEA,MAAMC,eAAN,CAAsB;EACpBC,EAAAA,WAAW,GAAG;EACZ,SAAKC,QAAL,GAAgBlF,QAAQ,CAACkD,IAAzB;EACD;;EAEDiC,EAAAA,QAAQ,GAAG;EACT;EACA,UAAMC,aAAa,GAAGpF,QAAQ,CAACqF,eAAT,CAAyBC,WAA/C;EACA,WAAOC,IAAI,CAACC,GAAL,CAASnF,MAAM,CAACoF,UAAP,GAAoBL,aAA7B,CAAP;EACD;;EAEDM,EAAAA,IAAI,GAAG;EACL,UAAMC,KAAK,GAAG,KAAKR,QAAL,EAAd;;EACA,SAAKS,gBAAL,GAFK;;;EAIL,SAAKC,qBAAL,CAA2B,KAAKX,QAAhC,EAA0C,cAA1C,EAA0DY,eAAe,IAAIA,eAAe,GAAGH,KAA/F,EAJK;;;EAML,SAAKE,qBAAL,CAA2Bf,sBAA3B,EAAmD,cAAnD,EAAmEgB,eAAe,IAAIA,eAAe,GAAGH,KAAxG;;EACA,SAAKE,qBAAL,CAA2Bd,uBAA3B,EAAoD,aAApD,EAAmEe,eAAe,IAAIA,eAAe,GAAGH,KAAxG;EACD;;EAEDC,EAAAA,gBAAgB,GAAG;EACjB,SAAKG,qBAAL,CAA2B,KAAKb,QAAhC,EAA0C,UAA1C;;EACA,SAAKA,QAAL,CAAcc,KAAd,CAAoBC,QAApB,GAA+B,QAA/B;EACD;;EAEDJ,EAAAA,qBAAqB,CAACrG,QAAD,EAAW0G,SAAX,EAAsB7C,QAAtB,EAAgC;EACnD,UAAM8C,cAAc,GAAG,KAAKhB,QAAL,EAAvB;;EACA,UAAMiB,oBAAoB,GAAG7G,OAAO,IAAI;EACtC,UAAIA,OAAO,KAAK,KAAK2F,QAAjB,IAA6B7E,MAAM,CAACoF,UAAP,GAAoBlG,OAAO,CAAC+F,WAAR,GAAsBa,cAA3E,EAA2F;EACzF;EACD;;EAED,WAAKJ,qBAAL,CAA2BxG,OAA3B,EAAoC2G,SAApC;;EACA,YAAMJ,eAAe,GAAGzF,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,EAAiC2G,SAAjC,CAAxB;EACA3G,MAAAA,OAAO,CAACyG,KAAR,CAAcE,SAAd,IAA4B,GAAE7C,QAAQ,CAAC7C,MAAM,CAACC,UAAP,CAAkBqF,eAAlB,CAAD,CAAqC,IAA3E;EACD,KARD;;EAUA,SAAKO,0BAAL,CAAgC7G,QAAhC,EAA0C4G,oBAA1C;EACD;;EAEDE,EAAAA,KAAK,GAAG;EACN,SAAKC,uBAAL,CAA6B,KAAKrB,QAAlC,EAA4C,UAA5C;;EACA,SAAKqB,uBAAL,CAA6B,KAAKrB,QAAlC,EAA4C,cAA5C;;EACA,SAAKqB,uBAAL,CAA6BzB,sBAA7B,EAAqD,cAArD;;EACA,SAAKyB,uBAAL,CAA6BxB,uBAA7B,EAAsD,aAAtD;EACD;;EAEDgB,EAAAA,qBAAqB,CAACxG,OAAD,EAAU2G,SAAV,EAAqB;EACxC,UAAMM,WAAW,GAAGjH,OAAO,CAACyG,KAAR,CAAcE,SAAd,CAApB;;EACA,QAAIM,WAAJ,EAAiB;EACfC,MAAAA,+BAAW,CAACC,gBAAZ,CAA6BnH,OAA7B,EAAsC2G,SAAtC,EAAiDM,WAAjD;EACD;EACF;;EAEDD,EAAAA,uBAAuB,CAAC/G,QAAD,EAAW0G,SAAX,EAAsB;EAC3C,UAAME,oBAAoB,GAAG7G,OAAO,IAAI;EACtC,YAAMuC,KAAK,GAAG2E,+BAAW,CAACE,gBAAZ,CAA6BpH,OAA7B,EAAsC2G,SAAtC,CAAd;;EACA,UAAI,OAAOpE,KAAP,KAAiB,WAArB,EAAkC;EAChCvC,QAAAA,OAAO,CAACyG,KAAR,CAAcY,cAAd,CAA6BV,SAA7B;EACD,OAFD,MAEO;EACLO,QAAAA,+BAAW,CAACI,mBAAZ,CAAgCtH,OAAhC,EAAyC2G,SAAzC;EACA3G,QAAAA,OAAO,CAACyG,KAAR,CAAcE,SAAd,IAA2BpE,KAA3B;EACD;EACF,KARD;;EAUA,SAAKuE,0BAAL,CAAgC7G,QAAhC,EAA0C4G,oBAA1C;EACD;;EAEDC,EAAAA,0BAA0B,CAAC7G,QAAD,EAAWsH,QAAX,EAAqB;EAC7C,QAAIhG,SAAS,CAACtB,QAAD,CAAb,EAAyB;EACvBsH,MAAAA,QAAQ,CAACtH,QAAD,CAAR;EACD,KAFD,MAEO;EACL2B,MAAAA,kCAAc,CAAC4F,IAAf,CAAoBvH,QAApB,EAA8B,KAAK0F,QAAnC,EAA6CvD,OAA7C,CAAqDmF,QAArD;EACD;EACF;;EAEDE,EAAAA,aAAa,GAAG;EACd,WAAO,KAAK7B,QAAL,KAAkB,CAAzB;EACD;;EA/EmB;;ECdtB;EACA;EACA;EACA;EACA;EACA;EAKA,MAAM8B,SAAO,GAAG;EACd7E,EAAAA,SAAS,EAAE,IADG;EACG;EACjB8E,EAAAA,UAAU,EAAE,KAFE;EAGdC,EAAAA,WAAW,EAAE,MAHC;EAGO;EACrBC,EAAAA,aAAa,EAAE;EAJD,CAAhB;EAOA,MAAMC,aAAW,GAAG;EAClBjF,EAAAA,SAAS,EAAE,SADO;EAElB8E,EAAAA,UAAU,EAAE,SAFM;EAGlBC,EAAAA,WAAW,EAAE,kBAHK;EAIlBC,EAAAA,aAAa,EAAE;EAJG,CAApB;EAMA,MAAMvD,MAAI,GAAG,UAAb;EACA,MAAMyD,mBAAmB,GAAG,gBAA5B;EACA,MAAMC,eAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EAEA,MAAMC,eAAe,GAAI,gBAAe5D,MAAK,EAA7C;;EAEA,MAAM6D,QAAN,CAAe;EACbzC,EAAAA,WAAW,CAAC1D,MAAD,EAAS;EAClB,SAAKoG,OAAL,GAAe,KAAKC,UAAL,CAAgBrG,MAAhB,CAAf;EACA,SAAKsG,WAAL,GAAmB,KAAnB;EACA,SAAK3C,QAAL,GAAgB,IAAhB;EACD;;EAED4C,EAAAA,IAAI,CAACzE,QAAD,EAAW;EACb,QAAI,CAAC,KAAKsE,OAAL,CAAavF,SAAlB,EAA6B;EAC3B+B,MAAAA,OAAO,CAACd,QAAD,CAAP;EACA;EACD;;EAED,SAAK0E,OAAL;;EAEA,QAAI,KAAKJ,OAAL,CAAaT,UAAjB,EAA6B;EAC3BpE,MAAAA,MAAM,CAAC,KAAKkF,WAAL,EAAD,CAAN;EACD;;EAED,SAAKA,WAAL,GAAmBtF,SAAnB,CAA6BuF,GAA7B,CAAiCT,iBAAjC;;EAEA,SAAKU,iBAAL,CAAuB,MAAM;EAC3B/D,MAAAA,OAAO,CAACd,QAAD,CAAP;EACD,KAFD;EAGD;;EAEDqC,EAAAA,IAAI,CAACrC,QAAD,EAAW;EACb,QAAI,CAAC,KAAKsE,OAAL,CAAavF,SAAlB,EAA6B;EAC3B+B,MAAAA,OAAO,CAACd,QAAD,CAAP;EACA;EACD;;EAED,SAAK2E,WAAL,GAAmBtF,SAAnB,CAA6ByF,MAA7B,CAAoCX,iBAApC;;EAEA,SAAKU,iBAAL,CAAuB,MAAM;EAC3B,WAAKE,OAAL;EACAjE,MAAAA,OAAO,CAACd,QAAD,CAAP;EACD,KAHD;EAID,GAtCY;;;EA0Cb2E,EAAAA,WAAW,GAAG;EACZ,QAAI,CAAC,KAAK9C,QAAV,EAAoB;EAClB,YAAMmD,QAAQ,GAAGrI,QAAQ,CAACsI,aAAT,CAAuB,KAAvB,CAAjB;EACAD,MAAAA,QAAQ,CAACE,SAAT,GAAqBjB,mBAArB;;EACA,UAAI,KAAKK,OAAL,CAAaT,UAAjB,EAA6B;EAC3BmB,QAAAA,QAAQ,CAAC3F,SAAT,CAAmBuF,GAAnB,CAAuBV,eAAvB;EACD;;EAED,WAAKrC,QAAL,GAAgBmD,QAAhB;EACD;;EAED,WAAO,KAAKnD,QAAZ;EACD;;EAED0C,EAAAA,UAAU,CAACrG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG0F,SADI;EAEP,UAAI,OAAO1F,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAFO,KAAT,CADiB;;EAOjBA,IAAAA,MAAM,CAAC4F,WAAP,GAAqBlG,UAAU,CAACM,MAAM,CAAC4F,WAAR,CAA/B;EACA9F,IAAAA,eAAe,CAACwC,MAAD,EAAOtC,MAAP,EAAe8F,aAAf,CAAf;EACA,WAAO9F,MAAP;EACD;;EAEDwG,EAAAA,OAAO,GAAG;EACR,QAAI,KAAKF,WAAT,EAAsB;EACpB;EACD;;EAED,SAAKF,OAAL,CAAaR,WAAb,CAAyBqB,WAAzB,CAAqC,KAAKR,WAAL,EAArC;;EAEAS,IAAAA,gCAAY,CAACC,EAAb,CAAgB,KAAKV,WAAL,EAAhB,EAAoCP,eAApC,EAAqD,MAAM;EACzDtD,MAAAA,OAAO,CAAC,KAAKwD,OAAL,CAAaP,aAAd,CAAP;EACD,KAFD;EAIA,SAAKS,WAAL,GAAmB,IAAnB;EACD;;EAEDO,EAAAA,OAAO,GAAG;EACR,QAAI,CAAC,KAAKP,WAAV,EAAuB;EACrB;EACD;;EAEDY,IAAAA,gCAAY,CAACE,GAAb,CAAiB,KAAKzD,QAAtB,EAAgCuC,eAAhC;;EAEA,SAAKvC,QAAL,CAAciD,MAAd;;EACA,SAAKN,WAAL,GAAmB,KAAnB;EACD;;EAEDK,EAAAA,iBAAiB,CAAC7E,QAAD,EAAW;EAC1Be,IAAAA,sBAAsB,CAACf,QAAD,EAAW,KAAK2E,WAAL,EAAX,EAA+B,KAAKL,OAAL,CAAaT,UAA5C,CAAtB;EACD;;EA/FY;;EC9Bf;EACA;EACA;EACA;EACA;EACA;EAgBA;EACA;EACA;EACA;EACA;;EAEA,MAAMrD,IAAI,GAAG,WAAb;EACA,MAAM+E,QAAQ,GAAG,cAAjB;EACA,MAAMC,SAAS,GAAI,IAAGD,QAAS,EAA/B;EACA,MAAME,YAAY,GAAG,WAArB;EACA,MAAMC,mBAAmB,GAAI,OAAMF,SAAU,GAAEC,YAAa,EAA5D;EACA,MAAME,UAAU,GAAG,QAAnB;EAEA,MAAM/B,OAAO,GAAG;EACdoB,EAAAA,QAAQ,EAAE,IADI;EAEdY,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,MAAM,EAAE;EAHM,CAAhB;EAMA,MAAM7B,WAAW,GAAG;EAClBgB,EAAAA,QAAQ,EAAE,SADQ;EAElBY,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,MAAM,EAAE;EAHU,CAApB;EAMA,MAAM1B,eAAe,GAAG,MAAxB;EACA,MAAM2B,aAAa,GAAG,iBAAtB;EAEA,MAAMC,UAAU,GAAI,OAAMP,SAAU,EAApC;EACA,MAAMQ,WAAW,GAAI,QAAOR,SAAU,EAAtC;EACA,MAAMS,UAAU,GAAI,OAAMT,SAAU,EAApC;EACA,MAAMU,YAAY,GAAI,SAAQV,SAAU,EAAxC;EACA,MAAMW,aAAa,GAAI,UAASX,SAAU,EAA1C;EACA,MAAMY,oBAAoB,GAAI,QAAOZ,SAAU,GAAEC,YAAa,EAA9D;EACA,MAAMY,mBAAmB,GAAI,gBAAeb,SAAU,EAAtD;EACA,MAAMc,qBAAqB,GAAI,kBAAiBd,SAAU,EAA1D;EAEA,MAAMe,qBAAqB,GAAG,+BAA9B;EACA,MAAMC,oBAAoB,GAAG,8BAA7B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,SAAN,SAAwBC,iCAAxB,CAAsC;EACpC9E,EAAAA,WAAW,CAAC1F,OAAD,EAAUgC,MAAV,EAAkB;EAC3B,UAAMhC,OAAN;EAEA,SAAKoI,OAAL,GAAe,KAAKC,UAAL,CAAgBrG,MAAhB,CAAf;EACA,SAAKyI,QAAL,GAAgB,KAAhB;EACA,SAAKC,SAAL,GAAiB,KAAKC,mBAAL,EAAjB;;EACA,SAAKC,kBAAL;EACD,GARmC;;;EAYrB,aAAJtG,IAAI,GAAG;EAChB,WAAOA,IAAP;EACD;;EAEiB,aAAPoD,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD,GAlBmC;;;EAsBpCmD,EAAAA,MAAM,CAACC,aAAD,EAAgB;EACpB,WAAO,KAAKL,QAAL,GAAgB,KAAKtE,IAAL,EAAhB,GAA8B,KAAKoC,IAAL,CAAUuC,aAAV,CAArC;EACD;;EAEDvC,EAAAA,IAAI,CAACuC,aAAD,EAAgB;EAClB,QAAI,KAAKL,QAAT,EAAmB;EACjB;EACD;;EAED,UAAMM,SAAS,GAAG7B,gCAAY,CAAC8B,OAAb,CAAqB,KAAKrF,QAA1B,EAAoCkE,UAApC,EAAgD;EAAEiB,MAAAA;EAAF,KAAhD,CAAlB;;EAEA,QAAIC,SAAS,CAACE,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKR,QAAL,GAAgB,IAAhB;EACA,SAAK9E,QAAL,CAAcc,KAAd,CAAoByE,UAApB,GAAiC,SAAjC;;EAEA,SAAKR,SAAL,CAAenC,IAAf;;EAEA,QAAI,CAAC,KAAKH,OAAL,CAAauB,MAAlB,EAA0B;EACxB,UAAIlE,eAAJ,GAAsBU,IAAtB;;EACA,WAAKgF,sBAAL,CAA4B,KAAKxF,QAAjC;EACD;;EAED,SAAKA,QAAL,CAAcyF,eAAd,CAA8B,aAA9B;;EACA,SAAKzF,QAAL,CAAc0F,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAK1F,QAAL,CAAc0F,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EACA,SAAK1F,QAAL,CAAcxC,SAAd,CAAwBuF,GAAxB,CAA4BT,eAA5B;;EAEA,UAAMqD,gBAAgB,GAAG,MAAM;EAC7BpC,MAAAA,gCAAY,CAAC8B,OAAb,CAAqB,KAAKrF,QAA1B,EAAoCmE,WAApC,EAAiD;EAAEgB,QAAAA;EAAF,OAAjD;EACD,KAFD;;EAIA,SAAKS,cAAL,CAAoBD,gBAApB,EAAsC,KAAK3F,QAA3C,EAAqD,IAArD;EACD;;EAEDQ,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKsE,QAAV,EAAoB;EAClB;EACD;;EAED,UAAMe,SAAS,GAAGtC,gCAAY,CAAC8B,OAAb,CAAqB,KAAKrF,QAA1B,EAAoCoE,UAApC,CAAlB;;EAEA,QAAIyB,SAAS,CAACP,gBAAd,EAAgC;EAC9B;EACD;;EAED/B,IAAAA,gCAAY,CAACE,GAAb,CAAiB3I,QAAjB,EAA2BwJ,aAA3B;;EACA,SAAKtE,QAAL,CAAc8F,IAAd;;EACA,SAAKhB,QAAL,GAAgB,KAAhB;;EACA,SAAK9E,QAAL,CAAcxC,SAAd,CAAwByF,MAAxB,CAA+BX,eAA/B;;EACA,SAAKyC,SAAL,CAAevE,IAAf;;EAEA,UAAMuF,gBAAgB,GAAG,MAAM;EAC7B,WAAK/F,QAAL,CAAc0F,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,WAAK1F,QAAL,CAAcyF,eAAd,CAA8B,YAA9B;;EACA,WAAKzF,QAAL,CAAcyF,eAAd,CAA8B,MAA9B;;EACA,WAAKzF,QAAL,CAAcc,KAAd,CAAoByE,UAApB,GAAiC,QAAjC;;EAEA,UAAI,CAAC,KAAK9C,OAAL,CAAauB,MAAlB,EAA0B;EACxB,YAAIlE,eAAJ,GAAsBsB,KAAtB;EACD;;EAEDmC,MAAAA,gCAAY,CAAC8B,OAAb,CAAqB,KAAKrF,QAA1B,EAAoCqE,YAApC;EACD,KAXD;;EAaA,SAAKuB,cAAL,CAAoBG,gBAApB,EAAsC,KAAK/F,QAA3C,EAAqD,IAArD;EACD;;EAEDkD,EAAAA,OAAO,GAAG;EACR,SAAK6B,SAAL,CAAe7B,OAAf;;EACA,UAAMA,OAAN;EACAK,IAAAA,gCAAY,CAACE,GAAb,CAAiB3I,QAAjB,EAA2BwJ,aAA3B;EACD,GAhGmC;;;EAoGpC5B,EAAAA,UAAU,CAACrG,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG0F,OADI;EAEP,SAAGR,+BAAW,CAACyE,iBAAZ,CAA8B,KAAKhG,QAAnC,CAFI;EAGP,UAAI,OAAO3D,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAHO,KAAT;EAKAF,IAAAA,eAAe,CAACwC,IAAD,EAAOtC,MAAP,EAAe8F,WAAf,CAAf;EACA,WAAO9F,MAAP;EACD;;EAED2I,EAAAA,mBAAmB,GAAG;EACpB,WAAO,IAAIxC,QAAJ,CAAa;EAClBtF,MAAAA,SAAS,EAAE,KAAKuF,OAAL,CAAaU,QADN;EAElBnB,MAAAA,UAAU,EAAE,IAFM;EAGlBC,MAAAA,WAAW,EAAE,KAAKjC,QAAL,CAAciG,UAHT;EAIlB/D,MAAAA,aAAa,EAAE,MAAM,KAAK1B,IAAL;EAJH,KAAb,CAAP;EAMD;;EAEDgF,EAAAA,sBAAsB,CAACnL,OAAD,EAAU;EAC9BkJ,IAAAA,gCAAY,CAACE,GAAb,CAAiB3I,QAAjB,EAA2BwJ,aAA3B,EAD8B;;EAE9Bf,IAAAA,gCAAY,CAACC,EAAb,CAAgB1I,QAAhB,EAA0BwJ,aAA1B,EAAyC4B,KAAK,IAAI;EAChD,UAAIpL,QAAQ,KAAKoL,KAAK,CAACzG,MAAnB,IACFpF,OAAO,KAAK6L,KAAK,CAACzG,MADhB,IAEF,CAACpF,OAAO,CAACoD,QAAR,CAAiByI,KAAK,CAACzG,MAAvB,CAFH,EAEmC;EACjCpF,QAAAA,OAAO,CAAC8L,KAAR;EACD;EACF,KAND;EAOA9L,IAAAA,OAAO,CAAC8L,KAAR;EACD;;EAEDlB,EAAAA,kBAAkB,GAAG;EACnB1B,IAAAA,gCAAY,CAACC,EAAb,CAAgB,KAAKxD,QAArB,EAA+BwE,mBAA/B,EAAoDE,qBAApD,EAA2E,MAAM,KAAKlE,IAAL,EAAjF;EAEA+C,IAAAA,gCAAY,CAACC,EAAb,CAAgB,KAAKxD,QAArB,EAA+ByE,qBAA/B,EAAsDyB,KAAK,IAAI;EAC7D,UAAI,KAAKzD,OAAL,CAAasB,QAAb,IAAyBmC,KAAK,CAACE,GAAN,KAActC,UAA3C,EAAuD;EACrD,aAAKtD,IAAL;EACD;EACF,KAJD;EAKD,GA3ImC;;;EA+Id,SAAf1B,eAAe,CAACzC,MAAD,EAAS;EAC7B,WAAO,KAAKgK,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAG1B,SAAS,CAAC2B,mBAAV,CAA8B,IAA9B,EAAoClK,MAApC,CAAb;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B;EACD;;EAED,UAAIiK,IAAI,CAACjK,MAAD,CAAJ,KAAiBtC,SAAjB,IAA8BsC,MAAM,CAAC3B,UAAP,CAAkB,GAAlB,CAA9B,IAAwD2B,MAAM,KAAK,aAAvE,EAAsF;EACpF,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAEDiK,MAAAA,IAAI,CAACjK,MAAD,CAAJ,CAAa,IAAb;EACD,KAZM,CAAP;EAaD;;EA7JmC;EAgKtC;EACA;EACA;EACA;EACA;;;AAEAkH,kCAAY,CAACC,EAAb,CAAgB1I,QAAhB,EAA0ByJ,oBAA1B,EAAgDI,oBAAhD,EAAsE,UAAUuB,KAAV,EAAiB;EACrF,QAAMzG,MAAM,GAAG5E,sBAAsB,CAAC,IAAD,CAArC;;EAEA,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcJ,QAAd,CAAuB,KAAK+L,OAA5B,CAAJ,EAA0C;EACxCN,IAAAA,KAAK,CAACO,cAAN;EACD;;EAED,MAAIpJ,UAAU,CAAC,IAAD,CAAd,EAAsB;EACpB;EACD;;EAEDkG,EAAAA,gCAAY,CAACmD,GAAb,CAAiBjH,MAAjB,EAAyB4E,YAAzB,EAAuC,MAAM;EAC3C;EACA,QAAInH,SAAS,CAAC,IAAD,CAAb,EAAqB;EACnB,WAAKiJ,KAAL;EACD;EACF,GALD,EAXqF;;EAmBrF,QAAMQ,YAAY,GAAG1K,kCAAc,CAACC,OAAf,CAAuB+H,aAAvB,CAArB;;EACA,MAAI0C,YAAY,IAAIA,YAAY,KAAKlH,MAArC,EAA6C;EAC3CmF,IAAAA,SAAS,CAACgC,WAAV,CAAsBD,YAAtB,EAAoCnG,IAApC;EACD;;EAED,QAAM8F,IAAI,GAAG1B,SAAS,CAAC2B,mBAAV,CAA8B9G,MAA9B,CAAb;EACA6G,EAAAA,IAAI,CAACpB,MAAL,CAAY,IAAZ;EACD,CA1BD;AA4BA3B,kCAAY,CAACC,EAAb,CAAgBrI,MAAhB,EAAwB0I,mBAAxB,EAA6C,MAC3C5H,kCAAc,CAAC4F,IAAf,CAAoBoC,aAApB,EAAmCxH,OAAnC,CAA2CoK,EAAE,IAAIjC,SAAS,CAAC2B,mBAAV,CAA8BM,EAA9B,EAAkCjE,IAAlC,EAAjD,CADF;EAIA;EACA;EACA;EACA;EACA;;EAEArE,kBAAkB,CAACqG,SAAD,CAAlB;;;;;;;;"}
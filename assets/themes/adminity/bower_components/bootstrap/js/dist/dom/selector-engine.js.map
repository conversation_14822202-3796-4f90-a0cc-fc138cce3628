{"version": 3, "file": "selector-engine.js", "sources": ["../../src/dom/selector-engine.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n"], "names": ["NODE_TEXT", "SelectorEngine", "find", "selector", "element", "document", "documentElement", "concat", "Element", "prototype", "querySelectorAll", "call", "findOne", "querySelector", "children", "filter", "child", "matches", "parents", "ancestor", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EAEA,MAAMA,SAAS,GAAG,CAAlB;QAEMC,cAAc,GAAG;EACrBC,EAAAA,IAAI,CAACC,QAAD,EAAWC,OAAO,GAAGC,QAAQ,CAACC,eAA9B,EAA+C;EACjD,WAAO,GAAGC,MAAH,CAAU,GAAGC,OAAO,CAACC,SAAR,CAAkBC,gBAAlB,CAAmCC,IAAnC,CAAwCP,OAAxC,EAAiDD,QAAjD,CAAb,CAAP;EACD,GAHoB;;EAKrBS,EAAAA,OAAO,CAACT,QAAD,EAAWC,OAAO,GAAGC,QAAQ,CAACC,eAA9B,EAA+C;EACpD,WAAOE,OAAO,CAACC,SAAR,CAAkBI,aAAlB,CAAgCF,IAAhC,CAAqCP,OAArC,EAA8CD,QAA9C,CAAP;EACD,GAPoB;;EASrBW,EAAAA,QAAQ,CAACV,OAAD,EAAUD,QAAV,EAAoB;EAC1B,WAAO,GAAGI,MAAH,CAAU,GAAGH,OAAO,CAACU,QAArB,EACJC,MADI,CACGC,KAAK,IAAIA,KAAK,CAACC,OAAN,CAAcd,QAAd,CADZ,CAAP;EAED,GAZoB;;EAcrBe,EAAAA,OAAO,CAACd,OAAD,EAAUD,QAAV,EAAoB;EACzB,UAAMe,OAAO,GAAG,EAAhB;EAEA,QAAIC,QAAQ,GAAGf,OAAO,CAACgB,UAAvB;;EAEA,WAAOD,QAAQ,IAAIA,QAAQ,CAACE,QAAT,KAAsBC,IAAI,CAACC,YAAvC,IAAuDJ,QAAQ,CAACE,QAAT,KAAsBrB,SAApF,EAA+F;EAC7F,UAAImB,QAAQ,CAACF,OAAT,CAAiBd,QAAjB,CAAJ,EAAgC;EAC9Be,QAAAA,OAAO,CAACM,IAAR,CAAaL,QAAb;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,UAApB;EACD;;EAED,WAAOF,OAAP;EACD,GA5BoB;;EA8BrBO,EAAAA,IAAI,CAACrB,OAAD,EAAUD,QAAV,EAAoB;EACtB,QAAIuB,QAAQ,GAAGtB,OAAO,CAACuB,sBAAvB;;EAEA,WAAOD,QAAP,EAAiB;EACf,UAAIA,QAAQ,CAACT,OAAT,CAAiBd,QAAjB,CAAJ,EAAgC;EAC9B,eAAO,CAACuB,QAAD,CAAP;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB;EACD;;EAED,WAAO,EAAP;EACD,GA1CoB;;EA4CrBC,EAAAA,IAAI,CAACxB,OAAD,EAAUD,QAAV,EAAoB;EACtB,QAAIyB,IAAI,GAAGxB,OAAO,CAACyB,kBAAnB;;EAEA,WAAOD,IAAP,EAAa;EACX,UAAIA,IAAI,CAACX,OAAL,CAAad,QAAb,CAAJ,EAA4B;EAC1B,eAAO,CAACyB,IAAD,CAAP;EACD;;EAEDA,MAAAA,IAAI,GAAGA,IAAI,CAACC,kBAAZ;EACD;;EAED,WAAO,EAAP;EACD;;EAxDoB;;;;;;;;"}
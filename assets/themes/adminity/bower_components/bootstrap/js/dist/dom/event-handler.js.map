{"version": 3, "file": "event-handler.js", "sources": ["../../src/util/index.js", "../../src/dom/event-handler.js"], "sourcesContent": ["import SelectorEngine from '../dom/selector-engine'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v5.0.2): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLower<PERSON>ase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return SelectorEngine.findOne(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.2): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst customEventsRegex = /^(mouseenter|mouseleave)/i\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, selector, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  let typeEvent = getTypeEvent(originalTypeEvent)\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (customEventsRegex.test(originalTypeEvent)) {\n    const wrapFn = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFn) {\n      delegationFn = wrapFn(delegationFn)\n    } else {\n      handler = wrapFn(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "document", "body", "hasAttribute", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "customEventsRegex", "nativeEvents", "Set", "getUidEvent", "element", "uid", "getEvent", "bootstrapHandler", "fn", "handler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "selector", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "parentNode", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "Object", "keys", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "getTypeEvent", "isNative", "has", "add<PERSON><PERSON><PERSON>", "test", "wrapFn", "relatedTarget", "contains", "call", "handlers", "previousFn", "replace", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "for<PERSON>ach", "handler<PERSON><PERSON>", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "elementEvent", "slice", "keyHandlers", "trigger", "args", "$", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "Event", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "key", "defineProperty", "get", "preventDefault", "dispatchEvent"], "mappings": ";;;;;;;;;;;EAiMA,MAAMA,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAaC,MAAnB;;EAEA,MAAID,MAAM,IAAI,CAACE,QAAQ,CAACC,IAAT,CAAcC,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOJ,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;ECjMA;EACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;;EAEA,MAAMK,cAAc,GAAG,oBAAvB;EACA,MAAMC,cAAc,GAAG,MAAvB;EACA,MAAMC,aAAa,GAAG,QAAtB;EACA,MAAMC,aAAa,GAAG,EAAtB;;EACA,IAAIC,QAAQ,GAAG,CAAf;EACA,MAAMC,YAAY,GAAG;EACnBC,EAAAA,UAAU,EAAE,WADO;EAEnBC,EAAAA,UAAU,EAAE;EAFO,CAArB;EAIA,MAAMC,iBAAiB,GAAG,2BAA1B;EACA,MAAMC,YAAY,GAAG,IAAIC,GAAJ,CAAQ,CAC3B,OAD2B,EAE3B,UAF2B,EAG3B,SAH2B,EAI3B,WAJ2B,EAK3B,aAL2B,EAM3B,YAN2B,EAO3B,gBAP2B,EAQ3B,WAR2B,EAS3B,UAT2B,EAU3B,WAV2B,EAW3B,aAX2B,EAY3B,WAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,mBAhB2B,EAiB3B,YAjB2B,EAkB3B,WAlB2B,EAmB3B,UAnB2B,EAoB3B,aApB2B,EAqB3B,aArB2B,EAsB3B,aAtB2B,EAuB3B,WAvB2B,EAwB3B,cAxB2B,EAyB3B,eAzB2B,EA0B3B,cA1B2B,EA2B3B,eA3B2B,EA4B3B,YA5B2B,EA6B3B,OA7B2B,EA8B3B,MA9B2B,EA+B3B,QA/B2B,EAgC3B,OAhC2B,EAiC3B,QAjC2B,EAkC3B,QAlC2B,EAmC3B,SAnC2B,EAoC3B,UApC2B,EAqC3B,MArC2B,EAsC3B,QAtC2B,EAuC3B,cAvC2B,EAwC3B,QAxC2B,EAyC3B,MAzC2B,EA0C3B,kBA1C2B,EA2C3B,kBA3C2B,EA4C3B,OA5C2B,EA6C3B,OA7C2B,EA8C3B,QA9C2B,CAAR,CAArB;EAiDA;EACA;EACA;EACA;EACA;;EAEA,SAASC,WAAT,CAAqBC,OAArB,EAA8BC,GAA9B,EAAmC;EACjC,SAAQA,GAAG,IAAK,GAAEA,GAAI,KAAIT,QAAQ,EAAG,EAA9B,IAAoCQ,OAAO,CAACR,QAA5C,IAAwDA,QAAQ,EAAvE;EACD;;EAED,SAASU,QAAT,CAAkBF,OAAlB,EAA2B;EACzB,QAAMC,GAAG,GAAGF,WAAW,CAACC,OAAD,CAAvB;EAEAA,EAAAA,OAAO,CAACR,QAAR,GAAmBS,GAAnB;EACAV,EAAAA,aAAa,CAACU,GAAD,CAAb,GAAqBV,aAAa,CAACU,GAAD,CAAb,IAAsB,EAA3C;EAEA,SAAOV,aAAa,CAACU,GAAD,CAApB;EACD;;EAED,SAASE,gBAAT,CAA0BH,OAA1B,EAAmCI,EAAnC,EAAuC;EACrC,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7BA,IAAAA,KAAK,CAACC,cAAN,GAAuBP,OAAvB;;EAEA,QAAIK,OAAO,CAACG,MAAZ,EAAoB;EAClBC,MAAAA,YAAY,CAACC,GAAb,CAAiBV,OAAjB,EAA0BM,KAAK,CAACK,IAAhC,EAAsCP,EAAtC;EACD;;EAED,WAAOA,EAAE,CAACQ,KAAH,CAASZ,OAAT,EAAkB,CAACM,KAAD,CAAlB,CAAP;EACD,GARD;EASD;;EAED,SAASO,0BAAT,CAAoCb,OAApC,EAA6Cc,QAA7C,EAAuDV,EAAvD,EAA2D;EACzD,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7B,UAAMS,WAAW,GAAGf,OAAO,CAACgB,gBAAR,CAAyBF,QAAzB,CAApB;;EAEA,SAAK,IAAI;EAAEG,MAAAA;EAAF,QAAaX,KAAtB,EAA6BW,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAACC,UAAxE,EAAoF;EAClF,WAAK,IAAIC,CAAC,GAAGJ,WAAW,CAACK,MAAzB,EAAiCD,CAAC,EAAlC,GAAuC;EACrC,YAAIJ,WAAW,CAACI,CAAD,CAAX,KAAmBF,MAAvB,EAA+B;EAC7BX,UAAAA,KAAK,CAACC,cAAN,GAAuBU,MAAvB;;EAEA,cAAIZ,OAAO,CAACG,MAAZ,EAAoB;EAClB;EACAC,YAAAA,YAAY,CAACC,GAAb,CAAiBV,OAAjB,EAA0BM,KAAK,CAACK,IAAhC,EAAsCG,QAAtC,EAAgDV,EAAhD;EACD;;EAED,iBAAOA,EAAE,CAACQ,KAAH,CAASK,MAAT,EAAiB,CAACX,KAAD,CAAjB,CAAP;EACD;EACF;EACF,KAhB4B;;;EAmB7B,WAAO,IAAP;EACD,GApBD;EAqBD;;EAED,SAASe,WAAT,CAAqBC,MAArB,EAA6BjB,OAA7B,EAAsCkB,kBAAkB,GAAG,IAA3D,EAAiE;EAC/D,QAAMC,YAAY,GAAGC,MAAM,CAACC,IAAP,CAAYJ,MAAZ,CAArB;;EAEA,OAAK,IAAIH,CAAC,GAAG,CAAR,EAAWQ,GAAG,GAAGH,YAAY,CAACJ,MAAnC,EAA2CD,CAAC,GAAGQ,GAA/C,EAAoDR,CAAC,EAArD,EAAyD;EACvD,UAAMb,KAAK,GAAGgB,MAAM,CAACE,YAAY,CAACL,CAAD,CAAb,CAApB;;EAEA,QAAIb,KAAK,CAACsB,eAAN,KAA0BvB,OAA1B,IAAqCC,KAAK,CAACiB,kBAAN,KAA6BA,kBAAtE,EAA0F;EACxF,aAAOjB,KAAP;EACD;EACF;;EAED,SAAO,IAAP;EACD;;EAED,SAASuB,eAAT,CAAyBC,iBAAzB,EAA4CzB,OAA5C,EAAqD0B,YAArD,EAAmE;EACjE,QAAMC,UAAU,GAAG,OAAO3B,OAAP,KAAmB,QAAtC;EACA,QAAMuB,eAAe,GAAGI,UAAU,GAAGD,YAAH,GAAkB1B,OAApD;EAEA,MAAI4B,SAAS,GAAGC,YAAY,CAACJ,iBAAD,CAA5B;EACA,QAAMK,QAAQ,GAAGtC,YAAY,CAACuC,GAAb,CAAiBH,SAAjB,CAAjB;;EAEA,MAAI,CAACE,QAAL,EAAe;EACbF,IAAAA,SAAS,GAAGH,iBAAZ;EACD;;EAED,SAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP;EACD;;EAED,SAASI,UAAT,CAAoBrC,OAApB,EAA6B8B,iBAA7B,EAAgDzB,OAAhD,EAAyD0B,YAAzD,EAAuEvB,MAAvE,EAA+E;EAC7E,MAAI,OAAOsB,iBAAP,KAA6B,QAA7B,IAAyC,CAAC9B,OAA9C,EAAuD;EACrD;EACD;;EAED,MAAI,CAACK,OAAL,EAAc;EACZA,IAAAA,OAAO,GAAG0B,YAAV;EACAA,IAAAA,YAAY,GAAG,IAAf;EACD,GAR4E;EAW7E;;;EACA,MAAInC,iBAAiB,CAAC0C,IAAlB,CAAuBR,iBAAvB,CAAJ,EAA+C;EAC7C,UAAMS,MAAM,GAAGnC,EAAE,IAAI;EACnB,aAAO,UAAUE,KAAV,EAAiB;EACtB,YAAI,CAACA,KAAK,CAACkC,aAAP,IAAyBlC,KAAK,CAACkC,aAAN,KAAwBlC,KAAK,CAACC,cAA9B,IAAgD,CAACD,KAAK,CAACC,cAAN,CAAqBkC,QAArB,CAA8BnC,KAAK,CAACkC,aAApC,CAA9E,EAAmI;EACjI,iBAAOpC,EAAE,CAACsC,IAAH,CAAQ,IAAR,EAAcpC,KAAd,CAAP;EACD;EACF,OAJD;EAKD,KAND;;EAQA,QAAIyB,YAAJ,EAAkB;EAChBA,MAAAA,YAAY,GAAGQ,MAAM,CAACR,YAAD,CAArB;EACD,KAFD,MAEO;EACL1B,MAAAA,OAAO,GAAGkC,MAAM,CAAClC,OAAD,CAAhB;EACD;EACF;;EAED,QAAM,CAAC2B,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,IAA2CJ,eAAe,CAACC,iBAAD,EAAoBzB,OAApB,EAA6B0B,YAA7B,CAAhE;EACA,QAAMT,MAAM,GAAGpB,QAAQ,CAACF,OAAD,CAAvB;EACA,QAAM2C,QAAQ,GAAGrB,MAAM,CAACW,SAAD,CAAN,KAAsBX,MAAM,CAACW,SAAD,CAAN,GAAoB,EAA1C,CAAjB;EACA,QAAMW,UAAU,GAAGvB,WAAW,CAACsB,QAAD,EAAWf,eAAX,EAA4BI,UAAU,GAAG3B,OAAH,GAAa,IAAnD,CAA9B;;EAEA,MAAIuC,UAAJ,EAAgB;EACdA,IAAAA,UAAU,CAACpC,MAAX,GAAoBoC,UAAU,CAACpC,MAAX,IAAqBA,MAAzC;EAEA;EACD;;EAED,QAAMP,GAAG,GAAGF,WAAW,CAAC6B,eAAD,EAAkBE,iBAAiB,CAACe,OAAlB,CAA0BzD,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB;EACA,QAAMgB,EAAE,GAAG4B,UAAU,GACnBnB,0BAA0B,CAACb,OAAD,EAAUK,OAAV,EAAmB0B,YAAnB,CADP,GAEnB5B,gBAAgB,CAACH,OAAD,EAAUK,OAAV,CAFlB;EAIAD,EAAAA,EAAE,CAACmB,kBAAH,GAAwBS,UAAU,GAAG3B,OAAH,GAAa,IAA/C;EACAD,EAAAA,EAAE,CAACwB,eAAH,GAAqBA,eAArB;EACAxB,EAAAA,EAAE,CAACI,MAAH,GAAYA,MAAZ;EACAJ,EAAAA,EAAE,CAACZ,QAAH,GAAcS,GAAd;EACA0C,EAAAA,QAAQ,CAAC1C,GAAD,CAAR,GAAgBG,EAAhB;EAEAJ,EAAAA,OAAO,CAAC8C,gBAAR,CAAyBb,SAAzB,EAAoC7B,EAApC,EAAwC4B,UAAxC;EACD;;EAED,SAASe,aAAT,CAAuB/C,OAAvB,EAAgCsB,MAAhC,EAAwCW,SAAxC,EAAmD5B,OAAnD,EAA4DkB,kBAA5D,EAAgF;EAC9E,QAAMnB,EAAE,GAAGiB,WAAW,CAACC,MAAM,CAACW,SAAD,CAAP,EAAoB5B,OAApB,EAA6BkB,kBAA7B,CAAtB;;EAEA,MAAI,CAACnB,EAAL,EAAS;EACP;EACD;;EAEDJ,EAAAA,OAAO,CAACgD,mBAAR,CAA4Bf,SAA5B,EAAuC7B,EAAvC,EAA2C6C,OAAO,CAAC1B,kBAAD,CAAlD;EACA,SAAOD,MAAM,CAACW,SAAD,CAAN,CAAkB7B,EAAE,CAACZ,QAArB,CAAP;EACD;;EAED,SAAS0D,wBAAT,CAAkClD,OAAlC,EAA2CsB,MAA3C,EAAmDW,SAAnD,EAA8DkB,SAA9D,EAAyE;EACvE,QAAMC,iBAAiB,GAAG9B,MAAM,CAACW,SAAD,CAAN,IAAqB,EAA/C;EAEAR,EAAAA,MAAM,CAACC,IAAP,CAAY0B,iBAAZ,EAA+BC,OAA/B,CAAuCC,UAAU,IAAI;EACnD,QAAIA,UAAU,CAACC,QAAX,CAAoBJ,SAApB,CAAJ,EAAoC;EAClC,YAAM7C,KAAK,GAAG8C,iBAAiB,CAACE,UAAD,CAA/B;EAEAP,MAAAA,aAAa,CAAC/C,OAAD,EAAUsB,MAAV,EAAkBW,SAAlB,EAA6B3B,KAAK,CAACsB,eAAnC,EAAoDtB,KAAK,CAACiB,kBAA1D,CAAb;EACD;EACF,GAND;EAOD;;EAED,SAASW,YAAT,CAAsB5B,KAAtB,EAA6B;EAC3B;EACAA,EAAAA,KAAK,GAAGA,KAAK,CAACuC,OAAN,CAAcxD,cAAd,EAA8B,EAA9B,CAAR;EACA,SAAOI,YAAY,CAACa,KAAD,CAAZ,IAAuBA,KAA9B;EACD;;QAEKG,YAAY,GAAG;EACnB+C,EAAAA,EAAE,CAACxD,OAAD,EAAUM,KAAV,EAAiBD,OAAjB,EAA0B0B,YAA1B,EAAwC;EACxCM,IAAAA,UAAU,CAACrC,OAAD,EAAUM,KAAV,EAAiBD,OAAjB,EAA0B0B,YAA1B,EAAwC,KAAxC,CAAV;EACD,GAHkB;;EAKnB0B,EAAAA,GAAG,CAACzD,OAAD,EAAUM,KAAV,EAAiBD,OAAjB,EAA0B0B,YAA1B,EAAwC;EACzCM,IAAAA,UAAU,CAACrC,OAAD,EAAUM,KAAV,EAAiBD,OAAjB,EAA0B0B,YAA1B,EAAwC,IAAxC,CAAV;EACD,GAPkB;;EASnBrB,EAAAA,GAAG,CAACV,OAAD,EAAU8B,iBAAV,EAA6BzB,OAA7B,EAAsC0B,YAAtC,EAAoD;EACrD,QAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAAC9B,OAA9C,EAAuD;EACrD;EACD;;EAED,UAAM,CAACgC,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,IAA2CJ,eAAe,CAACC,iBAAD,EAAoBzB,OAApB,EAA6B0B,YAA7B,CAAhE;EACA,UAAM2B,WAAW,GAAGzB,SAAS,KAAKH,iBAAlC;EACA,UAAMR,MAAM,GAAGpB,QAAQ,CAACF,OAAD,CAAvB;EACA,UAAM2D,WAAW,GAAG7B,iBAAiB,CAAC8B,UAAlB,CAA6B,GAA7B,CAApB;;EAEA,QAAI,OAAOhC,eAAP,KAA2B,WAA/B,EAA4C;EAC1C;EACA,UAAI,CAACN,MAAD,IAAW,CAACA,MAAM,CAACW,SAAD,CAAtB,EAAmC;EACjC;EACD;;EAEDc,MAAAA,aAAa,CAAC/C,OAAD,EAAUsB,MAAV,EAAkBW,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAG3B,OAAH,GAAa,IAArE,CAAb;EACA;EACD;;EAED,QAAIsD,WAAJ,EAAiB;EACflC,MAAAA,MAAM,CAACC,IAAP,CAAYJ,MAAZ,EAAoB+B,OAApB,CAA4BQ,YAAY,IAAI;EAC1CX,QAAAA,wBAAwB,CAAClD,OAAD,EAAUsB,MAAV,EAAkBuC,YAAlB,EAAgC/B,iBAAiB,CAACgC,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB;EACD,OAFD;EAGD;;EAED,UAAMV,iBAAiB,GAAG9B,MAAM,CAACW,SAAD,CAAN,IAAqB,EAA/C;EACAR,IAAAA,MAAM,CAACC,IAAP,CAAY0B,iBAAZ,EAA+BC,OAA/B,CAAuCU,WAAW,IAAI;EACpD,YAAMT,UAAU,GAAGS,WAAW,CAAClB,OAAZ,CAAoBvD,aAApB,EAAmC,EAAnC,CAAnB;;EAEA,UAAI,CAACoE,WAAD,IAAgB5B,iBAAiB,CAACyB,QAAlB,CAA2BD,UAA3B,CAApB,EAA4D;EAC1D,cAAMhD,KAAK,GAAG8C,iBAAiB,CAACW,WAAD,CAA/B;EAEAhB,QAAAA,aAAa,CAAC/C,OAAD,EAAUsB,MAAV,EAAkBW,SAAlB,EAA6B3B,KAAK,CAACsB,eAAnC,EAAoDtB,KAAK,CAACiB,kBAA1D,CAAb;EACD;EACF,KARD;EASD,GA7CkB;;EA+CnByC,EAAAA,OAAO,CAAChE,OAAD,EAAUM,KAAV,EAAiB2D,IAAjB,EAAuB;EAC5B,QAAI,OAAO3D,KAAP,KAAiB,QAAjB,IAA6B,CAACN,OAAlC,EAA2C;EACzC,aAAO,IAAP;EACD;;EAED,UAAMkE,CAAC,GAAGpF,SAAS,EAAnB;EACA,UAAMmD,SAAS,GAAGC,YAAY,CAAC5B,KAAD,CAA9B;EACA,UAAMoD,WAAW,GAAGpD,KAAK,KAAK2B,SAA9B;EACA,UAAME,QAAQ,GAAGtC,YAAY,CAACuC,GAAb,CAAiBH,SAAjB,CAAjB;EAEA,QAAIkC,WAAJ;EACA,QAAIC,OAAO,GAAG,IAAd;EACA,QAAIC,cAAc,GAAG,IAArB;EACA,QAAIC,gBAAgB,GAAG,KAAvB;EACA,QAAIC,GAAG,GAAG,IAAV;;EAEA,QAAIb,WAAW,IAAIQ,CAAnB,EAAsB;EACpBC,MAAAA,WAAW,GAAGD,CAAC,CAACM,KAAF,CAAQlE,KAAR,EAAe2D,IAAf,CAAd;EAEAC,MAAAA,CAAC,CAAClE,OAAD,CAAD,CAAWgE,OAAX,CAAmBG,WAAnB;EACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACM,oBAAZ,EAAX;EACAJ,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACO,6BAAZ,EAAlB;EACAJ,MAAAA,gBAAgB,GAAGH,WAAW,CAACQ,kBAAZ,EAAnB;EACD;;EAED,QAAIxC,QAAJ,EAAc;EACZoC,MAAAA,GAAG,GAAGtF,QAAQ,CAAC2F,WAAT,CAAqB,YAArB,CAAN;EACAL,MAAAA,GAAG,CAACM,SAAJ,CAAc5C,SAAd,EAAyBmC,OAAzB,EAAkC,IAAlC;EACD,KAHD,MAGO;EACLG,MAAAA,GAAG,GAAG,IAAIO,WAAJ,CAAgBxE,KAAhB,EAAuB;EAC3B8D,QAAAA,OAD2B;EAE3BW,QAAAA,UAAU,EAAE;EAFe,OAAvB,CAAN;EAID,KAjC2B;;;EAoC5B,QAAI,OAAOd,IAAP,KAAgB,WAApB,EAAiC;EAC/BxC,MAAAA,MAAM,CAACC,IAAP,CAAYuC,IAAZ,EAAkBZ,OAAlB,CAA0B2B,GAAG,IAAI;EAC/BvD,QAAAA,MAAM,CAACwD,cAAP,CAAsBV,GAAtB,EAA2BS,GAA3B,EAAgC;EAC9BE,UAAAA,GAAG,GAAG;EACJ,mBAAOjB,IAAI,CAACe,GAAD,CAAX;EACD;;EAH6B,SAAhC;EAKD,OAND;EAOD;;EAED,QAAIV,gBAAJ,EAAsB;EACpBC,MAAAA,GAAG,CAACY,cAAJ;EACD;;EAED,QAAId,cAAJ,EAAoB;EAClBrE,MAAAA,OAAO,CAACoF,aAAR,CAAsBb,GAAtB;EACD;;EAED,QAAIA,GAAG,CAACD,gBAAJ,IAAwB,OAAOH,WAAP,KAAuB,WAAnD,EAAgE;EAC9DA,MAAAA,WAAW,CAACgB,cAAZ;EACD;;EAED,WAAOZ,GAAP;EACD;;EA1GkB;;;;;;;;"}
(function(){var a;window.AmCharts?a=window.AmCharts:(a={},window.AmCharts=a,a.themes={},a.maps={},a.inheriting={},a.charts=[],a.onReadyArray=[],a.useUTC=!1,a.updateRate=60,a.uid=0,a.lang={},a.translations={},a.mapTranslations={},a.windows={},a.initHandlers=[],a.amString="am",a.pmString="pm");a.Class=function(e){var d=function(){arguments[0]!==a.inheriting&&(this.events={},this.construct.apply(this,arguments))};e.inherits?(d.prototype=new e.inherits(a.inheriting),d.base=e.inherits.prototype,delete e.inherits):(d.prototype.createEvents=function(){for(var b=0;b<arguments.length;b++){this.events[arguments[b]]=[]}},d.prototype.listenTo=function(h,g,i){this.removeListener(h,g,i);h.events[g].push({handler:i,scope:this})},d.prototype.addListener=function(h,g,i){this.removeListener(this,h,g);h&&this.events[h]&&this.events[h].push({handler:g,scope:i})},d.prototype.removeListener=function(h,g,i){if(h&&h.events&&(h=h.events[g])){for(g=h.length-1;0<=g;g--){h[g].handler===i&&h.splice(g,1)}}},d.prototype.fire=function(h){for(var g=this.events[h.type],j=0;j<g.length;j++){var i=g[j];i.handler.call(i.scope,h)}});for(var f in e){d.prototype[f]=e[f]}return d};a.addChart=function(b){window.requestAnimationFrame?a.animationRequested||(a.animationRequested=!0,window.requestAnimationFrame(a.update)):a.updateInt||(a.updateInt=setInterval(function(){a.update()},Math.round(1000/a.updateRate)));a.charts.push(b)};a.removeChart=function(e){for(var d=a.charts,f=d.length-1;0<=f;f--){d[f]==e&&d.splice(f,1)}0===d.length&&(a.requestAnimation&&(window.cancelAnimationFrame(a.requestAnimation),a.animationRequested=!1),a.updateInt&&(clearInterval(a.updateInt),a.updateInt=NaN))};a.isModern=!0;a.getIEVersion=function(){var e=0,d,f;"Microsoft Internet Explorer"==navigator.appName&&(d=navigator.userAgent,f=/MSIE ([0-9]{1,}[.0-9]{0,})/,null!==f.exec(d)&&(e=parseFloat(RegExp.$1)));return e};a.applyLang=function(e,d){var f=a.translations;d.dayNames=a.extend({},a.dayNames);d.shortDayNames=a.extend({},a.shortDayNames);d.monthNames=a.extend({},a.monthNames);d.shortMonthNames=a.extend({},a.shortMonthNames);d.amString="am";d.pmString="pm";f&&(f=f[e])&&(a.lang=f,d.langObj=f,f.monthNames&&(d.dayNames=a.extend({},f.dayNames),d.shortDayNames=a.extend({},f.shortDayNames),d.monthNames=a.extend({},f.monthNames),d.shortMonthNames=a.extend({},f.shortMonthNames)),f.am&&(d.amString=f.am),f.pm&&(d.pmString=f.pm));a.amString=d.amString;a.pmString=d.pmString};a.IEversion=a.getIEVersion();9>a.IEversion&&0<a.IEversion&&(a.isModern=!1,a.isIE=!0);a.dx=0;a.dy=0;if(document.addEventListener||window.opera){a.isNN=!0,a.isIE=!1,a.dx=0.5,a.dy=0.5}document.attachEvent&&(a.isNN=!1,a.isIE=!0,a.isModern||(a.dx=0,a.dy=0));window.chrome&&(a.chrome=!0);a.handleMouseUp=function(f){for(var d=a.charts,h=0;h<d.length;h++){var g=d[h];g&&g.handleReleaseOutside&&g.handleReleaseOutside(f)}};a.handleMouseMove=function(f){for(var d=a.charts,h=0;h<d.length;h++){var g=d[h];g&&g.handleMouseMove&&g.handleMouseMove(f)}};a.handleWheel=function(f){for(var d=a.charts,h=0;h<d.length;h++){var g=d[h];if(g&&g.mouseIsOver){(g.mouseWheelScrollEnabled||g.mouseWheelZoomEnabled)&&g.handleWheel&&g.handleWheel(f);break}}};a.resetMouseOver=function(){for(var e=a.charts,d=0;d<e.length;d++){var f=e[d];f&&(f.mouseIsOver=!1)}};a.ready=function(b){a.onReadyArray.push(b)};a.handleLoad=function(){a.isReady=!0;for(var e=a.onReadyArray,d=0;d<e.length;d++){var f=e[d];isNaN(a.processDelay)?f():setTimeout(f,a.processDelay*d)}a.onReadyArray=[]};a.addInitHandler=function(d,c){a.initHandlers.push({method:d,types:c})};a.callInitHandler=function(f){var d=a.initHandlers;if(a.initHandlers){for(var h=0;h<d.length;h++){var g=d[h];g.types?a.isInArray(g.types,f.type)&&g.method(f):g.method(f)}}};a.getUniqueId=function(){a.uid++;return"AmChartsEl-"+a.uid};a.isNN&&(document.addEventListener("mousemove",a.handleMouseMove),document.addEventListener("mouseup",a.handleMouseUp,!0),window.addEventListener("load",a.handleLoad,!0));a.isIE&&(document.attachEvent("onmousemove",a.handleMouseMove),document.attachEvent("onmouseup",a.handleMouseUp),window.attachEvent("onload",a.handleLoad));a.addWheelListeners=function(){a.wheelIsListened||(a.isNN&&(window.addEventListener("DOMMouseScroll",a.handleWheel,!0),document.addEventListener("mousewheel",a.handleWheel,!0)),a.isIE&&document.attachEvent("onmousewheel",a.handleWheel));a.wheelIsListened=!0};a.clear=function(){var d=a.charts;if(d){for(var c=d.length-1;0<=c;c--){d[c].clear()}}a.updateInt&&clearInterval(a.updateInt);a.requestAnimation&&window.cancelAnimationFrame(a.requestAnimation);a.charts=[];a.isNN&&(document.removeEventListener("mousemove",a.handleMouseMove,!0),document.removeEventListener("mouseup",a.handleMouseUp,!0),window.removeEventListener("load",a.handleLoad,!0),window.removeEventListener("DOMMouseScroll",a.handleWheel,!0),document.removeEventListener("mousewheel",a.handleWheel,!0));a.isIE&&(document.detachEvent("onmousemove",a.handleMouseMove),document.detachEvent("onmouseup",a.handleMouseUp),window.detachEvent("onload",a.handleLoad))};a.makeChart=function(g,d,l){var k=d.type,i=d.theme;a.isString(i)&&(i=a.themes[i],d.theme=i);var j;switch(k){case"serial":j=new a.AmSerialChart(i);break;case"xy":j=new a.AmXYChart(i);break;case"pie":j=new a.AmPieChart(i);break;case"radar":j=new a.AmRadarChart(i);break;case"gauge":j=new a.AmAngularGauge(i);break;case"funnel":j=new a.AmFunnelChart(i);break;case"map":j=new a.AmMap(i);break;case"stock":j=new a.AmStockChart(i);break;case"gantt":j=new a.AmGanttChart(i)}a.extend(j,d);a.isReady?isNaN(l)?j.write(g):setTimeout(function(){a.realWrite(j,g)},l):a.ready(function(){isNaN(l)?j.write(g):setTimeout(function(){a.realWrite(j,g)},l)});return j};a.realWrite=function(d,c){d.write(c)};a.updateCount=0;a.validateAt=Math.round(a.updateRate/10);a.update=function(){var e=a.charts;a.updateCount++;var d=!1;a.updateCount==a.validateAt&&(d=!0,a.updateCount=0);if(e){for(var f=e.length-1;0<=f;f--){e[f].update&&e[f].update(),d&&(e[f].autoResize?e[f].validateSize&&e[f].validateSize():e[f].premeasure&&e[f].premeasure())}}window.requestAnimationFrame&&(a.requestAnimation=window.requestAnimationFrame(a.update))};"complete"==document.readyState&&a.handleLoad()})();(function(){var a=window.AmCharts;a.toBoolean=function(d,c){if(void 0===d){return c}switch(String(d).toLowerCase()){case"true":case"yes":case"1":return !0;case"false":case"no":case"0":case null:return !1;default:return !!d}};a.removeFromArray=function(e,d){var f;if(void 0!==d&&void 0!==e){for(f=e.length-1;0<=f;f--){e[f]==d&&e.splice(f,1)}}};a.getPath=function(){var e=document.getElementsByTagName("script");if(e){for(var d=0;d<e.length;d++){var f=e[d].src;if(-1!==f.search(/\/(amcharts|ammap)\.js/)){return f.replace(/\/(amcharts|ammap)\.js.*/,"/")}}}};a.normalizeUrl=function(b){return""!==b&&-1===b.search(/\/$/)?b+"/":b};a.isAbsolute=function(b){return 0===b.search(/^http[s]?:|^\//)};a.isInArray=function(e,d){for(var f=0;f<e.length;f++){if(e[f]==d){return !0}}return !1};a.getDecimals=function(d){var c=0;isNaN(d)||(d=String(d),-1!=d.indexOf("e-")?c=Number(d.split("-")[1]):-1!=d.indexOf(".")&&(c=d.split(".")[1].length));return c};a.wordwrap=function(i,d,p,o){var l,n,m,j;i+="";if(1>d){return i}l=-1;for(i=(j=i.split(/\r\n|\n|\r/)).length;++l<i;j[l]+=m){m=j[l];for(j[l]="";m.length>d;j[l]+=a.trim(m.slice(0,n))+((m=m.slice(n)).length?p:"")){n=2==o||(n=m.slice(0,d+1).match(/\S*(\s)?$/))[1]?d:n.input.length-n[0].length||1==o&&d||n.input.length+(n=m.slice(d).match(/^\S*/))[0].length}m=a.trim(m)}return j.join(p)};a.trim=function(b){return b.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")};a.wrappedText=function(v,u,t,s,o,r,q,n){var j=a.text(v,u,t,s,o,r,q);if(j){var i=j.getBBox();if(i.width>n){var d="\n";a.isModern||(d="<br>");n=Math.floor(n/(i.width/u.length));2<n&&(n-=2);u=a.wordwrap(u,n,d,!0);j.remove();j=a.text(v,u,t,s,o,r,q)}}return j};a.getStyle=function(f,d){var h="";if(document.defaultView&&document.defaultView.getComputedStyle){try{h=document.defaultView.getComputedStyle(f,"").getPropertyValue(d)}catch(g){}}else{f.currentStyle&&(d=d.replace(/\-(\w)/g,function(e,c){return c.toUpperCase()}),h=f.currentStyle[d])}return h};a.removePx=function(b){if(void 0!==b){return Number(b.substring(0,b.length-2))}};a.getURL=function(e,d){if(e){if("_self"!=d&&d){if("_top"==d&&window.top){window.top.location.href=e}else{if("_parent"==d&&window.parent){window.parent.location.href=e}else{if("_blank"==d){window.open(e)}else{var f=document.getElementsByName(d)[0];f?f.src=e:(f=a.windows[d])?f.opener&&!f.opener.closed?f.location.href=e:a.windows[d]=window.open(e):a.windows[d]=window.open(e)}}}}else{window.location.href=e}}};a.ifArray=function(b){return b&&"object"==typeof b&&0<b.length?!0:!1};a.callMethod=function(i,h){var n;for(n=0;n<h.length;n++){var l=h[n];if(l){if(l[i]){l[i]()}var m=l.length;if(0<m){var k;for(k=0;k<m;k++){var j=l[k];if(j&&j[i]){j[i]()}}}}}};a.toNumber=function(b){return"number"==typeof b?b:Number(String(b).replace(/[^0-9\-.]+/g,""))};a.toColor=function(e){if(""!==e&&void 0!==e){if(-1!=e.indexOf(",")){e=e.split(",");var d;for(d=0;d<e.length;d++){var f=e[d].substring(e[d].length-6,e[d].length);e[d]="#"+f}}else{e=e.substring(e.length-6,e.length),e="#"+e}}return e};a.toCoordinate=function(f,d,h){var g;void 0!==f&&(f=String(f),h&&h<d&&(d=h),g=Number(f),-1!=f.indexOf("!")&&(g=d-Number(f.substr(1))),-1!=f.indexOf("%")&&(g=d*Number(f.substr(0,f.length-1))/100));return g};a.fitToBounds=function(e,d,f){e<d&&(e=d);e>f&&(e=f);return e};a.isDefined=function(b){return void 0===b?!1:!0};a.stripNumbers=function(b){return b.replace(/[0-9]+/g,"")};a.roundTo=function(e,d){if(0>d){return e}var f=Math.pow(10,d);return Math.round(e*f)/f};a.toFixed=function(h,g){var l=!1;0>h&&(l=!0,h=Math.abs(h));var j=String(Math.round(h*Math.pow(10,g)));if(0<g){var k=j.length;if(k<g){var i;for(i=0;i<g-k;i++){j="0"+j}}k=j.substring(0,j.length-g);""===k&&(k=0);j=k+"."+j.substring(j.length-g,j.length);return l?"-"+j:j}return String(j)};a.formatDuration=function(r,q,p,o,j,n){var m=a.intervals,i=n.decimalSeparator;if(r>=m[q].contains){var d=r-Math.floor(r/m[q].contains)*m[q].contains;"ss"==q?(d=a.formatNumber(d,n),1==d.split(i)[0].length&&(d="0"+d)):d=a.roundTo(d,n.precision);("mm"==q||"hh"==q)&&10>d&&(d="0"+d);p=d+""+o[q]+""+p;r=Math.floor(r/m[q].contains);q=m[q].nextInterval;return a.formatDuration(r,q,p,o,j,n)}"ss"==q&&(r=a.formatNumber(r,n),1==r.split(i)[0].length&&(r="0"+r));("mm"==q||"hh"==q)&&10>r&&(r="0"+r);p=r+""+o[q]+""+p;if(m[j].count>m[q].count){for(r=m[q].count;r<m[j].count;r++){q=m[q].nextInterval,"ss"==q||"mm"==q||"hh"==q?p="00"+o[q]+""+p:"DD"==q&&(p="0"+o[q]+""+p)}}":"==p.charAt(p.length-1)&&(p=p.substring(0,p.length-1));return p};a.formatNumber=function(v,u,t,s,o){v=a.roundTo(v,u.precision);isNaN(t)&&(t=u.precision);var r=u.decimalSeparator;u=u.thousandsSeparator;var q;q=0>v?"-":"";v=Math.abs(v);var n=String(v),j=!1;-1!=n.indexOf("e")&&(j=!0);0<=t&&!j&&(n=a.toFixed(v,t));var i="";if(j){i=n}else{var n=n.split("."),j=String(n[0]),d;for(d=j.length;0<=d;d-=3){i=d!=j.length?0!==d?j.substring(d-3,d)+u+i:j.substring(d-3,d)+i:j.substring(d-3,d)}void 0!==n[1]&&(i=i+r+n[1]);void 0!==t&&0<t&&"0"!=i&&(i=a.addZeroes(i,r,t))}i=q+i;""===q&&!0===s&&0!==v&&(i="+"+i);!0===o&&(i+="%");return i};a.addZeroes=function(e,d,f){e=e.split(d);void 0===e[1]&&0<f&&(e[1]="0");return e[1].length<f?(e[1]+="0",a.addZeroes(e[0]+d+e[1],d,f)):void 0!==e[1]?e[0]+d+e[1]:e[0]};a.scientificToNormal=function(f){var d;f=String(f).split("e");var h;if("-"==f[1].substr(0,1)){d="0.";for(h=0;h<Math.abs(Number(f[1]))-1;h++){d+="0"}d+=f[0].split(".").join("")}else{var g=0;d=f[0].split(".");d[1]&&(g=d[1].length);d=f[0].split(".").join("");for(h=0;h<Math.abs(Number(f[1]))-g;h++){d+="0"}}return d};a.toScientific=function(f,d){if(0===f){return"0"}var h=Math.floor(Math.log(Math.abs(f))*Math.LOG10E),g=String(g).split(".").join(d);return String(g)+"e"+h};a.randomColor=function(){return"#"+("00000"+(16777216*Math.random()<<0).toString(16)).substr(-6)};a.hitTest=function(r,q,p){var o=!1,j=r.x,n=r.x+r.width,m=r.y,i=r.y+r.height,d=a.isInRectangle;o||(o=d(j,m,q));o||(o=d(j,i,q));o||(o=d(n,m,q));o||(o=d(n,i,q));o||!0===p||(o=a.hitTest(q,r,!0));return o};a.isInRectangle=function(e,d,f){return e>=f.x-5&&e<=f.x+f.width+5&&d>=f.y-5&&d<=f.y+f.height+5?!0:!1};a.isPercents=function(b){if(-1!=String(b).indexOf("%")){return !0}};a.formatValue=function(v,u,t,s,o,r,q,n){if(u){void 0===o&&(o="");var j;for(j=0;j<t.length;j++){var i=t[j],d=u[i];void 0!==d&&(d=r?a.addPrefix(d,n,q,s):a.formatNumber(d,s),v=v.replace(new RegExp("\\[\\["+o+""+i+"\\]\\]","g"),d))}}return v};a.formatDataContextValue=function(g,f){if(g){var j=g.match(/\[\[.*?\]\]/g),h;for(h=0;h<j.length;h++){var i=j[h],i=i.substr(2,i.length-4);void 0!==f[i]&&(g=g.replace(new RegExp("\\[\\["+i+"\\]\\]","g"),f[i]))}}return g};a.massReplace=function(f,d){for(var h in d){if(d.hasOwnProperty(h)){var g=d[h];void 0===g&&(g="");f=f.replace(h,g)}}return f};a.cleanFromEmpty=function(b){return b.replace(/\[\[[^\]]*\]\]/g,"")};a.addPrefix=function(t,s,r,q,n){var p=a.formatNumber(t,q),o="",j,i,d;if(0===t){return"0"}0>t&&(o="-");t=Math.abs(t);if(1<t){for(j=s.length-1;-1<j;j--){if(t>=s[j].number&&(i=t/s[j].number,d=Number(q.precision),1>d&&(d=1),r=a.roundTo(i,d),d=a.formatNumber(r,{precision:-1,decimalSeparator:q.decimalSeparator,thousandsSeparator:q.thousandsSeparator}),!n||i==r)){p=o+""+d+""+s[j].prefix;break}}}else{for(j=0;j<r.length;j++){if(t<=r[j].number){i=t/r[j].number;d=Math.abs(Math.floor(Math.log(i)*Math.LOG10E));i=a.roundTo(i,d);p=o+""+i+""+r[j].prefix;break}}}return p};a.remove=function(b){b&&b.remove()};a.getEffect=function(b){">"==b&&(b="easeOutSine");"<"==b&&(b="easeInSine");"elastic"==b&&(b="easeOutElastic");return b};a.getObjById=function(g,f){var j,h;for(h=0;h<g.length;h++){var i=g[h];if(i.id==f){j=i;break}}return j};a.applyTheme=function(f,d,h){d||(d=a.theme);try{d=JSON.parse(JSON.stringify(d))}catch(g){}d&&d[h]&&a.extend(f,d[h])};a.isString=function(b){return"string"==typeof b?!0:!1};a.extend=function(f,d,h){var g;f||(f={});for(g in d){h?f.hasOwnProperty(g)||(f[g]=d[g]):f[g]=d[g]}return f};a.copyProperties=function(e,d){for(var f in e){e.hasOwnProperty(f)&&"events"!=f&&void 0!==e[f]&&"function"!=typeof e[f]&&"cname"!=f&&(d[f]=e[f])}};a.processObject=function(f,d,j,i){if(!1===f instanceof d&&(f=i?a.extend(new d(j),f):a.extend(f,new d(j),!0),f.listeners)){for(var g in f.listeners){d=f.listeners[g],f.addListener(d.event,d.method)}}return f};a.fixNewLines=function(d){var c=RegExp("\\n","g");d&&(d=d.replace(c,"<br />"));return d};a.fixBrakes=function(d){if(a.isModern){var c=RegExp("<br>","g");d&&(d=d.replace(c,"\n"))}else{d=a.fixNewLines(d)}return d};a.deleteObject=function(f,d){if(f){if(void 0===d||null===d){d=20}if(0!==d){if("[object Array]"===Object.prototype.toString.call(f)){for(var h=0;h<f.length;h++){a.deleteObject(f[h],d-1),f[h]=null}}else{if(f&&!f.tagName){try{for(h in f.theme=null,f){f[h]&&("object"==typeof f[h]&&a.deleteObject(f[h],d-1),"function"!=typeof f[h]&&(f[h]=null))}}catch(g){}}}}}};a.bounce=function(g,f,j,h,i){return(f/=i)<1/2.75?7.5625*h*f*f+j:f<2/2.75?h*(7.5625*(f-=1.5/2.75)*f+0.75)+j:f<2.5/2.75?h*(7.5625*(f-=2.25/2.75)*f+0.9375)+j:h*(7.5625*(f-=2.625/2.75)*f+0.984375)+j};a.easeInOutQuad=function(g,f,j,h,i){f/=i/2;if(1>f){return h/2*f*f+j}f--;return -h/2*(f*(f-2)-1)+j};a.easeInSine=function(g,f,j,h,i){return -h*Math.cos(f/i*(Math.PI/2))+h+j};a.easeOutSine=function(g,f,j,h,i){return h*Math.sin(f/i*(Math.PI/2))+j};a.easeOutElastic=function(i,h,n,l,m){i=1.70158;var k=0,j=l;if(0===h){return n}if(1==(h/=m)){return n+l}k||(k=0.3*m);j<Math.abs(l)?(j=l,i=k/4):i=k/(2*Math.PI)*Math.asin(l/j);return j*Math.pow(2,-10*h)*Math.sin(2*(h*m-i)*Math.PI/k)+l+n};a.fixStepE=function(d){d=d.toExponential(0).split("e");var c=Number(d[1]);9==Number(d[0])&&c++;return a.generateNumber(1,c)};a.generateNumber=function(g,f){var j="",h;h=0>f?Math.abs(f)-1:Math.abs(f);var i;for(i=0;i<h;i++){j+="0"}return 0>f?Number("0."+j+String(g)):Number(String(g)+j)};a.setCN=function(g,f,j,h){if(g.addClassNames&&f&&(f=f.node)&&j){var i=f.getAttribute("class");g=g.classNamePrefix+"-";h&&(g="");i?f.setAttribute("class",i+" "+g+j):f.setAttribute("class",g+j)}};a.removeCN=function(e,d,f){d&&(d=d.node)&&f&&(d=d.classList)&&d.remove(e.classNamePrefix+"-"+f)};a.parseDefs=function(f,d){for(var j in f){var i=typeof f[j];if(0<f[j].length&&"object"==i){for(var g=0;g<f[j].length;g++){i=document.createElementNS(a.SVG_NS,j),d.appendChild(i),a.parseDefs(f[j][g],i)}}else{"object"==i?(i=document.createElementNS(a.SVG_NS,j),d.appendChild(i),a.parseDefs(f[j],i)):d.setAttribute(j,f[j])}}}})();(function(){var a=window.AmCharts;a.AxisBase=a.Class({construct:function(b){this.createEvents("clickItem","rollOverItem","rollOutItem","rollOverGuide","rollOutGuide","clickGuide");this.titleDY=this.y=this.x=this.dy=this.dx=0;this.axisThickness=1;this.axisColor="#000000";this.axisAlpha=1;this.gridCount=this.tickLength=5;this.gridAlpha=0.15;this.gridThickness=1;this.gridColor="#000000";this.dashLength=0;this.labelFrequency=1;this.showLastLabel=this.showFirstLabel=!0;this.fillColor="#FFFFFF";this.fillAlpha=0;this.labelsEnabled=!0;this.labelRotation=0;this.autoGridCount=!0;this.offset=0;this.guides=[];this.visible=!0;this.counter=0;this.guides=[];this.ignoreAxisWidth=this.inside=!1;this.minHorizontalGap=75;this.minVerticalGap=35;this.titleBold=!0;this.minorGridEnabled=!1;this.minorGridAlpha=0.07;this.autoWrap=!1;this.titleAlign="middle";this.labelOffset=0;this.bcn="axis-";this.centerLabels=!1;this.firstDayOfWeek=1;this.centerLabelOnFullPeriod=this.markPeriodChange=this.boldPeriodBeginning=!0;this.titleWidth=0;this.periods=[{period:"fff",count:1},{period:"fff",count:5},{period:"fff",count:10},{period:"fff",count:50},{period:"fff",count:100},{period:"fff",count:500},{period:"ss",count:1},{period:"ss",count:5},{period:"ss",count:10},{period:"ss",count:30},{period:"mm",count:1},{period:"mm",count:5},{period:"mm",count:10},{period:"mm",count:30},{period:"hh",count:1},{period:"hh",count:3},{period:"hh",count:6},{period:"hh",count:12},{period:"DD",count:1},{period:"DD",count:2},{period:"DD",count:3},{period:"DD",count:4},{period:"DD",count:5},{period:"WW",count:1},{period:"MM",count:1},{period:"MM",count:2},{period:"MM",count:3},{period:"MM",count:6},{period:"YYYY",count:1},{period:"YYYY",count:2},{period:"YYYY",count:5},{period:"YYYY",count:10},{period:"YYYY",count:50},{period:"YYYY",count:100}];this.dateFormats=[{period:"fff",format:"NN:SS.QQQ"},{period:"ss",format:"JJ:NN:SS"},{period:"mm",format:"JJ:NN"},{period:"hh",format:"JJ:NN"},{period:"DD",format:"MMM DD"},{period:"WW",format:"MMM DD"},{period:"MM",format:"MMM"},{period:"YYYY",format:"YYYY"}];this.nextPeriod={fff:"ss",ss:"mm",mm:"hh",hh:"DD",DD:"MM",MM:"YYYY"};a.applyTheme(this,b,"AxisBase")},zoom:function(d,c){this.start=d;this.end=c;this.dataChanged=!0;this.draw()},fixAxisPosition:function(){var b=this.position;"H"==this.orientation?("left"==b&&(b="bottom"),"right"==b&&(b="top")):("bottom"==b&&(b="left"),"top"==b&&(b="right"));this.position=b},init:function(){this.createBalloon()},draw:function(){var e=this.chart;this.prevBY=this.prevBX=NaN;this.allLabels=[];this.counter=0;this.destroy();this.fixAxisPosition();this.setBalloonBounds();this.labels=[];var d=e.container,f=d.set();e.gridSet.push(f);this.set=f;d=d.set();e.axesLabelsSet.push(d);this.labelsSet=d;this.axisLine=new this.axisRenderer(this);this.autoGridCount?("V"==this.orientation?(e=this.height/this.minVerticalGap,3>e&&(e=3)):e=this.width/this.minHorizontalGap,this.gridCountR=Math.max(e,1)):this.gridCountR=this.gridCount;this.axisWidth=this.axisLine.axisWidth;this.addTitle()},setOrientation:function(b){this.orientation=b?"H":"V"},addTitle:function(){var f=this.title;this.titleLabel=null;if(f){var d=this.chart,h=this.titleColor;void 0===h&&(h=d.color);var g=this.titleFontSize;isNaN(g)&&(g=d.fontSize+1);f=a.text(d.container,f,h,d.fontFamily,g,this.titleAlign,this.titleBold);a.setCN(d,f,this.bcn+"title");this.titleLabel=f}},positionTitle:function(){var z=this.titleLabel;if(z){var y,x,w=this.labelsSet,t={};0<w.length()?t=w.getBBox():(t.x=0,t.y=0,t.width=this.width,t.height=this.height,a.VML&&(t.y+=this.y,t.x+=this.x));w.push(z);var w=t.x,v=t.y;a.VML&&(v-=this.y,w-=this.x);var u=t.width,t=t.height,s=this.width,r=this.height,o=0,i=z.getBBox().height/2,d=this.inside,j=this.titleAlign;switch(this.position){case"top":y="left"==j?-1:"right"==j?s:s/2;x=v-10-i;break;case"bottom":y="left"==j?-1:"right"==j?s:s/2;x=v+t+10+i;break;case"left":y=w-10-i;d&&(y-=5);o=-90;x=("left"==j?r+1:"right"==j?-1:r/2)+this.titleDY;this.titleWidth=i+10;break;case"right":y=w+u+10+i,d&&(y+=7),x=("left"==j?r+2:"right"==j?-2:r/2)+this.titleDY,this.titleWidth=i+10,o=-90}this.marginsChanged?(z.translate(y,x),this.tx=y,this.ty=x):z.translate(this.tx,this.ty);this.marginsChanged=!1;isNaN(this.titleRotation)||(o=this.titleRotation);0!==o&&z.rotate(o)}},pushAxisItem:function(f,d){var h=this,g=f.graphics();0<g.length()&&(d?h.labelsSet.push(g):h.set.push(g));if(g=f.getLabel()){h.labelsSet.push(g),g.click(function(c){h.handleMouse(c,f,"clickItem")}).touchend(function(c){h.handleMouse(c,f,"clickItem")}).mouseover(function(c){h.handleMouse(c,f,"rollOverItem")}).mouseout(function(c){h.handleMouse(c,f,"rollOutItem")})}},handleMouse:function(e,d,f){this.fire({type:f,value:d.value,serialDataItem:d.serialDataItem,axis:this,target:d.label,chart:this.chart,event:e})},addGuide:function(f){for(var d=this.guides,j=!1,i=d.length,g=0;g<d.length;g++){d[g]==f&&(j=!0,i=g)}f=a.processObject(f,a.Guide,this.theme);f.id||(f.id="guideAuto"+i+"_"+(new Date).getTime());j||d.push(f)},removeGuide:function(e){var d=this.guides,f;for(f=0;f<d.length;f++){d[f]==e&&d.splice(f,1)}},handleGuideOver:function(f){clearTimeout(this.chart.hoverInt);var d=f.graphics.getBBox(),h=this.x+d.x+d.width/2,d=this.y+d.y+d.height/2,g=f.fillColor;void 0===g&&(g=f.lineColor);this.chart.showBalloon(f.balloonText,g,!0,h,d);this.fire({type:"rollOverGuide",guide:f,chart:this.chart})},handleGuideOut:function(b){this.chart.hideBalloon();this.fire({type:"rollOutGuide",guide:b,chart:this.chart})},handleGuideClick:function(b){this.chart.hideBalloon();this.fire({type:"clickGuide",guide:b,chart:this.chart})},addEventListeners:function(e,d){var f=this;e.mouseover(function(){f.handleGuideOver(d)});e.mouseup(function(){f.handleGuideClick(d)});e.touchstart(function(){f.handleGuideOver(d)});e.mouseout(function(){f.handleGuideOut(d)})},getBBox:function(){var b;this.labelsSet&&(b=this.labelsSet.getBBox());b?a.VML||(b={x:b.x+this.x,y:b.y+this.y,width:b.width,height:b.height}):b={x:0,y:0,width:0,height:0};return b},destroy:function(){a.remove(this.set);a.remove(this.labelsSet);var b=this.axisLine;b&&a.remove(b.axisSet);a.remove(this.grid0)},chooseMinorFrequency:function(d){for(var c=10;0<c;c--){if(d/c==Math.round(d/c)){return d/c}}},parseDatesDraw:function(){var X,W=this.chart,V=this.showFirstLabel,U=this.showLastLabel,R,T="",S=a.extractPeriod(this.minPeriod),Q=a.getPeriodDuration(S.period,S.count),P,O,M,K,N,E=this.firstDayOfWeek,H=this.boldPeriodBeginning;X=this.minorGridEnabled;var s,d=this.gridAlpha,o,v=this.choosePeriod(0),L=v.period,v=v.count,i=a.getPeriodDuration(L,v);i<Q&&(L=S.period,v=S.count,i=Q);S=L;"WW"==S&&(S="DD");this.stepWidth=this.getStepWidth(this.timeDifference);var J=Math.ceil(this.timeDifference/i)+5,F=P=a.resetDateToMin(new Date(this.startTime-i),L,v,E).getTime();if(S==L&&1==v&&this.centerLabelOnFullPeriod||this.autoWrap||this.centerLabels){M=i*this.stepWidth,this.autoWrap&&!this.centerLabels&&(M=-M)}this.cellWidth=Q*this.stepWidth;K=Math.round(P/i);Q=-1;K/2==Math.round(K/2)&&(Q=-2,P-=i);K=this.firstTime;var G=0,j=0;X&&1<v&&(s=this.chooseMinorFrequency(v),o=a.getPeriodDuration(L,s),"DD"==L&&(o+=a.getPeriodDuration("hh")),"fff"==L&&(o=1));if(0<this.gridCountR){for(J-5-Q>this.autoRotateCount&&!isNaN(this.autoRotateAngle)&&(this.labelRotationR=this.autoRotateAngle),X=Q;X<=J;X++){N=K+i*(X+Math.floor((F-K)/i))-G;"DD"==L&&(N+=3600000);N=a.resetDateToMin(new Date(N),L,v,E).getTime();"MM"==L&&(R=(N-P)/i,1.5<=(N-P)/i&&(N=N-(R-1)*i+a.getPeriodDuration("DD",3),N=a.resetDateToMin(new Date(N),L,1).getTime(),G+=i));R=(N-this.startTime)*this.stepWidth;if("radar"==W.type){if(R=this.axisWidth-R,0>R||R>this.axisWidth){continue}}else{this.rotate?"date"==this.type&&"middle"==this.gridPosition&&(j=-i*this.stepWidth/2):"date"==this.type&&(R=this.axisWidth-R)}T=!1;this.nextPeriod[S]&&(T=this.checkPeriodChange(this.nextPeriod[S],1,N,P,S));P=!1;T&&this.markPeriodChange?(T=this.dateFormatsObject[this.nextPeriod[S]],this.twoLineMode&&(T=this.dateFormatsObject[S]+"\n"+T,T=a.fixBrakes(T)),P=!0):T=this.dateFormatsObject[S];H||(P=!1);this.currentDateFormat=T;T=a.formatDate(new Date(N),T,W);if(X==Q&&!V||X==J&&!U){T=" "}this.labelFunction&&(T=this.labelFunction(T,new Date(N),this,L,v,O).toString());this.boldLabels&&(P=!0);O=new this.axisItemRenderer(this,R,T,!1,M,j,!1,P);this.pushAxisItem(O);O=P=N;if(!isNaN(s)){for(R=1;R<v;R+=s){this.gridAlpha=this.minorGridAlpha,T=N+o*R,T=a.resetDateToMin(new Date(T),L,s,E).getTime(),T=new this.axisItemRenderer(this,(T-this.startTime)*this.stepWidth,void 0,void 0,void 0,void 0,void 0,void 0,void 0,!0),this.pushAxisItem(T)}}this.gridAlpha=d}}},choosePeriod:function(e){var d=a.getPeriodDuration(this.periods[e].period,this.periods[e].count),f=this.periods;return this.timeDifference<d&&0<e?f[e-1]:Math.ceil(this.timeDifference/d)<=this.gridCountR?f[e]:e+1<f.length?this.choosePeriod(e+1):f[e]},getStepWidth:function(d){var c;this.startOnAxis?(c=this.axisWidth/(d-1),1==d&&(c=this.axisWidth)):c=this.axisWidth/d;return c},timeZoom:function(d,c){this.startTime=d;this.endTime=c},minDuration:function(){var b=a.extractPeriod(this.minPeriod);return a.getPeriodDuration(b.period,b.count)},checkPeriodChange:function(i,d,n,m,j){n=new Date(n);var l=new Date(m),k=this.firstDayOfWeek;m=d;"DD"==i&&(d=1);n=a.resetDateToMin(n,i,d,k).getTime();d=a.resetDateToMin(l,i,d,k).getTime();return"DD"==i&&"hh"!=j&&n-d<a.getPeriodDuration(i,m)-a.getPeriodDuration("hh",1)?!1:n!=d?!0:!1},generateDFObject:function(){this.dateFormatsObject={};var d;for(d=0;d<this.dateFormats.length;d++){var c=this.dateFormats[d];this.dateFormatsObject[c.period]=c.format}},hideBalloon:function(){this.balloon&&this.balloon.hide&&this.balloon.hide();this.prevBY=this.prevBX=NaN},formatBalloonText:function(b){return b},showBalloon:function(h,g,l,j){var k=this.offset;switch(this.position){case"bottom":g=this.height+k;break;case"top":g=-k;break;case"left":h=-k;break;case"right":h=this.width+k}l||(l=this.currentDateFormat);if("V"==this.orientation){if(0>g||g>this.height){return}if(isNaN(g)){this.hideBalloon();return}g=this.adjustBalloonCoordinate(g,j);j=this.coordinateToValue(g)}else{if(0>h||h>this.width){return}if(isNaN(h)){this.hideBalloon();return}h=this.adjustBalloonCoordinate(h,j);j=this.coordinateToValue(h)}var i;if(k=this.chart.chartCursor){i=k.index}if(this.balloon&&void 0!==j&&this.balloon.enabled){if(this.balloonTextFunction){if("date"==this.type||!0===this.parseDates){j=new Date(j)}j=this.balloonTextFunction(j)}else{this.balloonText?j=this.formatBalloonText(this.balloonText,i,l):isNaN(j)||(j=this.formatValue(j,l))}if(h!=this.prevBX||g!=this.prevBY){this.balloon.setPosition(h,g),this.prevBX=h,this.prevBY=g,j&&this.balloon.showBalloon(j)}}},adjustBalloonCoordinate:function(b){return b},createBalloon:function(){var d=this.chart,c=d.chartCursor;c&&(c=c.cursorPosition,"mouse"!=c&&(this.stickBalloonToCategory=!0),"start"==c&&(this.stickBalloonToStart=!0),"ValueAxis"==this.cname&&(this.stickBalloonToCategory=!1));this.balloon&&(this.balloon.destroy&&this.balloon.destroy(),a.extend(this.balloon,d.balloon,!0))},setBalloonBounds:function(){var r=this.balloon;if(r){var q=this.chart;r.cornerRadius=0;r.shadowAlpha=0;r.borderThickness=1;r.borderAlpha=1;r.adjustBorderColor=!1;r.showBullet=!1;this.balloon=r;r.chart=q;r.mainSet=q.plotBalloonsSet;r.pointerWidth=this.tickLength;if(this.parseDates||"date"==this.type){r.pointerWidth=0}r.className=this.id;q="V";"V"==this.orientation&&(q="H");this.stickBalloonToCategory||(r.animationDuration=0);var p,n,o,m,j=this.inside,i=this.width,h=this.height;switch(this.position){case"bottom":p=0;n=i;j?(o=0,m=h):(o=h,m=h+1000);break;case"top":p=0;n=i;j?(o=0,m=h):(o=-1000,m=0);break;case"left":o=0;m=h;j?(p=0,n=i):(p=-1000,n=0);break;case"right":o=0,m=h,j?(p=0,n=i):(p=i,n=i+1000)}r.drop||(r.pointerOrientation=q);r.setBounds(p,o,n,m)}}})})();(function(){var a=window.AmCharts;a.ValueAxis=a.Class({inherits:a.AxisBase,construct:function(b){this.cname="ValueAxis";this.createEvents("axisChanged","logarithmicAxisFailed","axisZoomed","axisIntZoomed");a.ValueAxis.base.construct.call(this,b);this.dataChanged=!0;this.stackType="none";this.position="left";this.unitPosition="right";this.includeAllValues=this.recalculateToPercents=this.includeHidden=this.includeGuidesInMinMax=this.integersOnly=!1;this.durationUnits={DD:"d. ",hh:":",mm:":",ss:""};this.scrollbar=!1;this.baseValue=0;this.radarCategoriesEnabled=!0;this.axisFrequency=1;this.gridType="polygons";this.useScientificNotation=!1;this.axisTitleOffset=10;this.pointPosition="axis";this.minMaxMultiplier=1;this.logGridLimit=2;this.totalTextOffset=this.treatZeroAs=0;this.minPeriod="ss";this.relativeStart=0;this.relativeEnd=1;a.applyTheme(this,b,this.cname)},updateData:function(){0>=this.gridCountR&&(this.gridCountR=1);this.totals=[];this.data=this.chart.chartData;var b=this.chart;"xy"!=b.type&&(this.stackGraphs("smoothedLine"),this.stackGraphs("line"),this.stackGraphs("column"),this.stackGraphs("step"));this.recalculateToPercents&&this.recalculate();if(this.synchronizationMultiplier&&this.synchronizeWith){a.isString(this.synchronizeWith)&&(this.synchronizeWith=b.getValueAxisById(this.synchronizeWith)),this.synchronizeWith&&(this.synchronizeWithAxis(this.synchronizeWith),this.foundGraphs=!0)}else{if(this.foundGraphs=!1,this.getMinMax(),0===this.start&&this.end==this.data.length-1&&isNaN(this.minZoom)&&isNaN(this.maxZoom)||isNaN(this.fullMin)&&isNaN(this.fullMax)){this.fullMin=this.min,this.fullMax=this.max,"date"!=this.type&&this.strictMinMax&&(isNaN(this.minimum)||(this.fullMin=this.minimum),isNaN(this.maximum)||(this.fullMax=this.maximum)),this.logarithmic&&(this.fullMin=this.logMin,0===this.fullMin&&(this.fullMin=this.treatZeroAs)),"date"==this.type&&(this.minimumDate||(this.fullMin=this.minRR),this.maximumDate||(this.fullMax=this.maxRR),this.strictMinMax&&(this.minimumDate&&(this.fullMin=this.minimumDate.getTime()),this.maximumDate&&(this.fullMax=this.maximumDate.getTime())))}}},draw:function(){a.ValueAxis.base.draw.call(this);var ab=this.chart,aa=this.set;this.labelRotationR=this.labelRotation;a.setCN(ab,this.set,"value-axis value-axis-"+this.id);a.setCN(ab,this.labelsSet,"value-axis value-axis-"+this.id);a.setCN(ab,this.axisLine.axisSet,"value-axis value-axis-"+this.id);var Z=this.type;"duration"==Z&&(this.duration="ss");!0===this.dataChanged&&(this.updateData(),this.dataChanged=!1);"date"==Z&&(this.logarithmic=!1,this.min=this.minRR,this.max=this.maxRR,this.reversed=!1,this.getDateMinMax());if(this.logarithmic){var Y=this.treatZeroAs,V=this.getExtremes(0,this.data.length-1).min;!isNaN(this.minimum)&&this.minimum<V&&(V=this.minimum);this.logMin=V;this.minReal<V&&(this.minReal=V);isNaN(this.minReal)&&(this.minReal=V);0<Y&&0===V&&(this.minReal=V=Y);if(0>=V||0>=this.minimum){this.fire({type:"logarithmicAxisFailed",chart:ab});return}}this.grid0=null;var X,W,U=ab.dx,T=ab.dy,Y=!1,V=this.logarithmic;if(isNaN(this.min)||isNaN(this.max)||!this.foundGraphs||Infinity==this.min||-Infinity==this.max){Y=!0}else{"date"==this.type&&this.min==this.max&&(this.max+=this.minDuration(),this.min-=this.minDuration());var S=this.labelFrequency,P=this.showFirstLabel,N=this.showLastLabel,R=1,G=0;this.minCalc=this.min;this.maxCalc=this.max;if(this.strictMinMax&&(isNaN(this.minimum)||(this.min=this.minimum),isNaN(this.maximum)||(this.max=this.maximum),this.min==this.max)){return}isNaN(this.minZoom)||(this.minReal=this.min=this.minZoom);isNaN(this.maxZoom)||(this.max=this.maxZoom);if(this.logarithmic){W=this.fullMin;var L=this.fullMax;isNaN(this.minimum)||(W=this.minimum);isNaN(this.maximum)||(L=this.maximum);var L=Math.log(L)*Math.LOG10E-Math.log(W)*Math.LOG10E,E=Math.log(this.max)/Math.LN10-Math.log(W)*Math.LOG10E;this.relativeStart=a.roundTo((Math.log(this.minReal)/Math.LN10-Math.log(W)*Math.LOG10E)/L,5);this.relativeEnd=a.roundTo(E/L,5)}else{this.relativeStart=a.roundTo(a.fitToBounds((this.min-this.fullMin)/(this.fullMax-this.fullMin),0,1),5),this.relativeEnd=a.roundTo(a.fitToBounds((this.max-this.fullMin)/(this.fullMax-this.fullMin),0,1),5)}var L=Math.round((this.maxCalc-this.minCalc)/this.step)+1,i;!0===V?(i=Math.log(this.max)*Math.LOG10E-Math.log(this.minReal)*Math.LOG10E,this.stepWidth=this.axisWidth/i,i>this.logGridLimit&&(L=Math.ceil(Math.log(this.max)*Math.LOG10E)+1,G=Math.round(Math.log(this.minReal)*Math.LOG10E),L>this.gridCountR&&(R=Math.ceil(L/this.gridCountR)))):this.stepWidth=this.axisWidth/(this.max-this.min);var s=0;1>this.step&&-1<this.step&&(s=a.getDecimals(this.step));this.integersOnly&&(s=0);s>this.maxDecCount&&(s=this.maxDecCount);E=this.precision;isNaN(E)||(s=E);isNaN(this.maxZoom)&&(this.max=a.roundTo(this.max,this.maxDecCount),this.min=a.roundTo(this.min,this.maxDecCount));W={};W.precision=s;W.decimalSeparator=ab.nf.decimalSeparator;W.thousandsSeparator=ab.nf.thousandsSeparator;this.numberFormatter=W;var F;this.exponential=!1;for(W=G;W<L;W+=R){var O=a.roundTo(this.step*W+this.min,s);-1!=String(O).indexOf("e")&&(this.exponential=!0)}this.duration&&(this.maxInterval=a.getMaxInterval(this.max,this.duration));var s=this.step,j,O=this.minorGridAlpha;this.minorGridEnabled&&(j=this.getMinorGridStep(s,this.stepWidth*s));if(this.autoGridCount||0!==this.gridCount){if("date"==Z){this.generateDFObject(),this.timeDifference=this.max-this.min,this.maxTime=this.lastTime=this.max,this.startTime=this.firstTime=this.min,this.parseDatesDraw()}else{for(L>=this.autoRotateCount&&!isNaN(this.autoRotateAngle)&&(this.labelRotationR=this.autoRotateAngle),Z=this.minCalc,V&&(L++,Z=this.maxCalc-L*s),this.gridCountReal=L,W=this.startCount=G;W<L;W+=R){if(G=s*W+Z,G=a.roundTo(G,this.maxDecCount+1),!this.integersOnly||Math.round(G)==G){if(isNaN(E)||Number(a.toFixed(G,E))==G){if(!0===V){if(i>this.logGridLimit){G=Math.pow(10,W)}else{if(0>=G&&(G=Z+s*W+s/2,0>=G)){continue}}}F=this.formatValue(G,!1,W);Math.round(W/S)!=W/S&&(F=void 0);if(0===W&&!P||W==L-1&&!N){F=" "}X=this.getCoordinate(G);var M;this.rotate&&this.autoWrap&&(M=this.stepWidth*s-10);F=new this.axisItemRenderer(this,X,F,void 0,M,void 0,void 0,this.boldLabels);this.pushAxisItem(F);if(G==this.baseValue&&"radar"!=ab.type){var J,K,o=this.width,v=this.height;"H"==this.orientation?0<=X&&X<=o+1&&(J=[X,X,X+U],K=[v,0,T]):0<=X&&X<=v+1&&(J=[0,o,o+U],K=[X,X,X+T]);J&&(X=a.fitToBounds(2*this.gridAlpha,0,1),isNaN(this.zeroGridAlpha)||(X=this.zeroGridAlpha),X=a.line(ab.container,J,K,this.gridColor,X,1,this.dashLength),X.translate(this.x,this.y),this.grid0=X,ab.axesSet.push(X),X.toBack(),a.setCN(ab,X,this.bcn+"zero-grid-"+this.id),a.setCN(ab,X,this.bcn+"zero-grid"))}if(!isNaN(j)&&0<O&&W<L-1){X=s/j;V&&(j=s*(W+R)+this.minCalc,j=a.roundTo(j,this.maxDecCount+1),i>this.logGridLimit&&(j=Math.pow(10,W+R)),X=9,j=(j-G)/X);o=this.gridAlpha;this.gridAlpha=this.minorGridAlpha;for(v=1;v<X;v++){var d=this.getCoordinate(G+j*v),d=new this.axisItemRenderer(this,d,"",!1,0,0,!1,!1,0,!0);this.pushAxisItem(d)}this.gridAlpha=o}}}}}}i=this.guides;M=i.length;if(0<M){J=this.fillAlpha;for(W=this.fillAlpha=0;W<M;W++){K=i[W],U=NaN,j=K.above,isNaN(K.toValue)||(U=this.getCoordinate(K.toValue),F=new this.axisItemRenderer(this,U,"",!0,NaN,NaN,K),this.pushAxisItem(F,j)),T=NaN,isNaN(K.value)||(T=this.getCoordinate(K.value),F=new this.axisItemRenderer(this,T,K.label,!0,NaN,(U-T)/2,K),this.pushAxisItem(F,j)),isNaN(U)&&(T-=3,U=T+3),F&&(S=F.label)&&this.addEventListeners(S,K),isNaN(U-T)||0>T&&0>U||(U=new this.guideFillRenderer(this,T,U,K),this.pushAxisItem(U,j),j=U.graphics(),K.graphics=j,this.addEventListeners(j,K))}this.fillAlpha=J}F=this.baseValue;this.min>this.baseValue&&this.max>this.baseValue&&(F=this.min);this.min<this.baseValue&&this.max<this.baseValue&&(F=this.max);V&&F<this.minReal&&(F=this.minReal);this.baseCoord=this.getCoordinate(F,!0);F={type:"axisChanged",target:this,chart:ab};F.min=V?this.minReal:this.min;F.max=this.max;this.fire(F);this.axisCreated=!0}V=this.axisLine.set;F=this.labelsSet;aa.translate(this.x,this.y);F.translate(this.x,this.y);this.positionTitle();"radar"!=ab.type&&V.toFront();!this.visible||Y?(aa.hide(),V.hide(),F.hide()):(aa.show(),V.show(),F.show());this.axisY=this.y;this.axisX=this.x},getDateMinMax:function(){this.minimumDate&&(this.minimumDate instanceof Date||(this.minimumDate=a.getDate(this.minimumDate,this.chart.dataDateFormat,"fff")),this.min=this.minimumDate.getTime());this.maximumDate&&(this.maximumDate instanceof Date||(this.maximumDate=a.getDate(this.maximumDate,this.chart.dataDateFormat,"fff")),this.max=this.maximumDate.getTime())},formatValue:function(i,d,n){var m=this.exponential,j=this.logarithmic,l=this.numberFormatter,k=this.chart;if(l){return !0===this.logarithmic&&(m=-1!=String(i).indexOf("e")?!0:!1),this.useScientificNotation&&(m=!0),this.usePrefixes&&(m=!1),m?(n=-1==String(i).indexOf("e")?i.toExponential(15):String(i),m=n.split("e"),n=Number(m[0]),m=Number(m[1]),n=a.roundTo(n,14),d||isNaN(this.precision)||(n=a.roundTo(n,this.precision)),10==n&&(n=1,m+=1),n=n+"e"+m,0===i&&(n="0"),1==i&&(n="1")):(j&&(m=String(i).split("."),m[1]?(l.precision=m[1].length,0>n&&(l.precision=Math.abs(n)),d&&1<i&&(l.precision=0),d||isNaN(this.precision)||(l.precision=this.precision)):l.precision=-1),n=this.usePrefixes?a.addPrefix(i,k.prefixesOfBigNumbers,k.prefixesOfSmallNumbers,l,!d):a.formatNumber(i,l,l.precision)),this.duration&&(d&&(l.precision=0),n=a.formatDuration(i,this.duration,"",this.durationUnits,this.maxInterval,l)),"date"==this.type&&(n=a.formatDate(new Date(i),this.currentDateFormat,k)),this.recalculateToPercents?n+="%":(d=this.unit)&&(n="left"==this.unitPosition?d+n:n+d),this.labelFunction&&(n="date"==this.type?this.labelFunction(n,new Date(i),this).toString():this.labelFunction(i,n,this).toString()),n}},getMinorGridStep:function(i,h){var n=[5,4,2];60>h&&n.shift();for(var l=Math.floor(Math.log(Math.abs(i))*Math.LOG10E),m=0;m<n.length;m++){var k=i/n[m],j=Math.floor(Math.log(Math.abs(k))*Math.LOG10E);if(!(1<Math.abs(l-j))){if(1>i){if(j=Math.pow(10,-j)*k,j==Math.round(j)){return k}}else{if(k==Math.round(k)){return k}}}}},stackGraphs:function(F){var E=this.stackType;"stacked"==E&&(E="regular");"line"==E&&(E="none");"100% stacked"==E&&(E="100%");this.stackType=E;var D=[],B=[],x=[],A=[],y,v=this.chart.graphs,u,s,j,i,o,H=this.baseValue,d=!1;if("line"==F||"step"==F||"smoothedLine"==F){d=!0}if(d&&("regular"==E||"100%"==E)){for(i=0;i<v.length;i++){j=v[i],j.stackGraph=null,j.hidden||(s=j.type,j.chart==this.chart&&j.valueAxis==this&&F==s&&j.stackable&&(u&&(j.stackGraph=u),u=j))}}j=this.start-10;u=this.end+10;i=this.data.length-1;j=a.fitToBounds(j,0,i);u=a.fitToBounds(u,0,i);for(o=j;o<=u;o++){var G=0;for(i=0;i<v.length;i++){if(j=v[i],j.hidden){j.newStack&&(x[o]=NaN,B[o]=NaN)}else{if(s=j.type,j.chart==this.chart&&j.valueAxis==this&&F==s&&j.stackable){if(s=this.data[o].axes[this.id].graphs[j.id],y=s.values.value,isNaN(y)){j.newStack&&(x[o]=NaN,B[o]=NaN)}else{var C=a.getDecimals(y);G<C&&(G=C);isNaN(A[o])?A[o]=Math.abs(y):A[o]+=Math.abs(y);A[o]=a.roundTo(A[o],G);C=j.fillToGraph;d&&C&&(C=this.data[o].axes[this.id].graphs[C.id])&&(s.values.open=C.values.value);"regular"==E&&(d&&(isNaN(D[o])?(D[o]=y,s.values.close=y,s.values.open=this.baseValue):(isNaN(y)?s.values.close=D[o]:s.values.close=y+D[o],s.values.open=D[o],D[o]=s.values.close)),"column"==F&&(j.newStack&&(x[o]=NaN,B[o]=NaN),s.values.close=y,0>y?(s.values.close=y,isNaN(B[o])?s.values.open=H:(s.values.close+=B[o],s.values.open=B[o]),B[o]=s.values.close):(s.values.close=y,isNaN(x[o])?s.values.open=H:(s.values.close+=x[o],s.values.open=x[o]),x[o]=s.values.close)))}}}}}for(o=this.start;o<=this.end;o++){for(i=0;i<v.length;i++){(j=v[i],j.hidden)?j.newStack&&(x[o]=NaN,B[o]=NaN):(s=j.type,j.chart==this.chart&&j.valueAxis==this&&F==s&&j.stackable&&(s=this.data[o].axes[this.id].graphs[j.id],y=s.values.value,isNaN(y)||(D=y/A[o]*100,s.values.percents=D,s.values.total=A[o],j.newStack&&(x[o]=NaN,B[o]=NaN),"100%"==E&&(isNaN(B[o])&&(B[o]=0),isNaN(x[o])&&(x[o]=0),0>D?(s.values.close=a.fitToBounds(D+B[o],-100,100),s.values.open=B[o],B[o]=s.values.close):(s.values.close=a.fitToBounds(D+x[o],-100,100),s.values.open=x[o],x[o]=s.values.close)))))}}},recalculate:function(){var z=this.chart,y=z.graphs,x;for(x=0;x<y.length;x++){var w=y[x];if(w.valueAxis==this){var t="value";if("candlestick"==w.type||"ohlc"==w.type){t="open"}var v,u,s=this.end+2,s=a.fitToBounds(this.end+1,0,this.data.length-1),r=this.start;0<r&&r--;var o;u=this.start;w.compareFromStart&&(u=0);if(!isNaN(z.startTime)&&(o=z.categoryAxis)){var i=o.minDuration(),i=new Date(z.startTime+i/2),d=a.resetDateToMin(new Date(z.startTime),o.minPeriod).getTime();a.resetDateToMin(new Date(i),o.minPeriod).getTime()>d&&u++}if(o=z.recalculateFromDate){o=a.getDate(o,z.dataDateFormat,"fff"),u=z.getClosestIndex(z.chartData,"time",o.getTime(),!0,0,z.chartData.length),s=z.chartData.length-1}for(o=u;o<=s&&(u=this.data[o].axes[this.id].graphs[w.id],v=u.values[t],w.recalculateValue&&(v=u.dataContext[w.valueField+w.recalculateValue]),isNaN(v));o++){}this.recBaseValue=v;for(t=r;t<=s;t++){u=this.data[t].axes[this.id].graphs[w.id];u.percents={};var r=u.values,j;for(j in r){u.percents[j]="percents"!=j?r[j]/v*100-100:r[j]}}}}},getMinMax:function(){var f=!1,d=this.chart,j=d.graphs,i;for(i=0;i<j.length;i++){var g=j[i].type;("line"==g||"step"==g||"smoothedLine"==g)&&this.expandMinMax&&(f=!0)}f&&(0<this.start&&this.start--,this.end<this.data.length-1&&this.end++);"serial"==d.type&&(!0!==d.categoryAxis.parseDates||f||this.end<this.data.length-1&&this.end++);this.includeAllValues&&(this.start=0,this.end=this.data.length-1);f=this.minMaxMultiplier;d=this.getExtremes(this.start,this.end);this.min=d.min;this.max=d.max;this.minRR=this.min;this.maxRR=this.max;f=(this.max-this.min)*(f-1);this.min-=f;this.max+=f;f=this.guides.length;if(this.includeGuidesInMinMax&&0<f){for(d=0;d<f;d++){j=this.guides[d],j.toValue<this.min&&(this.min=j.toValue),j.value<this.min&&(this.min=j.value),j.toValue>this.max&&(this.max=j.toValue),j.value>this.max&&(this.max=j.value)}}isNaN(this.minimum)||(this.min=this.minimum);isNaN(this.maximum)||(this.max=this.maximum);"date"==this.type&&this.getDateMinMax();this.min>this.max&&(f=this.max,this.max=this.min,this.min=f);isNaN(this.minZoom)||(this.min=this.minZoom);isNaN(this.maxZoom)||(this.max=this.maxZoom);this.minCalc=this.min;this.maxCalc=this.max;this.minReal=this.min;this.maxReal=this.max;0===this.min&&0===this.max&&(this.max=9);this.min>this.max&&(this.min=this.max-1);f=this.min;d=this.max;j=this.max-this.min;i=0===j?Math.pow(10,Math.floor(Math.log(Math.abs(this.max))*Math.LOG10E))/10:Math.pow(10,Math.floor(Math.log(Math.abs(j))*Math.LOG10E))/10;isNaN(this.maximum)&&(this.max=Math.ceil(this.max/i)*i+i);isNaN(this.minimum)&&(this.min=Math.floor(this.min/i)*i-i);0>this.min&&0<=f&&(this.min=0);0<this.max&&0>=d&&(this.max=0);"100%"==this.stackType&&(this.min=0>this.min?-100:0,this.max=0>this.max?0:100);j=this.max-this.min;i=Math.pow(10,Math.floor(Math.log(Math.abs(j))*Math.LOG10E))/10;this.step=Math.ceil(j/this.gridCountR/i)*i;j=Math.pow(10,Math.floor(Math.log(Math.abs(this.step))*Math.LOG10E));j=a.fixStepE(j);i=Math.ceil(this.step/j);5<i&&(i=10);5>=i&&2<i&&(i=5);this.step=Math.ceil(this.step/(j*i))*j*i;isNaN(this.setStep)||(this.step=this.setStep);1>j?(this.maxDecCount=Math.abs(Math.log(Math.abs(j))*Math.LOG10E),this.maxDecCount=Math.round(this.maxDecCount),this.step=a.roundTo(this.step,this.maxDecCount+1)):this.maxDecCount=0;this.min=this.step*Math.floor(this.min/this.step);this.max=this.step*Math.ceil(this.max/this.step);0>this.min&&0<=f&&(this.min=0);0<this.max&&0>=d&&(this.max=0);1<this.minReal&&1<this.max-this.minReal&&(this.minReal=Math.floor(this.minReal));j=Math.pow(10,Math.floor(Math.log(Math.abs(this.minReal))*Math.LOG10E));0===this.min&&(this.minReal=j);0===this.min&&1<this.minReal&&(this.minReal=1);0<this.min&&0<this.minReal-this.step&&(this.minReal=this.min+this.step<this.minReal?this.min+this.step:this.min);this.logarithmic&&(2<Math.log(d)*Math.LOG10E-Math.log(f)*Math.LOG10E?(this.minReal=this.min=Math.pow(10,Math.floor(Math.log(Math.abs(f))*Math.LOG10E)),this.maxReal=this.max=Math.pow(10,Math.ceil(Math.log(Math.abs(d))*Math.LOG10E))):(f=Math.pow(10,Math.floor(Math.log(Math.abs(f))*Math.LOG10E))/10,Math.pow(10,Math.floor(Math.log(Math.abs(this.min))*Math.LOG10E))/10<f&&(this.minReal=this.min=10*f)))},getExtremes:function(t,s){var r,p,q;for(q=t;q<=s;q++){var o=this.data[q].axes[this.id].graphs,n;for(n in o){if(o.hasOwnProperty(n)){var j=this.chart.graphsById[n];if(j.includeInMinMax&&(!j.hidden||this.includeHidden)){isNaN(r)&&(r=Infinity);isNaN(p)&&(p=-Infinity);this.foundGraphs=!0;j=o[n].values;this.recalculateToPercents&&(j=o[n].percents);var i;if(this.minMaxField){i=j[this.minMaxField],i<r&&(r=i),i>p&&(p=i)}else{for(var h in j){j.hasOwnProperty(h)&&"percents"!=h&&"total"!=h&&"error"!=h&&(i=j[h],i<r&&(r=i),i>p&&(p=i))}}}}}}return{min:r,max:p}},zoomOut:function(b){this.maxZoom=this.minZoom=NaN;this.zoomToRelativeValues(0,1,b)},zoomToRelativeValues:function(i,h,n){if(this.reversed){var l=i;i=1-h;h=1-l}var m=this.fullMax,l=this.fullMin,k=l+(m-l)*i,j=l+(m-l)*h;0<=this.minimum&&0>k&&(k=0);this.logarithmic&&(isNaN(this.minimum)||(l=this.minimum),isNaN(this.maximum)||(m=this.maximum),m=Math.log(m)*Math.LOG10E-Math.log(l)*Math.LOG10E,k=Math.pow(10,m*i+Math.log(l)*Math.LOG10E),j=Math.pow(10,m*h+Math.log(l)*Math.LOG10E));return this.zoomToValues(k,j,n)},zoomToValues:function(g,d,l){if(d<g){var k=d;d=g;g=k}var i=this.fullMax,k=this.fullMin;this.relativeStart=a.roundTo((g-k)/(i-k),9);this.relativeEnd=a.roundTo((d-k)/(i-k),9);if(this.logarithmic){isNaN(this.minimum)||(k=this.minimum);isNaN(this.maximum)||(i=this.maximum);var i=Math.log(i)*Math.LOG10E-Math.log(k)*Math.LOG10E,j=Math.log(d)/Math.LN10-Math.log(k)*Math.LOG10E;this.relativeStart=a.roundTo((Math.log(g)/Math.LN10-Math.log(k)*Math.LOG10E)/i,9);this.relativeEnd=a.roundTo(j/i,9)}if(this.minZoom!=g||this.maxZoom!=d){return this.minZoom=g,this.maxZoom=d,k={type:"axisZoomed"},k.chart=this.chart,k.valueAxis=this,k.startValue=g,k.endValue=d,k.relativeStart=this.relativeStart,k.relativeEnd=this.relativeEnd,this.prevStartValue==g&&this.prevEndValue==d||this.fire(k),this.prevStartValue=g,this.prevEndValue=d,l||(g={},a.copyProperties(k,g),g.type="axisIntZoomed",this.fire(g)),0===this.relativeStart&&1==this.relativeEnd&&(this.maxZoom=this.minZoom=NaN),!0}},coordinateToValue:function(i){if(isNaN(i)){return NaN}var h=this.axisWidth,n=this.stepWidth,l=this.reversed,m=this.rotate,k=this.min,j=this.minReal;return !0===this.logarithmic?Math.pow(10,(m?!0===l?(h-i)/n:i/n:!0===l?i/n:(h-i)/n)+Math.log(j)*Math.LOG10E):!0===l?m?k-(i-h)/n:i/n+k:m?i/n+k:k-(i-h)/n},getCoordinate:function(i,h){if(isNaN(i)){return NaN}var p=this.rotate,n=this.reversed,o=this.axisWidth,m=this.stepWidth,l=this.min,j=this.minReal;!0===this.logarithmic?(0===i&&(i=this.treatZeroAs),l=Math.log(i)*Math.LOG10E-Math.log(j)*Math.LOG10E,p=p?!0===n?o-m*l:m*l:!0===n?m*l:o-m*l):p=!0===n?p?o-m*(i-l):m*(i-l):p?m*(i-l):o-m*(i-l);10000000<Math.abs(p)&&(p=p/Math.abs(p)*10000000);h||(p=Math.round(p));return p},synchronizeWithAxis:function(b){this.synchronizeWith=b;this.listenTo(this.synchronizeWith,"axisChanged",this.handleSynchronization)},handleSynchronization:function(){if(this.synchronizeWith){a.isString(this.synchronizeWith)&&(this.synchronizeWith=this.chart.getValueAxisById(this.synchronizeWith));var f=this.synchronizeWith,d=f.min,h=f.max,f=f.step,g=this.synchronizationMultiplier;g&&(this.min=d*g,this.max=h*g,this.step=f*g,d=Math.abs(Math.log(Math.abs(Math.pow(10,Math.floor(Math.log(Math.abs(this.step))*Math.LOG10E))))*Math.LOG10E),this.maxDecCount=d=Math.round(d),this.draw())}}})})();(function(){var a=window.AmCharts;a.RecAxis=a.Class({construct:function(z){var y=z.chart,x=z.axisThickness,w=z.axisColor,t=z.axisAlpha,v=z.offset,u=z.dx,s=z.dy,r=z.x,o=z.y,i=z.height,d=z.width,j=y.container;"H"==z.orientation?(w=a.line(j,[0,d],[0,0],w,t,x),this.axisWidth=z.width,"bottom"==z.position?(s=x/2+v+i+o-1,x=r):(s=-x/2-v+o+s,x=u+r)):(this.axisWidth=z.height,"right"==z.position?(w=a.line(j,[0,0,-u],[0,i,i-s],w,t,x),s=o+s,x=x/2+v+u+d+r-1):(w=a.line(j,[0,0],[0,i],w,t,x),s=o,x=-x/2-v+r));w.translate(x,s);x=y.container.set();x.push(w);y.axesSet.push(x);a.setCN(y,w,z.bcn+"line");this.axisSet=x;this.set=w}})})();(function(){var a=window.AmCharts;a.RecItem=a.Class({construct:function(aL,aK,aJ,aI,aF,aH,aG,aD,aC,aB,az,ax){aK=Math.round(aK);var aA=aL.chart;this.value=aJ;void 0==aJ&&(aJ="");aC||(aC=0);void 0==aI&&(aI=!0);var au=aA.fontFamily,aw=aL.fontSize;void 0==aw&&(aw=aA.fontSize);var ap=aL.color;void 0==ap&&(ap=aA.color);void 0!==az&&(ap=az);var am=aL.chart.container,ao=am.set();this.set=ao;var at=aL.axisThickness,ac=aL.axisColor,an=aL.axisAlpha,ab=aL.tickLength,U=aL.gridAlpha,W=aL.gridThickness,R=aL.gridColor,S=aL.dashLength,K=aL.fillColor,N=aL.fillAlpha,L=aL.labelsEnabled;az=aL.labelRotationR;var al=aL.counter,O=aL.inside,ad=aL.labelOffset,ag=aL.dx,E=aL.dy,aE=aL.orientation,j=aL.position,ae=aL.previousCoord,s=aL.height,ay=aL.width,d=aL.offset,ak,ah;aG?(void 0!==aG.id&&(ax=aA.classNamePrefix+"-guide-"+aG.id),L=!0,isNaN(aG.tickLength)||(ab=aG.tickLength),void 0!=aG.lineColor&&(R=aG.lineColor),void 0!=aG.color&&(ap=aG.color),isNaN(aG.lineAlpha)||(U=aG.lineAlpha),isNaN(aG.dashLength)||(S=aG.dashLength),isNaN(aG.lineThickness)||(W=aG.lineThickness),!0===aG.inside&&(O=!0,0<d&&(d=0)),isNaN(aG.labelRotation)||(az=aG.labelRotation),isNaN(aG.fontSize)||(aw=aG.fontSize),aG.position&&(j=aG.position),void 0!==aG.boldLabel&&(aD=aG.boldLabel),isNaN(aG.labelOffset)||(ad=aG.labelOffset)):""===aJ&&(ab=0);aB&&!isNaN(aL.minorTickLength)&&(ab=aL.minorTickLength);var af="start";0<aF&&(af="middle");aL.centerLabels&&(af="middle");var F=az*Math.PI/180,o,av,T=0,ar=0,aq=0,i=o=0,ai=0;"V"==aE&&(az=0);var aj;L&&""!==aJ&&(aj=aL.autoWrap&&0===az?a.wrappedText(am,aJ,ap,au,aw,af,aD,Math.abs(aF),0):a.text(am,aJ,ap,au,aw,af,aD),af=aj.getBBox(),i=af.width,ai=af.height);if("H"==aE){if(0<=aK&&aK<=ay+1&&(0<ab&&0<an&&aK+aC<=ay+1&&(ak=a.line(am,[aK+aC,aK+aC],[0,ab],ac,an,W),ao.push(ak)),0<U&&(ah=a.line(am,[aK,aK+ag,aK+ag],[s,s+E,E],R,U,W,S),ao.push(ah))),ar=0,T=aK,aG&&90==az&&O&&(T-=aw),!1===aI?(af="start",ar="bottom"==j?O?ar+ab:ar-ab:O?ar-ab:ar+ab,T+=3,0<aF&&(T+=aF/2-3,af="middle"),0<az&&(af="middle")):af="middle",1==al&&0<N&&!aG&&!aB&&ae<ay&&(aI=a.fitToBounds(aK,0,ay),ae=a.fitToBounds(ae,0,ay),o=aI-ae,0<o&&(av=a.rect(am,o,aL.height,K,N),av.translate(aI-o+ag,E),ao.push(av))),"bottom"==j?(ar+=s+aw/2+d,O?(0<az?(ar=s-i/2*Math.sin(F)-ab-3,aL.centerRotatedLabels||(T+=i/2*Math.cos(F)-4+2)):0>az?(ar=s+i*Math.sin(F)-ab-3+2,T+=-i*Math.cos(F)-ai*Math.sin(F)-4):ar-=ab+aw+3+3,ar-=ad):(0<az?(ar=s+i/2*Math.sin(F)+ab+3,aL.centerRotatedLabels||(T-=i/2*Math.cos(F))):0>az?(ar=s+ab+3-i/2*Math.sin(F)+2,T+=i/2*Math.cos(F)):ar+=ab+at+3+3,ar+=ad)):(ar+=E+aw/2-d,T+=ag,O?(0<az?(ar=i/2*Math.sin(F)+ab+3,aL.centerRotatedLabels||(T-=i/2*Math.cos(F))):ar+=ab+3,ar+=ad):(0<az?(ar=-(i/2)*Math.sin(F)-ab-6,aL.centerRotatedLabels||(T+=i/2*Math.cos(F))):ar-=ab+aw+3+at+3,ar-=ad)),"bottom"==j?o=(O?s-ab-1:s+at-1)+d:(aq=ag,o=(O?E:E-ab-at+1)-d),aH&&(T+=aH),aw=T,0<az&&(aw+=i/2*Math.cos(F)),aj&&(aH=0,O&&(aH=i/2*Math.cos(F)),aw+aH>ay+2||0>aw)){aj.remove(),aj=null}}else{0<=aK&&aK<=s+1&&(0<ab&&0<an&&aK+aC<=s+1&&(ak=a.line(am,[0,ab+1],[aK+aC,aK+aC],ac,an,W),ao.push(ak)),0<U&&(ah=a.line(am,[0,ag,ay+ag],[aK,aK+E,aK+E],R,U,W,S),ao.push(ah)));af="end";if(!0===O&&"left"==j||!1===O&&"right"==j){af="start"}ar=aK-ai/2+2;1==al&&0<N&&!aG&&!aB&&(aI=a.fitToBounds(aK,0,s),ae=a.fitToBounds(ae,0,s),F=aI-ae,av=a.polygon(am,[0,aL.width,aL.width,0],[0,0,F,F],K,N),av.translate(ag,aI-F+E),ao.push(av));ar+=aw/2;"right"==j?(T+=ag+ay+d,ar+=E,O?(aH||(ar-=aw/2+3),T=T-(ab+4)-ad):(T+=ab+4+at,ar-=2,T+=ad)):O?(T+=ab+4-d,aH||(ar-=aw/2+3),aG&&(T+=ag,ar+=E),T+=ad):(T+=-ab-at-4-2-d,ar-=2,T-=ad);ak&&("right"==j?(aq+=ag+d+ay-1,o+=E,aq=O?aq-at:aq+at):(aq-=d,O||(aq-=ab+at)));aH&&(ar+=aH);O=-3;"right"==j&&(O+=E);aj&&(ar>s+1||ar<O-aw/10)&&(aj.remove(),aj=null)}ak&&(ak.translate(aq,o),a.setCN(aA,ak,aL.bcn+"tick"),a.setCN(aA,ak,ax,!0),aG&&a.setCN(aA,ak,"guide"));!1===aL.visible&&(ak&&ak.remove(),aj&&(aj.remove(),aj=null));aj&&(aj.attr({"text-anchor":af}),aj.translate(T,ar,NaN,!0),0!==az&&aj.rotate(-az,aL.chart.backgroundColor),aL.allLabels.push(aj),this.label=aj,a.setCN(aA,aj,aL.bcn+"label"),a.setCN(aA,aj,ax,!0),aG&&a.setCN(aA,aj,"guide"));ah&&(a.setCN(aA,ah,aL.bcn+"grid"),a.setCN(aA,ah,ax,!0),aG&&a.setCN(aA,ah,"guide"));av&&(a.setCN(aA,av,aL.bcn+"fill"),a.setCN(aA,av,ax,!0));aB?ah&&a.setCN(aA,ah,aL.bcn+"grid-minor"):(aL.counter=0===al?1:0,aL.previousCoord=aK);0===this.set.node.childNodes.length&&this.set.remove()},graphics:function(){return this.set},getLabel:function(){return this.label}})})();(function(){var a=window.AmCharts;a.RecFill=a.Class({construct:function(v,u,t,s){var o=v.dx,r=v.dy,q=v.orientation,n=0;if(t<u){var j=u;u=t;t=j}var i=s.fillAlpha;isNaN(i)&&(i=0);var j=v.chart.container,d=s.fillColor;"V"==q?(u=a.fitToBounds(u,0,v.height),t=a.fitToBounds(t,0,v.height)):(u=a.fitToBounds(u,0,v.width),t=a.fitToBounds(t,0,v.width));t-=u;isNaN(t)&&(t=4,n=2,i=0);0>t&&"object"==typeof d&&(d=d.join(",").split(",").reverse());"V"==q?(q=a.rect(j,v.width,t,d,i),q.translate(o,u-n+r)):(q=a.rect(j,t,v.height,d,i),q.translate(u-n+o,r));a.setCN(v.chart,q,"guide-fill");s.id&&a.setCN(v.chart,q,"guide-fill-"+s.id);this.set=j.set([q])},graphics:function(){return this.set},getLabel:function(){}})})();(function(){var a=window.AmCharts;a.AmChart=a.Class({construct:function(e){this.svgIcons=this.tapToActivate=!0;this.theme=e;this.classNamePrefix="amcharts";this.addClassNames=!1;this.version="3.21.5";a.addChart(this);this.createEvents("buildStarted","dataUpdated","init","rendered","drawn","failed","resized","animationFinished");this.height=this.width="100%";this.dataChanged=!0;this.chartCreated=!1;this.previousWidth=this.previousHeight=0;this.backgroundColor="#FFFFFF";this.borderAlpha=this.backgroundAlpha=0;this.color=this.borderColor="#000000";this.fontFamily="Verdana";this.fontSize=11;this.usePrefixes=!1;this.autoResize=!0;this.autoDisplay=!1;this.addCodeCredits=this.accessible=!0;this.touchStartTime=this.touchClickDuration=0;this.precision=-1;this.percentPrecision=2;this.decimalSeparator=".";this.thousandsSeparator=",";this.labels=[];this.allLabels=[];this.titles=[];this.marginRight=this.marginLeft=this.autoMarginOffset=0;this.timeOuts=[];this.creditsPosition="top-left";var d=document.createElement("div"),f=d.style;f.overflow="hidden";f.position="relative";f.textAlign="left";this.chartDiv=d;d=document.createElement("div");f=d.style;f.overflow="hidden";f.position="relative";f.textAlign="left";this.legendDiv=d;this.titleHeight=0;this.hideBalloonTime=150;this.handDrawScatter=2;this.cssScale=this.handDrawThickness=1;this.cssAngle=0;this.prefixesOfBigNumbers=[{number:1000,prefix:"k"},{number:1000000,prefix:"M"},{number:1000000000,prefix:"G"},{number:1000000000000,prefix:"T"},{number:1000000000000000,prefix:"P"},{number:1000000000000000000,prefix:"E"},{number:1e+21,prefix:"Z"},{number:1e+24,prefix:"Y"}];this.prefixesOfSmallNumbers=[{number:1e-24,prefix:"y"},{number:1e-21,prefix:"z"},{number:1e-18,prefix:"a"},{number:1e-15,prefix:"f"},{number:1e-12,prefix:"p"},{number:1e-9,prefix:"n"},{number:0.000001,prefix:"\u03bc"},{number:0.001,prefix:"m"}];this.panEventsEnabled=!0;this.product="amcharts";this.animations=[];this.balloon=new a.AmBalloon(this.theme);this.balloon.chart=this;this.processTimeout=0;this.processCount=1000;this.animatable=[];this.langObj={};a.applyTheme(this,e,"AmChart")},drawChart:function(){0<this.realWidth&&0<this.realHeight&&(this.drawBackground(),this.redrawLabels(),this.drawTitles(),this.brr(),this.renderFix(),this.chartDiv&&(this.boundingRect=this.chartDiv.getBoundingClientRect()))},makeAccessible:function(e,d,f){this.accessible&&e&&(f&&e.setAttr("role",f),e.setAttr("aria-label",d))},drawBackground:function(){a.remove(this.background);var g=this.container,d=this.backgroundColor,l=this.backgroundAlpha,k=this.set;a.isModern||0!==l||(l=0.001);var i=this.updateWidth();this.realWidth=i;var j=this.updateHeight();this.realHeight=j;d=a.polygon(g,[0,i-1,i-1,0],[0,0,j-1,j-1],d,l,1,this.borderColor,this.borderAlpha);a.setCN(this,d,"bg");this.background=d;k.push(d);if(d=this.backgroundImage){g=g.image(d,0,0,i,j),a.setCN(this,d,"bg-image"),this.bgImg=g,k.push(g)}},drawTitles:function(r){var q=this.titles;this.titleHeight=0;if(a.ifArray(q)){var p=20,o;for(o=0;o<q.length;o++){var j=q[o],j=a.processObject(j,a.Title,this.theme);if(!1!==j.enabled){var n=j.color;void 0===n&&(n=this.color);var m=j.size;isNaN(m)&&(m=this.fontSize+2);isNaN(j.alpha);var i=this.marginLeft,d=!0;void 0!==j.bold&&(d=j.bold);n=a.wrappedText(this.container,j.text,n,this.fontFamily,m,"middle",d,this.realWidth-35-this.marginRight-i);n.translate(i+(this.realWidth-this.marginRight-i)/2,p);n.node.style.pointerEvents="none";j.sprite=n;void 0!==j.tabIndex&&n.setAttr("tabindex",j.tabIndex);a.setCN(this,n,"title");j.id&&a.setCN(this,n,"title-"+j.id);n.attr({opacity:j.alpha});p+=n.getBBox().height+5;r?n.remove():this.freeLabelsSet.push(n)}}this.titleHeight=p-10}},write:function(f){var d=this;if(d.listeners){for(var h=0;h<d.listeners.length;h++){var g=d.listeners[h];d.addListener(g.event,g.method)}}d.fire({type:"buildStarted",chart:d});d.afterWriteTO&&clearTimeout(d.afterWriteTO);0<d.processTimeout?d.afterWriteTO=setTimeout(function(){d.afterWrite.call(d,f)},d.processTimeout):d.afterWrite(f)},afterWrite:function(i){var d;if(d="object"!=typeof i?document.getElementById(i):i){for(;d.firstChild;){d.removeChild(d.firstChild)}this.div=d;d.style.overflow="hidden";d.style.textAlign="left";i=this.chartDiv;var p=this.legendDiv,o=this.legend,l=p.style,n=i.style;this.measure();this.previousHeight=this.divRealHeight;this.previousWidth=this.divRealWidth;var m,j=document.createElement("div");m=j.style;m.position="relative";this.containerDiv=j;j.className=this.classNamePrefix+"-main-div";i.className=this.classNamePrefix+"-chart-div";d.appendChild(j);(d=this.exportConfig)&&a.AmExport&&!this.AmExport&&(this.AmExport=new a.AmExport(this,d));this.amExport&&a.AmExport&&(this.AmExport=a.extend(this.amExport,new a.AmExport(this),!0));this.AmExport&&this.AmExport.init&&this.AmExport.init();if(o){o=this.addLegend(o,o.divId);if(o.enabled){switch(l.left=null,l.top=null,l.right=null,n.left=null,n.right=null,n.top=null,l.position="relative",n.position="relative",m.width="100%",m.height="100%",o.position){case"bottom":j.appendChild(i);j.appendChild(p);break;case"top":j.appendChild(p);j.appendChild(i);break;case"absolute":l.position="absolute";n.position="absolute";void 0!==o.left&&(l.left=o.left+"px");void 0!==o.right&&(l.right=o.right+"px");void 0!==o.top&&(l.top=o.top+"px");void 0!==o.bottom&&(l.bottom=o.bottom+"px");o.marginLeft=0;o.marginRight=0;j.appendChild(i);j.appendChild(p);break;case"right":l.position="relative";n.position="absolute";j.appendChild(i);j.appendChild(p);break;case"left":l.position="absolute";n.position="relative";j.appendChild(i);j.appendChild(p);break;case"outside":j.appendChild(i)}}else{j.appendChild(i)}this.prevLegendPosition=o.position}else{j.appendChild(i)}this.listenersAdded||(this.addListeners(),this.listenersAdded=!0);(this.mouseWheelScrollEnabled||this.mouseWheelZoomEnabled)&&a.addWheelListeners();this.initChart()}},createLabelsSet:function(){a.remove(this.labelsSet);this.labelsSet=this.container.set();this.freeLabelsSet.push(this.labelsSet)},initChart:function(){this.balloon=a.processObject(this.balloon,a.AmBalloon,this.theme);window.AmCharts_path&&(this.path=window.AmCharts_path);void 0===this.path&&(this.path=a.getPath());void 0===this.path&&(this.path="amcharts/");this.path=a.normalizeUrl(this.path);void 0===this.pathToImages&&(this.pathToImages=this.path+"images/");this.initHC||(a.callInitHandler(this),this.initHC=!0);a.applyLang(this.language,this);var b=this.numberFormatter;b&&(isNaN(b.precision)||(this.precision=b.precision),void 0!==b.thousandsSeparator&&(this.thousandsSeparator=b.thousandsSeparator),void 0!==b.decimalSeparator&&(this.decimalSeparator=b.decimalSeparator));(b=this.percentFormatter)&&!isNaN(b.precision)&&(this.percentPrecision=b.precision);this.nf={precision:this.precision,thousandsSeparator:this.thousandsSeparator,decimalSeparator:this.decimalSeparator};this.pf={precision:this.percentPrecision,thousandsSeparator:this.thousandsSeparator,decimalSeparator:this.decimalSeparator};this.destroy();(b=this.container)?(b.container.innerHTML="",b.width=this.realWidth,b.height=this.realHeight,b.addDefs(this),this.chartDiv.appendChild(b.container)):b=new a.AmDraw(this.chartDiv,this.realWidth,this.realHeight,this);this.container=b;this.extension=".png";this.svgIcons&&a.SVG&&(this.extension=".svg");this.checkDisplay();this.checkTransform(this.div);b.chart=this;a.VML||a.SVG?(b.handDrawn=this.handDrawn,b.handDrawScatter=this.handDrawScatter,b.handDrawThickness=this.handDrawThickness,a.remove(this.set),this.set=b.set(),a.remove(this.gridSet),this.gridSet=b.set(),a.remove(this.cursorLineSet),this.cursorLineSet=b.set(),a.remove(this.graphsBehindSet),this.graphsBehindSet=b.set(),a.remove(this.bulletBehindSet),this.bulletBehindSet=b.set(),a.remove(this.columnSet),this.columnSet=b.set(),a.remove(this.graphsSet),this.graphsSet=b.set(),a.remove(this.trendLinesSet),this.trendLinesSet=b.set(),a.remove(this.axesSet),this.axesSet=b.set(),a.remove(this.cursorSet),this.cursorSet=b.set(),a.remove(this.scrollbarsSet),this.scrollbarsSet=b.set(),a.remove(this.bulletSet),this.bulletSet=b.set(),a.remove(this.freeLabelsSet),this.freeLabelsSet=b.set(),a.remove(this.axesLabelsSet),this.axesLabelsSet=b.set(),a.remove(this.balloonsSet),this.balloonsSet=b.set(),a.remove(this.plotBalloonsSet),this.plotBalloonsSet=b.set(),a.remove(this.zoomButtonSet),this.zoomButtonSet=b.set(),a.remove(this.zbSet),this.zbSet=null,a.remove(this.linkSet),this.linkSet=b.set()):this.fire({type:"failed",chart:this})},premeasure:function(){var f=this.div;if(f){try{this.boundingRect=this.chartDiv.getBoundingClientRect()}catch(g){}var d=f.offsetWidth,h=f.offsetHeight;f.clientHeight&&(d=f.clientWidth,h=f.clientHeight);if(d!=this.mw||h!=this.mh){this.mw=d,this.mh=h,this.measure()}}},measure:function(){var g=this.div;if(g){var d=this.chartDiv,l=g.offsetWidth,k=g.offsetHeight,i=this.container;g.clientHeight&&(l=g.clientWidth,k=g.clientHeight);var k=Math.round(k),l=Math.round(l),g=Math.round(a.toCoordinate(this.width,l)),j=Math.round(a.toCoordinate(this.height,k));(l!=this.previousWidth||k!=this.previousHeight)&&0<g&&0<j&&(d.style.width=g+"px",d.style.height=j+"px",d.style.padding=0,i&&i.setSize(g,j),this.balloon=a.processObject(this.balloon,a.AmBalloon,this.theme));this.balloon&&this.balloon.setBounds&&this.balloon.setBounds(2,2,g-2,j);this.updateWidth();this.balloon.chart=this;this.realWidth=g;this.realHeight=j;this.divRealWidth=l;this.divRealHeight=k}},checkDisplay:function(){if(this.autoDisplay&&this.container){var d=a.rect(this.container,10,10),c=d.getBBox();0===c.width&&0===c.height&&(this.divRealHeight=this.divRealWidth=this.realHeight=this.realWidth=0,this.previousWidth=this.previousHeight=NaN);d.remove()}},checkTransform:function(e){if(this.autoTransform&&window.getComputedStyle&&e){if(e.style){var d=window.getComputedStyle(e,null);if(d&&(d=d.getPropertyValue("-webkit-transform")||d.getPropertyValue("-moz-transform")||d.getPropertyValue("-ms-transform")||d.getPropertyValue("-o-transform")||d.getPropertyValue("transform"))&&"none"!==d){var f=d.split("(")[1].split(")")[0].split(","),d=f[0],f=f[1],d=Math.sqrt(d*d+f*f);isNaN(d)||(this.cssScale*=d)}}e.parentNode&&this.checkTransform(e.parentNode)}},destroy:function(){this.chartDiv.innerHTML="";this.clearTimeOuts();this.legend&&this.legend.destroy()},clearTimeOuts:function(){var d=this.timeOuts;if(d){var c;for(c=0;c<d.length;c++){clearTimeout(d[c])}}this.timeOuts=[]},clear:function(d){try{document.removeEventListener("touchstart",this.docfn1,!0),document.removeEventListener("touchend",this.docfn2,!0)}catch(c){}a.callMethod("clear",[this.chartScrollbar,this.scrollbarV,this.scrollbarH,this.chartCursor]);this.chartCursor=this.scrollbarH=this.scrollbarV=this.chartScrollbar=null;this.clearTimeOuts();this.container&&(this.container.remove(this.chartDiv),this.container.remove(this.legendDiv));d||a.removeChart(this);if(d=this.div){for(;d.firstChild;){d.removeChild(d.firstChild)}}this.legend&&this.legend.destroy();this.AmExport&&this.AmExport.clear&&this.AmExport.clear()},setMouseCursor:function(b){"auto"==b&&a.isNN&&(b="default");this.chartDiv.style.cursor=b;this.legendDiv.style.cursor=b},redrawLabels:function(){this.labels=[];var d=this.allLabels;this.createLabelsSet();var c;for(c=0;c<d.length;c++){this.drawLabel(d[c])}},drawLabel:function(v){var u=this;if(u.container&&!1!==v.enabled){v=a.processObject(v,a.Label,u.theme);var t=v.y,s=v.text,o=v.align,r=v.size,q=v.color,n=v.rotation,j=v.alpha,i=v.bold,d=a.toCoordinate(v.x,u.realWidth),t=a.toCoordinate(t,u.realHeight);d||(d=0);t||(t=0);void 0===q&&(q=u.color);isNaN(r)&&(r=u.fontSize);o||(o="start");"left"==o&&(o="start");"right"==o&&(o="end");"center"==o&&(o="middle",n?t=u.realHeight-t+t/2:d=u.realWidth/2-d);void 0===j&&(j=1);void 0===n&&(n=0);t+=r/2;s=a.text(u.container,s,q,u.fontFamily,r,o,i,j);s.translate(d,t);void 0!==v.tabIndex&&s.setAttr("tabindex",v.tabIndex);a.setCN(u,s,"label");v.id&&a.setCN(u,s,"label-"+v.id);0!==n&&s.rotate(n);v.url?(s.setAttr("cursor","pointer"),s.click(function(){a.getURL(v.url,u.urlTarget)})):s.node.style.pointerEvents="none";u.labelsSet.push(s);u.labels.push(s)}},addLabel:function(t,s,r,p,q,o,n,j,i,h){t={x:t,y:s,text:r,align:p,size:q,color:o,alpha:j,rotation:n,bold:i,url:h,enabled:!0};this.container&&this.drawLabel(t);this.allLabels.push(t)},clearLabels:function(){var d=this.labels,c;for(c=d.length-1;0<=c;c--){d[c].remove()}this.labels=[];this.allLabels=[]},updateHeight:function(){var e=this.divRealHeight,d=this.legend;if(d){var f=this.legendDiv.offsetHeight,d=d.position;if("top"==d||"bottom"==d){e-=f;if(0>e||isNaN(e)){e=0}this.chartDiv.style.height=e+"px"}}return e},updateWidth:function(){var i=this.divRealWidth,h=this.divRealHeight,p=this.legend;if(p){var n=this.legendDiv,o=n.offsetWidth;isNaN(p.width)||(o=p.width);p.ieW&&(o=p.ieW);var m=n.offsetHeight,n=n.style,l=this.chartDiv.style,j=p.position;if(("right"==j||"left"==j)&&void 0===p.divId){i-=o;if(0>i||isNaN(i)){i=0}l.width=i+"px";this.balloon&&this.balloon.setBounds&&this.balloon.setBounds(2,2,i-2,this.realHeight);"left"==j?(l.left=o+"px",n.left="0px"):(l.left="0px",n.left=i+"px");h>m&&(n.top=(h-m)/2+"px")}}return i},getTitleHeight:function(){this.drawTitles(!0);return this.titleHeight},addTitle:function(g,f,j,h,i){isNaN(f)&&(f=this.fontSize+2);g={text:g,size:f,color:j,alpha:h,bold:i,enabled:!0};this.titles.push(g);return g},handleWheel:function(d){var c=0;d||(d=window.event);d.wheelDelta?c=d.wheelDelta/120:d.detail&&(c=-d.detail/3);c&&this.handleWheelReal(c,d.shiftKey);d.preventDefault&&d.preventDefault()},handleWheelReal:function(){},handleDocTouchStart:function(){this.handleMouseMove();this.tmx=this.mouseX;this.tmy=this.mouseY;this.touchStartTime=(new Date).getTime()},handleDocTouchEnd:function(){-0.5<this.tmx&&this.tmx<this.divRealWidth+1&&0<this.tmy&&this.tmy<this.divRealHeight?(this.handleMouseMove(),4>Math.abs(this.mouseX-this.tmx)&&4>Math.abs(this.mouseY-this.tmy)?(this.tapped=!0,this.panRequired&&this.panEventsEnabled&&this.chartDiv&&(this.chartDiv.style.msTouchAction="none",this.chartDiv.style.touchAction="none")):this.mouseIsOver||this.resetTouchStyle()):(this.tapped=!1,this.resetTouchStyle())},resetTouchStyle:function(){this.panEventsEnabled&&this.chartDiv&&(this.chartDiv.style.msTouchAction="auto",this.chartDiv.style.touchAction="auto")},checkTouchDuration:function(e){var d=this,f=(new Date).getTime();if(e){if(e.touches){d.isTouchEvent=!0}else{if(!d.isTouchEvent){return !0}}}if(f-d.touchStartTime>d.touchClickDuration){return !0}setTimeout(function(){d.resetTouchDuration()},300)},resetTouchDuration:function(){this.isTouchEvent=!1},checkTouchMoved:function(){if(4<Math.abs(this.mouseX-this.tmx)||4<Math.abs(this.mouseY-this.tmy)){return !0}},addListeners:function(){var d=this,c=d.chartDiv;document.addEventListener?("ontouchstart" in document.documentElement&&(c.addEventListener("touchstart",function(e){d.handleTouchStart.call(d,e)},!0),c.addEventListener("touchmove",function(e){d.handleMouseMove.call(d,e)},!0),c.addEventListener("touchend",function(e){d.handleTouchEnd.call(d,e)},!0),d.docfn1=function(e){d.handleDocTouchStart.call(d,e)},d.docfn2=function(e){d.handleDocTouchEnd.call(d,e)},document.addEventListener("touchstart",d.docfn1,!0),document.addEventListener("touchend",d.docfn2,!0)),c.addEventListener("mousedown",function(e){d.mouseIsOver=!0;d.handleMouseMove.call(d,e);d.handleMouseDown.call(d,e);d.handleDocTouchStart.call(d,e)},!0),c.addEventListener("mouseover",function(e){d.handleMouseOver.call(d,e)},!0),c.addEventListener("mouseout",function(e){d.handleMouseOut.call(d,e)},!0),c.addEventListener("mouseup",function(e){d.handleDocTouchEnd.call(d,e)},!0)):(c.attachEvent("onmousedown",function(e){d.handleMouseDown.call(d,e)}),c.attachEvent("onmouseover",function(e){d.handleMouseOver.call(d,e)}),c.attachEvent("onmouseout",function(e){d.handleMouseOut.call(d,e)}))},dispDUpd:function(){this.skipEvents||(this.dispatchDataUpdated&&(this.dispatchDataUpdated=!1,this.fire({type:"dataUpdated",chart:this})),this.chartCreated||(this.chartCreated=!0,this.fire({type:"init",chart:this})),this.chartRendered||(this.fire({type:"rendered",chart:this}),this.chartRendered=!0),this.fire({type:"drawn",chart:this}));this.skipEvents=!1},validateSize:function(){var e=this;e.premeasure();e.checkDisplay();e.cssScale=1;e.cssAngle=0;e.checkTransform(e.div);if(e.divRealWidth!=e.previousWidth||e.divRealHeight!=e.previousHeight){var d=e.legend;if(0<e.realWidth&&0<e.realHeight){e.sizeChanged=!0;if(d){e.legendInitTO&&clearTimeout(e.legendInitTO);var f=setTimeout(function(){d.invalidateSize()},10);e.timeOuts.push(f);e.legendInitTO=f}e.marginsUpdated=!1;clearTimeout(e.initTO);f=setTimeout(function(){e.initChart()},10);e.timeOuts.push(f);e.initTO=f}e.renderFix();d&&d.renderFix&&d.renderFix();clearTimeout(e.resizedTO);e.resizedTO=setTimeout(function(){e.fire({type:"resized",chart:e})},10);e.previousHeight=e.divRealHeight;e.previousWidth=e.divRealWidth}},invalidateSize:function(){this.previousHeight=this.previousWidth=NaN;this.invalidateSizeReal()},invalidateSizeReal:function(){var d=this;d.marginsUpdated=!1;clearTimeout(d.validateTO);var c=setTimeout(function(){d.validateSize()},5);d.timeOuts.push(c);d.validateTO=c},validateData:function(b){this.chartCreated&&(this.dataChanged=!0,this.marginsUpdated=!1,this.initChart(b))},validateNow:function(e,d){this.initTO&&clearTimeout(this.initTO);e&&(this.dataChanged=!0,this.marginsUpdated=!1);this.skipEvents=d;this.chartRendered=!1;var f=this.legend;f&&f.position!=this.prevLegendPosition&&(this.previousWidth=this.mw=0,f.invalidateSize&&(f.invalidateSize(),this.validateSize()));this.write(this.div)},showItem:function(b){b.hidden=!1;this.initChart()},hideItem:function(b){b.hidden=!0;this.initChart()},hideBalloon:function(){var b=this;clearTimeout(b.hoverInt);clearTimeout(b.balloonTO);b.hoverInt=setTimeout(function(){b.hideBalloonReal.call(b)},b.hideBalloonTime)},cleanChart:function(){},hideBalloonReal:function(){var b=this.balloon;b&&b.hide&&b.hide()},showBalloon:function(h,g,l,j,k){var i=this;clearTimeout(i.balloonTO);clearTimeout(i.hoverInt);i.balloonTO=setTimeout(function(){i.showBalloonReal.call(i,h,g,l,j,k)},1)},showBalloonReal:function(h,g,l,j,k){this.handleMouseMove();var i=this.balloon;i.enabled&&(i.followCursor(!1),i.changeColor(g),!l||i.fixedPosition?(i.setPosition(j,k),isNaN(j)||isNaN(k)?i.followCursor(!0):i.followCursor(!1)):i.followCursor(!0),h&&i.showBalloon(h))},handleMouseOver:function(){this.outTO&&clearTimeout(this.outTO);a.resetMouseOver();this.mouseIsOver=!0},handleMouseOut:function(){var b=this;a.resetMouseOver();b.outTO&&clearTimeout(b.outTO);b.outTO=setTimeout(function(){b.handleMouseOutReal()},10)},handleMouseOutReal:function(){this.mouseIsOver=!1},handleMouseMove:function(h){h||(h=window.event);this.mouse2Y=this.mouse2X=NaN;var g,l,j,k;if(h){if(h.touches){var i=h.touches.item(1);i&&this.panEventsEnabled&&this.boundingRect&&(j=i.clientX-this.boundingRect.left,k=i.clientY-this.boundingRect.top);h=h.touches.item(0);if(!h){return}}else{this.wasTouched=!1}this.boundingRect&&h.clientX&&(g=h.clientX-this.boundingRect.left,l=h.clientY-this.boundingRect.top);isNaN(j)?this.mouseX=g:(this.mouseX=Math.min(g,j),this.mouse2X=Math.max(g,j));isNaN(k)?this.mouseY=l:(this.mouseY=Math.min(l,k),this.mouse2Y=Math.max(l,k));this.autoTransform&&(this.mouseX/=this.cssScale,this.mouseY/=this.cssScale)}},handleTouchStart:function(b){this.hideBalloonReal();b&&(b.touches&&this.tapToActivate&&!this.tapped||!this.panRequired)||(this.handleMouseMove(b),this.handleMouseDown(b))},handleTouchEnd:function(b){this.wasTouched=!0;this.handleMouseMove(b);a.resetMouseOver();this.handleReleaseOutside(b)},handleReleaseOutside:function(){this.handleDocTouchEnd.call(this)},handleMouseDown:function(b){a.resetMouseOver();this.mouseIsOver=!0;b&&b.preventDefault&&(this.panEventsEnabled?b.preventDefault():b.touches||b.preventDefault())},addLegend:function(e,d){e=a.processObject(e,a.AmLegend,this.theme);e.divId=d;e.ieW=0;var f;f="object"!=typeof d&&d?document.getElementById(d):d;this.legend=e;e.chart=this;f?(e.div=f,e.position="outside",e.autoMargins=!1):e.div=this.legendDiv;return e},removeLegend:function(){this.legend=void 0;this.previousWidth=0;this.legendDiv.innerHTML=""},handleResize:function(){(a.isPercents(this.width)||a.isPercents(this.height))&&this.invalidateSizeReal();this.renderFix()},renderFix:function(){if(!a.VML){var b=this.container;b&&b.renderFix()}},getSVG:function(){if(a.hasSVG){return this.container}},animate:function(i,d,n,m,j,l,k){i["an_"+d]&&a.removeFromArray(this.animations,i["an_"+d]);n={obj:i,frame:0,attribute:d,from:n,to:m,time:j,effect:l,suffix:k};i["an_"+d]=n;this.animations.push(n);return n},setLegendData:function(d){var c=this.legend;c&&c.setData(d)},stopAnim:function(b){a.removeFromArray(this.animations,b)},updateAnimations:function(){var i;this.container&&this.container.update();if(this.animations){for(i=this.animations.length-1;0<=i;i--){var d=this.animations[i],p=a.updateRate*d.time,o=d.frame+1,l=d.obj,n=d.attribute;if(o<=p){d.frame++;var m=Number(d.from),j=Number(d.to)-m,p=a[d.effect](0,o,m,j,p);0===j?(this.animations.splice(i,1),l.node.style[n]=Number(d.to)+d.suffix):l.node.style[n]=p+d.suffix}else{l.node.style[n]=Number(d.to)+d.suffix,l.animationFinished=!0,this.animations.splice(i,1)}}}},update:function(){this.updateAnimations();var f=this.animatable;if(0<f.length){for(var d=!0,h=f.length-1;0<=h;h--){var g=f[h];g&&(g.animationFinished?f.splice(h,1):d=!1)}d&&(this.fire({type:"animationFinished",chart:this}),this.animatable=[])}},inIframe:function(){try{return window.self!==window.top}catch(b){return !0}},brr:function(){if(!this.hideCredits){var t="amcharts.com",s=window.location.hostname.split("."),r;2<=s.length&&(r=s[s.length-2]+"."+s[s.length-1]);this.amLink&&(s=this.amLink.parentNode)&&s.removeChild(this.amLink);s=this.creditsPosition;if(r!=t||!0===this.inIframe()){var t="http://www."+t,p=r=0,q=this.realWidth,o=this.realHeight,n=this.type;if("serial"==n||"xy"==n||"gantt"==n){r=this.marginLeftReal,p=this.marginTopReal,q=r+this.plotAreaWidth,o=p+this.plotAreaHeight}var n=t+"/javascript-charts/",j="JavaScript charts",i="JS chart by amCharts";"ammap"==this.product&&(n=t+"/javascript-maps/",j="Interactive JavaScript maps",i="JS map by amCharts");t=document.createElement("a");i=document.createTextNode(i);t.setAttribute("href",n);t.setAttribute("title",j);this.urlTarget&&t.setAttribute("target",this.urlTarget);t.appendChild(i);this.chartDiv.appendChild(t);this.amLink=t;n=t.style;n.position="absolute";n.textDecoration="none";n.color=this.color;n.fontFamily=this.fontFamily;n.fontSize="11px";n.opacity=0.7;n.display="block";var j=t.offsetWidth,t=t.offsetHeight,i=5+r,h=p+5;"bottom-left"==s&&(i=5+r,h=o-t-3);"bottom-right"==s&&(i=q-j-5,h=o-t-3);"top-right"==s&&(i=q-j-5,h=p+5);n.left=i+"px";n.top=h+"px"}}}});a.Slice=a.Class({construct:function(){}});a.SerialDataItem=a.Class({construct:function(){}});a.GraphDataItem=a.Class({construct:function(){}});a.Guide=a.Class({construct:function(b){this.cname="Guide";a.applyTheme(this,b,this.cname)}});a.Title=a.Class({construct:function(b){this.cname="Title";a.applyTheme(this,b,this.cname)}});a.Label=a.Class({construct:function(b){this.cname="Label";a.applyTheme(this,b,this.cname)}})})();(function(){var a=window.AmCharts;a.AmGraph=a.Class({construct:function(b){this.cname="AmGraph";this.createEvents("rollOverGraphItem","rollOutGraphItem","clickGraphItem","doubleClickGraphItem","rightClickGraphItem","clickGraph","rollOverGraph","rollOutGraph");this.type="line";this.stackable=!0;this.columnCount=1;this.columnIndex=0;this.centerCustomBullets=this.showBalloon=!0;this.maxBulletSize=50;this.minBulletSize=4;this.balloonText="[[value]]";this.hidden=this.scrollbar=this.animationPlayed=!1;this.pointPosition="middle";this.depthCount=1;this.includeInMinMax=!0;this.negativeBase=0;this.visibleInLegend=!0;this.showAllValueLabels=!1;this.showBulletsAt=this.showBalloonAt="close";this.lineThickness=1;this.dashLength=0;this.connect=!0;this.lineAlpha=1;this.bullet="none";this.bulletBorderThickness=2;this.bulletBorderAlpha=0;this.bulletAlpha=1;this.bulletSize=8;this.cornerRadiusTop=this.hideBulletsCount=this.bulletOffset=0;this.cursorBulletAlpha=1;this.gradientOrientation="vertical";this.dy=this.dx=0;this.periodValue="";this.clustered=!0;this.periodSpan=1;this.accessibleLabel="[[title]] [[category]] [[value]]";this.accessibleSkipText="Press enter to skip [[title]]";this.y=this.x=0;this.switchable=!0;this.tcc=this.minDistance=1;this.labelRotation=0;this.labelAnchor="auto";this.labelOffset=3;this.bcn="graph-";this.dateFormat="MMM DD, YYYY";this.noRounding=!0;a.applyTheme(this,b,this.cname)},init:function(){this.createBalloon()},draw:function(){var g=this.chart;g.isRolledOverBullet=!1;var d=g.type;if(g.drawGraphs){isNaN(this.precision)||(this.numberFormatter?this.numberFormatter.precision=this.precision:this.numberFormatter={precision:this.precision,decimalSeparator:g.decimalSeparator,thousandsSeparator:g.thousandsSeparator});var l=g.container;this.container=l;this.destroy();var k=l.set();this.set=k;k.translate(this.x,this.y);var i=l.set();this.bulletSet=i;i.translate(this.x,this.y);this.behindColumns?(g.graphsBehindSet.push(k),g.bulletBehindSet.push(i)):(g.graphsSet.push(k),g.bulletSet.push(i));var j=this.bulletAxis;a.isString(j)&&(this.bulletAxis=g.getValueAxisById(j));l=l.set();a.remove(this.columnsSet);this.columnsSet=l;a.setCN(g,k,"graph-"+this.type);a.setCN(g,k,"graph-"+this.id);a.setCN(g,i,"graph-"+this.type);a.setCN(g,i,"graph-"+this.id);this.columnsArray=[];this.ownColumns=[];this.allBullets=[];this.animationArray=[];i=this.labelPosition;i||(j=this.valueAxis.stackType,i="top","column"==this.type&&(g.rotate&&(i="right"),"100%"==j||"regular"==j)&&(i="middle"),this.labelPosition=i);a.ifArray(this.data)&&(g=!1,"xy"==d?this.xAxis.axisCreated&&this.yAxis.axisCreated&&(g=!0):this.valueAxis.axisCreated&&(g=!0),!this.hidden&&g&&this.createGraph());k.push(l)}},createGraph:function(){var e=this,d=e.chart;e.startAlpha=d.startAlpha;e.seqAn=d.sequencedAnimation;e.baseCoord=e.valueAxis.baseCoord;void 0===e.fillAlphas&&(e.fillAlphas=0);e.bulletColorR=e.bulletColor;void 0===e.bulletColorR&&(e.bulletColorR=e.lineColorR,e.bulletColorNegative=e.negativeLineColor);void 0===e.bulletAlpha&&(e.bulletAlpha=e.lineAlpha);if("step"==f||a.VML){e.noRounding=!1}var f=d.type;"gantt"==f&&(f="serial");clearTimeout(e.playedTO);if(!isNaN(e.valueAxis.min)&&!isNaN(e.valueAxis.max)){switch(f){case"serial":e.categoryAxis&&(e.createSerialGraph(),"candlestick"==e.type&&1>e.valueAxis.minMaxMultiplier&&e.positiveClip(e.set));break;case"radar":e.createRadarGraph();break;case"xy":e.createXYGraph()}e.playedTO=setTimeout(function(){e.setAnimationPlayed.call(e)},500*e.chart.startDuration)}},setAnimationPlayed:function(){this.animationPlayed=!0},createXYGraph:function(){var v=[],u=[],t=this.xAxis,r=this.yAxis;this.pmh=r.height;this.pmw=t.width;this.pmy=this.pmx=0;var s;for(s=this.start;s<=this.end;s++){var q=this.data[s].axes[t.id].graphs[this.id],o=q.values,n=o.x,j=o.y,o=t.getCoordinate(n,this.noRounding),i=r.getCoordinate(j,this.noRounding);if(!isNaN(n)&&!isNaN(j)&&(v.push(o),u.push(i),q.x=o,q.y=i,n=this.createBullet(q,o,i,s),j=this.labelText)){var j=this.createLabel(q,j),h=0;n&&(h=n.size);this.positionLabel(q,o,i,j,h)}}this.drawLineGraph(v,u);this.launchAnimation()},createRadarGraph:function(){var F=this.valueAxis.stackType,E=[],D=[],A=[],B=[],y,x,v,u,s;for(s=this.start;s<=this.end;s++){var j=this.data[s].axes[this.valueAxis.id].graphs[this.id],i,o;"none"==F||"3d"==F?i=j.values.value:(i=j.values.close,o=j.values.open);if(isNaN(i)){this.connect||(this.drawLineGraph(E,D,A,B),E=[],D=[],A=[],B=[])}else{var H=this.valueAxis.getCoordinate(i,this.noRounding)-this.height,H=H*this.valueAxis.rMultiplier,h=-360/(this.end-this.start+1)*s;"middle"==this.valueAxis.pointPosition&&(h-=180/(this.end-this.start+1));i=H*Math.sin(h/180*Math.PI);H*=Math.cos(h/180*Math.PI);E.push(i);D.push(H);if(!isNaN(o)){var G=this.valueAxis.getCoordinate(o,this.noRounding)-this.height,G=G*this.valueAxis.rMultiplier,C=G*Math.sin(h/180*Math.PI),h=G*Math.cos(h/180*Math.PI);A.push(C);B.push(h);isNaN(v)&&(v=C);isNaN(u)&&(u=h)}h=this.createBullet(j,i,H,s);j.x=i;j.y=H;if(C=this.labelText){C=this.createLabel(j,C),G=0,h&&(G=h.size),this.positionLabel(j,i,H,C,G)}isNaN(y)&&(y=i);isNaN(x)&&(x=H)}}E.push(y);D.push(x);isNaN(v)||(A.push(v),B.push(u));this.drawLineGraph(E,D,A,B);this.launchAnimation()},positionLabel:function(F,E,D,A,B){if(A){var y=this.chart,x=this.valueAxis,v="middle",u=!1,s=this.labelPosition,j=A.getBBox(),i=this.chart.rotate,o=F.isNegative,H=this.fontSize;void 0===H&&(H=this.chart.fontSize);D-=j.height/2-H/2-1;void 0!==F.labelIsNegative&&(o=F.labelIsNegative);switch(s){case"right":s=i?o?"left":"right":"right";break;case"top":s=i?"top":o?"bottom":"top";break;case"bottom":s=i?"bottom":o?"top":"bottom";break;case"left":s=i?o?"right":"left":"left"}var H=F.columnGraphics,h=0,G=0;H&&(h=H.x,G=H.y);var C=this.labelOffset;switch(s){case"right":v="start";E+=B/2+C;break;case"top":D=x.reversed?D+(B/2+j.height/2+C):D-(B/2+j.height/2+C);break;case"bottom":D=x.reversed?D-(B/2+j.height/2+C):D+(B/2+j.height/2+C);break;case"left":v="end";E-=B/2+C;break;case"inside":"column"==this.type&&(u=!0,i?o?(v="end",E=h-3-C):(v="start",E=h+3+C):D=o?G+7+C:G-10-C);break;case"middle":"column"==this.type&&(u=!0,i?E-=(E-h)/2+C-3:D-=(D-G)/2+C-3)}"auto"!=this.labelAnchor&&(v=this.labelAnchor);A.attr({"text-anchor":v});this.labelRotation&&A.rotate(this.labelRotation);A.translate(E,D);!this.showAllValueLabels&&H&&u&&(j=A.getBBox(),j.height>F.columnHeight||j.width>F.columnWidth)&&(A.remove(),A=null);if(A&&"radar"!=y.type){if(i){if(0>D||D>this.height){A.remove(),A=null}!this.showAllValueLabels&&A&&(0>E||E>this.width)&&(A.remove(),A=null)}else{if(0>E||E>this.width){A.remove(),A=null}!this.showAllValueLabels&&A&&(0>D||D>this.height)&&(A.remove(),A=null)}}A&&this.allBullets.push(A);return A}},getGradRotation:function(){var b=270;"horizontal"==this.gradientOrientation&&(b=0);return this.gradientRotation=b},createSerialGraph:function(){this.dashLengthSwitched=this.fillColorsSwitched=this.lineColorSwitched=void 0;var bP=this.chart,bO=this.id,bN=this.index,bM=this.data,bH=this.chart.container,bK=this.valueAxis,bI=this.type,bF=this.columnWidthReal,bE=this.showBulletsAt;isNaN(this.columnWidth)||(bF=this.columnWidth);isNaN(bF)&&(bF=0.8);var bD=this.useNegativeColorIfDown,bB=this.width,bz=this.height,bC=this.y,bw=this.rotate,by=this.columnCount,bt=a.toCoordinate(this.cornerRadiusTop,bF/2),bp=this.connect,bs=[],bv=[],cs,br,cq,co,cp=this.chart.graphs.length,ch,ci=this.dx/this.tcc,b5=this.dy/this.tcc,b9=bK.stackType,b6=this.start,ay=this.end,cf=this.scrollbar,cE="graph-column-";cf&&(cE="scrollbar-graph-column-");var cI=this.categoryAxis,cx=this.baseCoord,aA=this.negativeBase,bU=this.columnIndex,bl=this.lineThickness,bW=this.lineAlpha,ad=this.lineColorR,a9=this.dashLength,a0=this.set,aN,aP=this.getGradRotation(),bY=this.chart.columnSpacing,bV=cI.cellWidth,aw=(bV*bF-by)/by;bY>aw&&(bY=aw);var ck,bu,bT,aG=bz,ar=bB,bL=0,aJ=0,aB=0,at=0,aO=0,aF=0,ak=this.fillColorsR,aj=this.negativeFillColors,bR=this.negativeLineColor,a6=this.fillAlphas,aV=this.negativeFillAlphas;"object"==typeof a6&&(a6=a6[0]);"object"==typeof aV&&(aV=aV[0]);var s=this.noRounding;"step"==bI&&(s=!1);var ax=bK.getCoordinate(bK.min);bK.logarithmic&&(ax=bK.getCoordinate(bK.minReal));this.minCoord=ax;this.resetBullet&&(this.bullet="none");if(!(cf||"line"!=bI&&"smoothedLine"!=bI&&"step"!=bI||(1==bM.length&&"step"!=bI&&"none"==this.bullet&&(this.bullet="round",this.resetBullet=!0),!aj&&void 0==bR||bD))){var cz=aA;cz>bK.max&&(cz=bK.max);cz<bK.min&&(cz=bK.min);bK.logarithmic&&(cz=bK.minReal);var bn=bK.getCoordinate(cz),a1=bK.getCoordinate(bK.max);bw?(aG=bz,ar=Math.abs(a1-bn),aB=bz,at=Math.abs(ax-bn),aF=aJ=0,bK.reversed?(bL=0,aO=bn):(bL=bn,aO=0)):(ar=bB,aG=Math.abs(a1-bn),at=bB,aB=Math.abs(ax-bn),aO=bL=0,bK.reversed?(aF=bC,aJ=bn):aF=bn)}var be=Math.round;this.pmx=be(bL);this.pmy=be(aJ);this.pmh=be(aG);this.pmw=be(ar);this.nmx=be(aO);this.nmy=be(aF);this.nmh=be(aB);this.nmw=be(at);a.isModern||(this.nmy=this.nmx=0,this.nmh=this.height);this.clustered||(by=1);bF="column"==bI?(bV*bF-bY*(by-1))/by:bV*bF;1>bF&&(bF=1);var aQ=this.fixedColumnWidth;isNaN(aQ)||(bF=aQ);var cd;if("line"==bI||"step"==bI||"smoothedLine"==bI){if(0<b6){for(cd=b6-1;-1<cd;cd--){if(ck=bM[cd],bu=ck.axes[bK.id].graphs[bO],bT=bu.values.value,!isNaN(bT)){b6=cd;break}}if(this.lineColorField){for(cd=b6;-1<cd;cd--){if(ck=bM[cd],bu=ck.axes[bK.id].graphs[bO],bu.lineColor){this.lineColorSwitched=bu.lineColor;void 0===this.bulletColor&&(this.bulletColorSwitched=this.lineColorSwitched);break}}}if(this.fillColorsField){for(cd=b6;-1<cd;cd--){if(ck=bM[cd],bu=ck.axes[bK.id].graphs[bO],bu.fillColors){this.fillColorsSwitched=bu.fillColors;break}}}if(this.dashLengthField){for(cd=b6;-1<cd;cd--){if(ck=bM[cd],bu=ck.axes[bK.id].graphs[bO],!isNaN(bu.dashLength)){this.dashLengthSwitched=bu.dashLength;break}}}}if(ay<bM.length-1){for(cd=ay+1;cd<bM.length;cd++){if(ck=bM[cd],bu=ck.axes[bK.id].graphs[bO],bT=bu.values.value,!isNaN(bT)){ay=cd;break}}}}ay<bM.length-1&&ay++;var b0=[],bZ=[],a2=!1;if("line"==bI||"step"==bI||"smoothedLine"==bI){if(this.stackable&&"regular"==b9||"100%"==b9||this.fillToGraph){a2=!0}}var aH=this.noStepRisers,ao=-1000,ag=-1000,d=this.minDistance,o=!0,ct=!1;for(cd=b6;cd<=ay;cd++){ck=bM[cd];bu=ck.axes[bK.id].graphs[bO];bu.index=cd;var cD,cK=NaN;if(bD&&void 0==this.openField){for(var cL=cd+1;cL<bM.length&&(!bM[cL]||!(cD=bM[cd+1].axes[bK.id].graphs[bO])||!cD.values||(cK=cD.values.value,isNaN(cK)));cL++){}}var b2,b3,ce,cl,ap=NaN,cn=NaN,cm=NaN,b7=NaN,b8=NaN,bg=NaN,a4=NaN,aT=NaN,aK=NaN,cM=NaN,an=NaN,ah=NaN,i=NaN,bX=NaN,cA=NaN,aW=NaN,aC=NaN,au=void 0,aR=ak,b4=a6,cG=ad,aE,cB,aL=this.proCandlesticks,cH=this.topRadius,af=bz-1,bq=bB-1,cj=this.pattern;void 0!=bu.pattern&&(cj=bu.pattern);isNaN(bu.alpha)||(b4=bu.alpha);isNaN(bu.dashLength)||(a9=bu.dashLength);var cv=bu.values;bK.recalculateToPercents&&(cv=bu.percents);"none"==b9&&(bU=isNaN(bu.columnIndex)?this.columnIndex:bu.columnIndex);if(cv){bX=this.stackable&&"none"!=b9&&"3d"!=b9?cv.close:cv.value;if("candlestick"==bI||"ohlc"==bI){bX=cv.close,aW=cv.low,a4=bK.getCoordinate(aW),cA=cv.high,aK=bK.getCoordinate(cA)}aC=cv.open;cm=bK.getCoordinate(bX,s);isNaN(aC)||(b8=bK.getCoordinate(aC,s),bD&&"regular"!=b9&&"100%"!=b9&&(cK=aC,aC=b8=NaN));bD&&(void 0==this.openField?cD&&(cD.isNegative=cK<bX?!0:!1,isNaN(cK)&&(bu.isNegative=!o)):bu.isNegative=cK>bX?!0:!1);if(!cf){switch(this.showBalloonAt){case"close":bu.y=cm;break;case"open":bu.y=b8;break;case"high":bu.y=aK;break;case"low":bu.y=a4}}var ap=ck.x[cI.id],bA=this.periodSpan-1;"step"!=bI||isNaN(ck.cellWidth)||(bV=ck.cellWidth);var al=Math.floor(bV/2)+Math.floor(bA*bV/2),cO=al,cw=0;"left"==this.stepDirection&&(cw=(2*bV+bA*bV)/2,ap-=cw);"center"==this.stepDirection&&(cw=bV/2,ap-=cw);"start"==this.pointPosition&&(ap-=bV/2+Math.floor(bA*bV/2),al=0,cO=Math.floor(bV)+Math.floor(bA*bV));"end"==this.pointPosition&&(ap+=bV/2+Math.floor(bA*bV/2),al=Math.floor(bV)+Math.floor(bA*bV),cO=0);if(aH){var aD=this.columnWidth;isNaN(aD)||(al*=aD,cO*=aD)}cf||(bu.x=ap);-100000>ap&&(ap=-100000);ap>bB+100000&&(ap=bB+100000);bw?(cn=cm,b7=b8,b8=cm=ap,isNaN(aC)&&!this.fillToGraph&&(b7=cx),bg=a4,aT=aK):(b7=cn=ap,isNaN(aC)&&!this.fillToGraph&&(b8=cx));if(!aL&&bX<aC||aL&&bX<aN){bu.isNegative=!0,aj&&(aR=aj),aV&&(b4=aV),void 0!=bR&&(cG=bR)}ct=!1;isNaN(bX)||(bD?bX>cK?(o&&(ct=!0),o=!1):(o||(ct=!0),o=!0):bu.isNegative=bX<aA?!0:!1,aN=bX);var az=!1;cf&&bP.chartScrollbar.ignoreCustomColors&&(az=!0);az||(void 0!=bu.color&&(aR=bu.color),bu.fillColors&&(aR=bu.fillColors));cm=a.fitToBounds(cm,-30000,30000);switch(bI){case"line":if(isNaN(bX)){bp||(this.drawLineGraph(bs,bv,b0,bZ),bs=[],bv=[],b0=[],bZ=[])}else{if(Math.abs(cn-ao)>=d||Math.abs(cm-ag)>=d){bs.push(cn),bv.push(cm),ao=cn,ag=cm}cM=cn;an=cm;ah=cn;i=cm;!a2||isNaN(b8)||isNaN(b7)||(b0.push(b7),bZ.push(b8));if(ct||void 0!=bu.lineColor&&bu.lineColor!=this.lineColorSwitched||void 0!=bu.fillColors&&bu.fillColors!=this.fillColorsSwitched||!isNaN(bu.dashLength)){this.drawLineGraph(bs,bv,b0,bZ),bs=[cn],bv=[cm],b0=[],bZ=[],!a2||isNaN(b8)||isNaN(b7)||(b0.push(b7),bZ.push(b8)),bD?(o?(this.lineColorSwitched=ad,this.fillColorsSwitched=ak):(this.lineColorSwitched=bR,this.fillColorsSwitched=aj),void 0===this.bulletColor&&(this.bulletColorSwitched=ad)):(this.lineColorSwitched=bu.lineColor,this.fillColorsSwitched=bu.fillColors,void 0===this.bulletColor&&(this.bulletColorSwitched=this.lineColorSwitched)),this.dashLengthSwitched=bu.dashLength}bu.gap&&(this.drawLineGraph(bs,bv,b0,bZ),bs=[],bv=[],b0=[],bZ=[])}break;case"smoothedLine":if(isNaN(bX)){bp||(this.drawSmoothedGraph(bs,bv,b0,bZ),bs=[],bv=[],b0=[],bZ=[])}else{if(Math.abs(cn-ao)>=d||Math.abs(cm-ag)>=d){bs.push(cn),bv.push(cm),ao=cn,ag=cm}cM=cn;an=cm;ah=cn;i=cm;!a2||isNaN(b8)||isNaN(b7)||(b0.push(b7),bZ.push(b8));void 0==bu.lineColor&&void 0==bu.fillColors&&isNaN(bu.dashLength)||(this.drawSmoothedGraph(bs,bv,b0,bZ),bs=[cn],bv=[cm],b0=[],bZ=[],!a2||isNaN(b8)||isNaN(b7)||(b0.push(b7),bZ.push(b8)),this.lineColorSwitched=bu.lineColor,this.fillColorsSwitched=bu.fillColors,this.dashLengthSwitched=bu.dashLength);bu.gap&&(this.drawSmoothedGraph(bs,bv,b0,bZ),bs=[],bv=[],b0=[],bZ=[])}break;case"step":if(!isNaN(bX)){bw?(isNaN(cs)||(bs.push(cs),bv.push(cm-al)),bv.push(cm-al),bs.push(cn),bv.push(cm+cO),bs.push(cn),!a2||isNaN(b8)||isNaN(b7)||(isNaN(cq)||(b0.push(cq),bZ.push(b8-al)),b0.push(b7),bZ.push(b8-al),b0.push(b7),bZ.push(b8+cO))):(isNaN(br)||(bv.push(br),bs.push(cn-al)),bs.push(cn-al),bv.push(cm),bs.push(cn+cO),bv.push(cm),!a2||isNaN(b8)||isNaN(b7)||(isNaN(co)||(b0.push(b7-al),bZ.push(co)),b0.push(b7-al),bZ.push(b8),b0.push(b7+cO),bZ.push(b8)));cs=cn;br=cm;cq=b7;co=b8;cM=cn;an=cm;ah=cn;i=cm;if(ct||void 0!=bu.lineColor||void 0!=bu.fillColors||!isNaN(bu.dashLength)){var av=bs[bs.length-2],bj=bv[bv.length-2];bs.pop();bv.pop();b0.pop();bZ.pop();this.drawLineGraph(bs,bv,b0,bZ);bs=[av];bv=[bj];b0=[];bZ=[];a2&&(b0=[av,av+al+cO],bZ=[co,co]);bw?(bv.push(cm+cO),bs.push(cn)):(bs.push(cn+cO),bv.push(cm));this.lineColorSwitched=bu.lineColor;this.fillColorsSwitched=bu.fillColors;this.dashLengthSwitched=bu.dashLength;bD&&(o?(this.lineColorSwitched=ad,this.fillColorsSwitched=ak):(this.lineColorSwitched=bR,this.fillColorsSwitched=aj))}if(aH||bu.gap){cs=br=NaN,this.drawLineGraph(bs,bv,b0,bZ),bs=[],bv=[],b0=[],bZ=[]}}else{if(!bp){if(1>=this.periodSpan||1<this.periodSpan&&cn-cs>al+cO){cs=br=NaN}this.drawLineGraph(bs,bv,b0,bZ);bs=[];bv=[];b0=[];bZ=[]}}break;case"column":aE=cG;void 0!=bu.lineColor&&(aE=bu.lineColor);if(!isNaN(bX)){bD||(bu.isNegative=bX<aA?!0:!1);bu.isNegative&&(aj&&(aR=aj),void 0!=bR&&(aE=bR));var aq=bK.min,ai=bK.max,bS=aC;isNaN(bS)&&(bS=aA);if(!(bX<aq&&bS<aq||bX>ai&&bS>ai)){var aX;if(bw){"3d"==b9?(b3=cm-(by/2-this.depthCount+1)*(bF+bY)+bY/2+b5*bU,b2=b7+ci*bU,aX=bU):(b3=Math.floor(cm-(by/2-bU)*(bF+bY)+bY/2),b2=b7,aX=0);ce=bF;cM=cn;an=b3+bF/2;ah=cn;i=b3+bF/2;b3+ce>bz+aX*b5&&(ce=bz-b3+aX*b5);b3<aX*b5&&(ce+=b3,b3=aX*b5);cl=cn-b7;var a7=b2;b2=a.fitToBounds(b2,0,bB);cl+=a7-b2;cl=a.fitToBounds(cl,-b2,bB-b2+ci*bU);bu.labelIsNegative=0>cl?!0:!1;0===cl&&1/bX===1/-0&&(bu.labelIsNegative=!0);isNaN(ck.percentWidthValue)||(ce=this.height*ck.percentWidthValue/100,b3=ap-ce/2,an=b3+ce/2);ce=a.roundTo(ce,2);cl=a.roundTo(cl,2);b3<bz&&0<ce&&(au=new a.Cuboid(bH,cl,ce,ci-bP.d3x,b5-bP.d3y,aR,b4,bl,aE,bW,aP,bt,bw,a9,cj,cH,cE),bu.columnWidth=Math.abs(cl),bu.columnHeight=Math.abs(ce))}else{"3d"==b9?(b2=cn-(by/2-this.depthCount+1)*(bF+bY)+bY/2+ci*bU,b3=b8+b5*bU,aX=bU):(b2=cn-(by/2-bU)*(bF+bY)+bY/2,b3=b8,aX=0);ce=bF;cM=b2+bF/2;an=cm;ah=b2+bF/2;i=cm;b2+ce>bB+aX*ci&&(ce=bB-b2+aX*ci);b2<aX*ci&&(ce+=b2-aX*ci,b2=aX*ci);cl=cm-b8;bu.labelIsNegative=0<cl?!0:!1;0===cl&&1/bX!==1/Math.abs(bX)&&(bu.labelIsNegative=!0);var aY=b3;b3=a.fitToBounds(b3,this.dy,bz);cl+=aY-b3;cl=a.fitToBounds(cl,-b3+b5*aX,bz-b3);isNaN(ck.percentWidthValue)||(ce=this.width*ck.percentWidthValue/100,b2=ap-ce/2,cM=b2+ce/2);ce=a.roundTo(ce,2);cl=a.roundTo(cl,2);b2<bB+bU*ci&&0<ce&&(this.showOnAxis&&(b3-=b5/2),au=new a.Cuboid(bH,ce,cl,ci-bP.d3x,b5-bP.d3y,aR,b4,bl,aE,this.lineAlpha,aP,bt,bw,a9,cj,cH,cE),bu.columnHeight=Math.abs(cl),bu.columnWidth=Math.abs(ce))}}if(au){cB=au.set;a.setCN(bP,au.set,"graph-"+this.type);a.setCN(bP,au.set,"graph-"+this.id);bu.className&&a.setCN(bP,au.set,bu.className,!0);bu.columnGraphics=cB;b2=a.roundTo(b2,2);b3=a.roundTo(b3,2);cB.translate(b2,b3);(bu.url||this.showHandOnHover)&&cB.setAttr("cursor","pointer");if(!cf){"none"==b9&&(ch=bw?(this.end+1-cd)*cp-bN:cp*cd+bN);"3d"==b9&&(bw?(ch=(this.end+1-cd)*cp-bN-1000*this.depthCount,cM+=ci*bU,ah+=ci*bU,bu.y+=ci*bU):(ch=(cp-bN)*(cd+1)+1000*this.depthCount,an+=b5*bU,i+=b5*bU,bu.y+=b5*bU));if("regular"==b9||"100%"==b9){ch=bw?0<cv.value?(this.end+1-cd)*cp+bN:(this.end+1-cd)*cp-bN:0<cv.value?cp*cd+bN:cp*cd-bN}this.columnsArray.push({column:au,depth:ch});bu.x=bw?b3+ce/2:b2+ce/2;this.ownColumns.push(au);this.animateColumns(au,cd,cn,b7,cm,b8);this.addListeners(cB,bu);void 0!==this.tabIndex&&cB.setAttr("tabindex",this.tabIndex)}this.columnsSet.push(cB)}}break;case"candlestick":if(!isNaN(aC)&&!isNaN(bX)){var bi,bJ;aE=cG;void 0!=bu.lineColor&&(aE=bu.lineColor);cM=cn;i=an=cm;ah=cn;if(bw){"open"==bE&&(ah=b7);"high"==bE&&(ah=aT);"low"==bE&&(ah=bg);cn=a.fitToBounds(cn,0,bq);b7=a.fitToBounds(b7,0,bq);bg=a.fitToBounds(bg,0,bq);aT=a.fitToBounds(aT,0,bq);if(0===cn&&0===b7&&0===bg&&0===aT){continue}if(cn==bq&&b7==bq&&bg==bq&&aT==bq){continue}b3=cm-bF/2;b2=b7;ce=bF;b3+ce>bz&&(ce=bz-b3);0>b3&&(ce+=b3,b3=0);if(b3<bz&&0<ce){var am,ae;bX>aC?(am=[cn,aT],ae=[b7,bg]):(am=[b7,aT],ae=[cn,bg]);!isNaN(aT)&&!isNaN(bg)&&cm<bz&&0<cm&&(bi=a.line(bH,am,[cm,cm],aE,bW,bl),bJ=a.line(bH,ae,[cm,cm],aE,bW,bl));cl=cn-b7;au=new a.Cuboid(bH,cl,ce,ci,b5,aR,a6,bl,aE,bW,aP,bt,bw,a9,cj,cH,cE)}}else{"open"==bE&&(i=b8);"high"==bE&&(i=aK);"low"==bE&&(i=a4);cm=a.fitToBounds(cm,0,af);b8=a.fitToBounds(b8,0,af);a4=a.fitToBounds(a4,0,af);aK=a.fitToBounds(aK,0,af);if(0===cm&&0===b8&&0===a4&&0===aK){continue}if(cm==af&&b8==af&&a4==af&&aK==af){continue}b2=cn-bF/2;b3=b8+bl/2;ce=bF;b2+ce>bB&&(ce=bB-b2);0>b2&&(ce+=b2,b2=0);cl=cm-b8;if(b2<bB&&0<ce){aL&&bX>=aC&&(b4=0);var au=new a.Cuboid(bH,ce,cl,ci,b5,aR,b4,bl,aE,bW,aP,bt,bw,a9,cj,cH,cE),cN,cF;bX>aC?(cN=[cm,aK],cF=[b8,a4]):(cN=[b8,aK],cF=[cm,a4]);!isNaN(aK)&&!isNaN(a4)&&cn<bB&&0<cn&&(bi=a.line(bH,[cn,cn],cN,aE,bW,bl),bJ=a.line(bH,[cn,cn],cF,aE,bW,bl),a.setCN(bP,bi,this.bcn+"line-high"),bu.className&&a.setCN(bP,bi,bu.className,!0),a.setCN(bP,bJ,this.bcn+"line-low"),bu.className&&a.setCN(bP,bJ,bu.className,!0))}}au&&(cB=au.set,bu.columnGraphics=cB,a0.push(cB),cB.translate(b2,b3-bl/2),(bu.url||this.showHandOnHover)&&cB.setAttr("cursor","pointer"),bi&&(a0.push(bi),a0.push(bJ)),cf||(bu.x=bw?b3+ce/2:b2+ce/2,this.animateColumns(au,cd,cn,b7,cm,b8),this.addListeners(cB,bu),void 0!==this.tabIndex&&cB.setAttr("tabindex",this.tabIndex)))}break;case"ohlc":if(!(isNaN(aC)||isNaN(cA)||isNaN(aW)||isNaN(bX))){var j=bH.set();a0.push(j);bX<aC&&(bu.isNegative=!0,void 0!=bR&&(cG=bR));void 0!=bu.lineColor&&(cG=bu.lineColor);var bo,bf,a3;if(bw){i=cm;ah=cn;"open"==bE&&(ah=b7);"high"==bE&&(ah=aT);"low"==bE&&(ah=bg);bg=a.fitToBounds(bg,0,bq);aT=a.fitToBounds(aT,0,bq);if(0===cn&&0===b7&&0===bg&&0===aT){continue}if(cn==bq&&b7==bq&&bg==bq&&aT==bq){continue}var cu=cm-bF/2,cu=a.fitToBounds(cu,0,bz),cJ=a.fitToBounds(cm,0,bz),bQ=cm+bF/2,bQ=a.fitToBounds(bQ,0,bz);0<=b7&&b7<=bq&&(bf=a.line(bH,[b7,b7],[cu,cJ],cG,bW,bl,a9));0<cm&&cm<bz&&(bo=a.line(bH,[bg,aT],[cm,cm],cG,bW,bl,a9));0<=cn&&cn<=bq&&(a3=a.line(bH,[cn,cn],[cJ,bQ],cG,bW,bl,a9))}else{i=cm;"open"==bE&&(i=b8);"high"==bE&&(i=aK);"low"==bE&&(i=a4);var ah=cn,a4=a.fitToBounds(a4,0,af),aK=a.fitToBounds(aK,0,af),bm=cn-bF/2,bm=a.fitToBounds(bm,0,bB),cy=a.fitToBounds(cn,0,bB),bd=cn+bF/2,bd=a.fitToBounds(bd,0,bB);0<=b8&&b8<=af&&(bf=a.line(bH,[bm,cy],[b8,b8],cG,bW,bl,a9));0<cn&&cn<bB&&(bo=a.line(bH,[cn,cn],[a4,aK],cG,bW,bl,a9));0<=cm&&cm<=af&&(a3=a.line(bH,[cy,bd],[cm,cm],cG,bW,bl,a9))}a0.push(bf);a0.push(bo);a0.push(a3);a.setCN(bP,bf,this.bcn+"stroke-open");a.setCN(bP,a3,this.bcn+"stroke-close");a.setCN(bP,bo,this.bcn+"stroke");bu.className&&a.setCN(bP,j,bu.className,!0);cM=cn;an=cm}}if(!cf&&!isNaN(bX)){var b1=this.hideBulletsCount;if(this.end-this.start<=b1||0===b1){var bx=this.createBullet(bu,ah,i,cd),bh=this.labelText;if(bh&&!isNaN(cM)&&!isNaN(cM)){var aM=this.createLabel(bu,bh),a5=0;bx&&(a5=bx.size);this.positionLabel(bu,cM,an,aM,a5)}if("regular"==b9||"100%"==b9){var aU=bK.totalText;if(aU){var aI=this.createLabel(bu,aU,bK.totalTextColor);a.setCN(bP,aI,this.bcn+"label-total");this.allBullets.push(aI);if(aI){var cr=aI.getBBox(),cC=cr.width,cg=cr.height,bk,a8,aS=bK.totalTextOffset,bG=bK.totals[cd];bG&&bG.remove();var aZ=0;"column"!=bI&&(aZ=this.bulletSize);bw?(a8=an,bk=0>bX?cn-cC/2-2-aZ-aS:cn+cC/2+3+aZ+aS):(bk=cM,a8=0>bX?cm+cg/2+aZ+aS:cm-cg/2-3-aZ-aS);aI.translate(bk,a8);bK.totals[cd]=aI;bw?(0>a8||a8>bz)&&aI.remove():(0>bk||bk>bB)&&aI.remove()}}}}}}}this.lastDataItem=bu;if("line"==bI||"step"==bI||"smoothedLine"==bI){"smoothedLine"==bI?this.drawSmoothedGraph(bs,bv,b0,bZ):this.drawLineGraph(bs,bv,b0,bZ),cf||this.launchAnimation()}this.bulletsHidden&&this.hideBullets();this.customBulletsHidden&&this.hideCustomBullets()},animateColumns:function(f,d){var h=this,g=h.chart.startDuration;0<g&&!h.animationPlayed&&(h.seqAn?(f.set.hide(),h.animationArray.push(f),g=setTimeout(function(){h.animate.call(h)},g/(h.end-h.start+1)*(d-h.start)*1000),h.timeOuts.push(g)):h.animate(f),h.chart.animatable.push(f))},createLabel:function(g,d,l){var k=this.chart,i=g.labelColor;i||(i=this.color);i||(i=k.color);l&&(i=l);l=this.fontSize;void 0===l&&(this.fontSize=l=k.fontSize);var j=this.labelFunction;d=k.formatString(d,g);d=a.cleanFromEmpty(d);j&&(d=j(g,d));if(void 0!==d&&""!==d){return g=a.text(this.container,d,i,k.fontFamily,l),g.node.style.pointerEvents="none",a.setCN(k,g,this.bcn+"label"),this.bulletSet.push(g),g}},positiveClip:function(b){b.clipRect(this.pmx,this.pmy,this.pmw,this.pmh)},negativeClip:function(b){b.clipRect(this.nmx,this.nmy,this.nmw,this.nmh)},drawLineGraph:function(P,O,N,M){var J=this;if(1<P.length){var L=J.noRounding,K=J.set,I=J.chart,H=J.container,G=H.set(),E=H.set();K.push(E);K.push(G);var C=J.lineAlpha,F=J.lineThickness,K=J.fillAlphas,v=J.lineColorR,B=J.negativeLineAlpha;isNaN(B)&&(B=C);var o=J.lineColorSwitched;o&&(v=o);var o=J.fillColorsR,d=J.fillColorsSwitched;d&&(o=d);var j=J.dashLength;(d=J.dashLengthSwitched)&&(j=d);var d=J.negativeLineColor,s=J.negativeFillColors,D=J.negativeFillAlphas,i=J.baseCoord;0!==J.negativeBase&&(i=J.valueAxis.getCoordinate(J.negativeBase,L),i>J.height&&(i=J.height),0>i&&(i=0));C=a.line(H,P,O,v,C,F,j,!1,!0,L);C.node.setAttribute("stroke-linejoin","round");a.setCN(I,C,J.bcn+"stroke");G.push(C);G.click(function(b){J.handleGraphEvent(b,"clickGraph")}).mouseover(function(b){J.handleGraphEvent(b,"rollOverGraph")}).mouseout(function(b){J.handleGraphEvent(b,"rollOutGraph")}).touchmove(function(b){J.chart.handleMouseMove(b)}).touchend(function(b){J.chart.handleTouchEnd(b)});void 0===d||J.useNegativeColorIfDown||(F=a.line(H,P,O,d,B,F,j,!1,!0,L),F.node.setAttribute("stroke-linejoin","round"),a.setCN(I,F,J.bcn+"stroke"),a.setCN(I,F,J.bcn+"stroke-negative"),E.push(F));if(0<K||0<D){if(F=P.join(";").split(";"),B=O.join(";").split(";"),C=I.type,"serial"==C||"radar"==C?0<N.length?(N.reverse(),M.reverse(),F=P.concat(N),B=O.concat(M)):"radar"==C?(B.push(0),F.push(0)):J.rotate?(B.push(B[B.length-1]),F.push(i),B.push(B[0]),F.push(i),B.push(B[0]),F.push(F[0])):(F.push(F[F.length-1]),B.push(i),F.push(F[0]),B.push(i),F.push(P[0]),B.push(B[0])):"xy"==C&&(O=J.fillToAxis)&&(a.isString(O)&&(O=I.getValueAxisById(O)),"H"==O.orientation?(i="top"==O.position?0:O.height,F.push(F[F.length-1]),B.push(i),F.push(F[0]),B.push(i),F.push(P[0]),B.push(B[0])):(i="left"==O.position?0:O.width,B.push(B[B.length-1]),F.push(i),B.push(B[0]),F.push(i),B.push(B[0]),F.push(F[0]))),P=J.gradientRotation,0<K&&(O=a.polygon(H,F,B,o,K,1,"#000",0,P,L),O.pattern(J.pattern,NaN,I.path),a.setCN(I,O,J.bcn+"fill"),G.push(O)),s||void 0!==d){isNaN(D)&&(D=K),s||(s=d),L=a.polygon(H,F,B,s,D,1,"#000",0,P,L),a.setCN(I,L,J.bcn+"fill"),a.setCN(I,L,J.bcn+"fill-negative"),L.pattern(J.pattern,NaN,I.path),E.push(L),E.click(function(b){J.handleGraphEvent(b,"clickGraph")}).mouseover(function(b){J.handleGraphEvent(b,"rollOverGraph")}).mouseout(function(b){J.handleGraphEvent(b,"rollOutGraph")}).touchmove(function(b){J.chart.handleMouseMove(b)}).touchend(function(b){J.chart.handleTouchEnd(b)})}}J.applyMask(E,G)}},applyMask:function(e,d){var f=e.length();"serial"!=this.chart.type||this.scrollbar||(this.positiveClip(d),0<f&&this.negativeClip(e))},drawSmoothedGraph:function(N,M,L,K){if(1<N.length){var H=this.set,J=this.chart,I=this.container,G=I.set(),F=I.set();H.push(F);H.push(G);var E=this.lineAlpha,C=this.lineThickness,H=this.dashLength,y=this.fillAlphas,D=this.lineColorR,s=this.fillColorsR,v=this.negativeLineColor,j=this.negativeFillColors,d=this.negativeFillAlphas,i=this.baseCoord,o=this.lineColorSwitched;o&&(D=o);(o=this.fillColorsSwitched)&&(s=o);var B=this.negativeLineAlpha;isNaN(B)&&(B=E);o=this.getGradRotation();E=new a.Bezier(I,N,M,D,E,C,s,0,H,void 0,o);a.setCN(J,E,this.bcn+"stroke");G.push(E.path);void 0!==v&&(C=new a.Bezier(I,N,M,v,B,C,s,0,H,void 0,o),a.setCN(J,C,this.bcn+"stroke"),a.setCN(J,C,this.bcn+"stroke-negative"),F.push(C.path));0<y&&(C=N.join(";").split(";"),E=M.join(";").split(";"),D="",0<L.length?(L.push("M"),K.push("M"),L.reverse(),K.reverse(),C=N.concat(L),E=M.concat(K)):(this.rotate?(D+=" L"+i+","+M[M.length-1],D+=" L"+i+","+M[0]):(D+=" L"+N[N.length-1]+","+i,D+=" L"+N[0]+","+i),D+=" L"+N[0]+","+M[0]),N=new a.Bezier(I,C,E,NaN,0,0,s,y,H,D,o),a.setCN(J,N,this.bcn+"fill"),N.path.pattern(this.pattern,NaN,J.path),G.push(N.path),j||void 0!==v)&&(d||(d=y),j||(j=v),I=new a.Bezier(I,C,E,NaN,0,0,j,d,H,D,o),I.path.pattern(this.pattern,NaN,J.path),a.setCN(J,I,this.bcn+"fill"),a.setCN(J,I,this.bcn+"fill-negative"),F.push(I.path));this.applyMask(F,G)}},launchAnimation:function(){var f=this,d=f.chart.startDuration;if(0<d&&!f.animationPlayed){var h=f.set,g=f.bulletSet;a.VML||(h.attr({opacity:f.startAlpha}),g.attr({opacity:f.startAlpha}));h.hide();g.hide();f.seqAn?(d=setTimeout(function(){f.animateGraphs.call(f)},f.index*d*1000),f.timeOuts.push(d)):f.animateGraphs()}},animateGraphs:function(){var i=this.chart,h=this.set,n=this.bulletSet,l=this.x,m=this.y;h.show();n.show();var k=i.startDuration,j=i.startEffect;h&&(this.rotate?(h.translate(-1000,m),n.translate(-1000,m)):(h.translate(l,-1000),n.translate(l,-1000)),h.animate({opacity:1,translate:l+","+m},k,j),n.animate({opacity:1,translate:l+","+m},k,j),i.animatable.push(h))},animate:function(e){var d=this.chart,f=this.animationArray;!e&&0<f.length&&(e=f[0],f.shift());f=a[a.getEffect(d.startEffect)];d=d.startDuration;e&&(this.rotate?e.animateWidth(d,f):e.animateHeight(d,f),e.set.show())},legendKeyColor:function(){var d=this.legendColor,c=this.lineAlpha;void 0===d&&(d=this.lineColorR,0===c&&(c=this.fillColorsR)&&(d="object"==typeof c?c[0]:c));return d},legendKeyAlpha:function(){var b=this.legendAlpha;void 0===b&&(b=this.lineAlpha,this.fillAlphas>b&&(b=this.fillAlphas),0===b&&(b=this.bulletAlpha),0===b&&(b=1));return b},createBullet:function(C,B,A){if(!isNaN(B)&&!isNaN(A)&&("none"!=this.bullet||this.customBullet||C.bullet||C.customBullet)){var z=this.chart,w=this.container,y=this.bulletOffset,x=this.bulletSize;isNaN(C.bulletSize)||(x=C.bulletSize);var v=C.values.value,u=this.maxValue,s=this.minValue,j=this.maxBulletSize,i=this.minBulletSize;isNaN(u)||(isNaN(v)||(x=(v-s)/(u-s)*(j-i)+i),s==u&&(x=j));u=x;this.bulletAxis&&(x=C.values.error,isNaN(x)||(v=x),x=this.bulletAxis.stepWidth*v);x<this.minBulletSize&&(x=this.minBulletSize);this.rotate?B=C.isNegative?B-y:B+y:A=C.isNegative?A+y:A-y;i=this.bulletColorR;C.lineColor&&void 0===this.bulletColor&&(this.bulletColorSwitched=C.lineColor);this.bulletColorSwitched&&(i=this.bulletColorSwitched);C.isNegative&&void 0!==this.bulletColorNegative&&(i=this.bulletColorNegative);void 0!==C.color&&(i=C.color);var o;"xy"==z.type&&this.valueField&&(o=this.pattern,C.pattern&&(o=C.pattern));y=this.bullet;C.bullet&&(y=C.bullet);var v=this.bulletBorderThickness,s=this.bulletBorderColorR,j=this.bulletBorderAlpha,D=this.bulletAlpha;s||(s=i);this.useLineColorForBulletBorder&&(s=this.lineColorR,C.isNegative&&this.negativeLineColor&&(s=this.negativeLineColor),this.lineColorSwitched&&(s=this.lineColorSwitched));var d=C.alpha;isNaN(d)||(D=d);o=a.bullet(w,y,x,i,D,v,s,j,u,0,o,z.path);u=this.customBullet;C.customBullet&&(u=C.customBullet);u&&(o&&o.remove(),"function"==typeof u?(u=new u,u.chart=z,C.bulletConfig&&(u.availableSpace=A,u.graph=this,u.graphDataItem=C,u.bulletY=A,C.bulletConfig.minCoord=this.minCoord-A,u.bulletConfig=C.bulletConfig),u.write(w),o&&u.showBullet&&u.set.push(o),C.customBulletGraphics=u.cset,o=u.set):(o=w.set(),u=w.image(u,0,0,x,x),o.push(u),this.centerCustomBullets&&u.translate(-x/2,-x/2)));if(o){(C.url||this.showHandOnHover)&&o.setAttr("cursor","pointer");if("serial"==z.type||"gantt"==z.type){if(-0.5>B||B>this.width||A<-x/2||A>this.height){o.remove(),o=null}}o&&(this.bulletSet.push(o),o.translate(B,A),this.addListeners(o,C),this.allBullets.push(o));C.bx=B;C.by=A;a.setCN(z,o,this.bcn+"bullet");C.className&&a.setCN(z,o,C.className,!0)}if(o){o.size=x||0;if(z=this.bulletHitAreaSize){w=a.circle(w,z,"#FFFFFF",0.001,0),w.translate(B,A),C.hitBullet=w,this.bulletSet.push(w),this.addListeners(w,C)}C.bulletGraphics=o;void 0!==this.tabIndex&&o.setAttr("tabindex",this.tabIndex)}else{o={size:0}}o.graphDataItem=C;return o}},showBullets:function(){var d=this.allBullets,c;this.bulletsHidden=!1;for(c=0;c<d.length;c++){d[c].show()}},hideBullets:function(){var d=this.allBullets,c;this.bulletsHidden=!0;for(c=0;c<d.length;c++){d[c].hide()}},showCustomBullets:function(){var e=this.allBullets,d;this.customBulletsHidden=!1;for(d=0;d<e.length;d++){var f=e[d].graphDataItem;f&&f.customBulletGraphics&&f.customBulletGraphics.show()}},hideCustomBullets:function(){var e=this.allBullets,d;this.customBulletsHidden=!0;for(d=0;d<e.length;d++){var f=e[d].graphDataItem;f&&f.customBulletGraphics&&f.customBulletGraphics.hide()}},addListeners:function(g,f){var j=this;g.mouseover(function(b){j.handleRollOver(f,b)}).mouseout(function(b){j.handleRollOut(f,b)}).touchend(function(b){j.handleRollOver(f,b);j.chart.panEventsEnabled&&j.handleClick(f,b)}).touchstart(function(b){j.handleRollOver(f,b)}).click(function(b){j.handleClick(f,b)}).dblclick(function(b){j.handleDoubleClick(f,b)}).contextmenu(function(b){j.handleRightClick(f,b)});var h=j.chart;if(h.accessible&&j.accessibleLabel){var i=h.formatString(j.accessibleLabel,f);h.makeAccessible(g,i)}},handleRollOver:function(f,d){this.handleGraphEvent(d,"rollOverGraph");if(f){var h=this.chart;f.bulletConfig&&(h.isRolledOverBullet=!0);var g={type:"rollOverGraphItem",item:f,index:f.index,graph:this,target:this,chart:this.chart,event:d};this.fire(g);h.fire(g);clearTimeout(h.hoverInt);(h=h.chartCursor)&&h.valueBalloonsEnabled||this.showGraphBalloon(f,"V",!0)}},handleRollOut:function(f,d){var h=this.chart;if(f){var g={type:"rollOutGraphItem",item:f,index:f.index,graph:this,target:this,chart:this.chart,event:d};this.fire(g);h.fire(g);h.isRolledOverBullet=!1}this.handleGraphEvent(d,"rollOutGraph");(h=h.chartCursor)&&h.valueBalloonsEnabled||this.hideBalloon()},handleClick:function(e,d){if(!this.chart.checkTouchMoved()&&this.chart.checkTouchDuration(d)){if(e){var f={type:"clickGraphItem",item:e,index:e.index,graph:this,target:this,chart:this.chart,event:d};this.fire(f);this.chart.fire(f);a.getURL(e.url,this.urlTarget)}this.handleGraphEvent(d,"clickGraph")}},handleGraphEvent:function(e,d){var f={type:d,graph:this,target:this,chart:this.chart,event:e};this.fire(f);this.chart.fire(f)},handleRightClick:function(e,d){if(e){var f={type:"rightClickGraphItem",item:e,index:e.index,graph:this,target:this,chart:this.chart,event:d};this.fire(f);this.chart.fire(f)}},handleDoubleClick:function(e,d){if(e){var f={type:"doubleClickGraphItem",item:e,index:e.index,graph:this,target:this,chart:this.chart,event:d};this.fire(f);this.chart.fire(f)}},zoom:function(d,c){this.start=d;this.end=c;this.draw()},changeOpacity:function(f){var d=this.set;d&&d.setAttr("opacity",f);if(d=this.ownColumns){var h;for(h=0;h<d.length;h++){var g=d[h].set;g&&g.setAttr("opacity",f)}}(d=this.bulletSet)&&d.setAttr("opacity",f)},destroy:function(){a.remove(this.set);a.remove(this.bulletSet);var d=this.timeOuts;if(d){var c;for(c=0;c<d.length;c++){clearTimeout(d[c])}}this.timeOuts=[]},createBalloon:function(){var d=this.chart;this.balloon?this.balloon.destroy&&this.balloon.destroy():this.balloon={};var c=this.balloon;a.extend(c,d.balloon,!0);c.chart=d;c.mainSet=d.plotBalloonsSet;c.className=this.id},hideBalloon:function(){var d=this,c=d.chart;c.chartCursor?c.chartCursor.valueBalloonsEnabled||c.hideBalloon():c.hideBalloon();clearTimeout(d.hoverInt);d.hoverInt=setTimeout(function(){d.hideBalloonReal.call(d)},c.hideBalloonTime)},hideBalloonReal:function(){this.balloon&&this.balloon.hide();this.fixBulletSize()},fixBulletSize:function(){if(a.isModern){var e=this.resizedDItem;if(e){var d=e.bulletGraphics;if(d&&!d.doNotScale){d.translate(e.bx,e.by,1);var f=this.bulletAlpha;isNaN(e.alpha)||(f=e.alpha);d.setAttr("fill-opacity",f);d.setAttr("stroke-opacity",this.bulletBorderAlpha)}}this.resizedDItem=null}},showGraphBalloon:function(x,w,v,u,r){if(x){var t=this.chart,s=this.balloon,o=0,n=0,j=t.chartCursor,i=!0;j?j.valueBalloonsEnabled||(s=t.balloon,o=this.x,n=this.y,i=!1):(s=t.balloon,o=this.x,n=this.y,i=!1);clearTimeout(this.hoverInt);if(t.chartCursor&&(this.currentDataItem=x,"serial"==t.type&&t.isRolledOverBullet&&t.chartCursor.valueBalloonsEnabled)){this.hideBalloonReal();return}this.resizeBullet(x,u,r);if(s&&s.enabled&&this.showBalloon&&!this.hidden){var j=t.formatString(this.balloonText,x,!0),d=this.balloonFunction;d&&(j=d(x,x.graph));j&&(j=a.cleanFromEmpty(j));j&&""!==j?(u=t.getBalloonColor(this,x),s.drop||(s.pointerOrientation=w),w=x.x,r=x.y,t.rotate&&(w=x.y,r=x.x),w+=o,r+=n,isNaN(w)||isNaN(r)?this.hideBalloonReal():(x=this.width,d=this.height,i&&s.setBounds(o,n,x+o,d+n),s.changeColor(u),s.setPosition(w,r),s.fixPrevious(),s.fixedPosition&&(v=!1),!v&&"radar"!=t.type&&(w<o-0.5||w>x+o||r<n-0.5||r>d+n)?(s.showBalloon(j),s.hide(0)):(s.followCursor(v),s.showBalloon(j)))):(this.hideBalloonReal(),s.hide(),this.resizeBullet(x,u,r))}else{this.hideBalloonReal()}}},resizeBullet:function(f,d,h){this.fixBulletSize();if(f&&a.isModern&&(1!=d||!isNaN(h))){var g=f.bulletGraphics;g&&!g.doNotScale&&(g.translate(f.bx,f.by,d),isNaN(h)||(g.setAttr("fill-opacity",h),g.setAttr("stroke-opacity",h)),this.resizedDItem=f)}}})})();(function(){var a=window.AmCharts;a.ChartCursor=a.Class({construct:function(b){this.cname="ChartCursor";this.createEvents("changed","zoomed","onHideCursor","onShowCursor","draw","selected","moved","panning","zoomStarted");this.enabled=!0;this.cursorAlpha=1;this.selectionAlpha=0.2;this.cursorColor="#CC0000";this.categoryBalloonAlpha=1;this.color="#FFFFFF";this.type="cursor";this.zoomed=!1;this.zoomable=!0;this.pan=!1;this.categoryBalloonDateFormat="MMM DD, YYYY";this.categoryBalloonText="[[category]]";this.categoryBalloonEnabled=this.valueBalloonsEnabled=!0;this.rolledOver=!1;this.cursorPosition="middle";this.bulletsEnabled=this.skipZoomDispatch=!1;this.bulletSize=8;this.selectWithoutZooming=this.oneBalloonOnly=!1;this.graphBulletSize=1.7;this.animationDuration=0.3;this.zooming=!1;this.adjustment=0;this.avoidBalloonOverlapping=!0;this.leaveCursor=!1;this.leaveAfterTouch=!0;this.valueZoomable=!1;this.balloonPointerOrientation="horizontal";this.hLineEnabled=this.vLineEnabled=!0;this.vZoomEnabled=this.hZoomEnabled=!1;a.applyTheme(this,b,this.cname)},draw:function(){this.destroy();var d=this.chart;d.panRequired=!0;var c=d.container;this.rotate=d.rotate;this.container=c;this.prevLineHeight=this.prevLineWidth=NaN;c=c.set();c.translate(this.x,this.y);this.set=c;d.cursorSet.push(c);this.createElements();a.isString(this.limitToGraph)&&(this.limitToGraph=a.getObjById(d.graphs,this.limitToGraph),this.fullWidth=!1,this.cursorPosition="middle");this.pointer=this.balloonPointerOrientation.substr(0,1).toUpperCase();this.isHidden=!1;this.hideLines();this.valueLineAxis||(this.valueLineAxis=d.valueAxes[0])},createElements:function(){var t=this,s=t.chart,r=s.dx,q=s.dy,n=t.width,p=t.height,o,j,i=t.cursorAlpha,d=t.valueLineAlpha;t.rotate?(o=d,j=i):(j=d,o=i);"xy"==s.type&&(j=i,void 0!==d&&(j=d),o=i);t.vvLine=a.line(t.container,[r,0,0],[q,0,p],t.cursorColor,o,1);a.setCN(s,t.vvLine,"cursor-line");a.setCN(s,t.vvLine,"cursor-line-vertical");t.hhLine=a.line(t.container,[0,n,n+r],[0,0,q],t.cursorColor,j,1);a.setCN(s,t.hhLine,"cursor-line");a.setCN(s,t.hhLine,"cursor-line-horizontal");t.vLine=t.rotate?t.vvLine:t.hhLine;t.set.push(t.vvLine);t.set.push(t.hhLine);t.set.node.style.pointerEvents="none";t.fullLines=t.container.set();s=s.cursorLineSet;s.push(t.fullLines);s.translate(t.x,t.y);s.clipRect(-1,-1,n+2,p+2);void 0!==t.tabIndex&&(s.setAttr("tabindex",t.tabIndex),s.keyup(function(c){t.handleKeys(c)}).focus(function(c){t.showCursor()}).blur(function(c){t.hideCursor()}));t.set.clipRect(0,0,n,p)},handleKeys:function(f){var d=this.prevIndex,h=this.chart;if(h){var g=h.chartData;g&&(isNaN(d)&&(d=g.length-1),37!=f.keyCode&&40!=f.keyCode||d--,39!=f.keyCode&&38!=f.keyCode||d++,d=a.fitToBounds(d,h.startIndex,h.endIndex),(f=this.chart.chartData[d])&&this.setPosition(f.x.categoryAxis),this.prevIndex=d)}},update:function(){var f=this.chart;if(f){var d=f.mouseX-this.x,h=f.mouseY-this.y;this.mouseX=d;this.mouseY=h;this.mouse2X=f.mouse2X-this.x;this.mouse2Y=f.mouse2Y-this.y;var g;if(f.chartData&&0<f.chartData.length){this.mouseIsOver()?(this.hideGraphBalloons=!1,this.rolledOver=g=!0,this.updateDrawing(),this.vvLine&&isNaN(this.fx)&&(f.rotate||!this.limitToGraph)&&this.vvLine.translate(d,0),!this.hhLine||!isNaN(this.fy)||f.rotate&&this.limitToGraph||this.hhLine.translate(0,h),isNaN(this.mouse2X)?this.dispatchMovedEvent(d,h):g=!1):this.forceShow||this.hideCursor();if(this.zooming){if(!isNaN(this.mouse2X)){isNaN(this.mouse2X0)||this.dispatchPanEvent();return}if(this.pan){this.dispatchPanEvent();return}(this.hZoomEnabled||this.vZoomEnabled)&&this.zooming&&this.updateSelection()}g&&this.showCursor()}}},updateDrawing:function(){this.drawing&&this.chart.setMouseCursor("crosshair");if(this.drawingNow&&(a.remove(this.drawingLine),1<Math.abs(this.drawStartX-this.mouseX)||1<Math.abs(this.drawStartY-this.mouseY))){var d=this.chart,c=d.marginTop,d=d.marginLeft;this.drawingLine=a.line(this.container,[this.drawStartX+d,this.mouseX+d],[this.drawStartY+c,this.mouseY+c],this.cursorColor,1,1)}},fixWidth:function(e){if(this.fullWidth&&this.prevLineWidth!=e){var d=this.vvLine,f=0;d&&(d.remove(),f=d.x);d=this.container.set();d.translate(f,0);f=a.rect(this.container,e,this.height,this.cursorColor,this.cursorAlpha,this.cursorAlpha,this.cursorColor);a.setCN(this.chart,f,"cursor-fill");f.translate(-e/2-1,0);d.push(f);this.vvLine=d;this.fullLines.push(d);this.prevLineWidth=e}},fixHeight:function(e){if(this.fullWidth&&this.prevLineHeight!=e){var d=this.hhLine,f=0;d&&(d.remove(),f=d.y);d=this.container.set();d.translate(0,f);f=a.rect(this.container,this.width,e,this.cursorColor,this.cursorAlpha);f.translate(0,-e/2);d.push(f);this.fullLines.push(d);this.hhLine=d;this.prevLineHeight=e}},fixVLine:function(g,f){if(!isNaN(g)){if(isNaN(this.prevLineX)){var j=0,h=this.mouseX;if(this.limitToGraph){var i=this.chart.categoryAxis;i&&(this.chart.rotate||(j="bottom"==i.position?this.height:-this.height),h=g)}this.vvLine.translate(h,j)}else{this.prevLineX!=g&&this.vvLine.translate(this.prevLineX,this.prevLineY)}this.fx=g;this.prevLineX!=g&&(j=this.animationDuration,this.zooming&&(j=0),this.vvLine.stop(),this.vvLine.animate({translate:g+","+f},j,"easeOutSine"),this.prevLineX=g,this.prevLineY=f)}},fixHLine:function(g,f){if(!isNaN(g)){if(isNaN(this.prevLineY)){var j=0,h=this.mouseY;if(this.limitToGraph){var i=this.chart.categoryAxis;i&&(this.chart.rotate&&(j="right"==i.position?this.width:-this.width),h=g)}this.hhLine.translate(j,h)}else{this.prevLineY!=g&&this.hhLine.translate(this.prevLineX,this.prevLineY)}this.fy=g;this.prevLineY!=g&&(j=this.animationDuration,this.zooming&&(j=0),this.hhLine.stop(),this.hhLine.animate({translate:f+","+g},j,"easeOutSine"),this.prevLineY=g,this.prevLineX=f)}},hideCursor:function(b){this.forceShow=!1;this.chart.wasTouched&&this.leaveAfterTouch||this.isHidden||this.leaveCursor||(this.hideCursorReal(),b?this.chart.handleCursorHide():this.fire({target:this,chart:this.chart,type:"onHideCursor"}),this.chart.setMouseCursor("auto"))},hideCursorReal:function(){this.hideLines();this.isHidden=!0;this.index=this.prevLineY=this.prevLineX=this.mouseY0=this.mouseX0=this.fy=this.fx=NaN},hideLines:function(){this.vvLine&&this.vvLine.hide();this.hhLine&&this.hhLine.hide();this.fullLines&&this.fullLines.hide();this.isHidden=!0;this.chart.handleCursorHide()},showCursor:function(b){!this.drawing&&this.enabled&&(this.vLineEnabled&&this.vvLine&&this.vvLine.show(),this.hLineEnabled&&this.hhLine&&this.hhLine.show(),this.isHidden=!1,this.updateFullLine(),b||this.fire({target:this,chart:this.chart,type:"onShowCursor"}),this.pan&&this.chart.setMouseCursor("move"))},updateFullLine:function(){this.zooming&&this.fullWidth&&this.selection&&(this.rotate?0<this.selection.height&&this.hhLine.hide():0<this.selection.width&&this.vvLine.hide())},updateSelection:function(){if(!this.pan&&this.enabled){var i=this.mouseX,d=this.mouseY;isNaN(this.fx)||(i=this.fx);isNaN(this.fy)||(d=this.fy);this.clearSelection();var n=this.mouseX0,m=this.mouseY0,j=this.width,l=this.height,i=a.fitToBounds(i,0,j),d=a.fitToBounds(d,0,l),k;i<n&&(k=i,i=n,n=k);d<m&&(k=d,d=m,m=k);this.hZoomEnabled?j=i-n:n=0;this.vZoomEnabled?l=d-m:m=0;isNaN(this.mouse2X)&&0<Math.abs(j)&&0<Math.abs(l)&&(i=this.chart,d=a.rect(this.container,j,l,this.cursorColor,this.selectionAlpha),a.setCN(i,d,"cursor-selection"),d.width=j,d.height=l,d.translate(n,m),this.set.push(d),this.selection=d);this.updateFullLine()}},mouseIsOver:function(){var d=this.mouseX,c=this.mouseY;if(this.justReleased){return this.justReleased=!1,!0}if(this.mouseIsDown){return !0}if(!this.chart.mouseIsOver){return this.handleMouseOut(),!1}if(0<d&&d<this.width&&0<c&&c<this.height){return !0}this.handleMouseOut()},fixPosition:function(){this.prevY=this.prevX=NaN},handleMouseDown:function(){this.update();if(this.mouseIsOver()){if(this.mouseIsDown=!0,this.mouseX0=this.mouseX,this.mouseY0=this.mouseY,this.mouse2X0=this.mouse2X,this.mouse2Y0=this.mouse2Y,this.drawing){this.drawStartY=this.mouseY,this.drawStartX=this.mouseX,this.drawingNow=!0}else{if(this.dispatchMovedEvent(this.mouseX,this.mouseY),!this.pan&&isNaN(this.mouse2X0)&&(isNaN(this.fx)||(this.mouseX0=this.fx),isNaN(this.fy)||(this.mouseY0=this.fy)),this.hZoomEnabled||this.vZoomEnabled){this.zooming=!0;var b={chart:this.chart,target:this,type:"zoomStarted"};b.x=this.mouseX/this.width;b.y=this.mouseY/this.height;this.index0=b.index=this.index;this.timestamp0=this.timestamp;this.fire(b)}}}},registerInitialMouse:function(){},handleReleaseOutside:function(){this.mouseIsDown=!1;if(this.drawingNow){this.drawingNow=!1;a.remove(this.drawingLine);var f=this.drawStartX,d=this.drawStartY,j=this.mouseX,i=this.mouseY,g=this.chart;(2<Math.abs(f-j)||2<Math.abs(d-i))&&this.fire({type:"draw",target:this,chart:g,initialX:f,initialY:d,finalX:j,finalY:i})}this.zooming&&(this.zooming=!1,this.selectWithoutZooming?this.dispatchZoomEvent("selected"):(this.hZoomEnabled||this.vZoomEnabled)&&this.dispatchZoomEvent("zoomed"),this.rolledOver&&this.dispatchMovedEvent(this.mouseX,this.mouseY));this.mouse2Y0=this.mouse2X0=this.mouseY0=this.mouseX0=NaN},dispatchZoomEvent:function(t){if(!this.pan){var s=this.selection;if(s&&3<Math.abs(s.width)&&3<Math.abs(s.height)){var r=Math.min(this.index,this.index0),p=Math.max(this.index,this.index0),q=r,o=p,n=this.chart,j=n.chartData,i=n.categoryAxis;i&&i.parseDates&&!i.equalSpacing&&(q=j[r]?j[r].time:Math.min(this.timestamp0,this.timestamp),o=j[p]?n.getEndTime(j[p].time):Math.max(this.timestamp0,this.timestamp));var s={type:t,chart:this.chart,target:this,end:o,start:q,startIndex:r,endIndex:p,selectionHeight:s.height,selectionWidth:s.width,selectionY:s.y,selectionX:s.x},h;this.hZoomEnabled&&4<Math.abs(this.mouseX0-this.mouseX)&&(s.startX=this.mouseX0/this.width,s.endX=this.mouseX/this.width,h=!0);this.vZoomEnabled&&4<Math.abs(this.mouseY0-this.mouseY)&&(s.startY=1-this.mouseY0/this.height,s.endY=1-this.mouseY/this.height,h=!0);h&&(this.prevY=this.prevX=NaN,this.fire(s),"selected"!=t&&this.clearSelection());this.hideCursor()}}},dispatchMovedEvent:function(i,h,n,l){i=Math.round(i);h=Math.round(h);if(!this.isHidden&&(i!=this.prevX||h!=this.prevY||"changed"==n)){n||(n="moved");var m=this.fx,k=this.fy;isNaN(m)&&(m=i);isNaN(k)&&(k=h);var j=!1;this.zooming&&this.pan&&(j=!0);j={hidden:this.isHidden,type:n,chart:this.chart,target:this,x:i,y:h,finalX:m,finalY:k,zooming:this.zooming,panning:j,mostCloseGraph:this.mostCloseGraph,index:this.index,skip:l,hideBalloons:this.hideGraphBalloons};this.prevIndex=this.index;this.rotate?(j.position=h,j.finalPosition=k):(j.position=i,j.finalPosition=m);this.prevX=i;this.prevY=h;l?this.chart.handleCursorMove(j):(this.fire(j),"changed"==n&&this.chart.fire(j))}},dispatchPanEvent:function(){if(this.mouseIsDown){var f=a.roundTo((this.mouseX-this.mouseX0)/this.width,3),d=a.roundTo((this.mouseY-this.mouseY0)/this.height,3),j=a.roundTo((this.mouse2X-this.mouse2X0)/this.width,3),i=a.roundTo((this.mouse2Y-this.mouse2Y0)/this.height,2),g=!1;0!==Math.abs(f)&&0!==Math.abs(d)&&(g=!0);if(this.prevDeltaX==f||this.prevDeltaY==d){g=!1}isNaN(j)||isNaN(i)||(0!==Math.abs(j)&&0!==Math.abs(i)&&(g=!0),this.prevDelta2X!=j&&this.prevDelta2Y!=i)||(g=!1);g&&(this.hideLines(),this.fire({type:"panning",chart:this.chart,target:this,deltaX:f,deltaY:d,delta2X:j,delta2Y:i,index:this.index}),this.prevDeltaX=f,this.prevDeltaY=d,this.prevDelta2X=j,this.prevDelta2Y=i)}},clearSelection:function(){var b=this.selection;b&&(b.width=0,b.height=0,b.remove())},destroy:function(){this.clear();a.remove(this.selection);this.selection=null;clearTimeout(this.syncTO);a.remove(this.set)},clear:function(){},setTimestamp:function(b){this.timestamp=b},setIndex:function(d,c){d!=this.index&&(this.index=d,c||this.isHidden||this.dispatchMovedEvent(this.mouseX,this.mouseY,"changed"))},handleMouseOut:function(){this.enabled&&this.rolledOver&&(this.leaveCursor||this.setIndex(void 0),this.forceShow=!1,this.hideCursor(),this.rolledOver=!1)},showCursorAt:function(d){var c=this.chart.categoryAxis;c&&this.setPosition(c.categoryToCoordinate(d),d)},setPosition:function(h,g){var l=this.chart,j=l.categoryAxis;if(j){var k,i;void 0===g&&(g=j.coordinateToValue(h));j.showBalloonAt(g,h);this.forceShow=!0;j.stickBalloonToCategory?l.rotate?this.fixHLine(h,0):this.fixVLine(h,0):(this.showCursor(),l.rotate?this.hhLine.translate(0,h):this.vvLine.translate(h,0));l.rotate?k=h:i=h;l.rotate?(this.vvLine&&this.vvLine.hide(),this.hhLine&&this.hhLine.show()):(this.hhLine&&this.hhLine.hide(),this.vvLine&&this.vvLine.show());this.updateFullLine();this.isHidden=!1;this.dispatchMovedEvent(i,k,"moved",!0)}},enableDrawing:function(b){this.enabled=!b;this.hideCursor();this.drawing=b},syncWithCursor:function(d,c){clearTimeout(this.syncTO);d&&(d.isHidden?this.hideCursor(!0):this.syncWithCursorReal(d,c))},isZooming:function(b){this.zooming=b},syncWithCursorReal:function(h,g){var l=h.vvLine,j=h.hhLine;this.index=h.index;this.forceShow=!0;this.zooming&&this.pan||this.showCursor(!0);this.hideGraphBalloons=g;this.justReleased=h.justReleased;this.zooming=h.zooming;this.index0=h.index0;this.mouseX0=h.mouseX0;this.mouseY0=h.mouseY0;this.mouse2X0=h.mouse2X0;this.mouse2Y0=h.mouse2Y0;this.timestamp0=h.timestamp0;this.prevDeltaX=h.prevDeltaX;this.prevDeltaY=h.prevDeltaY;this.prevDelta2X=h.prevDelta2X;this.prevDelta2Y=h.prevDelta2Y;this.fx=h.fx;this.fy=h.fy;h.zooming&&this.updateSelection();var k=h.mouseX,i=h.mouseY;this.rotate?(k=NaN,this.vvLine&&this.vvLine.hide(),this.hhLine&&j&&(isNaN(h.fy)?this.hhLine.translate(0,h.mouseY):this.fixHLine(h.fy,0))):(i=NaN,this.hhLine&&this.hhLine.hide(),this.vvLine&&l&&(isNaN(h.fx)?this.vvLine.translate(h.mouseX,0):this.fixVLine(h.fx,0)));this.dispatchMovedEvent(k,i,"moved",!0)}})})();(function(){var a=window.AmCharts;a.SimpleChartScrollbar=a.Class({construct:function(b){this.createEvents("zoomed","zoomStarted","zoomEnded");this.backgroundColor="#D4D4D4";this.backgroundAlpha=1;this.selectedBackgroundColor="#EFEFEF";this.scrollDuration=this.selectedBackgroundAlpha=1;this.resizeEnabled=!0;this.hideResizeGrips=!1;this.scrollbarHeight=20;this.updateOnReleaseOnly=!1;9>document.documentMode&&(this.updateOnReleaseOnly=!0);this.dragIconHeight=this.dragIconWidth=35;this.dragIcon="dragIconRoundBig";this.dragCursorHover="cursor: move; cursor: grab; cursor: -moz-grab; cursor: -webkit-grab;";this.dragCursorDown="cursor: move; cursor: grab; cursor: -moz-grabbing; cursor: -webkit-grabbing;";this.vResizeCursor="ns-resize";this.hResizeCursor="ew-resize";this.enabled=!0;this.percentStart=this.offset=0;this.percentEnd=1;a.applyTheme(this,b,"SimpleChartScrollbar")},getPercents:function(){var f=this.getDBox(),d=f.x,h=f.y,g=f.width,f=f.height;this.rotate?(d=1-h/this.height,h=1-(h+f)/this.height):(h=d/this.width,d=(d+g)/this.width);this.percentStart=h;this.percentEnd=d},draw:function(){var z=this;z.destroy();if(z.enabled){var y=z.chart.container,x=z.rotate,w=z.chart;w.panRequired=!0;var t=y.set();z.set=t;x?a.setCN(w,t,"scrollbar-vertical"):a.setCN(w,t,"scrollbar-horizontal");w.scrollbarsSet.push(t);var v,u;x?(v=z.scrollbarHeight,u=w.plotAreaHeight):(u=z.scrollbarHeight,v=w.plotAreaWidth);z.width=v;if((z.height=u)&&v){var s=a.rect(y,v,u,z.backgroundColor,z.backgroundAlpha,1,z.backgroundColor,z.backgroundAlpha);a.setCN(w,s,"scrollbar-bg");z.bg=s;t.push(s);s=a.rect(y,v,u,"#000",0.005);t.push(s);z.invisibleBg=s;s.click(function(){z.handleBgClick()}).mouseover(function(){z.handleMouseOver()}).mouseout(function(){z.handleMouseOut()}).touchend(function(){z.handleBgClick()});s=a.rect(y,v,u,z.selectedBackgroundColor,z.selectedBackgroundAlpha);a.setCN(w,s,"scrollbar-bg-selected");z.selectedBG=s;t.push(s);v=a.rect(y,v,u,"#000",0.005);z.dragger=v;t.push(v);v.mousedown(function(c){z.handleDragStart(c)}).mouseup(function(){z.handleDragStop()}).mouseover(function(){z.handleDraggerOver()}).mouseout(function(){z.handleMouseOut()}).touchstart(function(c){z.handleDragStart(c)}).touchend(function(){z.handleDragStop()});u=w.pathToImages;var r,s=z.dragIcon.replace(/\.[a-z]*$/i,"");a.isAbsolute(s)&&(u="");x?(r=u+s+"H"+w.extension,u=z.dragIconWidth,x=z.dragIconHeight):(r=u+s+w.extension,x=z.dragIconWidth,u=z.dragIconHeight);s=y.image(r,0,0,x,u);a.setCN(w,s,"scrollbar-grip-left");r=y.image(r,0,0,x,u);a.setCN(w,r,"scrollbar-grip-right");var o=10,i=20;w.panEventsEnabled&&(o=25,i=z.scrollbarHeight);var d=a.rect(y,o,i,"#000",0.005),j=a.rect(y,o,i,"#000",0.005);j.translate(-(o-x)/2,-(i-u)/2);d.translate(-(o-x)/2,-(i-u)/2);x=y.set([s,j]);y=y.set([r,d]);z.iconLeft=x;t.push(z.iconLeft);z.iconRight=y;t.push(y);z.updateGripCursor(!1);w.makeAccessible(x,z.accessibleLabel);w.makeAccessible(y,z.accessibleLabel);w.makeAccessible(v,z.accessibleLabel);x.setAttr("role","menuitem");y.setAttr("role","menuitem");v.setAttr("role","menuitem");void 0!==z.tabIndex&&(x.setAttr("tabindex",z.tabIndex),x.keyup(function(c){z.handleKeys(c,1,0)}));void 0!==z.tabIndex&&(v.setAttr("tabindex",z.tabIndex),v.keyup(function(c){z.handleKeys(c,1,1)}));void 0!==z.tabIndex&&(y.setAttr("tabindex",z.tabIndex),y.keyup(function(c){z.handleKeys(c,0,1)}));x.mousedown(function(){z.leftDragStart()}).mouseup(function(){z.leftDragStop()}).mouseover(function(){z.iconRollOver()}).mouseout(function(){z.iconRollOut()}).touchstart(function(){z.leftDragStart()}).touchend(function(){z.leftDragStop()});y.mousedown(function(){z.rightDragStart()}).mouseup(function(){z.rightDragStop()}).mouseover(function(){z.iconRollOver()}).mouseout(function(){z.iconRollOut()}).touchstart(function(){z.rightDragStart()}).touchend(function(){z.rightDragStop()});a.ifArray(w.chartData)?t.show():t.hide();z.hideDragIcons();z.clipDragger(!1)}t.translate(z.x,z.y);t.node.style.msTouchAction="none";t.node.style.touchAction="none"}},handleKeys:function(h,g,l){this.getPercents();var j=this.percentStart,k=this.percentEnd;if(this.rotate){var i=k,k=j,j=i}if(37==h.keyCode||40==h.keyCode){j-=0.02*g,k-=0.02*l}if(39==h.keyCode||38==h.keyCode){j+=0.02*g,k+=0.02*l}this.rotate&&(h=k,k=j,j=h);isNaN(k)||isNaN(j)||this.percentZoom(j,k,!0)},updateScrollbarSize:function(i,h){if(!isNaN(i)&&!isNaN(h)){i=Math.round(i);h=Math.round(h);var p=this.dragger,n,o,m,l,j;this.rotate?(n=0,o=i,m=this.width+1,l=h-i,p.setAttr("height",h-i),p.setAttr("y",o)):(n=i,o=0,m=h-i,l=this.height+1,j=h-i,p.setAttr("x",n),p.setAttr("width",j));this.clipAndUpdate(n,o,m,l)}},update:function(){var C,B=!1,A,y,z=this.x,x=this.y,w=this.dragger,v=this.getDBox();if(v){A=v.x+z;y=v.y+x;var u=v.width,v=v.height,s=this.rotate,j=this.chart,i=this.width,o=this.height,D=j.mouseX,j=j.mouseY;C=this.initialMouse;this.forceClip&&this.clipDragger(!0);if(this.dragging){var h=this.initialCoord;s?(C=h+(j-C),0>C&&(C=0),h=o-v,C>h&&(C=h),w.setAttr("y",C)):(C=h+(D-C),0>C&&(C=0),h=i-u,C>h&&(C=h),w.setAttr("x",C));this.clipDragger(!0)}if(this.resizingRight){if(s){if(C=j-y,!isNaN(this.maxHeight)&&C>this.maxHeight&&(C=this.maxHeight),C+y>o+x&&(C=o-y+x),0>C){this.resizingRight=!1,B=this.resizingLeft=!0}else{if(0===C||isNaN(C)){C=0.1}w.setAttr("height",C)}}else{if(C=D-A,!isNaN(this.maxWidth)&&C>this.maxWidth&&(C=this.maxWidth),C+A>i+z&&(C=i-A+z),0>C){this.resizingRight=!1,B=this.resizingLeft=!0}else{if(0===C||isNaN(C)){C=0.1}w.setAttr("width",C)}}this.clipDragger(!0)}if(this.resizingLeft){if(s){if(A=y,y=j,y<x&&(y=x),isNaN(y)&&(y=x),y>o+x&&(y=o+x),C=!0===B?A-y:v+A-y,!isNaN(this.maxHeight)&&C>this.maxHeight&&(C=this.maxHeight,y=A),0>C){this.resizingRight=!0,this.resizingLeft=!1,w.setAttr("y",A+v-x)}else{if(0===C||isNaN(C)){C=0.1}w.setAttr("y",y-x);w.setAttr("height",C)}}else{if(y=D,y<z&&(y=z),isNaN(y)&&(y=z),y>i+z&&(y=i+z),C=!0===B?A-y:u+A-y,!isNaN(this.maxWidth)&&C>this.maxWidth&&(C=this.maxWidth,y=A),0>C){this.resizingRight=!0,this.resizingLeft=!1,w.setAttr("x",A+u-z)}else{if(0===C||isNaN(C)){C=0.1}w.setAttr("x",y-z);w.setAttr("width",C)}}this.clipDragger(!0)}}},stopForceClip:function(){this.animating=this.forceClip=!1},clipDragger:function(h){var g=this.getDBox();if(g){var l=g.x,j=g.y,k=g.width,g=g.height,i=!1;if(this.rotate){if(l=0,k=this.width+1,this.clipY!=j||this.clipH!=g){i=!0}}else{if(j=0,g=this.height+1,this.clipX!=l||this.clipW!=k){i=!0}}i&&(this.clipAndUpdate(l,j,k,g),h&&(this.updateOnReleaseOnly||this.dispatchScrollbarEvent()))}},maskGraphs:function(){},clipAndUpdate:function(f,e,h,g){this.clipX=f;this.clipY=e;this.clipW=h;this.clipH=g;this.selectedBG.setAttr("width",h);this.selectedBG.setAttr("height",g);this.selectedBG.translate(f,e);this.updateDragIconPositions();this.maskGraphs(f,e,h,g)},dispatchScrollbarEvent:function(){if(this.skipEvent){this.skipEvent=!1}else{var f=this.chart;f.hideBalloon();var e=this.getDBox(),j=e.x,i=e.y,g=e.width,e=e.height;this.getPercents();this.rotate?(j=i,g=this.height/e):g=this.width/g;this.fire({type:"zoomed",position:j,chart:f,target:this,multiplier:g,relativeStart:this.percentStart,relativeEnd:this.percentEnd})}},updateDragIconPositions:function(){var i=this.getDBox(),e=i.x,p=i.y,o=this.iconLeft,l=this.iconRight,n,m,j=this.scrollbarHeight;this.rotate?(n=this.dragIconWidth,m=this.dragIconHeight,o.translate((j-m)/2,p-n/2),l.translate((j-m)/2,p+i.height-n/2)):(n=this.dragIconHeight,m=this.dragIconWidth,o.translate(e-m/2,(j-n)/2),l.translate(e-m/2+i.width,(j-n)/2))},showDragIcons:function(){this.resizeEnabled&&(this.iconLeft.show(),this.iconRight.show())},hideDragIcons:function(){if(!this.resizingLeft&&!this.resizingRight&&!this.dragging){if(this.hideResizeGrips||!this.resizeEnabled){this.iconLeft.hide(),this.iconRight.hide()}this.removeCursors()}},removeCursors:function(){this.chart.setMouseCursor("auto")},fireZoomEvent:function(b){this.fire({type:b,chart:this.chart,target:this})},percentZoom:function(f,d,j){f=a.fitToBounds(f,0,d);d=a.fitToBounds(d,f,1);if(this.dragger&&this.enabled){this.dragger.stop();isNaN(f)&&(f=0);isNaN(d)&&(d=1);var i,g;this.rotate?(i=this.height,d=i-i*d,g=i-i*f):(i=this.width,g=i*d,d=i*f);this.updateScrollbarSize(d,g);this.clipDragger(!1);this.getPercents();j&&this.dispatchScrollbarEvent()}},destroy:function(){this.clear();a.remove(this.set);a.remove(this.iconRight);a.remove(this.iconLeft)},clear:function(){},handleDragStart:function(){if(this.enabled){this.fireZoomEvent("zoomStarted");var d=this.chart;this.dragger.stop();this.removeCursors();a.isModern&&(this.dragger.node.style.cssText=this.dragCursorDown);this.dragging=!0;var c=this.getDBox();this.rotate?(this.initialCoord=c.y,this.initialMouse=d.mouseY):(this.initialCoord=c.x,this.initialMouse=d.mouseX)}},handleDragStop:function(){this.updateOnReleaseOnly&&(this.update(),this.skipEvent=!1,this.dispatchScrollbarEvent());this.dragging=!1;this.mouseIsOver&&this.removeCursors();a.isModern&&(this.dragger.node.style.cssText=this.dragCursorHover);this.update();this.fireZoomEvent("zoomEnded")},handleDraggerOver:function(){this.handleMouseOver();a.isModern&&(this.dragger.node.style.cssText=this.dragCursorHover)},leftDragStart:function(){this.fireZoomEvent("zoomStarted");this.dragger.stop();this.resizingLeft=!0;this.updateGripCursor(!0)},updateGripCursor:function(b){a.isModern&&(b=this.rotate?b?this.vResizeCursorDown:this.vResizeCursorHover:b?this.hResizeCursorDown:this.hResizeCursorHover)&&(this.iconRight&&(this.iconRight.node.style.cssText=b),this.iconLeft&&(this.iconLeft.node.style.cssText=b))},leftDragStop:function(){this.resizingLeft&&(this.resizingLeft=!1,this.mouseIsOver||this.removeCursors(),this.updateOnRelease(),this.fireZoomEvent("zoomEnded"));this.updateGripCursor(!1)},rightDragStart:function(){this.fireZoomEvent("zoomStarted");this.dragger.stop();this.resizingRight=!0;this.updateGripCursor(!0)},rightDragStop:function(){this.resizingRight&&(this.resizingRight=!1,this.mouseIsOver||this.removeCursors(),this.updateOnRelease(),this.fireZoomEvent("zoomEnded"));this.updateGripCursor(!1)},iconRollOut:function(){this.removeCursors()},iconRollOver:function(){this.rotate?this.vResizeCursor&&this.chart.setMouseCursor(this.vResizeCursor):this.hResizeCursor&&this.chart.setMouseCursor(this.hResizeCursor);this.handleMouseOver()},getDBox:function(){if(this.dragger){return this.dragger.getBBox()}},handleBgClick:function(){var t=this;if(!t.resizingRight&&!t.resizingLeft){t.zooming=!0;var s,r,q=t.scrollDuration,n=t.dragger;s=t.getDBox();var p=s.height,o=s.width;r=t.chart;var j=t.y,i=t.x,d=t.rotate;d?(s="y",r=r.mouseY-p/2-j,r=a.fitToBounds(r,0,t.height-p)):(s="x",r=r.mouseX-o/2-i,r=a.fitToBounds(r,0,t.width-o));t.updateOnReleaseOnly?(t.skipEvent=!1,n.setAttr(s,r),t.dispatchScrollbarEvent(),t.clipDragger()):(t.animating=!0,r=Math.round(r),d?n.animate({y:r},q,">"):n.animate({x:r},q,">"),t.forceClip=!0,clearTimeout(t.forceTO),t.forceTO=setTimeout(function(){t.stopForceClip.call(t)},5000*q))}},updateOnRelease:function(){this.updateOnReleaseOnly&&(this.update(),this.skipEvent=!1,this.dispatchScrollbarEvent())},handleReleaseOutside:function(){if(this.set){if(this.resizingLeft||this.resizingRight||this.dragging){this.dragging=this.resizingRight=this.resizingLeft=!1,this.updateOnRelease(),this.removeCursors()}this.animating=this.mouseIsOver=!1;this.hideDragIcons();this.update()}},handleMouseOver:function(){this.mouseIsOver=!0;this.showDragIcons()},handleMouseOut:function(){this.mouseIsOver=!1;this.hideDragIcons();this.removeCursors()}})})();(function(){var a=window.AmCharts;a.ChartScrollbar=a.Class({inherits:a.SimpleChartScrollbar,construct:function(b){this.cname="ChartScrollbar";a.ChartScrollbar.base.construct.call(this,b);this.graphLineColor="#BBBBBB";this.graphLineAlpha=0;this.graphFillColor="#BBBBBB";this.graphFillAlpha=1;this.selectedGraphLineColor="#888888";this.selectedGraphLineAlpha=0;this.selectedGraphFillColor="#888888";this.selectedGraphFillAlpha=1;this.gridCount=0;this.gridColor="#FFFFFF";this.gridAlpha=0.7;this.skipEvent=this.autoGridCount=!1;this.color="#FFFFFF";this.scrollbarCreated=!1;this.oppositeAxis=!0;this.accessibleLabel="Zoom chart using cursor arrows";a.applyTheme(this,b,this.cname)},init:function(){var f=this.categoryAxis,d=this.chart,h=this.gridAxis;f||("CategoryAxis"==this.gridAxis.cname?(this.catScrollbar=!0,f=new a.CategoryAxis,f.id="scrollbar"):(f=new a.ValueAxis,f.data=d.chartData,f.id=h.id,f.type=h.type,f.maximumDate=h.maximumDate,f.minimumDate=h.minimumDate,f.minPeriod=h.minPeriod,f.minMaxField=h.minMaxField),this.categoryAxis=f);f.chart=d;var g=d.categoryAxis;g&&(f.firstDayOfWeek=g.firstDayOfWeek);f.dateFormats=h.dateFormats;f.markPeriodChange=h.markPeriodChange;f.boldPeriodBeginning=h.boldPeriodBeginning;f.labelFunction=h.labelFunction;f.axisItemRenderer=a.RecItem;f.axisRenderer=a.RecAxis;f.guideFillRenderer=a.RecFill;f.inside=!0;f.fontSize=this.fontSize;f.tickLength=0;f.axisAlpha=0;a.isString(this.graph)&&(this.graph=a.getObjById(d.graphs,this.graph));(f=this.graph)&&this.catScrollbar&&(h=this.valueAxis,h||(this.valueAxis=h=new a.ValueAxis,h.visible=!1,h.scrollbar=!0,h.axisItemRenderer=a.RecItem,h.axisRenderer=a.RecAxis,h.guideFillRenderer=a.RecFill,h.labelsEnabled=!1,h.chart=d),d=this.unselectedGraph,d||(d=new a.AmGraph,d.scrollbar=!0,this.unselectedGraph=d,d.negativeBase=f.negativeBase,d.noStepRisers=f.noStepRisers),d=this.selectedGraph,d||(d=new a.AmGraph,d.scrollbar=!0,this.selectedGraph=d,d.negativeBase=f.negativeBase,d.noStepRisers=f.noStepRisers));this.scrollbarCreated=!0},draw:function(){var F=this;a.ChartScrollbar.base.draw.call(F);if(F.enabled){F.scrollbarCreated||F.init();var E=F.chart,D=E.chartData,B=F.categoryAxis,x=F.rotate,A=F.x,y=F.y,v=F.width,u=F.height,s=F.gridAxis,j=F.set;B.setOrientation(!x);B.parseDates=s.parseDates;"ValueAxis"==F.categoryAxis.cname&&(B.rotate=!x);B.equalSpacing=s.equalSpacing;B.minPeriod=s.minPeriod;B.startOnAxis=s.startOnAxis;B.width=v-1;B.height=u;B.gridCount=F.gridCount;B.gridColor=F.gridColor;B.gridAlpha=F.gridAlpha;B.color=F.color;B.tickLength=0;B.axisAlpha=0;B.autoGridCount=F.autoGridCount;B.parseDates&&!B.equalSpacing&&B.timeZoom(E.firstTime,E.lastTime);B.minimum=F.gridAxis.fullMin;B.maximum=F.gridAxis.fullMax;B.strictMinMax=!0;B.zoom(0,D.length-1);if((s=F.graph)&&F.catScrollbar){var i=F.valueAxis,o=s.valueAxis;i.id=o.id;i.rotate=x;i.setOrientation(x);i.width=v;i.height=u;i.dataProvider=D;i.reversed=o.reversed;i.logarithmic=o.logarithmic;i.gridAlpha=0;i.axisAlpha=0;j.push(i.set);x?(i.y=y,i.x=0):(i.x=A,i.y=0);var A=Infinity,y=-Infinity,H;for(H=0;H<D.length;H++){var d=D[H].axes[o.id].graphs[s.id].values,G;for(G in d){if(d.hasOwnProperty(G)&&"percents"!=G&&"total"!=G){var C=d[G];C<A&&(A=C);C>y&&(y=C)}}}Infinity!=A&&(i.minimum=A);-Infinity!=y&&(i.maximum=y+0.1*(y-A));A==y&&(--i.minimum,i.maximum+=1);void 0!==F.minimum&&(i.minimum=F.minimum);void 0!==F.maximum&&(i.maximum=F.maximum);i.zoom(0,D.length-1);G=F.unselectedGraph;G.id=s.id;G.bcn="scrollbar-graph-";G.rotate=x;G.chart=E;G.data=D;G.valueAxis=i;G.chart=s.chart;G.categoryAxis=F.categoryAxis;G.periodSpan=s.periodSpan;G.valueField=s.valueField;G.openField=s.openField;G.closeField=s.closeField;G.highField=s.highField;G.lowField=s.lowField;G.lineAlpha=F.graphLineAlpha;G.lineColorR=F.graphLineColor;G.fillAlphas=F.graphFillAlpha;G.fillColorsR=F.graphFillColor;G.connect=s.connect;G.hidden=s.hidden;G.width=v;G.height=u;G.pointPosition=s.pointPosition;G.stepDirection=s.stepDirection;G.periodSpan=s.periodSpan;o=F.selectedGraph;o.id=s.id;o.bcn=G.bcn+"selected-";o.rotate=x;o.chart=E;o.data=D;o.valueAxis=i;o.chart=s.chart;o.categoryAxis=B;o.periodSpan=s.periodSpan;o.valueField=s.valueField;o.openField=s.openField;o.closeField=s.closeField;o.highField=s.highField;o.lowField=s.lowField;o.lineAlpha=F.selectedGraphLineAlpha;o.lineColorR=F.selectedGraphLineColor;o.fillAlphas=F.selectedGraphFillAlpha;o.fillColorsR=F.selectedGraphFillColor;o.connect=s.connect;o.hidden=s.hidden;o.width=v;o.height=u;o.pointPosition=s.pointPosition;o.stepDirection=s.stepDirection;o.periodSpan=s.periodSpan;E=F.graphType;E||(E=s.type);G.type=E;o.type=E;D=D.length-1;G.zoom(0,D);o.zoom(0,D);o.set.click(function(){F.handleBackgroundClick()}).mouseover(function(){F.handleMouseOver()}).mouseout(function(){F.handleMouseOut()});G.set.click(function(){F.handleBackgroundClick()}).mouseover(function(){F.handleMouseOver()}).mouseout(function(){F.handleMouseOut()});j.push(G.set);j.push(o.set)}j.push(B.set);j.push(B.labelsSet);F.bg.toBack();F.invisibleBg.toFront();F.dragger.toFront();F.iconLeft.toFront();F.iconRight.toFront()}},timeZoom:function(e,d,f){this.startTime=e;this.endTime=d;this.timeDifference=d-e;this.skipEvent=!a.toBoolean(f);this.zoomScrollbar();this.dispatchScrollbarEvent()},zoom:function(d,c){this.start=d;this.end=c;this.skipEvent=!0;this.zoomScrollbar()},dispatchScrollbarEvent:function(){if(this.categoryAxis&&"ValueAxis"==this.categoryAxis.cname){a.ChartScrollbar.base.dispatchScrollbarEvent.call(this)}else{if(this.skipEvent){this.skipEvent=!1}else{var t=this.chart.chartData,s,r,q=this.dragger.getBBox();s=q.x;var n=q.y,p=q.width,q=q.height,o=this.chart;this.rotate?(s=n,r=q):r=p;p={type:"zoomed",target:this};p.chart=o;var j=this.categoryAxis,i=this.stepWidth,n=o.minSelectedTime,q=o.maxSelectedTime,d=!1;if(j.parseDates&&!j.equalSpacing){if(t=o.lastTime,o=o.firstTime,j=Math.round(s/i)+o,s=this.dragging?j+this.timeDifference:Math.round((s+r)/i)+o,j>s&&(j=s),0<n&&s-j<n&&(s=Math.round(j+(s-j)/2),d=Math.round(n/2),j=s-d,s+=d,d=!0),0<q&&s-j>q&&(s=Math.round(j+(s-j)/2),d=Math.round(q/2),j=s-d,s+=d,d=!0),s>t&&(s=t),s-n<j&&(j=s-n),j<o&&(j=o),j+n>s&&(s=j+n),j!=this.startTime||s!=this.endTime){this.startTime=j,this.endTime=s,p.start=j,p.end=s,p.startDate=new Date(j),p.endDate=new Date(s),this.fire(p)}}else{j.startOnAxis||(s+=i/2);r-=this.stepWidth/2;n=j.xToIndex(s);s=j.xToIndex(s+r);if(n!=this.start||this.end!=s){j.startOnAxis&&(this.resizingRight&&n==s&&s++,this.resizingLeft&&n==s&&(0<n?n--:s=1)),this.start=n,this.end=this.dragging?this.start+this.difference:s,p.start=this.start,p.end=this.end,j.parseDates&&(t[this.start]&&(p.startDate=new Date(t[this.start].time)),t[this.end]&&(p.endDate=new Date(t[this.end].time))),this.fire(p)}this.percentStart=n;this.percentEnd=s}d&&this.zoomScrollbar(!0)}}},zoomScrollbar:function(f){if((!(this.dragging||this.resizingLeft||this.resizingRight||this.animating)||f)&&this.dragger&&this.enabled){var e,j,i=this.chart;f=i.chartData;var g=this.categoryAxis;g.parseDates&&!g.equalSpacing?(f=g.stepWidth,j=i.firstTime,e=f*(this.startTime-j),j=f*(this.endTime-j)):(f[this.start]&&(e=f[this.start].x[g.id]),f[this.end]&&(j=f[this.end].x[g.id]),f=g.stepWidth,g.startOnAxis||(i=f/2,e-=i,j+=i));this.stepWidth=f;isNaN(e)||isNaN(j)||this.updateScrollbarSize(e,j)}},maskGraphs:function(f,e,j,i){var g=this.selectedGraph;g&&g.set.clipRect(f,e,j,i)},handleDragStart:function(){a.ChartScrollbar.base.handleDragStart.call(this);this.difference=this.end-this.start;this.timeDifference=this.endTime-this.startTime;0>this.timeDifference&&(this.timeDifference=0)},handleBackgroundClick:function(){a.ChartScrollbar.base.handleBackgroundClick.call(this);this.dragging||(this.difference=this.end-this.start,this.timeDifference=this.endTime-this.startTime,0>this.timeDifference&&(this.timeDifference=0))}})})();(function(){var a=window.AmCharts;a.AmBalloon=a.Class({construct:function(b){this.cname="AmBalloon";this.enabled=!0;this.fillColor="#FFFFFF";this.fillAlpha=0.8;this.borderThickness=2;this.borderColor="#FFFFFF";this.borderAlpha=1;this.cornerRadius=0;this.maxWidth=220;this.horizontalPadding=8;this.verticalPadding=4;this.pointerWidth=6;this.pointerOrientation="V";this.color="#000000";this.adjustBorderColor=!0;this.show=this.follow=this.showBullet=!1;this.bulletSize=3;this.shadowAlpha=0.4;this.shadowColor="#000000";this.fadeOutDuration=this.animationDuration=0.3;this.fixedPosition=!0;this.offsetY=6;this.offsetX=1;this.textAlign="center";this.disableMouseEvents=!0;this.deltaSignX=this.deltaSignY=1;a.isModern||(this.offsetY*=1.5);this.sdy=this.sdx=0;a.applyTheme(this,b,this.cname)},draw:function(){var aj=this.pointToX,ai=this.pointToY;a.isModern||(this.drop=!1);var ah=this.chart;a.VML&&(this.fadeOutDuration=0);this.xAnim&&ah.stopAnim(this.xAnim);this.yAnim&&ah.stopAnim(this.yAnim);this.sdy=this.sdx=0;if(!isNaN(aj)){var ag=this.follow,ad=ah.container,af=this.set;a.remove(af);this.removeDiv();af=ad.set();af.node.style.pointerEvents="none";this.set=af;this.mainSet?(this.mainSet.push(this.set),this.sdx=this.mainSet.x,this.sdy=this.mainSet.y):ah.balloonsSet.push(af);if(this.show){var ae=this.l,ac=this.t,ab=this.r,Z=this.b,W=this.balloonColor,U=this.fillColor,X=this.borderColor,N=U;void 0!=W&&(this.adjustBorderColor?N=X=W:U=W);var S=this.horizontalPadding,K=this.verticalPadding,o=this.pointerWidth,F=this.pointerOrientation,L=this.cornerRadius,V=ah.fontFamily,v=this.fontSize;void 0==v&&(v=ah.fontSize);var W=document.createElement("div"),T=ah.classNamePrefix;W.className=T+"-balloon-div";this.className&&(W.className=W.className+" "+T+"-balloon-div-"+this.className);T=W.style;this.disableMouseEvents&&(T.pointerEvents="none");T.position="absolute";var O=this.minWidth,R=document.createElement("div");W.appendChild(R);var E=R.style;isNaN(O)||(E.minWidth=O-2*S+"px");E.textAlign=this.textAlign;E.maxWidth=this.maxWidth+"px";E.fontSize=v+"px";E.color=this.color;E.fontFamily=V;R.innerHTML=this.text;ah.chartDiv.appendChild(W);this.textDiv=W;var E=W.offsetWidth,G=W.offsetHeight;W.clientHeight&&(E=W.clientWidth,G=W.clientHeight);V=G+2*K;R=E+2*S;!isNaN(O)&&R<O&&(R=O);window.opera&&(V+=2);var d=!1,v=this.offsetY;ah.handDrawn&&(v+=ah.handDrawScatter+2);"H"!=F?(O=aj-R/2,ai<ac+V+10&&"down"!=F?(d=!0,ag&&(ai+=v),v=ai+o,this.deltaSignY=-1):(ag&&(ai-=v),v=ai-V-o,this.deltaSignY=1)):(2*o>V&&(o=V/2),v=ai-V/2,aj<ae+(ab-ae)/2?(O=aj+o,this.deltaSignX=-1):(O=aj-R-o,this.deltaSignX=1));v+V>=Z&&(v=Z-V);v<ac&&(v=ac);O<ae&&(O=ae);O+R>ab&&(O=ab-R);var ac=v+K,Z=O+S,j=this.shadowAlpha,i=this.shadowColor,S=this.borderThickness,Y=this.bulletSize,s,K=this.fillAlpha,ak=this.borderAlpha;this.showBullet&&(s=a.circle(ad,Y,N,K),af.push(s));this.drop?(ae=R/1.6,ab=0,"V"==F&&(F="down"),"H"==F&&(F="left"),"down"==F&&(O=aj+1,v=ai-ae-ae/3),"up"==F&&(ab=180,O=aj+1,v=ai+ae+ae/3),"left"==F&&(ab=270,O=aj+ae+ae/3+2,v=ai),"right"==F&&(ab=90,O=aj-ae-ae/3+2,v=ai),ac=v-G/2+1,Z=O-E/2-1,U=a.drop(ad,ae,ab,U,K,S,X,ak)):0<L||0===o?(0<j&&(aj=a.rect(ad,R,V,U,0,S+1,i,j,L),a.isModern?aj.translate(1,1):aj.translate(4,4),af.push(aj)),U=a.rect(ad,R,V,U,K,S,X,ak,L)):(N=[],L=[],"H"!=F?(ae=aj-O,ae>R-o&&(ae=R-o),ae<o&&(ae=o),N=[0,ae-o,aj-O,ae+o,R,R,0,0],L=d?[0,0,ai-v,0,0,V,V,0]:[V,V,ai-v,V,V,0,0,V]):(F=ai-v,F>V-o&&(F=V-o),F<o&&(F=o),L=[0,F-o,ai-v,F+o,V,V,0,0],N=aj<ae+(ab-ae)/2?[0,0,O<aj?0:aj-O,0,0,R,R,0]:[R,R,O+R>aj?R:aj-O,R,R,0,0,R]),0<j&&(aj=a.polygon(ad,N,L,U,0,S,i,j),aj.translate(1,1),af.push(aj)),U=a.polygon(ad,N,L,U,K,S,X,ak));this.bg=U;af.push(U);U.toFront();a.setCN(ah,U,"balloon-bg");this.className&&a.setCN(ah,U,"balloon-bg-"+this.className);ad=1*this.deltaSignX;Z+=this.sdx;ac+=this.sdy;T.left=Z+"px";T.top=ac+"px";af.translate(O-ad,v,1,!0);U=U.getBBox();this.bottom=v+V+1;this.yPos=U.y+v;s&&s.translate(this.pointToX-O+ad,ai-v);ai=this.animationDuration;0<this.animationDuration&&!ag&&!isNaN(this.prevX)&&(af.translate(this.prevX,this.prevY,NaN,!0),af.animate({translate:O-ad+","+v},ai,"easeOutSine"),W&&(T.left=this.prevTX+"px",T.top=this.prevTY+"px",this.xAnim=ah.animate({node:W},"left",this.prevTX,Z,ai,"easeOutSine","px"),this.yAnim=ah.animate({node:W},"top",this.prevTY,ac,ai,"easeOutSine","px")));this.prevX=O-ad;this.prevY=v;this.prevTX=Z;this.prevTY=ac}}},fixPrevious:function(){this.rPrevX=this.prevX;this.rPrevY=this.prevY;this.rPrevTX=this.prevTX;this.rPrevTY=this.prevTY},restorePrevious:function(){this.prevX=this.rPrevX;this.prevY=this.rPrevY;this.prevTX=this.rPrevTX;this.prevTY=this.rPrevTY},followMouse:function(){if(this.follow&&this.show){var f=this.chart.mouseX-this.offsetX*this.deltaSignX-this.sdx,e=this.chart.mouseY-this.sdy;this.pointToX=f;this.pointToY=e;if(f!=this.previousX||e!=this.previousY){if(this.previousX=f,this.previousY=e,0===this.cornerRadius){this.draw()}else{var j=this.set;if(j){var i=j.getBBox(),f=f-i.width/2,g=e-i.height-10;f<this.l&&(f=this.l);f>this.r-i.width&&(f=this.r-i.width);g<this.t&&(g=e+10);j.translate(f,g);e=this.textDiv.style;e.left=f+this.horizontalPadding+"px";e.top=g+this.verticalPadding+"px"}}}}},changeColor:function(b){this.balloonColor=b},setBounds:function(f,e,h,g){this.l=f;this.t=e;this.r=h;this.b=g;this.destroyTO&&clearTimeout(this.destroyTO)},showBalloon:function(b){if(this.text!=b||this.positionChanged){this.text=b,this.isHiding=!1,this.show=!0,this.destroyTO&&clearTimeout(this.destroyTO),b=this.chart,this.fadeAnim1&&b.stopAnim(this.fadeAnim1),this.fadeAnim2&&b.stopAnim(this.fadeAnim2),this.draw(),this.positionChanged=!1}},hide:function(f){var e=this;e.text=void 0;isNaN(f)&&(f=e.fadeOutDuration);var h=e.chart;if(0<f&&!e.isHiding){e.isHiding=!0;e.destroyTO&&clearTimeout(e.destroyTO);e.destroyTO=setTimeout(function(){e.destroy.call(e)},1000*f);e.follow=!1;e.show=!1;var g=e.set;g&&(g.setAttr("opacity",e.fillAlpha),e.fadeAnim1=g.animate({opacity:0},f,"easeInSine"));e.textDiv&&(e.fadeAnim2=h.animate({node:e.textDiv},"opacity",1,0,f,"easeInSine",""))}else{e.show=!1,e.follow=!1,e.destroy()}},setPosition:function(d,c){if(d!=this.pointToX||c!=this.pointToY){this.previousX=this.pointToX,this.previousY=this.pointToY,this.pointToX=d,this.pointToY=c,this.positionChanged=!0}},followCursor:function(f){var e=this;e.follow=f;clearInterval(e.interval);var h=e.chart.mouseX-e.sdx,g=e.chart.mouseY-e.sdy;!isNaN(h)&&f&&(e.pointToX=h-e.offsetX*e.deltaSignX,e.pointToY=g,e.followMouse(),e.interval=setInterval(function(){e.followMouse.call(e)},40))},removeDiv:function(){if(this.textDiv){var b=this.textDiv.parentNode;b&&b.removeChild(this.textDiv)}},destroy:function(){clearInterval(this.interval);a.remove(this.set);this.removeDiv();this.set=null}})})();(function(){var a=window.AmCharts;a.AmCoordinateChart=a.Class({inherits:a.AmChart,construct:function(b){a.AmCoordinateChart.base.construct.call(this,b);this.theme=b;this.createEvents("rollOverGraphItem","rollOutGraphItem","clickGraphItem","doubleClickGraphItem","rightClickGraphItem","clickGraph","rollOverGraph","rollOutGraph");this.startAlpha=1;this.startDuration=0;this.startEffect="elastic";this.sequencedAnimation=!0;this.colors="#FF6600 #FCD202 #B0DE09 #0D8ECF #2A0CD0 #CD0D74 #CC0000 #00CC00 #0000CC #DDDDDD #999999 #333333 #990000".split(" ");this.balloonDateFormat="MMM DD, YYYY";this.valueAxes=[];this.graphs=[];this.guides=[];this.gridAboveGraphs=!1;a.applyTheme(this,b,"AmCoordinateChart")},initChart:function(){a.AmCoordinateChart.base.initChart.call(this);this.drawGraphs=!0;var b=this.categoryAxis;b&&(this.categoryAxis=a.processObject(b,a.CategoryAxis,this.theme));this.processValueAxes();this.createValueAxes();this.processGraphs();this.processGuides();a.VML&&(this.startAlpha=1);this.setLegendData(this.graphs);this.gridAboveGraphs&&(this.gridSet.toFront(),this.bulletSet.toFront(),this.balloonsSet.toFront())},createValueAxes:function(){if(0===this.valueAxes.length){var b=new a.ValueAxis;this.addValueAxis(b)}},parseData:function(){this.processValueAxes();this.processGraphs()},parseSerialData:function(e){this.chartData=[];if(e){if(0<this.processTimeout){1>this.processCount&&(this.processCount=1);var d=e.length/this.processCount;this.parseCount=Math.ceil(d)-1;for(var f=0;f<d;f++){this.delayParseSerialData(e,f)}}else{this.parseCount=0,this.parsePartSerialData(e,0,e.length,0)}}else{this.onDataUpdated()}},delayParseSerialData:function(f,e){var h=this,g=h.processCount;setTimeout(function(){h.parsePartSerialData.call(h,f,e*g,(e+1)*g,e)},h.processTimeout)},parsePartSerialData:function(ak,aj,ai,ah){ai>ak.length&&(ai=ak.length);var ae=this.graphs,ag={},af=this.seriesIdField;af||(af=this.categoryField);var ad=!1,ac,ab=this.categoryAxis,X,V,Y;ab&&(ad=ab.parseDates,X=ab.forceShowField,Y=ab.classNameField,V=ab.labelColorField,ac=ab.categoryFunction);var O,T,L={},s;ad&&(O=a.extractPeriod(ab.minPeriod),T=O.period,O=O.count,s=a.getPeriodDuration(T,O));var G={};this.lookupTable=G;var N,W=this.dataDateFormat,E={};for(N=aj;N<ai;N++){var U={},R=ak[N];aj=R[this.categoryField];U.dataContext=R;U.category=ac?ac(aj,R,ab):String(aj);X&&(U.forceShow=R[X]);Y&&(U.className=R[Y]);V&&(U.labelColor=R[V]);G[R[af]]=U;if(ad&&(ab.categoryFunction?aj=ab.categoryFunction(aj,R,ab):(!W||aj instanceof Date||(aj=aj.toString()+" |"),aj=a.getDate(aj,W,ab.minPeriod)),aj=a.resetDateToMin(aj,T,O,ab.firstDayOfWeek),U.category=aj,U.time=aj.getTime(),isNaN(U.time))){continue}var S=this.valueAxes;U.axes={};U.x={};var F;for(F=0;F<S.length;F++){var K=S[F].id;U.axes[K]={};U.axes[K].graphs={};var i;for(i=0;i<ae.length;i++){aj=ae[i];var o=aj.id,j=1.1;isNaN(aj.gapPeriod)||(j=aj.gapPeriod);var Z=aj.periodValue;if(aj.valueAxis.id==K){U.axes[K].graphs[o]={};var v={};v.index=N;var al=R;aj.dataProvider&&(al=ag);v.values=this.processValues(al,aj,Z);if(!aj.connect||aj.forceGap&&!isNaN(aj.gapPeriod)){if(E&&E[o]&&0<j&&U.time-L[o]>=s*j&&(E[o].gap=!0),aj.forceGap){var j=0,d;for(d in v.values){j++}0<j&&(L[o]=U.time,E[o]=v)}else{L[o]=U.time,E[o]=v}}this.processFields(aj,v,al);v.category=U.category;v.serialDataItem=U;v.graph=aj;U.axes[K].graphs[o]=v}}}this.chartData[N]=U}if(this.parseCount==ah){for(ak=0;ak<ae.length;ak++){aj=ae[ak],aj.dataProvider&&this.parseGraphData(aj)}this.dataChanged=!1;this.dispatchDataUpdated=!0;this.onDataUpdated()}},processValues:function(t,s,r){var q={},n,p=!1;"candlestick"!=s.type&&"ohlc"!=s.type||""===r||(p=!0);for(var o="value error open close low high".split(" "),j=0;j<o.length;j++){var i=o[j];"value"!=i&&"error"!=i&&p&&(r=i.charAt(0).toUpperCase()+i.slice(1));var d=t[s[i+"Field"]+r];null!==d&&(n=Number(d),isNaN(n)||(q[i]=n),"date"==s.valueAxis.type&&void 0!==d&&(n=a.getDate(d,s.chart.dataDateFormat),q[i]=n.getTime()))}return q},parseGraphData:function(i){var e=i.dataProvider,n=i.seriesIdField;n||(n=this.seriesIdField);n||(n=this.categoryField);var m;for(m=0;m<e.length;m++){var j=e[m],l=this.lookupTable[String(j[n])],k=i.valueAxis.id;l&&(k=l.axes[k].graphs[i.id],k.serialDataItem=l,k.values=this.processValues(j,i,i.periodValue),this.processFields(i,k,j))}},addValueAxis:function(b){b.chart=this;this.valueAxes.push(b);this.validateData()},removeValueAxesAndGraphs:function(){var d=this.valueAxes,c;for(c=d.length-1;-1<c;c--){this.removeValueAxis(d[c])}},removeValueAxis:function(f){var e=this.graphs,h;for(h=e.length-1;0<=h;h--){var g=e[h];g&&g.valueAxis==f&&this.removeGraph(g)}e=this.valueAxes;for(h=e.length-1;0<=h;h--){e[h]==f&&e.splice(h,1)}this.validateData()},addGraph:function(b){this.graphs.push(b);this.chooseGraphColor(b,this.graphs.length-1);this.validateData()},removeGraph:function(e){var d=this.graphs,f;for(f=d.length-1;0<=f;f--){d[f]==e&&(d.splice(f,1),e.destroy())}this.validateData()},handleValueAxisZoom:function(){},processValueAxes:function(){var e=this.valueAxes,d;for(d=0;d<e.length;d++){var f=e[d],f=a.processObject(f,a.ValueAxis,this.theme);e[d]=f;f.chart=this;f.init();this.listenTo(f,"axisIntZoomed",this.handleValueAxisZoom);f.id||(f.id="valueAxisAuto"+d+"_"+(new Date).getTime());void 0===f.usePrefixes&&(f.usePrefixes=this.usePrefixes)}},processGuides:function(){var f=this.guides,d=this.categoryAxis;if(f){for(var j=0;j<f.length;j++){var i=f[j];(void 0!==i.category||void 0!==i.date)&&d&&d.addGuide(i);i.id||(i.id="guideAuto"+j+"_"+(new Date).getTime());var g=i.valueAxis;g?(a.isString(g)&&(g=this.getValueAxisById(g)),g?g.addGuide(i):this.valueAxes[0].addGuide(i)):isNaN(i.value)||this.valueAxes[0].addGuide(i)}}},processGraphs:function(){var e=this.graphs,d;this.graphsById={};for(d=0;d<e.length;d++){var f=e[d],f=a.processObject(f,a.AmGraph,this.theme);e[d]=f;this.chooseGraphColor(f,d);f.chart=this;f.init();a.isString(f.valueAxis)&&(f.valueAxis=this.getValueAxisById(f.valueAxis));f.valueAxis||(f.valueAxis=this.valueAxes[0]);f.id||(f.id="graphAuto"+d+"_"+(new Date).getTime());this.graphsById[f.id]=f}},formatString:function(f,d,j){var i=d.graph,g=i.valueAxis;g.duration&&d.values.value&&(g=a.formatDuration(d.values.value,g.duration,"",g.durationUnits,g.maxInterval,g.numberFormatter),f=f.split("[[value]]").join(g));f=a.massReplace(f,{"[[title]]":i.title,"[[description]]":d.description});f=j?a.fixNewLines(f):a.fixBrakes(f);return f=a.cleanFromEmpty(f)},getBalloonColor:function(f,d,j){var i=f.lineColor,g=f.balloonColor;j&&(g=i);j=f.fillColorsR;"object"==typeof j?i=j[0]:void 0!==j&&(i=j);d.isNegative&&(j=f.negativeLineColor,f=f.negativeFillColors,"object"==typeof f?j=f[0]:void 0!==f&&(j=f),void 0!==j&&(i=j));void 0!==d.color&&(i=d.color);void 0!==d.lineColor&&(i=d.lineColor);d=d.fillColors;void 0!==d&&(i=d,a.ifArray(d)&&(i=d[0]));void 0===g&&(g=i);return g},getGraphById:function(b){return a.getObjById(this.graphs,b)},getValueAxisById:function(b){return a.getObjById(this.valueAxes,b)},processFields:function(i,d,n){if(i.itemColors){var m=i.itemColors,j=d.index;d.color=j<m.length?m[j]:a.randomColor()}m="lineColor color alpha fillColors description bullet customBullet bulletSize bulletConfig url labelColor dashLength pattern gap className columnIndex".split(" ");for(j=0;j<m.length;j++){var l=m[j],k=i[l+"Field"];k&&(k=n[k],a.isDefined(k)&&(d[l]=k))}d.dataContext=n},chooseGraphColor:function(e,d){if(e.lineColor){e.lineColorR=e.lineColor}else{var f;f=this.colors.length>d?this.colors[d]:e.lineColorR?e.lineColorR:a.randomColor();e.lineColorR=f}e.fillColorsR=e.fillColors?e.fillColors:e.lineColorR;e.bulletBorderColorR=e.bulletBorderColor?e.bulletBorderColor:e.useLineColorForBulletBorder?e.lineColorR:e.bulletColor;e.bulletColorR=e.bulletColor?e.bulletColor:e.lineColorR;if(f=this.patterns){e.pattern=f[d]}},handleLegendEvent:function(f){var e=f.type;if(f=f.dataItem){var h=f.hidden,g=f.showBalloon;switch(e){case"clickMarker":this.textClickEnabled&&(g?this.hideGraphsBalloon(f):this.showGraphsBalloon(f));break;case"clickLabel":g?this.hideGraphsBalloon(f):this.showGraphsBalloon(f);break;case"rollOverItem":h||this.highlightGraph(f);break;case"rollOutItem":h||this.unhighlightGraph();break;case"hideItem":this.hideGraph(f);break;case"showItem":this.showGraph(f)}}},highlightGraph:function(f){var e=this.graphs;if(e){var j,i=0.2;this.legend&&(i=this.legend.rollOverGraphAlpha);if(1!=i){for(j=0;j<e.length;j++){var g=e[j];g!=f&&g.changeOpacity(i)}}}},unhighlightGraph:function(){var d;this.legend&&(d=this.legend.rollOverGraphAlpha);if(1!=d){d=this.graphs;var c;for(c=0;c<d.length;c++){d[c].changeOpacity(1)}}},showGraph:function(b){b.switchable&&(b.hidden=!1,this.dataChanged=!0,"xy"!=this.type&&(this.marginsUpdated=!1),this.chartCreated&&this.initChart())},hideGraph:function(b){b.switchable&&(this.dataChanged=!0,"xy"!=this.type&&(this.marginsUpdated=!1),b.hidden=!0,this.chartCreated&&this.initChart())},hideGraphsBalloon:function(b){b.showBalloon=!1;this.updateLegend()},showGraphsBalloon:function(b){b.showBalloon=!0;this.updateLegend()},updateLegend:function(){this.legend&&this.legend.invalidateSize()},resetAnimation:function(){var d=this.graphs;if(d){var c;for(c=0;c<d.length;c++){d[c].animationPlayed=!1}}},animateAgain:function(){this.resetAnimation();this.validateNow()}})})();(function(){var a=window.AmCharts;a.TrendLine=a.Class({construct:function(b){this.cname="TrendLine";this.createEvents("click","rollOver","rollOut");this.isProtected=!1;this.dashLength=0;this.lineColor="#00CC00";this.lineThickness=this.lineAlpha=1;a.applyTheme(this,b,this.cname)},draw:function(){var N=this;N.destroy();var M=N.chart,L=M.container,K,H,J,I,G=N.categoryAxis,F=N.initialDate,E=N.initialCategory,C=N.finalDate,y=N.finalCategory,D=N.valueAxis,s=N.valueAxisX,v=N.initialXValue,j=N.finalXValue,d=N.initialValue,i=N.finalValue,o=D.recalculateToPercents,B=M.dataDateFormat;G&&(F&&(F=a.getDate(F,B,"fff"),N.initialDate=F,K=G.dateToCoordinate(F)),E&&(K=G.categoryToCoordinate(E)),C&&(C=a.getDate(C,B,"fff"),N.finalDate=C,H=G.dateToCoordinate(C)),y&&(H=G.categoryToCoordinate(y)));s&&!o&&(isNaN(v)||(K=s.getCoordinate(v)),isNaN(j)||(H=s.getCoordinate(j)));D&&!o&&(isNaN(d)||(J=D.getCoordinate(d)),isNaN(i)||(I=D.getCoordinate(i)));if(!(isNaN(K)||isNaN(H)||isNaN(J)||isNaN(J))){M.rotate?(G=[J,I],I=[K,H]):(G=[K,H],I=[J,I]);F=N.lineColor;J=a.line(L,G,I,F,N.lineAlpha,N.lineThickness,N.dashLength);K=G;H=I;y=G[1]-G[0];D=I[1]-I[0];0===y&&(y=0.01);0===D&&(D=0.01);E=y/Math.abs(y);C=D/Math.abs(D);D=90*Math.PI/180-Math.asin(y/(y*D/Math.abs(y*D)*Math.sqrt(Math.pow(y,2)+Math.pow(D,2))));y=Math.abs(5*Math.cos(D));D=Math.abs(5*Math.sin(D));K.push(G[1]-E*D,G[0]-E*D);H.push(I[1]+C*y,I[0]+C*y);I=a.polygon(L,K,H,F,0.005,0);L=L.set([I,J]);L.translate(M.marginLeftReal,M.marginTopReal);M.trendLinesSet.push(L);a.setCN(M,J,"trend-line");a.setCN(M,J,"trend-line-"+N.id);N.line=J;N.set=L;if(J=N.initialImage){J=a.processObject(J,a.Image,N.theme),J.chart=M,J.draw(),J.translate(K[0]+J.offsetX,H[0]+J.offsetY),L.push(J.set)}if(J=N.finalImage){J=a.processObject(J,a.Image,N.theme),J.chart=M,J.draw(),J.translate(K[1]+J.offsetX,H[1]+J.offsetY),L.push(J.set)}I.mouseup(function(){N.handleLineClick()}).mouseover(function(){N.handleLineOver()}).mouseout(function(){N.handleLineOut()});I.touchend&&I.touchend(function(){N.handleLineClick()});L.clipRect(0,0,M.plotAreaWidth,M.plotAreaHeight)}},handleLineClick:function(){this.fire({type:"click",trendLine:this,chart:this.chart})},handleLineOver:function(){var b=this.rollOverColor;void 0!==b&&this.line.attr({stroke:b});this.balloonText&&(clearTimeout(this.chart.hoverInt),b=this.line.getBBox(),this.chart.showBalloon(this.balloonText,this.lineColor,!0,this.x+b.x+b.width/2,this.y+b.y+b.height/2));this.fire({type:"rollOver",trendLine:this,chart:this.chart})},handleLineOut:function(){this.line.attr({stroke:this.lineColor});this.balloonText&&this.chart.hideBalloon();this.fire({type:"rollOut",trendLine:this,chart:this.chart})},destroy:function(){a.remove(this.set)}})})();(function(){var a=window.AmCharts;a.Image=a.Class({construct:function(b){this.cname="Image";this.height=this.width=20;this.rotation=this.offsetY=this.offsetX=0;this.balloonColor=this.color="#000000";this.opacity=1;a.applyTheme(this,b,this.cname)},draw:function(){var f=this;f.set&&f.set.remove();var e=f.chart.container;f.set=e.set();var h,g;f.url?(h=e.image(f.url,0,0,f.width,f.height),g=1):f.svgPath&&(h=e.path(f.svgPath),h.setAttr("fill",f.color),h.setAttr("stroke",f.outlineColor),e=h.getBBox(),g=Math.min(f.width/e.width,f.height/e.height));h&&(h.setAttr("opacity",f.opacity),f.set.rotate(f.rotation),h.translate(-f.width/2,-f.height/2,g),f.balloonText&&h.mouseover(function(){f.chart.showBalloon(f.balloonText,f.balloonColor,!0)}).mouseout(function(){f.chart.hideBalloon()}).touchend(function(){f.chart.hideBalloon()}).touchstart(function(){f.chart.showBalloon(f.balloonText,f.balloonColor,!0)}),f.set.push(h))},translate:function(d,c){this.set&&this.set.translate(d,c)}})})();(function(){var a=window.AmCharts;a.circle=function(r,q,p,o,j,n,m,i,d){0>=q&&(q=0.001);if(void 0==j||0===j){j=0.01}void 0===n&&(n="#000000");void 0===m&&(m=0);o={fill:p,stroke:n,"fill-opacity":o,"stroke-width":j,"stroke-opacity":m};r=isNaN(d)?r.circle(0,0,q).attr(o):r.ellipse(0,0,q,d).attr(o);i&&r.gradient("radialGradient",[p,a.adjustLuminosity(p,-0.6)]);return r};a.text=function(i,d,p,o,l,n,m,j){n||(n="middle");"right"==n&&(n="end");"left"==n&&(n="start");isNaN(j)&&(j=1);void 0!==d&&(d=String(d),a.isIE&&!a.isModern&&(d=d.replace("&amp;","&"),d=d.replace("&","&amp;")));p={fill:p,"font-family":o,"font-size":l+"px",opacity:j};!0===m&&(p["font-weight"]="bold");p["text-anchor"]=n;return i.text(d,p)};a.polygon=function(A,z,y,x,u,w,v,s,r,o,i){isNaN(w)&&(w=0.01);isNaN(s)&&(s=u);var d=x,j=!1;"object"==typeof d&&1<d.length&&(j=!0,d=d[0]);void 0===v&&(v=d);u={fill:d,stroke:v,"fill-opacity":u,"stroke-width":w,"stroke-opacity":s};void 0!==i&&0<i&&(u["stroke-dasharray"]=i);i=a.dx;w=a.dy;A.handDrawn&&(y=a.makeHD(z,y,A.handDrawScatter),z=y[0],y=y[1]);v=Math.round;o&&(z[B]=a.roundTo(z[B],5),y[B]=a.roundTo(y[B],5),v=Number);s="M"+(v(z[0])+i)+","+(v(y[0])+w);for(var B=1;B<z.length;B++){o&&(z[B]=a.roundTo(z[B],5),y[B]=a.roundTo(y[B],5)),s+=" L"+(v(z[B])+i)+","+(v(y[B])+w)}A=A.path(s+" Z").attr(u);j&&A.gradient("linearGradient",x,r);return A};a.rect=function(C,B,A,z,w,y,x,v,u,s,j){if(isNaN(B)||isNaN(A)){return C.set()}isNaN(y)&&(y=0);void 0===u&&(u=0);void 0===s&&(s=270);isNaN(w)&&(w=0);var i=z,o=!1;"object"==typeof i&&(i=i[0],o=!0);void 0===x&&(x=i);void 0===v&&(v=w);B=Math.round(B);A=Math.round(A);var D=0,d=0;0>B&&(B=Math.abs(B),D=-B);0>A&&(A=Math.abs(A),d=-A);D+=a.dx;d+=a.dy;w={fill:i,stroke:x,"fill-opacity":w,"stroke-opacity":v};void 0!==j&&0<j&&(w["stroke-dasharray"]=j);C=C.rect(D,d,B,A,u,y).attr(w);o&&C.gradient("linearGradient",z,s);return C};a.bullet=function(A,z,y,x,u,w,v,s,r,o,i,d,j){var B;"circle"==z&&(z="round");switch(z){case"round":B=a.circle(A,y/2,x,u,w,v,s);break;case"square":B=a.polygon(A,[-y/2,y/2,y/2,-y/2],[y/2,y/2,-y/2,-y/2],x,u,w,v,s,o-180,void 0,j);break;case"rectangle":B=a.polygon(A,[-y,y,y,-y],[y/2,y/2,-y/2,-y/2],x,u,w,v,s,o-180,void 0,j);break;case"diamond":B=a.polygon(A,[-y/2,0,y/2,0],[0,-y/2,0,y/2],x,u,w,v,s);break;case"triangleUp":B=a.triangle(A,y,0,x,u,w,v,s);break;case"triangleDown":B=a.triangle(A,y,180,x,u,w,v,s);break;case"triangleLeft":B=a.triangle(A,y,270,x,u,w,v,s);break;case"triangleRight":B=a.triangle(A,y,90,x,u,w,v,s);break;case"bubble":B=a.circle(A,y/2,x,u,w,v,s,!0);break;case"line":B=a.line(A,[-y/2,y/2],[0,0],x,u,w,v,s);break;case"yError":B=A.set();B.push(a.line(A,[0,0],[-y/2,y/2],x,u,w));B.push(a.line(A,[-r,r],[-y/2,-y/2],x,u,w));B.push(a.line(A,[-r,r],[y/2,y/2],x,u,w));break;case"xError":B=A.set(),B.push(a.line(A,[-y/2,y/2],[0,0],x,u,w)),B.push(a.line(A,[-y/2,-y/2],[-r,r],x,u,w)),B.push(a.line(A,[y/2,y/2],[-r,r],x,u,w))}B&&B.pattern(i,NaN,d);return B};a.triangle=function(r,q,p,o,j,n,m,i){if(void 0===n||0===n){n=1}void 0===m&&(m="#000");void 0===i&&(i=0);o={fill:o,stroke:m,"fill-opacity":j,"stroke-width":n,"stroke-opacity":i};q/=2;var e;0===p&&(e=" M"+-q+","+q+" L0,"+-q+" L"+q+","+q+" Z");180==p&&(e=" M"+-q+","+-q+" L0,"+q+" L"+q+","+-q+" Z");90==p&&(e=" M"+-q+","+-q+" L"+q+",0 L"+-q+","+q+" Z");270==p&&(e=" M"+-q+",0 L"+q+","+q+" L"+q+","+-q+" Z");return r.path(e).attr(o)};a.line=function(v,u,t,s,o,r,q,n,j,i,d){if(v.handDrawn&&!d){return a.handDrawnLine(v,u,t,s,o,r,q,n,j,i,d)}r={fill:"none","stroke-width":r};void 0!==q&&0<q&&(r["stroke-dasharray"]=q);isNaN(o)||(r["stroke-opacity"]=o);s&&(r.stroke=s);s=Math.round;i&&(s=Number,u[0]=a.roundTo(u[0],5),t[0]=a.roundTo(t[0],5));i=a.dx;o=a.dy;q="M"+(s(u[0])+i)+","+(s(t[0])+o);for(n=1;n<u.length;n++){u[n]=a.roundTo(u[n],5),t[n]=a.roundTo(t[n],5),q+=" L"+(s(u[n])+i)+","+(s(t[n])+o)}if(a.VML){return v.path(q,void 0,!0).attr(r)}j&&(q+=" M0,0 L0,0");return v.path(q).attr(r)};a.makeHD=function(z,y,x){for(var w=[],t=[],v=1;v<z.length;v++){for(var u=Number(z[v-1]),s=Number(y[v-1]),r=Number(z[v]),o=Number(y[v]),i=Math.round(Math.sqrt(Math.pow(r-u,2)+Math.pow(o-s,2))/50)+1,r=(r-u)/i,o=(o-s)/i,e=0;e<=i;e++){var j=s+e*o+Math.random()*x;w.push(u+e*r+Math.random()*x);t.push(j)}}return[w,t]};a.handDrawnLine=function(C,B,A,z,w,y,x,v,u,s){var j,i=C.set();for(j=1;j<B.length;j++){for(var o=[B[j-1],B[j]],D=[A[j-1],A[j]],D=a.makeHD(o,D,C.handDrawScatter),o=D[0],D=D[1],d=1;d<o.length;d++){i.push(a.line(C,[o[d-1],o[d]],[D[d-1],D[d]],z,w,y+Math.random()*C.handDrawThickness-C.handDrawThickness/2,x,v,u,s,!0))}}return i};a.doNothing=function(b){return b};a.drop=function(F,E,D,B,x,A,y,v){var u=1/180*Math.PI,s=D-20,j=Math.sin(s*u)*E,i=Math.cos(s*u)*E,o=Math.sin((s+40)*u)*E,H=Math.cos((s+40)*u)*E,e=0.8*E,G=-E/3,C=E/3;0===D&&(G=-G,C=0);180==D&&(C=0);90==D&&(G=0);270==D&&(G=0,C=-C);D={fill:B,stroke:y,"stroke-width":A,"stroke-opacity":v,"fill-opacity":x};E="M"+j+","+i+" A"+E+","+E+",0,1,1,"+o+","+H+(" A"+e+","+e+",0,0,0,"+(Math.sin((s+20)*u)*E+C)+","+(Math.cos((s+20)*u)*E+G));E+=" A"+e+","+e+",0,0,0,"+j+","+i;return F.path(E,void 0,void 0,"1000,1000").attr(D)};a.wedge=function(aj,ai,ah,ag,ad,af,ae,ac,ab,Z,W,U,X,N){var S=Math.round;af=S(af);ae=S(ae);ac=S(ac);var K=S(ae/af*ac),o=a.VML,F=359.5+af/100;359.94<F&&(F=359.94);ad>=F&&(ad=F);var L=1/180*Math.PI,F=ai+Math.sin(ag*L)*ac,V=ah-Math.cos(ag*L)*K,v=ai+Math.sin(ag*L)*af,T=ah-Math.cos(ag*L)*ae,O=ai+Math.sin((ag+ad)*L)*af,R=ah-Math.cos((ag+ad)*L)*ae,E=ai+Math.sin((ag+ad)*L)*ac,L=ah-Math.cos((ag+ad)*L)*K,G={fill:a.adjustLuminosity(Z.fill,-0.2),"stroke-opacity":0,"fill-opacity":Z["fill-opacity"]},d=0;180<Math.abs(ad)&&(d=1);ag=aj.set();var j;o&&(F=S(10*F),v=S(10*v),O=S(10*O),E=S(10*E),V=S(10*V),T=S(10*T),R=S(10*R),L=S(10*L),ai=S(10*ai),ab=S(10*ab),ah=S(10*ah),af*=10,ae*=10,ac*=10,K*=10,1>Math.abs(ad)&&1>=Math.abs(O-v)&&1>=Math.abs(R-T)&&(j=!0));ad="";var i;U&&(G["fill-opacity"]=0,G["stroke-opacity"]=Z["stroke-opacity"]/2,G.stroke=Z.stroke);if(0<ab){i=" M"+F+","+(V+ab)+" L"+v+","+(T+ab);o?(j||(i+=" A"+(ai-af)+","+(ab+ah-ae)+","+(ai+af)+","+(ab+ah+ae)+","+v+","+(T+ab)+","+O+","+(R+ab)),i+=" L"+E+","+(L+ab),0<ac&&(j||(i+=" B"+(ai-ac)+","+(ab+ah-K)+","+(ai+ac)+","+(ab+ah+K)+","+E+","+(ab+L)+","+F+","+(ab+V)))):(i+=" A"+af+","+ae+",0,"+d+",1,"+O+","+(R+ab)+" L"+E+","+(L+ab),0<ac&&(i+=" A"+ac+","+K+",0,"+d+",0,"+F+","+(V+ab)));i+=" Z";var Y=ab;o&&(Y/=10);for(var s=0;s<Y;s+=10){var ak=aj.path(i,void 0,void 0,"1000,1000").attr(G);ag.push(ak);ak.translate(0,-s)}i=aj.path(" M"+F+","+V+" L"+F+","+(V+ab)+" L"+v+","+(T+ab)+" L"+v+","+T+" L"+F+","+V+" Z",void 0,void 0,"1000,1000").attr(G);ab=aj.path(" M"+O+","+R+" L"+O+","+(R+ab)+" L"+E+","+(L+ab)+" L"+E+","+L+" L"+O+","+R+" Z",void 0,void 0,"1000,1000").attr(G);ag.push(i);ag.push(ab)}o?(j||(ad=" A"+S(ai-af)+","+S(ah-ae)+","+S(ai+af)+","+S(ah+ae)+","+S(v)+","+S(T)+","+S(O)+","+S(R)),ae=" M"+S(F)+","+S(V)+" L"+S(v)+","+S(T)+ad+" L"+S(E)+","+S(L)):ae=" M"+F+","+V+" L"+v+","+T+(" A"+af+","+ae+",0,"+d+",1,"+O+","+R)+" L"+E+","+L;0<ac&&(o?j||(ae+=" B"+(ai-ac)+","+(ah-K)+","+(ai+ac)+","+(ah+K)+","+E+","+L+","+F+","+V):ae+=" A"+ac+","+K+",0,"+d+",0,"+F+","+V);aj.handDrawn&&(ac=a.line(aj,[F,v],[V,T],Z.stroke,Z.thickness*Math.random()*aj.handDrawThickness,Z["stroke-opacity"]),ag.push(ac));aj=aj.path(ae+" Z",void 0,void 0,"1000,1000").attr(Z);if(W){ac=[];for(K=0;K<W.length;K++){ac.push(a.adjustLuminosity(Z.fill,W[K]))}"radial"!=N||a.isModern||(ac=[]);0<ac.length&&aj.gradient(N+"Gradient",ac)}a.isModern&&"radial"==N&&aj.grad&&(aj.grad.setAttribute("gradientUnits","userSpaceOnUse"),aj.grad.setAttribute("r",af),aj.grad.setAttribute("cx",ai),aj.grad.setAttribute("cy",ah));aj.pattern(U,NaN,X);ag.wedge=aj;ag.push(aj);return ag};a.rgb2hex=function(b){return(b=b.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i))&&4===b.length?"#"+("0"+parseInt(b[1],10).toString(16)).slice(-2)+("0"+parseInt(b[2],10).toString(16)).slice(-2)+("0"+parseInt(b[3],10).toString(16)).slice(-2):""};a.adjustLuminosity=function(f,d){f&&-1!=f.indexOf("rgb")&&(f=a.rgb2hex(f));f=String(f).replace(/[^0-9a-f]/gi,"");6>f.length&&(f=String(f[0])+String(f[0])+String(f[1])+String(f[1])+String(f[2])+String(f[2]));d=d||0;var j="#",i,g;for(g=0;3>g;g++){i=parseInt(f.substr(2*g,2),16),i=Math.round(Math.min(Math.max(0,i+i*d),255)).toString(16),j+=("00"+i).substr(i.length)}return j}})();(function(){var a=window.AmCharts;a.Bezier=a.Class({construct:function(D,C,B,A,x,z,y,v,u,s,j){var i=D.chart,o=a.bezierX,F=a.bezierY;isNaN(i.bezierX)||(o=i.bezierX);isNaN(i.bezierY)||(F=i.bezierY);isNaN(o)&&(i.rotate?(o=20,F=4):(F=20,o=4));var d,E;"object"==typeof y&&1<y.length&&(E=!0,d=y,y=y[0]);"object"==typeof v&&(v=v[0]);0===v&&(y="none");z={fill:y,"fill-opacity":v,"stroke-width":z};void 0!==u&&0<u&&(z["stroke-dasharray"]=u);isNaN(x)||(z["stroke-opacity"]=x);A&&(z.stroke=A);A="M"+Math.round(C[0])+","+Math.round(B[0])+" ";x=[];for(u=0;u<C.length;u++){isNaN(C[u])||isNaN(B[u])?(A+=this.drawSegment(x,o,F),u<C.length-1&&(A+="L"+C[u+1]+","+B[u+1]+" "),x=[]):x.push({x:Number(C[u]),y:Number(B[u])})}A+=this.drawSegment(x,o,F);s?A+=s:a.VML||(A+="M0,0 L0,0");this.path=D.path(A).attr(z);this.node=this.path.node;E&&this.path.gradient("linearGradient",d,j)},drawSegment:function(t,s,r){var q="";if(2<t.length){for(var n=0;n<t.length-1;n++){var p=[],o=t[n-1],j=t[n],i=t[n+1],e=t[n+2];0===n?(p.push({x:j.x,y:j.y}),p.push({x:j.x,y:j.y}),p.push({x:i.x,y:i.y}),p.push({x:e.x,y:e.y})):n>=t.length-2?(p.push({x:o.x,y:o.y}),p.push({x:j.x,y:j.y}),p.push({x:i.x,y:i.y}),p.push({x:i.x,y:i.y})):(p.push({x:o.x,y:o.y}),p.push({x:j.x,y:j.y}),p.push({x:i.x,y:i.y}),p.push({x:e.x,y:e.y}));o=[];j=Math.round;o.push({x:j(p[1].x),y:j(p[1].y)});o.push({x:j((-p[0].x+s*p[1].x+p[2].x)/s),y:j((-p[0].y+r*p[1].y+p[2].y)/r)});o.push({x:j((p[1].x+s*p[2].x-p[3].x)/s),y:j((p[1].y+r*p[2].y-p[3].y)/r)});o.push({x:j(p[2].x),y:j(p[2].y)});q+="C"+o[1].x+","+o[1].y+","+o[2].x+","+o[2].y+","+o[3].x+","+o[3].y+" "}}else{1<t.length&&(q+="L"+t[1].x+","+t[1].y)}return q}})})();(function(){var a=window.AmCharts;a.AmDraw=a.Class({construct:function(f,d,h,g){a.SVG_NS="http://www.w3.org/2000/svg";a.SVG_XLINK="http://www.w3.org/1999/xlink";a.hasSVG=!!document.createElementNS&&!!document.createElementNS(a.SVG_NS,"svg").createSVGRect;1>d&&(d=10);1>h&&(h=10);this.div=f;this.width=d;this.height=h;this.rBin=document.createElement("div");a.hasSVG?(a.SVG=!0,d=this.createSvgElement("svg"),f.appendChild(d),this.container=d,this.addDefs(g),this.R=new a.SVGRenderer(this)):a.isIE&&a.VMLRenderer&&(a.VML=!0,a.vmlStyleSheet||(document.namespaces.add("amvml","urn:schemas-microsoft-com:vml"),31>document.styleSheets.length?(d=document.createStyleSheet(),d.addRule(".amvml","behavior:url(#default#VML); display:inline-block; antialias:true"),a.vmlStyleSheet=d):document.styleSheets[0].addRule(".amvml","behavior:url(#default#VML); display:inline-block; antialias:true")),this.container=f,this.R=new a.VMLRenderer(this,g),this.R.disableSelection(f))},createSvgElement:function(b){return document.createElementNS(a.SVG_NS,b)},circle:function(f,d,j,i){var g=new a.AmDObject("circle",this);g.attr({r:j,cx:f,cy:d});this.addToContainer(g.node,i);return g},ellipse:function(g,d,l,k,i){var j=new a.AmDObject("ellipse",this);j.attr({rx:l,ry:k,cx:g,cy:d});this.addToContainer(j.node,i);return j},setSize:function(d,c){0<d&&0<c&&(this.container.style.width=d+"px",this.container.style.height=c+"px")},rect:function(i,d,p,o,l,n,m){var j=new a.AmDObject("rect",this);a.VML&&(l=Math.round(100*l/Math.min(p,o)),p+=2*n,o+=2*n,j.bw=n,j.node.style.marginLeft=-n,j.node.style.marginTop=-n);1>p&&(p=1);1>o&&(o=1);j.attr({x:i,y:d,width:p,height:o,rx:l,ry:l,"stroke-width":n});this.addToContainer(j.node,m);return j},image:function(i,d,n,m,j,l){var k=new a.AmDObject("image",this);k.attr({x:d,y:n,width:m,height:j});this.R.path(k,i);this.addToContainer(k.node,l);return k},addToContainer:function(d,c){c||(c=this.container);c.appendChild(d)},text:function(e,d,f){return this.R.text(e,d,f)},path:function(f,d,j,i){var g=new a.AmDObject("path",this);i||(i="100,100");g.attr({cs:i});j?g.attr({dd:f}):g.attr({d:f});this.addToContainer(g.node,d);return g},set:function(b){return this.R.set(b)},remove:function(d){if(d){var c=this.rBin;c.appendChild(d);c.innerHTML=""}},renderFix:function(){var g=this.container,e=g.style;e.top="0px";e.left="0px";try{var l=g.getBoundingClientRect(),k=l.left-Math.round(l.left),i=l.top-Math.round(l.top);k&&(e.left=k+"px");i&&(e.top=i+"px")}catch(j){}},update:function(){this.R.update()},addDefs:function(f){if(a.hasSVG){var d=this.createSvgElement("desc"),h=this.container;h.setAttribute("version","1.1");h.style.position="absolute";this.setSize(this.width,this.height);if(f.accessibleTitle){var g=this.createSvgElement("text");h.appendChild(g);g.innerHTML=f.accessibleTitle;g.style.opacity=0}a.rtl&&(h.setAttribute("direction","rtl"),h.style.left="auto",h.style.right="0px");f&&(f.addCodeCredits&&d.appendChild(document.createTextNode("JavaScript chart by amCharts "+f.version)),h.appendChild(d),f.defs&&(d=this.createSvgElement("defs"),h.appendChild(d),a.parseDefs(f.defs,d),this.defs=d))}}})})();(function(){var a=window.AmCharts;a.AmDObject=a.Class({construct:function(d,c){this.D=c;this.R=c.R;this.node=this.R.create(this,d);this.y=this.x=0;this.scale=1},attr:function(b){this.R.attr(this,b);return this},getAttr:function(b){return this.node.getAttribute(b)},setAttr:function(d,c){this.R.setAttr(this,d,c);return this},clipRect:function(f,e,h,g){this.R.clipRect(this,f,e,h,g)},translate:function(f,e,h,g){g||(f=Math.round(f),e=Math.round(e));this.R.move(this,f,e,h);this.x=f;this.y=e;this.scale=h;this.angle&&this.rotate(this.angle)},rotate:function(d,c){this.R.rotate(this,d,c);this.angle=d},animate:function(g,d,l){for(var k in g){if(g.hasOwnProperty(k)){var i=k,j=g[k];l=a.getEffect(l);this.R.animate(this,i,j,d,l)}}},push:function(e){if(e){var d=this.node;d.appendChild(e.node);var f=e.clipPath;f&&d.appendChild(f);(e=e.grad)&&d.appendChild(e)}},text:function(b){this.R.setText(this,b)},remove:function(){this.stop();this.R.remove(this)},clear:function(){var b=this.node;if(b.hasChildNodes()){for(;1<=b.childNodes.length;){b.removeChild(b.firstChild)}}},hide:function(){this.setAttr("visibility","hidden")},show:function(){this.setAttr("visibility","visible")},getBBox:function(){return this.R.getBBox(this)},toFront:function(){var d=this.node;if(d){this.prevNextNode=d.nextSibling;var c=d.parentNode;c&&c.appendChild(d)}},toPrevious:function(){var b=this.node;b&&this.prevNextNode&&(b=b.parentNode)&&b.insertBefore(this.prevNextNode,null)},toBack:function(){var e=this.node;if(e){this.prevNextNode=e.nextSibling;var d=e.parentNode;if(d){var f=d.firstChild;f&&d.insertBefore(e,f)}}},mouseover:function(b){this.R.addListener(this,"mouseover",b);return this},mouseout:function(b){this.R.addListener(this,"mouseout",b);return this},click:function(b){this.R.addListener(this,"click",b);return this},dblclick:function(b){this.R.addListener(this,"dblclick",b);return this},mousedown:function(b){this.R.addListener(this,"mousedown",b);return this},mouseup:function(b){this.R.addListener(this,"mouseup",b);return this},touchmove:function(b){this.R.addListener(this,"touchmove",b);return this},touchstart:function(b){this.R.addListener(this,"touchstart",b);return this},touchend:function(b){this.R.addListener(this,"touchend",b);return this},keyup:function(b){this.R.addListener(this,"keyup",b);return this},focus:function(b){this.R.addListener(this,"focus",b);return this},blur:function(b){this.R.addListener(this,"blur",b);return this},contextmenu:function(b){this.node.addEventListener?this.node.addEventListener("contextmenu",b,!0):this.R.addListener(this,"contextmenu",b);return this},stop:function(){a.removeFromArray(this.R.animations,this.an_translate);a.removeFromArray(this.R.animations,this.an_y);a.removeFromArray(this.R.animations,this.an_x)},length:function(){return this.node.childNodes.length},gradient:function(e,d,f){this.R.gradient(this,e,d,f)},pattern:function(e,d,f){e&&this.R.pattern(this,e,d,f)}})})();(function(){var a=window.AmCharts;a.VMLRenderer=a.Class({construct:function(d,c){this.chart=c;this.D=d;this.cNames={circle:"oval",ellipse:"oval",rect:"roundrect",path:"shape"};this.styleMap={x:"left",y:"top",width:"width",height:"height","font-family":"fontFamily","font-size":"fontSize",visibility:"visibility"}},create:function(f,e){var j;if("group"==e){j=document.createElement("div"),f.type="div"}else{if("text"==e){j=document.createElement("div"),f.type="text"}else{if("image"==e){j=document.createElement("img"),f.type="image"}else{f.type="shape";f.shapeType=this.cNames[e];j=document.createElement("amvml:"+this.cNames[e]);var i=document.createElement("amvml:stroke");j.appendChild(i);f.stroke=i;var g=document.createElement("amvml:fill");j.appendChild(g);f.fill=g;g.className="amvml";i.className="amvml";j.className="amvml"}}}j.style.position="absolute";j.style.top=0;j.style.left=0;return j},path:function(d,c){d.node.setAttribute("src",c)},setAttr:function(i,d,p){if(void 0!==p){var o;8===document.documentMode&&(o=!0);var l=i.node,n=i.type,m=l.style;"r"==d&&(m.width=2*p,m.height=2*p);"oval"==i.shapeType&&("rx"==d&&(m.width=2*p),"ry"==d&&(m.height=2*p));"roundrect"==i.shapeType&&("width"!=d&&"height"!=d||--p);"cursor"==d&&(m.cursor=p);"cx"==d&&(m.left=p-a.removePx(m.width)/2);"cy"==d&&(m.top=p-a.removePx(m.height)/2);var j=this.styleMap[d];"width"==j&&0>p&&(p=0);void 0!==j&&(m[j]=p);"text"==n&&("text-anchor"==d&&(i.anchor=p,j=l.clientWidth,"end"==p&&(m.marginLeft=-j+"px"),"middle"==p&&(m.marginLeft=-(j/2)+"px",m.textAlign="center"),"start"==p&&(m.marginLeft="0px")),"fill"==d&&(m.color=p),"font-weight"==d&&(m.fontWeight=p));if(m=i.children){for(j=0;j<m.length;j++){m[j].setAttr(d,p)}}if("shape"==n){"cs"==d&&(l.style.width="100px",l.style.height="100px",l.setAttribute("coordsize",p));"d"==d&&l.setAttribute("path",this.svgPathToVml(p));"dd"==d&&l.setAttribute("path",p);n=i.stroke;i=i.fill;"stroke"==d&&(o?n.color=p:n.setAttribute("color",p));"stroke-width"==d&&(o?n.weight=p:n.setAttribute("weight",p));"stroke-opacity"==d&&(o?n.opacity=p:n.setAttribute("opacity",p));"stroke-dasharray"==d&&(m="solid",0<p&&3>p&&(m="dot"),3<=p&&6>=p&&(m="dash"),6<p&&(m="longdash"),o?n.dashstyle=m:n.setAttribute("dashstyle",m));if("fill-opacity"==d||"opacity"==d){0===p?o?i.on=!1:i.setAttribute("on",!1):o?i.opacity=p:i.setAttribute("opacity",p)}"fill"==d&&(o?i.color=p:i.setAttribute("color",p));"rx"==d&&(o?l.arcSize=p+"%":l.setAttribute("arcsize",p+"%"))}}},attr:function(e,d){for(var f in d){d.hasOwnProperty(f)&&this.setAttr(e,f,d[f])}},text:function(f,d,j){var i=new a.AmDObject("text",this.D),g=i.node;g.style.whiteSpace="pre";g.innerHTML=f;this.D.addToContainer(g,j);this.attr(i,d);return i},getBBox:function(b){return this.getBox(b.node)},getBox:function(v){var u=v.offsetLeft,t=v.offsetTop,s=v.offsetWidth,o=v.offsetHeight,r;if(v.hasChildNodes()){var q,n,j;for(j=0;j<v.childNodes.length;j++){r=this.getBox(v.childNodes[j]);var i=r.x;isNaN(i)||(isNaN(q)?q=i:i<q&&(q=i));var e=r.y;isNaN(e)||(isNaN(n)?n=e:e<n&&(n=e));i=r.width+i;isNaN(i)||(s=Math.max(s,i));r=r.height+e;isNaN(r)||(o=Math.max(o,r))}0>q&&(u+=q);0>n&&(t+=n)}return{x:u,y:t,width:s,height:o}},setText:function(e,d){var f=e.node;f&&(f.innerHTML=d);this.setAttr(e,"text-anchor",e.anchor)},addListener:function(e,d,f){e.node["on"+d]=f},move:function(f,d,j){var i=f.node,g=i.style;"text"==f.type&&(j-=a.removePx(g.fontSize)/2-1);"oval"==f.shapeType&&(d-=a.removePx(g.width)/2,j-=a.removePx(g.height)/2);f=f.bw;isNaN(f)||(d-=f,j-=f);isNaN(d)||isNaN(j)||(i.style.left=d+"px",i.style.top=j+"px")},svgPathToVml:function(A){var z=A.split(" ");A="";var y,x=Math.round,u;for(u=0;u<z.length;u++){var w=z[u],v=w.substring(0,1),w=w.substring(1),s=w.split(","),r=x(s[0])+","+x(s[1]);"M"==v&&(A+=" m "+r);"L"==v&&(A+=" l "+r);"Z"==v&&(A+=" x e");if("Q"==v){var o=y.length,i=y[o-1],e=s[0],j=s[1],r=s[2],B=s[3];y=x(y[o-2]/3+2/3*e);i=x(i/3+2/3*j);e=x(2/3*e+r/3);j=x(2/3*j+B/3);A+=" c "+y+","+i+","+e+","+j+","+r+","+B}"C"==v&&(A+=" c "+s[0]+","+s[1]+","+s[2]+","+s[3]+","+s[4]+","+s[5]);"A"==v&&(A+=" wa "+w);"B"==v&&(A+=" at "+w);y=s}return A},animate:function(i,e,p,o,l){var n=i.node,m=this.chart;i.animationFinished=!1;if("translate"==e){e=p.split(",");p=e[1];var j=n.offsetTop;m.animate(i,"left",n.offsetLeft,e[0],o,l,"px");m.animate(i,"top",j,p,o,l,"px")}},clipRect:function(f,e,j,i,g){f=f.node;0===e&&0===j?(f.style.width=i+"px",f.style.height=g+"px",f.style.overflow="hidden"):f.style.clip="rect("+j+"px "+(e+i)+"px "+(j+g)+"px "+e+"px)"},rotate:function(r,q,p){if(0!==Number(q)){var o=r.node;r=o.style;p||(p=this.getBGColor(o.parentNode));r.backgroundColor=p;r.paddingLeft=1;p=q*Math.PI/180;var j=Math.cos(p),n=Math.sin(p),m=a.removePx(r.left),i=a.removePx(r.top),d=o.offsetWidth,o=o.offsetHeight;q/=Math.abs(q);r.left=m+d/2-d/2*Math.cos(p)-q*o/2*Math.sin(p)+3;r.top=i-q*d/2*Math.sin(p)+q*o/2*Math.sin(p);r.cssText=r.cssText+"; filter:progid:DXImageTransform.Microsoft.Matrix(M11='"+j+"', M12='"+-n+"', M21='"+n+"', M22='"+j+"', sizingmethod='auto expand');"}},getBGColor:function(e){var d="#FFFFFF";if(e.style){var f=e.style.backgroundColor;""!==f?d=f:e.parentNode&&(d=this.getBGColor(e.parentNode))}return d},set:function(e){var d=new a.AmDObject("group",this.D);this.D.container.appendChild(d.node);if(e){var f;for(f=0;f<e.length;f++){d.push(e[f])}}return d},gradient:function(g,e,l,k){var i="";"radialGradient"==e&&(e="gradientradial",l.reverse());"linearGradient"==e&&(e="gradient");var j;for(j=0;j<l.length;j++){i+=Math.round(100*j/(l.length-1))+"% "+l[j],j<l.length-1&&(i+=",")}g=g.fill;90==k?k=0:270==k?k=180:180==k?k=90:0===k&&(k=270);8===document.documentMode?(g.type=e,g.angle=k):(g.setAttribute("type",e),g.setAttribute("angle",k));i&&(g.colors.value=i)},remove:function(b){b.clipPath&&this.D.remove(b.clipPath);this.D.remove(b.node)},disableSelection:function(b){b.onselectstart=function(){return !1};b.style.cursor="default"},pattern:function(f,d,j,i){j=f.node;f=f.fill;var g="none";d.color&&(g=d.color);j.fillColor=g;d=d.url;a.isAbsolute(d)||(d=i+d);8===document.documentMode?(f.type="tile",f.src=d):(f.setAttribute("type","tile"),f.setAttribute("src",d))},update:function(){}})})();(function(){var a=window.AmCharts;a.SVGRenderer=a.Class({construct:function(b){this.D=b;this.animations=[]},create:function(d,c){return document.createElementNS(a.SVG_NS,c)},attr:function(e,d){for(var f in d){d.hasOwnProperty(f)&&this.setAttr(e,f,d[f])}},setAttr:function(e,d,f){void 0!==f&&e.node.setAttribute(d,f)},animate:function(g,d,l,k,i){g.animationFinished=!1;var j=g.node;g["an_"+d]&&a.removeFromArray(this.animations,g["an_"+d]);"translate"==d?(j=(j=j.getAttribute("transform"))?String(j).substring(10,j.length-1):"0,0",j=j.split(", ").join(" "),j=j.split(" ").join(","),0===j&&(j="0,0")):j=Number(j.getAttribute(d));l={obj:g,frame:0,attribute:d,from:j,to:l,time:k,effect:i};this.animations.push(l);g["an_"+d]=l},update:function(){var t,s=this.animations;for(t=s.length-1;0<=t;t--){var r=s[t],q=r.time*a.updateRate,n=r.frame+1,p=r.obj,o=r.attribute,j,i,d;if(n<=q){r.frame++;if("translate"==o){j=r.from.split(",");o=Number(j[0]);j=Number(j[1]);isNaN(j)&&(j=0);i=r.to.split(",");d=Number(i[0]);i=Number(i[1]);d=0===d-o?d:Math.round(a[r.effect](0,n,o,d-o,q));r=0===i-j?i:Math.round(a[r.effect](0,n,j,i-j,q));o="transform";if(isNaN(d)||isNaN(r)){continue}r="translate("+d+","+r+")"}else{i=Number(r.from),j=Number(r.to),d=j-i,r=a[r.effect](0,n,i,d,q),isNaN(r)&&(r=j),0===d&&this.animations.splice(t,1)}this.setAttr(p,o,r)}else{"translate"==o?(i=r.to.split(","),d=Number(i[0]),i=Number(i[1]),p.translate(d,i)):(j=Number(r.to),this.setAttr(p,o,j)),p.animationFinished=!0,this.animations.splice(t,1)}}},getBBox:function(d){if(d=d.node){try{return d.getBBox()}catch(c){}}return{width:0,height:0,x:0,y:0}},path:function(d,c){d.node.setAttributeNS(a.SVG_XLINK,"xlink:href",c)},clipRect:function(i,d,p,o,l){var n=i.node,m=i.clipPath;m&&this.D.remove(m);var j=n.parentNode;j&&(n=document.createElementNS(a.SVG_NS,"clipPath"),m=a.getUniqueId(),n.setAttribute("id",m),this.D.rect(d,p,o,l,0,0,n),j.appendChild(n),d="#",a.baseHref&&!a.isIE&&(d=this.removeTarget(window.location.href)+d),this.setAttr(i,"clip-path","url("+d+m+")"),this.clipPathC++,i.clipPath=n)},text:function(i,d,n){var m=new a.AmDObject("text",this.D);i=String(i).split("\n");var j=a.removePx(d["font-size"]),l;for(l=0;l<i.length;l++){var k=this.create(null,"tspan");k.appendChild(document.createTextNode(i[l]));k.setAttribute("y",(j+2)*l+Math.round(j/2));k.setAttribute("x",0);m.node.appendChild(k)}m.node.setAttribute("y",Math.round(j/2));this.attr(m,d);this.D.addToContainer(m.node,n);return m},setText:function(e,d){var f=e.node;f&&(f.removeChild(f.firstChild),f.appendChild(document.createTextNode(d)))},move:function(f,e,h,g){isNaN(e)&&(e=0);isNaN(h)&&(h=0);e="translate("+e+","+h+")";g&&(e=e+" scale("+g+")");this.setAttr(f,"transform",e)},rotate:function(f,e){var h=f.node.getAttribute("transform"),g="rotate("+e+")";h&&(g=h+" "+g);this.setAttr(f,"transform",g)},set:function(e){var d=new a.AmDObject("g",this.D);this.D.container.appendChild(d.node);if(e){var f;for(f=0;f<e.length;f++){d.push(e[f])}}return d},addListener:function(e,d,f){e.node["on"+d]=f},gradient:function(t,s,r,q){var n=t.node,p=t.grad;p&&this.D.remove(p);s=document.createElementNS(a.SVG_NS,s);p=a.getUniqueId();s.setAttribute("id",p);if(!isNaN(q)){var o=0,j=0,i=0,d=0;90==q?i=100:270==q?d=100:180==q?o=100:0===q&&(j=100);s.setAttribute("x1",o+"%");s.setAttribute("x2",j+"%");s.setAttribute("y1",i+"%");s.setAttribute("y2",d+"%")}for(q=0;q<r.length;q++){o=document.createElementNS(a.SVG_NS,"stop"),j=100*q/(r.length-1),0===q&&(j=0),o.setAttribute("offset",j+"%"),o.setAttribute("stop-color",r[q]),s.appendChild(o)}n.parentNode.appendChild(s);r="#";a.baseHref&&!a.isIE&&(r=this.removeTarget(window.location.href)+r);n.setAttribute("fill","url("+r+p+")");t.grad=s},removeTarget:function(b){return b.split("#")[0]},pattern:function(v,u,t,s){var o=v.node;isNaN(t)&&(t=1);var r=v.patternNode;r&&this.D.remove(r);var r=document.createElementNS(a.SVG_NS,"pattern"),q=a.getUniqueId(),n=u;u.url&&(n=u.url);a.isAbsolute(n)||-1!=n.indexOf("data:image")||(n=s+n);s=Number(u.width);isNaN(s)&&(s=4);var j=Number(u.height);isNaN(j)&&(j=4);s/=t;j/=t;t=u.x;isNaN(t)&&(t=0);var i=-Math.random()*Number(u.randomX);isNaN(i)||(t=i);i=u.y;isNaN(i)&&(i=0);var d=-Math.random()*Number(u.randomY);isNaN(d)||(i=d);r.setAttribute("id",q);r.setAttribute("width",s);r.setAttribute("height",j);r.setAttribute("patternUnits","userSpaceOnUse");r.setAttribute("xlink:href",n);u.color&&(d=document.createElementNS(a.SVG_NS,"rect"),d.setAttributeNS(null,"height",s),d.setAttributeNS(null,"width",j),d.setAttributeNS(null,"fill",u.color),r.appendChild(d));this.D.image(n,0,0,s,j,r).translate(t,i);n="#";a.baseHref&&!a.isIE&&(n=this.removeTarget(window.location.href)+n);o.setAttribute("fill","url("+n+q+")");v.patternNode=r;o.parentNode.appendChild(r)},remove:function(b){b.clipPath&&this.D.remove(b.clipPath);b.grad&&this.D.remove(b.grad);b.patternNode&&this.D.remove(b.patternNode);this.D.remove(b.node)}})})();(function(){var a=window.AmCharts;a.AmLegend=a.Class({construct:function(b){this.enabled=!0;this.cname="AmLegend";this.createEvents("rollOverMarker","rollOverItem","rollOutMarker","rollOutItem","showItem","hideItem","clickMarker","clickLabel");this.position="bottom";this.borderColor=this.color="#000000";this.borderAlpha=0;this.markerLabelGap=5;this.verticalGap=10;this.align="left";this.horizontalGap=0;this.spacing=10;this.markerDisabledColor="#AAB3B3";this.markerType="square";this.markerSize=16;this.markerBorderThickness=this.markerBorderAlpha=1;this.marginBottom=this.marginTop=0;this.marginLeft=this.marginRight=20;this.autoMargins=!0;this.valueWidth=50;this.switchable=!0;this.switchType="x";this.switchColor="#FFFFFF";this.rollOverColor="#CC0000";this.reversedOrder=!1;this.labelText="[[title]]";this.valueText="[[value]]";this.accessibleLabel="[[title]]";this.useMarkerColorForLabels=!1;this.rollOverGraphAlpha=1;this.textClickEnabled=!1;this.equalWidths=!0;this.backgroundColor="#FFFFFF";this.backgroundAlpha=0;this.useGraphSettings=!1;this.showEntries=!0;this.labelDx=0;a.applyTheme(this,b,this.cname)},setData:function(b){this.legendData=b;this.invalidateSize()},invalidateSize:function(){this.destroy();this.entries=[];this.valueLabels=[];var b=this.legendData;this.enabled&&(a.ifArray(b)||a.ifArray(this.data))&&this.drawLegend()},drawLegend:function(){var i=this.chart,d=this.position,p=this.width,o=i.divRealWidth,l=i.divRealHeight,n=this.div,m=this.legendData;this.data&&(m=this.combineLegend?this.legendData.concat(this.data):this.data);isNaN(this.fontSize)&&(this.fontSize=i.fontSize);this.maxColumnsReal=this.maxColumns;if("right"==d||"left"==d){this.maxColumnsReal=1,this.autoMargins&&(this.marginLeft=this.marginRight=10)}else{if(this.autoMargins){this.marginRight=i.marginRight;this.marginLeft=i.marginLeft;var j=i.autoMarginOffset;"bottom"==d?(this.marginBottom=j,this.marginTop=0):(this.marginTop=j,this.marginBottom=0)}}p=void 0!==p?a.toCoordinate(p,o):"right"!=d&&"left"!=d?i.realWidth:0<this.ieW?this.ieW:i.realWidth;"outside"==d?(p=n.offsetWidth,l=n.offsetHeight,n.clientHeight&&(p=n.clientWidth,l=n.clientHeight)):(isNaN(p)||(n.style.width=p+"px"),n.className="amChartsLegend "+i.classNamePrefix+"-legend-div");this.divWidth=p;(d=this.container)?(d.container.innerHTML="",n.appendChild(d.container),d.width=p,d.height=l,d.setSize(p,l),d.addDefs(i)):d=new a.AmDraw(n,p,l,i);this.container=d;this.lx=0;this.ly=8;l=this.markerSize;l>this.fontSize&&(this.ly=l/2-1);0<l&&(this.lx+=l+this.markerLabelGap);this.titleWidth=0;if(l=this.title){l=a.text(this.container,l,this.color,i.fontFamily,this.fontSize,"start",!0),a.setCN(i,l,"legend-title"),l.translate(this.marginLeft,this.marginTop+this.verticalGap+this.ly+1),i=l.getBBox(),this.titleWidth=i.width+15,this.titleHeight=i.height+6}this.index=this.maxLabelWidth=0;if(this.showEntries){for(i=0;i<m.length;i++){this.createEntry(m[i])}for(i=this.index=0;i<m.length;i++){this.createValue(m[i])}}this.arrangeEntries();this.updateValues()},arrangeEntries:function(){var Z=this.position,Y=this.marginLeft+this.titleWidth,X=this.marginRight,W=this.marginTop,T=this.marginBottom,V=this.horizontalGap,U=this.div,S=this.divWidth,R=this.maxColumnsReal,Q=this.verticalGap,O=this.spacing,M=S-X-Y,P=0,F=0,K=this.container;this.set&&this.set.remove();var v=K.set();this.set=v;var d=K.set();v.push(d);var o=this.entries,E,N;for(N=0;N<o.length;N++){E=o[N].getBBox();var i=E.width;i>P&&(P=i);E=E.height;E>F&&(F=E)}var i=F=0,L=V,G=0,J=0;for(N=0;N<o.length;N++){var j=o[N];this.reversedOrder&&(j=o[o.length-N-1]);E=j.getBBox();var s;this.equalWidths?s=i*(P+O+this.markerLabelGap):(s=L,L=L+E.width+V+O);s+E.width>M&&0<N&&0!==i&&(F++,s=i=0,L=s+E.width+V+O,G=G+J+Q,J=0);E.height>J&&(J=E.height);j.translate(s,G);i++;!isNaN(R)&&i>=R&&(i=0,F++,G=G+J+Q,L=V,J=0);d.push(j)}E=d.getBBox();R=E.height+2*Q-1;"left"==Z||"right"==Z?(O=E.width+2*V,S=O+Y+X,U.style.width=S+"px",this.ieW=S):O=S-Y-X-1;X=a.polygon(this.container,[0,O,O,0],[0,0,R,R],this.backgroundColor,this.backgroundAlpha,1,this.borderColor,this.borderAlpha);a.setCN(this.chart,X,"legend-bg");v.push(X);v.translate(Y,W);X.toBack();Y=V;if("top"==Z||"bottom"==Z||"absolute"==Z||"outside"==Z){"center"==this.align?Y=V+(O-E.width)/2:"right"==this.align&&(Y=V+O-E.width)}d.translate(Y,Q+1);this.titleHeight>R&&(R=this.titleHeight);W=R+W+T+1;0>W&&(W=0);"absolute"!=Z&&"outside"!=Z&&W>this.chart.divRealHeight&&(U.style.top="0px");U.style.height=Math.round(W)+"px";K.setSize(this.divWidth,W)},createEntry:function(L){if(!1!==L.visibleInLegend&&!L.hideFromLegend){var K=this,J=K.chart,I=K.useGraphSettings,F=L.markerType;F&&(I=!1);L.legendEntryWidth=K.markerSize;F||(F=K.markerType);var H=L.color,G=L.alpha;L.legendKeyColor&&(H=L.legendKeyColor());L.legendKeyAlpha&&(G=L.legendKeyAlpha());var E;!0===L.hidden&&(E=H=K.markerDisabledColor);var D=L.pattern,C,A=L.customMarker;A||(A=K.customMarker);var y=K.container,B=K.markerSize,s=0,v=0,j=B/2;if(I){I=L.type;K.switchType=void 0;if("line"==I||"step"==I||"smoothedLine"==I||"ohlc"==I){C=y.set(),L.hidden||(H=L.lineColorR,E=L.bulletBorderColorR),s=a.line(y,[0,2*B],[B/2,B/2],H,L.lineAlpha,L.lineThickness,L.dashLength),a.setCN(J,s,"graph-stroke"),C.push(s),L.bullet&&(L.hidden||(H=L.bulletColorR),s=a.bullet(y,L.bullet,L.bulletSize,H,L.bulletAlpha,L.bulletBorderThickness,E,L.bulletBorderAlpha))&&(a.setCN(J,s,"graph-bullet"),s.translate(B+1,B/2),C.push(s)),j=0,s=B,v=B/3}else{L.getGradRotation&&(C=L.getGradRotation(),0===C&&(C=180));s=L.fillColorsR;!0===L.hidden&&(s=H);if(C=K.createMarker("rectangle",s,L.fillAlphas,L.lineThickness,H,L.lineAlpha,C,D,L.dashLength)){j=B,C.translate(j,B/2)}s=B}a.setCN(J,C,"graph-"+I);a.setCN(J,C,"graph-"+L.id)}else{if(A){C=y.image(A,0,0,B,B)}else{var d;isNaN(K.gradientRotation)||(d=180+K.gradientRotation);(C=K.createMarker(F,H,G,void 0,void 0,void 0,d,D))&&C.translate(B/2,B/2)}}a.setCN(J,C,"legend-marker");K.addListeners(C,L);y=y.set([C]);K.switchable&&L.switchable&&y.setAttr("cursor","pointer");void 0!==L.id&&a.setCN(J,y,"legend-item-"+L.id);a.setCN(J,y,L.className,!0);E=K.switchType;var i;E&&"none"!=E&&0<B&&("x"==E?(i=K.createX(),i.translate(B/2,B/2)):i=K.createV(),i.dItem=L,!0!==L.hidden?"x"==E?i.hide():i.show():"x"!=E&&i.hide(),K.switchable||i.hide(),K.addListeners(i,L),L.legendSwitch=i,y.push(i),a.setCN(J,i,"legend-switch"));E=K.color;L.showBalloon&&K.textClickEnabled&&void 0!==K.selectedColor&&(E=K.selectedColor);K.useMarkerColorForLabels&&!D&&(E=H);!0===L.hidden&&(E=K.markerDisabledColor);H=a.massReplace(K.labelText,{"[[title]]":L.title});void 0!==K.tabIndex&&(y.setAttr("tabindex",K.tabIndex),y.setAttr("role","menuitem"),y.keyup(function(b){13==b.keyCode&&K.clickMarker(L,b)}));J.accessible&&K.accessibleLabel&&(D=a.massReplace(K.accessibleLabel,{"[[title]]":L.title}),J.makeAccessible(y,D));D=K.fontSize;C&&(B<=D&&(B=B/2+K.ly-D/2+(D+2-B)/2-v,C.translate(j,B),i&&i.translate(i.x,B)),L.legendEntryWidth=C.getBBox().width);var o;H&&(H=a.fixBrakes(H),L.legendTextReal=H,o=K.labelWidth,o=isNaN(o)?a.text(K.container,H,E,J.fontFamily,D,"start"):a.wrappedText(K.container,H,E,J.fontFamily,D,"start",!1,o,0),a.setCN(J,o,"legend-label"),o.translate(K.lx+s,K.ly),y.push(o),K.labelDx=s,J=o.getBBox().width,K.maxLabelWidth<J&&(K.maxLabelWidth=J));K.entries[K.index]=y;L.legendEntry=K.entries[K.index];L.legendMarker=C;L.legendLabel=o;K.index++}},addListeners:function(e,d){var f=this;e&&e.mouseover(function(b){f.rollOverMarker(d,b)}).mouseout(function(b){f.rollOutMarker(d,b)}).click(function(b){f.clickMarker(d,b)})},rollOverMarker:function(d,c){this.switchable&&this.dispatch("rollOverMarker",d,c);this.dispatch("rollOverItem",d,c)},rollOutMarker:function(d,c){this.switchable&&this.dispatch("rollOutMarker",d,c);this.dispatch("rollOutItem",d,c)},clickMarker:function(d,c){this.switchable&&(!0===d.hidden?this.dispatch("showItem",d,c):this.dispatch("hideItem",d,c));this.dispatch("clickMarker",d,c)},rollOverLabel:function(d,c){d.hidden||this.textClickEnabled&&d.legendLabel&&d.legendLabel.attr({fill:this.rollOverColor});this.dispatch("rollOverItem",d,c)},rollOutLabel:function(e,d){if(!e.hidden&&this.textClickEnabled&&e.legendLabel){var f=this.color;void 0!==this.selectedColor&&e.showBalloon&&(f=this.selectedColor);this.useMarkerColorForLabels&&(f=e.lineColor,void 0===f&&(f=e.color));e.legendLabel.attr({fill:f})}this.dispatch("rollOutItem",e,d)},clickLabel:function(d,c){this.textClickEnabled?d.hidden||this.dispatch("clickLabel",d,c):this.switchable&&(!0===d.hidden?this.dispatch("showItem",d,c):this.dispatch("hideItem",d,c))},dispatch:function(e,d,f){e={type:e,dataItem:d,target:this,event:f,chart:this.chart};this.chart&&this.chart.handleLegendEvent(e);this.fire(e)},createValue:function(r){var q=this,p=q.fontSize,o=q.chart;if(!1!==r.visibleInLegend&&!r.hideFromLegend){var j=q.maxLabelWidth;q.forceWidth&&(j=q.labelWidth);q.equalWidths||(q.valueAlign="left");"left"==q.valueAlign&&r.legendLabel&&(j=r.legendLabel.getBBox().width);var n=j;if(q.valueText&&0<q.valueWidth){var m=q.color;q.useMarkerColorForValues&&(m=r.color,r.legendKeyColor&&(m=r.legendKeyColor()));!0===r.hidden&&(m=q.markerDisabledColor);var i=q.valueText,j=j+q.lx+q.labelDx+q.markerLabelGap+q.valueWidth,d="end";"left"==q.valueAlign&&(j-=q.valueWidth,d="start");m=a.text(q.container,i,m,q.chart.fontFamily,p,d);a.setCN(o,m,"legend-value");m.translate(j,q.ly);q.entries[q.index].push(m);n+=q.valueWidth+2*q.markerLabelGap;m.dItem=r;q.valueLabels.push(m)}q.index++;o=q.markerSize;o<p+7&&(o=p+7,a.VML&&(o+=3));p=q.container.rect(r.legendEntryWidth,0,n,o,0,0).attr({stroke:"none",fill:"#fff","fill-opacity":0.005});p.dItem=r;q.entries[q.index-1].push(p);p.mouseover(function(b){q.rollOverLabel(r,b)}).mouseout(function(b){q.rollOutLabel(r,b)}).click(function(b){q.clickLabel(r,b)})}},createV:function(){var b=this.markerSize;return a.polygon(this.container,[b/5,b/2,b-b/5,b/2],[b/3,b-b/5,b/5,b/1.7],this.switchColor)},createX:function(){var f=(this.markerSize-4)/2,d={stroke:this.switchColor,"stroke-width":3},h=this.container,g=a.line(h,[-f,f],[-f,f]).attr(d),f=a.line(h,[-f,f],[f,-f]).attr(d);return this.container.set([g,f])},createMarker:function(v,u,t,s,o,r,q,n,j){var i=this.markerSize,d=this.container;o||(o=this.markerBorderColor);o||(o=u);isNaN(s)&&(s=this.markerBorderThickness);isNaN(r)&&(r=this.markerBorderAlpha);return a.bullet(d,v,i,u,t,s,o,r,i,q,n,this.chart.path,j)},validateNow:function(){this.invalidateSize()},updateValues:function(){var v=this.valueLabels,u=this.chart,t,s=this.data;if(v){for(t=0;t<v.length;t++){var o=v[t],r=o.dItem;r.periodDataItem=void 0;r.periodPercentDataItem=void 0;var q=" ";if(s){r.value?o.text(r.value):o.text("")}else{var n=null;if(void 0!==r.type){var n=r.currentDataItem,j=this.periodValueText;r.legendPeriodValueText&&(j=r.legendPeriodValueText);r.legendPeriodValueTextR&&(j=r.legendPeriodValueTextR);n?(q=this.valueText,r.legendValueText&&(q=r.legendValueText),r.legendValueTextR&&(q=r.legendValueTextR),q=u.formatString(q,n)):j&&u.formatPeriodString&&(j=a.massReplace(j,{"[[title]]":r.title}),q=u.formatPeriodString(j,r))}else{q=u.formatString(this.valueText,r)}j=r;n&&(j=n);var i=this.valueFunction;i&&(q=i(j,q,u.periodDataItem));var d;this.useMarkerColorForLabels&&!n&&r.lastDataItem&&(n=r.lastDataItem);n?d=u.getBalloonColor(r,n):r.legendKeyColor&&(d=r.legendKeyColor());r.legendColorFunction&&(d=r.legendColorFunction(j,q,r.periodDataItem,r.periodPercentDataItem));o.text(q);if(!r.pattern&&(this.useMarkerColorForValues&&o.setAttr("fill",d),this.useMarkerColorForLabels)){if(o=r.legendMarker){o.setAttr("fill",d),o.setAttr("stroke",d)}(r=r.legendLabel)&&r.setAttr("fill",d)}}}}},renderFix:function(){if(!a.VML&&this.enabled){var b=this.container;b&&b.renderFix()}},destroy:function(){this.div.innerHTML="";a.remove(this.set)}})})();(function(){var a=window.AmCharts;a.formatMilliseconds=function(f,e){if(-1!=f.indexOf("fff")){var h=e.getMilliseconds(),g=String(h);10>h&&(g="00"+h);10<=h&&100>h&&(g="0"+h);f=f.replace(/fff/g,g)}return f};a.extractPeriod=function(e){var d=a.stripNumbers(e),f=1;d!=e&&(f=Number(e.slice(0,e.indexOf(d))));return{period:d,count:f}};a.getDate=function(e,d,f){return e instanceof Date?a.newDate(e,f):d&&isNaN(e)?a.stringToDate(e,d):new Date(e)};a.daysInMonth=function(b){return(new Date(b.getYear(),b.getMonth()+1,0)).getDate()};a.newDate=function(d,c){return c&&-1==c.indexOf("fff")?new Date(d):new Date(d.getFullYear(),d.getMonth(),d.getDate(),d.getHours(),d.getMinutes(),d.getSeconds(),d.getMilliseconds())};a.resetDateToMin=function(v,u,t,s){void 0===s&&(s=1);var o,r,q,n,j,i,d;a.useUTC?(o=v.getUTCFullYear(),r=v.getUTCMonth(),q=v.getUTCDate(),n=v.getUTCHours(),j=v.getUTCMinutes(),i=v.getUTCSeconds(),d=v.getUTCMilliseconds(),v=v.getUTCDay()):(o=v.getFullYear(),r=v.getMonth(),q=v.getDate(),n=v.getHours(),j=v.getMinutes(),i=v.getSeconds(),d=v.getMilliseconds(),v=v.getDay());switch(u){case"YYYY":o=Math.floor(o/t)*t;r=0;q=1;d=i=j=n=0;break;case"MM":r=Math.floor(r/t)*t;q=1;d=i=j=n=0;break;case"WW":q=v>=s?q-v+s:q-(7+v)+s;d=i=j=n=0;break;case"DD":d=i=j=n=0;break;case"hh":n=Math.floor(n/t)*t;d=i=j=0;break;case"mm":j=Math.floor(j/t)*t;d=i=0;break;case"ss":i=Math.floor(i/t)*t;d=0;break;case"fff":d=Math.floor(d/t)*t}a.useUTC?(v=new Date,v.setUTCFullYear(o,r,q),v.setUTCHours(n,j,i,d)):v=new Date(o,r,q,n,j,i,d);return v};a.getPeriodDuration=function(e,d){void 0===d&&(d=1);var f;switch(e){case"YYYY":f=31622400000;break;case"MM":f=2678400000;break;case"WW":f=604800000;break;case"DD":f=86400000;break;case"hh":f=3600000;break;case"mm":f=60000;break;case"ss":f=1000;break;case"fff":f=1}return f*d};a.intervals={s:{nextInterval:"ss",contains:1000},ss:{nextInterval:"mm",contains:60,count:0},mm:{nextInterval:"hh",contains:60,count:1},hh:{nextInterval:"DD",contains:24,count:2},DD:{nextInterval:"",contains:Infinity,count:3}};a.getMaxInterval=function(e,d){var f=a.intervals;return e>=f[d].contains?(e=Math.round(e/f[d].contains),d=f[d].nextInterval,a.getMaxInterval(e,d)):"ss"==d?f[d].nextInterval:d};a.dayNames="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" ");a.shortDayNames="Sun Mon Tue Wed Thu Fri Sat".split(" ");a.monthNames="January February March April May June July August September October November December".split(" ");a.shortMonthNames="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" ");a.getWeekNumber=function(d){d=new Date(d);d.setHours(0,0,0);d.setDate(d.getDate()+4-(d.getDay()||7));var c=new Date(d.getFullYear(),0,1);return Math.ceil(((d-c)/86400000+1)/7)};a.stringToDate=function(t,s){var r={},q=[{pattern:"YYYY",period:"year"},{pattern:"YY",period:"year"},{pattern:"MM",period:"month"},{pattern:"M",period:"month"},{pattern:"DD",period:"date"},{pattern:"D",period:"date"},{pattern:"JJ",period:"hours"},{pattern:"J",period:"hours"},{pattern:"HH",period:"hours"},{pattern:"H",period:"hours"},{pattern:"KK",period:"hours"},{pattern:"K",period:"hours"},{pattern:"LL",period:"hours"},{pattern:"L",period:"hours"},{pattern:"NN",period:"minutes"},{pattern:"N",period:"minutes"},{pattern:"SS",period:"seconds"},{pattern:"S",period:"seconds"},{pattern:"QQQ",period:"milliseconds"},{pattern:"QQ",period:"milliseconds"},{pattern:"Q",period:"milliseconds"}],n=!0,p=s.indexOf("AA");-1!=p&&(t.substr(p,2),"pm"==t.toLowerCase&&(n=!1));var p=s,o,j,i;for(i=0;i<q.length;i++){j=q[i].period,r[j]=0,"date"==j&&(r[j]=1)}for(i=0;i<q.length;i++){if(o=q[i].pattern,j=q[i].period,-1!=s.indexOf(o)){var d=a.getFromDateString(o,t,p);s=s.replace(o,"");if("KK"==o||"K"==o||"LL"==o||"L"==o){n||(d+=12)}r[j]=d}}a.useUTC?(q=new Date,q.setUTCFullYear(r.year,r.month,r.date),q.setUTCHours(r.hours,r.minutes,r.seconds,r.milliseconds)):q=new Date(r.year,r.month,r.date,r.hours,r.minutes,r.seconds,r.milliseconds);return q};a.getFromDateString=function(e,d,f){if(void 0!==d){return f=f.indexOf(e),d=String(d),d=d.substr(f,e.length),"0"==d.charAt(0)&&(d=d.substr(1,d.length-1)),d=Number(d),isNaN(d)&&(d=0),-1!=e.indexOf("M")&&d--,d}};a.formatDate=function(D,C,B){B||(B=a);var A,x,z,y,v,u,s,j,i=a.getWeekNumber(D);a.useUTC?(A=D.getUTCFullYear(),x=D.getUTCMonth(),z=D.getUTCDate(),y=D.getUTCDay(),v=D.getUTCHours(),u=D.getUTCMinutes(),s=D.getUTCSeconds(),j=D.getUTCMilliseconds()):(A=D.getFullYear(),x=D.getMonth(),z=D.getDate(),y=D.getDay(),v=D.getHours(),u=D.getMinutes(),s=D.getSeconds(),j=D.getMilliseconds());var o=String(A).substr(2,2),F="0"+y;C=C.replace(/W/g,i);i=v;24==i&&(i=0);var d=i;10>d&&(d="0"+d);C=C.replace(/JJ/g,d);C=C.replace(/J/g,i);d=v;0===d&&(d=24,-1!=C.indexOf("H")&&(z--,0===z&&(A=new Date(D),A.setDate(A.getDate()-1),x=A.getMonth(),z=A.getDate(),A=A.getFullYear())));D=x+1;9>x&&(D="0"+D);i=z;10>z&&(i="0"+z);var E=d;10>E&&(E="0"+E);C=C.replace(/HH/g,E);C=C.replace(/H/g,d);d=v;11<d&&(d-=12);E=d;10>E&&(E="0"+E);C=C.replace(/KK/g,E);C=C.replace(/K/g,d);d=v;0===d&&(d=12);12<d&&(d-=12);E=d;10>E&&(E="0"+E);C=C.replace(/LL/g,E);C=C.replace(/L/g,d);d=u;10>d&&(d="0"+d);C=C.replace(/NN/g,d);C=C.replace(/N/g,u);u=s;10>u&&(u="0"+u);C=C.replace(/SS/g,u);C=C.replace(/S/g,s);s=j;10>s?s="00"+s:100>s&&(s="0"+s);u=j;10>u&&(u="00"+u);C=C.replace(/A/g,"@A@");C=C.replace(/QQQ/g,s);C=C.replace(/QQ/g,u);C=C.replace(/Q/g,j);C=C.replace(/YYYY/g,"@IIII@");C=C.replace(/YY/g,"@II@");C=C.replace(/MMMM/g,"@XXXX@");C=C.replace(/MMM/g,"@XXX@");C=C.replace(/MM/g,"@XX@");C=C.replace(/M/g,"@X@");C=C.replace(/DD/g,"@RR@");C=C.replace(/D/g,"@R@");C=C.replace(/EEEE/g,"@PPPP@");C=C.replace(/EEE/g,"@PPP@");C=C.replace(/EE/g,"@PP@");C=C.replace(/E/g,"@P@");C=C.replace(/@IIII@/g,A);C=C.replace(/@II@/g,o);C=C.replace(/@XXXX@/g,B.monthNames[x]);C=C.replace(/@XXX@/g,B.shortMonthNames[x]);C=C.replace(/@XX@/g,D);C=C.replace(/@X@/g,x+1);C=C.replace(/@RR@/g,i);C=C.replace(/@R@/g,z);C=C.replace(/@PPPP@/g,B.dayNames[y]);C=C.replace(/@PPP@/g,B.shortDayNames[y]);C=C.replace(/@PP@/g,F);C=C.replace(/@P@/g,y);return C=12>v?C.replace(/@A@/g,B.amString):C.replace(/@A@/g,B.pmString)};a.changeDate=function(g,d,l,k,i){if(a.useUTC){return a.changeUTCDate(g,d,l,k,i)}var j=-1;void 0===k&&(k=!0);void 0===i&&(i=!1);!0===k&&(j=1);switch(d){case"YYYY":g.setFullYear(g.getFullYear()+l*j);k||i||g.setDate(g.getDate()+1);break;case"MM":d=g.getMonth();g.setMonth(g.getMonth()+l*j);g.getMonth()>d+l*j&&g.setDate(g.getDate()-1);k||i||g.setDate(g.getDate()+1);break;case"DD":g.setDate(g.getDate()+l*j);break;case"WW":g.setDate(g.getDate()+l*j*7);break;case"hh":g.setHours(g.getHours()+l*j);break;case"mm":g.setMinutes(g.getMinutes()+l*j);break;case"ss":g.setSeconds(g.getSeconds()+l*j);break;case"fff":g.setMilliseconds(g.getMilliseconds()+l*j)}return g};a.changeUTCDate=function(g,e,l,k,i){var j=-1;void 0===k&&(k=!0);void 0===i&&(i=!1);!0===k&&(j=1);switch(e){case"YYYY":g.setUTCFullYear(g.getUTCFullYear()+l*j);k||i||g.setUTCDate(g.getUTCDate()+1);break;case"MM":e=g.getUTCMonth();g.setUTCMonth(g.getUTCMonth()+l*j);g.getUTCMonth()>e+l*j&&g.setUTCDate(g.getUTCDate()-1);k||i||g.setUTCDate(g.getUTCDate()+1);break;case"DD":g.setUTCDate(g.getUTCDate()+l*j);break;case"WW":g.setUTCDate(g.getUTCDate()+l*j*7);break;case"hh":g.setUTCHours(g.getUTCHours()+l*j);break;case"mm":g.setUTCMinutes(g.getUTCMinutes()+l*j);break;case"ss":g.setUTCSeconds(g.getUTCSeconds()+l*j);break;case"fff":g.setUTCMilliseconds(g.getUTCMilliseconds()+l*j)}return g}})();
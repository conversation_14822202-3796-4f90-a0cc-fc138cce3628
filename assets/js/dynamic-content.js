/**
 * Dynamic Content Loader
 * Handles AJAX content loading for the dashboard
 */

class DynamicContentLoader {
    constructor() {
        this.contentContainer = '.pcoded-inner-content';
        this.loadingClass = 'content-loading';
        this.currentUrl = '';
        this.baseUrl = WGI_APP_BASE_URL || '';
        
        this.init();
    }

    init() {
        // Initialize event listeners
        this.bindNavigationEvents();
        this.bindPopStateEvent();
        
        // Set initial state
        this.currentUrl = window.location.pathname;
        
        console.log('Dynamic Content Loader initialized');
    }

    /**
     * Bind click events to navigation links
     */
    bindNavigationEvents() {
        // Handle sidebar navigation
        $(document).on('click', 'a[data-ajax-load]', (e) => {
            e.preventDefault();
            const url = $(e.currentTarget).attr('href');
            const title = $(e.currentTarget).text().trim();
            this.loadContent(url, title);
        });

        // Handle any link with class 'ajax-link'
        $(document).on('click', 'a.ajax-link', (e) => {
            e.preventDefault();
            const url = $(e.currentTarget).attr('href');
            const title = $(e.currentTarget).attr('data-title') || $(e.currentTarget).text().trim();
            this.loadContent(url, title);
        });
    }

    /**
     * Handle browser back/forward buttons
     */
    bindPopStateEvent() {
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.url) {
                this.loadContent(e.state.url, e.state.title, false);
            }
        });
    }

    /**
     * Load content via AJAX
     * @param {string} url - The URL to load
     * @param {string} title - Page title
     * @param {boolean} pushState - Whether to push state to history
     */
    loadContent(url, title = '', pushState = true) {
        // Show loading state
        this.showLoading();

        // Extract controller and method from URL
        const urlParts = this.parseUrl(url);
        
        $.ajax({
            url: this.baseUrl + 'ajax_content/load_content/' + urlParts.controller + '/' + urlParts.method,
            type: 'GET',
            dataType: 'json',
            timeout: 30000,
            success: (response) => {
                this.handleSuccess(response, url, title, pushState);
            },
            error: (xhr, status, error) => {
                this.handleError(xhr, status, error);
            },
            complete: () => {
                this.hideLoading();
            }
        });
    }

    /**
     * Load content for specific module
     * @param {string} controller - Controller name
     * @param {string} method - Method name
     * @param {string} title - Page title
     */
    loadModule(controller, method = 'index', title = '') {
        const url = this.baseUrl + controller + '/' + method;
        this.loadContent(url, title);
    }

    /**
     * Parse URL to extract controller and method
     * @param {string} url - URL to parse
     * @returns {object} - Object with controller and method
     */
    parseUrl(url) {
        // Remove base URL if present
        let cleanUrl = url.replace(this.baseUrl, '');
        
        // Remove leading slash
        cleanUrl = cleanUrl.replace(/^\//, '');
        
        // Remove index.php if present
        cleanUrl = cleanUrl.replace(/^index\.php\//, '');
        
        // Split into parts
        const parts = cleanUrl.split('/');
        
        let controller = parts[0] || 'dashboard';
        let method = 'index';
        
        // Handle nested controllers (e.g., perjanjian_konsesijasa/Perjanjian_konsesijasa)
        if (parts.length > 1) {
            if (parts[1] && parts[1].charAt(0) === parts[1].charAt(0).toUpperCase()) {
                // Second part is capitalized, likely a controller class
                controller = parts[0] + '/' + parts[1];
                method = parts[2] || 'page';
            } else {
                // Second part is likely a method
                method = parts[1];
            }
        }

        return { controller, method };
    }

    /**
     * Handle successful AJAX response
     */
    handleSuccess(response, url, title, pushState) {
        if (response.success) {
            // Update content
            $(this.contentContainer).html(response.content);
            
            // Update page title
            if (response.title) {
                document.title = response.title + ' - Database Aset Konsesi Jasa BUJT';
            }
            
            // Update browser history
            if (pushState) {
                history.pushState(
                    { url: url, title: response.title },
                    response.title,
                    url
                );
            }
            
            // Update current URL
            this.currentUrl = url;
            
            // Trigger custom event for other scripts
            $(document).trigger('contentLoaded', [response]);
            
            // Re-initialize any plugins or scripts needed for the new content
            this.reinitializePlugins();
            
        } else {
            this.showError(response.error || 'Unknown error occurred');
            
            // Handle session expiration
            if (response.redirect) {
                setTimeout(() => {
                    window.location.href = response.redirect;
                }, 2000);
            }
        }
    }

    /**
     * Handle AJAX error
     */
    handleError(xhr, status, error) {
        let errorMessage = 'Failed to load content';
        
        if (status === 'timeout') {
            errorMessage = 'Request timed out. Please try again.';
        } else if (xhr.status === 404) {
            errorMessage = 'Page not found.';
        } else if (xhr.status === 500) {
            errorMessage = 'Server error. Please try again later.';
        } else if (xhr.responseJSON && xhr.responseJSON.error) {
            errorMessage = xhr.responseJSON.error;
        }
        
        this.showError(errorMessage);
    }

    /**
     * Show loading state
     */
    showLoading() {
        $(this.contentContainer).addClass(this.loadingClass);
        
        // Show loading spinner
        const loadingHtml = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Loading content...</p>
            </div>
        `;
        
        $(this.contentContainer).html(loadingHtml);
    }

    /**
     * Hide loading state
     */
    hideLoading() {
        $(this.contentContainer).removeClass(this.loadingClass);
    }

    /**
     * Show error message
     */
    showError(message) {
        const errorHtml = `
            <div class="alert alert-danger text-center">
                <h4>Error Loading Content</h4>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="location.reload()">Reload Page</button>
            </div>
        `;
        
        $(this.contentContainer).html(errorHtml);
    }

    /**
     * Re-initialize plugins and scripts for new content
     */
    reinitializePlugins() {
        // Re-initialize DataTables if present
        if (typeof $.fn.DataTable !== 'undefined') {
            $('table.dataTable').each(function() {
                if ($.fn.DataTable.isDataTable(this)) {
                    $(this).DataTable().destroy();
                }
            });
        }

        // Re-initialize select pickers
        if (typeof $.fn.selectpicker !== 'undefined') {
            $('.selectpicker').selectpicker('refresh');
        }

        // Re-initialize modals
        if (typeof $.fn.modal !== 'undefined') {
            $('.modal').modal('hide');
        }

        // Trigger custom reinitialize event
        $(document).trigger('pluginsReinitialized');
    }
}

// Initialize when document is ready
$(document).ready(function() {
    // Only initialize if we're in the dashboard layout
    if ($('.pcoded-inner-content').length > 0) {
        window.dynamicLoader = new DynamicContentLoader();
    }
});

// Utility functions for easy access
function loadPage(controller, method = 'index', title = '') {
    if (window.dynamicLoader) {
        window.dynamicLoader.loadModule(controller, method, title);
    }
}

function loadPerjanjianKonsesiJasa() {
    loadPage('perjanjian_konsesijasa/Perjanjian_konsesijasa', 'page', 'Perjanjian Konsesi Jasa');
}

function loadSLO() {
    loadPage('slo/Slo', 'page', 'SLO (Surat Layak Operasi)');
}

function loadSaldoAwal() {
    loadPage('saldo_awal/Saldo_awal', 'page', 'Saldo Awal');
}

function loadSaldoAwalLP() {
    loadPage('saldo_awal_lp/Saldo_awal_lp', 'page', 'Saldo Awal LP');
}

function loadPerolehanMutasiLP() {
    loadPage('perolehan_mutasi_lp/Perolehan_mutasi_lp', 'page', 'Perolehan Mutasi LP');
}

function loadPerolehanMutasiKuantitas() {
    loadPage('perolehan_mutasi_kuantitas/Perolehan_mutasi_kuantitas', 'page', 'Perolehan Mutasi Kuantitas');
}

function loadSaldoAwalKuantitas() {
    loadPage('saldo_awal_kuantitas/Saldo_awal_kuantitas', 'page', 'Saldo Awal Kuantitas');
}

function loadSaldoAwalKuantitasGov() {
    loadPage('saldo_awal_kuantitas_gov/Saldo_awal_kuantitas_gov', 'page', 'Saldo Awal Kuantitas Gov');
}

function loadPerolehanMutasiKuantitasGov() {
    loadPage('perolehan_mutasi_kuantitas_gov/Perolehan_mutasi_kuantitas_gov', 'page', 'Perolehan Mutasi Kuantitas Gov');
}

function loadSaldoAwalKewajiban() {
    loadPage('saldo_awal_kewajiban/Saldo_awal_kewajiban', 'page', 'Saldo Awal Kewajiban');
}

function loadPerolehanMutasiKewajiban() {
    loadPage('perolehan_mutasi_kewajiban/Perolehan_mutasi_kewajiban', 'index', 'Perolehan Mutasi Kewajiban');
}

function loadDashboard() {
    loadPage('dashboard', 'index', 'Dashboard');
}

{"version": 3, "sources": ["../../scss/bootstrap.scss", "../../scss/_print.scss", "bootstrap.css", "../../scss/_reboot.scss", "../../scss/_variables.scss", "../../scss/mixins/_hover.scss", "../../scss/_type.scss", "../../scss/mixins/_lists.scss", "../../scss/_images.scss", "../../scss/mixins/_image.scss", "../../scss/mixins/_border-radius.scss", "../../scss/mixins/_transition.scss", "../../scss/_code.scss", "../../scss/_grid.scss", "../../scss/mixins/_grid.scss", "../../scss/mixins/_breakpoints.scss", "../../scss/mixins/_grid-framework.scss", "../../scss/_tables.scss", "../../scss/mixins/_table-row.scss", "../../scss/_functions.scss", "../../scss/_forms.scss", "../../scss/mixins/_forms.scss", "../../scss/_buttons.scss", "../../scss/mixins/_buttons.scss", "../../scss/_transitions.scss", "../../scss/_dropdown.scss", "../../scss/mixins/_nav-divider.scss", "../../scss/_button-group.scss", "../../scss/_input-group.scss", "../../scss/_custom-forms.scss", "../../scss/_nav.scss", "../../scss/_navbar.scss", "../../scss/_card.scss", "../../scss/_breadcrumb.scss", "../../scss/mixins/_clearfix.scss", "../../scss/_pagination.scss", "../../scss/mixins/_pagination.scss", "../../scss/_badge.scss", "../../scss/mixins/_badge.scss", "../../scss/_jumbotron.scss", "../../scss/_alert.scss", "../../scss/mixins/_alert.scss", "../../scss/_progress.scss", "../../scss/mixins/_gradients.scss", "../../scss/_media.scss", "../../scss/_list-group.scss", "../../scss/mixins/_list-group.scss", "../../scss/_close.scss", "../../scss/_modal.scss", "../../scss/_tooltip.scss", "../../scss/mixins/_reset-text.scss", "../../scss/_popover.scss", "../../scss/_carousel.scss", "../../scss/utilities/_align.scss", "../../scss/mixins/_background-variant.scss", "../../scss/utilities/_background.scss", "../../scss/utilities/_borders.scss", "../../scss/utilities/_display.scss", "../../scss/utilities/_embed.scss", "../../scss/utilities/_flex.scss", "../../scss/utilities/_float.scss", "../../scss/mixins/_float.scss", "../../scss/utilities/_position.scss", "../../scss/utilities/_screenreaders.scss", "../../scss/mixins/_screen-reader.scss", "../../scss/utilities/_sizing.scss", "../../scss/utilities/_spacing.scss", "../../scss/utilities/_text.scss", "../../scss/mixins/_text-truncate.scss", "../../scss/mixins/_text-emphasis.scss", "../../scss/mixins/_text-hide.scss", "../../scss/utilities/_visibility.scss", "../../scss/mixins/_visibility.scss"], "names": [], "mappings": "AAAA;;;;;GAKG;ACMD;EACE;;;IAME,6BAA4B;IAE5B,4BAA2B;GAC5B;EAED;;IAEE,2BAA0B;GAC3B;EAOD;IACE,8BAA6B;GAC9B;EAaD;IACE,iCAAgC;GACjC;EACD;;IAEE,uBAAgC;IAChC,yBAAwB;GACzB;EAOD;IACE,4BAA2B;GAC5B;EAED;;IAEE,yBAAwB;GACzB;EAED;;;IAGE,WAAU;IACV,UAAS;GACV;EAED;;IAEE,wBAAuB;GACxB;EAKD;IACE,cAAa;GACd;EACD;IACE,uBAAgC;GACjC;EAED;IACE,qCAAoC;GAMrC;EAPD;;IAKI,kCAAiC;GAClC;EAEH;;IAGI,kCAAiC;GAClC;CC3CN;;AC1CD;EACE,uBAAsB;EACtB,wBAAuB;EACvB,kBAAiB;EACjB,+BAA8B;EAC9B,2BAA0B;EAC1B,8BAA6B;EAC7B,yCAA0C;CAC3C;;AAED;;;EAGE,oBAAmB;CACpB;;AAIC;EAAgB,oBAAmB;CD4CpC;;ACxCD;EACE,eAAc;CACf;;AAOD;EACE,UAAS;EACT,wGCoLiH;EDnLjH,gBCuLmB;EDtLnB,oBC0LyB;EDzLzB,iBC6LoB;ED5LpB,eCEgB;EDDhB,uBCRW;CDSZ;;ADuCD;EC/BE,yBAAwB;CACzB;;AAQD;EACE,wBAAuB;EACvB,UAAS;EACT,kBAAiB;CAClB;;AAWD;EACE,cAAa;EACb,qBAAoB;CACrB;;AAMD;EACE,cAAa;EACb,oBAAmB;CACpB;;AASD;;EAEE,2BAA0B;EAC1B,0CAAiC;UAAjC,kCAAiC;EACjC,aAAY;EACZ,iBAAgB;CACjB;;AAED;EACE,oBAAmB;EACnB,mBAAkB;EAClB,qBAAoB;CACrB;;AAED;;;EAGE,cAAa;EACb,oBAAmB;CACpB;;AAED;;;;EAIE,iBAAgB;CACjB;;AAED;EACE,kBCqGqB;CDpGtB;;AAED;EACE,qBAAoB;EACpB,eAAc;CACf;;AAED;EACE,iBAAgB;CACjB;;AAED;EACE,mBAAkB;CACnB;;AAED;;EAEE,oBAAmB;CACpB;;AAED;EACE,eAAc;CACf;;AAOD;;EAEE,mBAAkB;EAClB,eAAc;EACd,eAAc;EACd,yBAAwB;CACzB;;AAED;EAAM,eAAc;CAAK;;AACzB;EAAM,WAAU;CAAK;;AAOrB;EACE,eClHe;EDmHf,sBCxB0B;EDyB1B,8BAA6B;EAC7B,sCAAqC;CAMtC;;AE1LG;EFuLA,eC5B4C;ED6B5C,2BC5B6B;CC5JR;;AFkMzB;EACE,eAAc;EACd,sBAAqB;CAUtB;;AEnMG;EF4LA,eAAc;EACd,sBAAqB;CE1LpB;;AFoLL;EAUI,WAAU;CACX;;AAQH;;;;EAIE,kCAAiC;EACjC,eAAc;CACf;;AAED;EAEE,cAAa;EAEb,oBAAmB;EAEnB,eAAc;CACf;;AAOD;EAEE,iBAAgB;CACjB;;AAOD;EACE,uBAAsB;EACtB,mBAAkB;CACnB;;AAED;EACE,iBAAgB;CACjB;;AAaD;;;;;;;;;EASE,+BAA0B;MAA1B,2BAA0B;CAC3B;;AAOD;EACE,0BAAyB;CAC1B;;AAED;EACE,qBCEoC;EDDpC,wBCCoC;EDApC,eCpPgB;EDqPhB,iBAAgB;EAChB,qBAAoB;CACrB;;AAED;EAEE,iBAAgB;CACjB;;AAOD;EAEE,sBAAqB;EACrB,qBAAoB;CACrB;;AAMD;EACE,oBAAmB;EACnB,2CAA0C;CAC3C;;AAED;;;;;EAKE,UAAS;EACT,qBAAoB;EACpB,mBAAkB;EAClB,qBAAoB;CACrB;;AAED;;EAEE,kBAAiB;CAClB;;AAED;;EAEE,qBAAoB;CACrB;;AAKD;;;;EAIE,2BAA0B;CAC3B;;AAGD;;;;EAIE,WAAU;EACV,mBAAkB;CACnB;;AAED;;EAEE,uBAAsB;EACtB,WAAU;CACX;;AAGD;;;;EASE,4BAA2B;CAC5B;;AAED;EACE,eAAc;EAEd,iBAAgB;CACjB;;AAED;EAME,aAAY;EAEZ,WAAU;EACV,UAAS;EACT,UAAS;CACV;;AAID;EACE,eAAc;EACd,YAAW;EACX,gBAAe;EACf,WAAU;EACV,qBAAoB;EACpB,kBAAiB;EACjB,qBAAoB;EACpB,eAAc;EACd,oBAAmB;CACpB;;AAED;EACE,yBAAwB;CACzB;;ADpED;;ECyEE,aAAY;CACb;;ADrED;EC4EE,qBAAoB;EACpB,yBAAwB;CACzB;;ADzED;;ECiFE,yBAAwB;CACzB;;AAOD;EACE,cAAa;EACb,2BAA0B;CAC3B;;AAMD;EACE,sBAAqB;CACtB;;AAED;EACE,mBAAkB;CACnB;;AAED;EACE,cAAa;CACd;;ADtFD;EC2FE,yBAAwB;CACzB;;AG5dD;;EAEE,sBFwPoC;EEvPpC,qBFwP8B;EEvP9B,iBFwP0B;EEvP1B,iBFwP0B;EEvP1B,eFwP8B;CEvP/B;;AAED;EAAU,kBF0OW;CE1OiB;;AACtC;EAAU,gBF0OS;CE1OmB;;AACtC;EAAU,mBF0OY;CE1OgB;;AACtC;EAAU,kBF0OW;CE1OiB;;AACtC;EAAU,mBF0OY;CE1OgB;;AACtC;EAAU,gBF0OS;CE1OmB;;AAEtC;EACE,mBF0PwB;EEzPxB,iBF0PoB;CEzPrB;;AAGD;EACE,gBFyOkB;EExOlB,iBF6OuB;EE5OvB,iBFoO0B;CEnO3B;;AACD;EACE,kBFqOoB;EEpOpB,iBFyOuB;EExOvB,iBF+N0B;CE9N3B;;AACD;EACE,kBFiOoB;EEhOpB,iBFqOuB;EEpOvB,iBF0N0B;CEzN3B;;AACD;EACE,kBF6NoB;EE5NpB,iBFiOuB;EEhOvB,iBFqN0B;CEpN3B;;AAOD;EACE,iBAAgB;EAChB,oBAAmB;EACnB,UAAS;EACT,yCFIW;CEHZ;;AAOD;;EAEE,eFgNmB;EE/MnB,oBF8KyB;CE7K1B;;AAED;;EAEE,eFoNiB;EEnNjB,0BF4Ne;CE3NhB;;AAOD;EC7EE,gBAAe;EACf,iBAAgB;CD8EjB;;AAGD;EClFE,gBAAe;EACf,iBAAgB;CDmFjB;;AACD;EACE,sBAAqB;CAKtB;;AAND;EAII,kBFsMqB;CErMtB;;AASH;EACE,eAAc;EACd,0BAAyB;CAC1B;;AAGD;EACE,oBFyBW;EExBX,mBFwKgD;CEvKjD;;AAED;EACE,eAAc;EACd,eAAc;EACd,eF7DgB;CEkEjB;;AARD;EAMI,uBAAsB;CACvB;;AElHH;ECIE,gBAAe;EAGf,aAAY;CDLb;;AAID;EACE,iBJkvBkC;EIjvBlC,uBJmCW;EIlCX,uBJmvBgC;EM/vB9B,uBNmN2B;EOlNzB,iCPiwB2C;EK3vB/C,gBAAe;EAGf,aAAY;CDSb;;AAMD;EAEE,sBAAqB;CACtB;;AAED;EACE,sBAA4B;EAC5B,eAAc;CACf;;AAED;EACE,eJmuB4B;EIluB5B,eJegB;CIdjB;;AIzCD;;;;EAIE,kFRqO2F;CQpO5F;;AAGD;EACE,uBRkzBiC;EQjzBjC,eR+yB+B;EQ9yB/B,eRizBmC;EQhzBnC,0BRsCgB;EM/Cd,uBNmN2B;CQjM9B;;AALC;EACE,WAAU;EACV,eAAc;EACd,0BAAyB;CAC1B;;AAIH;EACE,uBRkyBiC;EQjyBjC,eR+xB+B;EQ9xB/B,YRsBW;EQrBX,0BR8BgB;EMvDd,sBNqN0B;CQlL7B;;AAdD;EASI,WAAU;EACV,gBAAe;EACf,kBR8MmB;CQ5MpB;;AAIH;EACE,eAAc;EACd,cAAa;EACb,oBAAmB;EACnB,eR4wB+B;EQ3wB/B,eRYgB;CQFjB;;AAfD;EASI,WAAU;EACV,mBAAkB;EAClB,eAAc;EACd,8BAA6B;EAC7B,iBAAgB;CACjB;;AAIH;EACE,kBRuwBiC;EQtwBjC,mBAAkB;CACnB;;AC1DC;ECAA,mBAAkB;EAClB,kBAAiB;EACjB,oBAAuC;EACvC,mBAAuC;EACvC,YAAW;CDDV;;AEgDC;EFnDF;ICYI,iBV8KK;GSvLR;CXwlBF;;AaxiBG;EFnDF;ICYI,iBV+KK;GSxLR;CX8lBF;;Aa9iBG;EFnDF;ICYI,iBVgLK;GSzLR;CXomBF;;AapjBG;EFnDF;ICYI,kBViLM;GS1LT;CX0mBF;;AWjmBC;EACE,YAAW;ECbb,mBAAkB;EAClB,kBAAiB;EACjB,oBAAuC;EACvC,mBAAuC;EACvC,YAAW;CDWV;;AAQD;ECLA,qBAAa;EAAb,cAAa;EACb,oBAAe;MAAf,gBAAe;EACf,oBAAuC;EACvC,mBAAuC;CDItC;;AAID;EACE,gBAAe;EACf,eAAc;CAOf;;AATD;;EAMI,iBAAgB;EAChB,gBAAe;CAChB;;AGnCH;;;;;;EACE,mBAAkB;EAClB,YAAW;EACX,gBAAe;EACf,oBAA4B;EAC5B,mBAA4B;CAC7B;;AAkBG;EACE,2BAAa;MAAb,cAAa;EACb,qBAAY;MAAZ,aAAY;EACZ,gBAAe;CAChB;;AACD;EACE,mBAAc;MAAd,eAAc;EACd,YAAW;EACX,gBAAe;CAChB;;AAGC;EFFN,wBAAsC;MAAtC,oBAAsC;EAItC,qBAAuC;CEAhC;;AAFD;EFFN,yBAAsC;MAAtC,qBAAsC;EAItC,sBAAuC;CEAhC;;AAFD;EFFN,kBAAsC;MAAtC,cAAsC;EAItC,eAAuC;CEAhC;;AAFD;EFFN,yBAAsC;MAAtC,qBAAsC;EAItC,sBAAuC;CEAhC;;AAFD;EFFN,yBAAsC;MAAtC,qBAAsC;EAItC,sBAAuC;CEAhC;;AAFD;EFFN,kBAAsC;MAAtC,cAAsC;EAItC,eAAuC;CEAhC;;AAFD;EFFN,yBAAsC;MAAtC,qBAAsC;EAItC,sBAAuC;CEAhC;;AAFD;EFFN,yBAAsC;MAAtC,qBAAsC;EAItC,sBAAuC;CEAhC;;AAFD;EFFN,kBAAsC;MAAtC,cAAsC;EAItC,eAAuC;CEAhC;;AAFD;EFFN,yBAAsC;MAAtC,qBAAsC;EAItC,sBAAuC;CEAhC;;AAFD;EFFN,yBAAsC;MAAtC,qBAAsC;EAItC,sBAAuC;CEAhC;;AAFD;EFFN,mBAAsC;MAAtC,eAAsC;EAItC,gBAAuC;CEAhC;;AAID;EACE,kBAFU;MAEV,SAFU;CAGX;;AAFD;EACE,kBAFU;MAEV,SAFU;CAGX;;AAFD;EACE,kBAFU;MAEV,SAFU;CAGX;;AAFD;EACE,kBAFU;MAEV,SAFU;CAGX;;AAFD;EACE,kBAFU;MAEV,SAFU;CAGX;;AAFD;EACE,kBAFU;MAEV,SAFU;CAGX;;AAFD;EACE,kBAFU;MAEV,SAFU;CAGX;;AAFD;EACE,kBAFU;MAEV,SAFU;CAGX;;AAFD;EACE,kBAFU;MAEV,SAFU;CAGX;;AAFD;EACE,mBAFU;MAEV,UAFU;CAGX;;AAFD;EACE,mBAFU;MAEV,UAFU;CAGX;;AAFD;EACE,mBAFU;MAEV,UAFU;CAGX;;ADKL;ECzBE;IACE,2BAAa;QAAb,cAAa;IACb,qBAAY;QAAZ,aAAY;IACZ,gBAAe;GAChB;EACD;IACE,mBAAc;QAAd,eAAc;IACd,YAAW;IACX,gBAAe;GAChB;EAGC;IFFN,wBAAsC;QAAtC,oBAAsC;IAItC,qBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,mBAAsC;QAAtC,eAAsC;IAItC,gBAAuC;GEAhC;EAID;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;Cdg0BR;;Aa3zBG;ECzBE;IACE,2BAAa;QAAb,cAAa;IACb,qBAAY;QAAZ,aAAY;IACZ,gBAAe;GAChB;EACD;IACE,mBAAc;QAAd,eAAc;IACd,YAAW;IACX,gBAAe;GAChB;EAGC;IFFN,wBAAsC;QAAtC,oBAAsC;IAItC,qBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,mBAAsC;QAAtC,eAAsC;IAItC,gBAAuC;GEAhC;EAID;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;Cdi6BR;;Aa55BG;ECzBE;IACE,2BAAa;QAAb,cAAa;IACb,qBAAY;QAAZ,aAAY;IACZ,gBAAe;GAChB;EACD;IACE,mBAAc;QAAd,eAAc;IACd,YAAW;IACX,gBAAe;GAChB;EAGC;IFFN,wBAAsC;QAAtC,oBAAsC;IAItC,qBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,mBAAsC;QAAtC,eAAsC;IAItC,gBAAuC;GEAhC;EAID;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;CdkgCR;;Aa7/BG;ECzBE;IACE,2BAAa;QAAb,cAAa;IACb,qBAAY;QAAZ,aAAY;IACZ,gBAAe;GAChB;EACD;IACE,mBAAc;QAAd,eAAc;IACd,YAAW;IACX,gBAAe;GAChB;EAGC;IFFN,wBAAsC;QAAtC,oBAAsC;IAItC,qBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,kBAAsC;QAAtC,cAAsC;IAItC,eAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,yBAAsC;QAAtC,qBAAsC;IAItC,sBAAuC;GEAhC;EAFD;IFFN,mBAAsC;QAAtC,eAAsC;IAItC,gBAAuC;GEAhC;EAID;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,kBAFU;QAEV,SAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;EAFD;IACE,mBAFU;QAEV,UAFU;GAGX;CdmmCR;;AelpCD;EACE,YAAW;EACX,gBAAe;EACf,oBbgIW;Ea/HX,8BbuSyC;CalR1C;;AAzBD;;EAQI,iBbgSkC;Ea/RlC,oBAAmB;EACnB,8BbsCc;CarCf;;AAXH;EAcI,uBAAsB;EACtB,iCbiCc;CahCf;;AAhBH;EAmBI,8Bb6Bc;Ca5Bf;;AApBH;EAuBI,uBbuBS;CatBV;;AAQH;;EAGI,gBbsQiC;CarQlC;;AAQH;EACE,0BbGgB;CaUjB;;AAdD;;EAKI,0BbDc;CaEf;;AANH;;EAWM,yBAA8C;CAC/C;;AASL;EAEI,sCbXS;CaYV;;AAQH;EAGM,uCbvBO;CCjDY;;AaNvB;;;EAII,0BC4EmE;CD3EpE;;AAKH;EAKM,0BAJsC;CbLrB;;AaIvB;;EASQ,0BARoC;CASrC;;AApBP;;;EAII,0BC4EmE;CD3EpE;;AAKH;EAKM,0BAJsC;CbLrB;;AaIvB;;EASQ,0BARoC;CASrC;;AApBP;;;EAII,0BC4EmE;CD3EpE;;AAKH;EAKM,0BAJsC;CbLrB;;AaIvB;;EASQ,0BARoC;CASrC;;AApBP;;;EAII,0BC4EmE;CD3EpE;;AAKH;EAKM,0BAJsC;CbLrB;;AaIvB;;EASQ,0BARoC;CASrC;;AApBP;;;EAII,0BC4EmE;CD3EpE;;AAKH;EAKM,0BAJsC;CbLrB;;AaIvB;;EASQ,0BARoC;CASrC;;AApBP;;;EAII,0BC4EmE;CD3EpE;;AAKH;EAKM,0BAJsC;CbLrB;;AaIvB;;EASQ,0BARoC;CASrC;;AApBP;;;EAII,0BC4EmE;CD3EpE;;AAKH;EAKM,0BAJsC;CbLrB;;AaIvB;;EASQ,0BARoC;CASrC;;AApBP;;;EAII,0BC4EmE;CD3EpE;;AAKH;EAKM,0BAJsC;CbLrB;;AaIvB;;EASQ,0BARoC;CASrC;;AApBP;;;EAII,uCdmDO;CclDR;;AAKH;EAKM,uCAJsC;CbLrB;;AaIvB;;EASQ,uCARoC;CASrC;;ADgFT;EAEI,YbzDS;Ea0DT,0BbjDc;CakDf;;AAGH;EAEI,ebzDc;Ea0Dd,0Bb/Dc;CagEf;;AAGH;EACE,YbtEW;EauEX,0Bb9DgB;CauFjB;;AA3BD;;;EAOI,sBb+LoD;Ca9LrD;;AARH;EAWI,UAAS;CACV;;AAZH;EAgBM,4CbrFO;CasFR;;AAjBL;EAuBQ,6Cb5FK;CCvCY;;AU0DrB;EEsFJ;IAEI,eAAc;IACd,YAAW;IACX,iBAAgB;IAChB,6CAA4C;GAO/C;EAZD;IASM,UAAS;GACV;Cf2tCJ;;AkB13CD;EACE,eAAc;EACd,YAAW;EAGX,wBhB2TgC;EgB1ThC,gBhBiOmB;EgBhOnB,kBhB0T8B;EgBzT9B,ehB2CgB;EgB1ChB,uBhBmCW;EgBjCX,uBAAsB;EACtB,6BAA4B;EAC5B,sChByCW;EgBpCT,uBhB+L2B;EOlNzB,yEP6XqF;CgBtU1F;;AAtDD;EA6BI,8BAA6B;EAC7B,UAAS;CACV;;ACxBD;EACE,ejB2Cc;EiB1Cd,uBjBmCS;EiBlCT,sBjBiWiE;EiBhWjE,cAAa;CAEd;;ADbH;EAsCI,ehBYc;EgBVd,WAAU;CACX;;AAzCH;EAsCI,ehBYc;EgBVd,WAAU;CACX;;AAzCH;EAsCI,ehBYc;EgBVd,WAAU;CACX;;AAzCH;EAkDI,0BhBJc;EgBMd,WAAU;CACX;;AAGH;EAEI,4BhB0TkF;CgBzTnF;;AAHH;EAWI,ehBhBc;EgBiBd,uBhBxBS;CgByBV;;AAIH;;EAEE,eAAc;CACf;;AASD;EACE,oCAA2E;EAC3E,uCAA8E;EAC9E,iBAAgB;CACjB;;AAED;EACE,oCAA8E;EAC9E,uCAAiF;EACjF,mBhB0IsB;CgBzIvB;;AAED;EACE,qCAA8E;EAC9E,wCAAiF;EACjF,oBhBqIsB;CgBpIvB;;AASD;EACE,oBhBgN+B;EgB/M/B,uBhB+M+B;EgB9M/B,iBAAgB;EAChB,gBhBqHmB;CgBpHpB;;AAQD;EACE,oBhBmM+B;EgBlM/B,uBhBkM+B;EgBjM/B,iBAAgB;EAChB,kBhBkM8B;EgBjM9B,0BAAyB;EACzB,oBAAuC;CAOxC;;AAbD;;;;;EAUI,iBAAgB;EAChB,gBAAe;CAChB;;AAYH;;;EACE,wBhBgL+B;EgB/K/B,oBhBoFsB;EgBnFtB,iBhB+K6B;EMvU3B,sBNqN0B;CgB3D7B;;AAED;;;EAEI,8BhB2NqF;CgB1NtF;;AAGH;;;EACE,qBhBuK8B;EgBtK9B,mBhBsEsB;EgBrEtB,iBhBsK6B;EM3U3B,sBNoN0B;CgB7C7B;;AAED;;;EAEI,8BhBiNqF;CgBhNtF;;AASH;EACE,oBhBmNmC;CgBlNpC;;AAED;EACE,eAAc;EACd,oBhBqM+B;CgBpMhC;;AAOD;EACE,qBAAa;EAAb,cAAa;EACb,oBAAe;MAAf,gBAAe;EACf,mBAAkB;EAClB,kBAAiB;CAOlB;;AAXD;;EAQI,mBAAkB;EAClB,kBAAiB;CAClB;;AAQH;EACE,mBAAkB;EAClB,eAAc;EACd,sBhB0K+B;CgBnKhC;;AAVD;EAOM,ehBxKY;CgByKb;;AAIL;EACE,sBhBiKiC;EgBhKjC,iBAAgB;CACjB;;AAED;EACE,mBAAkB;EAClB,oBhB4JgC;EgB3JhC,sBhB0JiC;CgBrJlC;;AARD;EAMI,iBAAgB;CACjB;;AAIH;EACE,sBAAqB;CAStB;;AAVD;EAII,uBAAsB;CACvB;;AALH;EAQI,qBhB8I+B;CgB7IhC;;AAWH;EACE,cAAa;EACb,mBAAkB;EAClB,mBAAkB;EAClB,ehB/Le;CgBgMhB;;AAED;EACE,mBAAkB;EAClB,UAAS;EACT,WAAU;EACV,cAAa;EACb,aAAY;EACZ,eAAc;EACd,kBAAiB;EACjB,mBAAkB;EAClB,eAAc;EACd,YAAW;EACX,yChB7Me;EgB8Mf,qBAAoB;CACrB;;AClQG;;;EAEE,sBjBoDW;CiB1CZ;;AAZD;;;EAKI,iDjBiDS;CiBhDV;;AANH;;;;;;;;EAUI,eAAc;CACf;;AAOH;EAGI,ejBiCS;CiBhCV;;AAMH;EAGI,0CjBuBS;CiBtBV;;AAJH;EAMI,ejBoBS;CiBnBV;;AAMH;EAGI,sBjBUS;CiBPV;;AANH;EAKgB,sBAAqB;CAAK;;AAL1C;EAQI,iDjBKS;CiBJV;;AAlDH;;;EAEE,sBjBiDW;CiBvCZ;;AAZD;;;EAKI,iDjB8CS;CiB7CV;;AANH;;;;;;;;EAUI,eAAc;CACf;;AAOH;EAGI,ejB8BS;CiB7BV;;AAMH;EAGI,0CjBoBS;CiBnBV;;AAJH;EAMI,ejBiBS;CiBhBV;;AAMH;EAGI,sBjBOS;CiBJV;;AANH;EAKgB,sBAAqB;CAAK;;AAL1C;EAQI,iDjBES;CiBDV;;AD8NP;EACE,qBAAa;EAAb,cAAa;EACb,wBAAmB;MAAnB,oBAAmB;EACnB,uBAAmB;MAAnB,oBAAmB;CAuFpB;;AA1FD;EASI,YAAW;CACZ;;AL7PC;EKmPJ;IAeM,qBAAa;IAAb,cAAa;IACb,uBAAmB;QAAnB,oBAAmB;IACnB,sBAAuB;QAAvB,wBAAuB;IACvB,iBAAgB;GACjB;EAnBL;IAuBM,qBAAa;IAAb,cAAa;IACb,mBAAc;QAAd,eAAc;IACd,wBAAmB;QAAnB,oBAAmB;IACnB,uBAAmB;QAAnB,oBAAmB;IACnB,iBAAgB;GACjB;EA5BL;IAgCM,sBAAqB;IACrB,YAAW;IACX,uBAAsB;GACvB;EAnCL;IAuCM,sBAAqB;GACtB;EAxCL;IA2CM,YAAW;GACZ;EA5CL;IA+CM,iBAAgB;IAChB,uBAAsB;GACvB;EAjDL;IAsDM,qBAAa;IAAb,cAAa;IACb,uBAAmB;QAAnB,oBAAmB;IACnB,sBAAuB;QAAvB,wBAAuB;IACvB,YAAW;IACX,cAAa;IACb,iBAAgB;GACjB;EA5DL;IA8DM,gBAAe;GAChB;EA/DL;IAiEM,mBAAkB;IAClB,cAAa;IACb,sBhB2B4B;IgB1B5B,eAAc;GACf;EArEL;IAyEM,qBAAa;IAAb,cAAa;IACb,uBAAmB;QAAnB,oBAAmB;IACnB,sBAAuB;QAAvB,wBAAuB;IACvB,gBAAe;GAChB;EA7EL;IA+EM,iBAAgB;IAChB,sBAAqB;IACrB,sBhBa4B;IgBZ5B,4BAA2B;GAC5B;EAnFL;IAuFM,OAAM;GACP;ClBi3CJ;;AoB9uDD;EACE,sBAAqB;EACrB,oBlByOyB;EkBxOzB,mBAAkB;EAClB,oBAAmB;EACnB,uBAAsB;EACtB,0BAAiB;KAAjB,uBAAiB;MAAjB,sBAAiB;UAAjB,kBAAiB;EACjB,8BAAiD;ECiEjD,wBnBwPgC;EmBvPhC,gBnB8JmB;EmB7JnB,kBnBuP8B;EMnU5B,uBNmN2B;EOlNzB,kCP0V+C;CkBxTpD;;AjBjBG;EiBHA,sBAAqB;CjBMpB;;AiBnBL;EAiBI,WAAU;EACV,8ClBkDa;CkBjDd;;AAnBH;EAwBI,aAAY;CAEb;;AA1BH;EA8BI,uBAAsB;CAEvB;;AAIH;;EAEE,qBAAoB;CACrB;;AAQC;EHQE,YAAW;EItDb,0BnBmEe;EmBlEf,sBnBkEe;CkBnBd;;AC5CD;EJkDE,YAAW;EIhDX,0BARqF;EASrF,sBAT2H;CAU5H;;AAED;EAMI,6CnBmDW;CmBjDd;;AAGD;EAEE,0BnB4Ca;EmB3Cb,sBnB2Ca;CmB1Cd;;AAED;;EAGE,0BAhCqF;EAiCrF,uBAAsB;EACtB,sBAlC2H;CAoC5H;;ADYD;EHQE,YAAW;EItDb,0BnBiDgB;EmBhDhB,sBnBgDgB;CkBDf;;AC5CD;EJkDE,YAAW;EIhDX,0BARqF;EASrF,sBAT2H;CAU5H;;AAED;EAMI,+CnBiCY;CmB/Bf;;AAGD;EAEE,0BnB0Bc;EmBzBd,sBnByBc;CmBxBf;;AAED;;EAGE,0BAhCqF;EAiCrF,uBAAsB;EACtB,sBAlC2H;CAoC5H;;ADYD;EHQE,YAAW;EItDb,0BnB0Ee;EmBzEf,sBnByEe;CkB1Bd;;AC5CD;EJkDE,YAAW;EIhDX,0BARqF;EASrF,sBAT2H;CAU5H;;AAED;EAMI,6CnB0DW;CmBxDd;;AAGD;EAEE,0BnBmDa;EmBlDb,sBnBkDa;CmBjDd;;AAED;;EAGE,0BAhCqF;EAiCrF,uBAAsB;EACtB,sBAlC2H;CAoC5H;;ADYD;EHQE,YAAW;EItDb,0BnB4Ee;EmB3Ef,sBnB2Ee;CkB5Bd;;AC5CD;EJkDE,YAAW;EIhDX,0BARqF;EASrF,sBAT2H;CAU5H;;AAED;EAMI,8CnB4DW;CmB1Dd;;AAGD;EAEE,0BnBqDa;EmBpDb,sBnBoDa;CmBnDd;;AAED;;EAGE,0BAhCqF;EAiCrF,uBAAsB;EACtB,sBAlC2H;CAoC5H;;ADYD;EHME,YAAW;EIpDb,0BnByEe;EmBxEf,sBnBwEe;CkBzBd;;AC5CD;EJgDE,YAAW;EI9CX,0BARqF;EASrF,sBAT2H;CAU5H;;AAED;EAMI,6CnByDW;CmBvDd;;AAGD;EAEE,0BnBkDa;EmBjDb,sBnBiDa;CmBhDd;;AAED;;EAGE,0BAhCqF;EAiCrF,uBAAsB;EACtB,sBAlC2H;CAoC5H;;ADYD;EHQE,YAAW;EItDb,0BnBuEe;EmBtEf,sBnBsEe;CkBvBd;;AC5CD;EJkDE,YAAW;EIhDX,0BARqF;EASrF,sBAT2H;CAU5H;;AAED;EAMI,6CnBuDW;CmBrDd;;AAGD;EAEE,0BnBgDa;EmB/Cb,sBnB+Ca;CmB9Cd;;AAED;;EAGE,0BAhCqF;EAiCrF,uBAAsB;EACtB,sBAlC2H;CAoC5H;;ADYD;EHME,YAAW;EIpDb,0BnB4CgB;EmB3ChB,sBnB2CgB;CkBIf;;AC5CD;EJgDE,YAAW;EI9CX,0BARqF;EASrF,sBAT2H;CAU5H;;AAED;EAMI,+CnB4BY;CmB1Bf;;AAGD;EAEE,0BnBqBc;EmBpBd,sBnBoBc;CmBnBf;;AAED;;EAGE,0BAhCqF;EAiCrF,uBAAsB;EACtB,sBAlC2H;CAoC5H;;ADYD;EHQE,YAAW;EItDb,0BnBmDgB;EmBlDhB,sBnBkDgB;CkBHf;;AC5CD;EJkDE,YAAW;EIhDX,0BARqF;EASrF,sBAT2H;CAU5H;;AAED;EAMI,4CnBmCY;CmBjCf;;AAGD;EAEE,0BnB4Bc;EmB3Bd,sBnB2Bc;CmB1Bf;;AAED;;EAGE,0BAhCqF;EAiCrF,uBAAsB;EACtB,sBAlC2H;CAoC5H;;ADkBD;ECdA,enB6Be;EmB5Bf,8BAA6B;EAC7B,uBAAsB;EACtB,sBnB0Be;CkBbd;;AjBlDC;EkBwCA,YDS4C;ECR5C,0BnBsBa;EmBrBb,sBnBqBa;CC/DQ;;AkB6CvB;EAEE,6CnBgBa;CmBfd;;AAED;EAEE,enBWa;EmBVb,8BAA6B;CAC9B;;AAED;;EAGE,YDV4C;ECW5C,0BnBGa;EmBFb,sBnBEa;CmBDd;;ADdD;ECdA,enBWgB;EmBVhB,8BAA6B;EAC7B,uBAAsB;EACtB,sBnBQgB;CkBKf;;AjBlDC;EkBwCA,YDS4C;ECR5C,0BnBIc;EmBHd,sBnBGc;CC7CO;;AkB6CvB;EAEE,+CnBFc;CmBGf;;AAED;EAEE,enBPc;EmBQd,8BAA6B;CAC9B;;AAED;;EAGE,YDV4C;ECW5C,0BnBfc;EmBgBd,sBnBhBc;CmBiBf;;ADdD;ECdA,enBoCe;EmBnCf,8BAA6B;EAC7B,uBAAsB;EACtB,sBnBiCe;CkBpBd;;AjBlDC;EkBwCA,YDS4C;ECR5C,0BnB6Ba;EmB5Bb,sBnB4Ba;CCtEQ;;AkB6CvB;EAEE,6CnBuBa;CmBtBd;;AAED;EAEE,enBkBa;EmBjBb,8BAA6B;CAC9B;;AAED;;EAGE,YDV4C;ECW5C,0BnBUa;EmBTb,sBnBSa;CmBRd;;ADdD;ECdA,enBsCe;EmBrCf,8BAA6B;EAC7B,uBAAsB;EACtB,sBnBmCe;CkBtBd;;AjBlDC;EkBwCA,YDS4C;ECR5C,0BnB+Ba;EmB9Bb,sBnB8Ba;CCxEQ;;AkB6CvB;EAEE,8CnByBa;CmBxBd;;AAED;EAEE,enBoBa;EmBnBb,8BAA6B;CAC9B;;AAED;;EAGE,YDV4C;ECW5C,0BnBYa;EmBXb,sBnBWa;CmBVd;;ADdD;ECdA,enBmCe;EmBlCf,8BAA6B;EAC7B,uBAAsB;EACtB,sBnBgCe;CkBnBd;;AjBlDC;EkBwCA,YDS4C;ECR5C,0BnB4Ba;EmB3Bb,sBnB2Ba;CCrEQ;;AkB6CvB;EAEE,6CnBsBa;CmBrBd;;AAED;EAEE,enBiBa;EmBhBb,8BAA6B;CAC9B;;AAED;;EAGE,YDV4C;ECW5C,0BnBSa;EmBRb,sBnBQa;CmBPd;;ADdD;ECdA,enBiCe;EmBhCf,8BAA6B;EAC7B,uBAAsB;EACtB,sBnB8Be;CkBjBd;;AjBlDC;EkBwCA,YDS4C;ECR5C,0BnB0Ba;EmBzBb,sBnByBa;CCnEQ;;AkB6CvB;EAEE,6CnBoBa;CmBnBd;;AAED;EAEE,enBea;EmBdb,8BAA6B;CAC9B;;AAED;;EAGE,YDV4C;ECW5C,0BnBOa;EmBNb,sBnBMa;CmBLd;;ADdD;ECdA,enBMgB;EmBLhB,8BAA6B;EAC7B,uBAAsB;EACtB,sBnBGgB;CkBUf;;AjBlDC;EkBwCA,YDS4C;ECR5C,0BnBDc;EmBEd,sBnBFc;CCxCO;;AkB6CvB;EAEE,+CnBPc;CmBQf;;AAED;EAEE,enBZc;EmBad,8BAA6B;CAC9B;;AAED;;EAGE,YDV4C;ECW5C,0BnBpBc;EmBqBd,sBnBrBc;CmBsBf;;ADdD;ECdA,enBagB;EmBZhB,8BAA6B;EAC7B,uBAAsB;EACtB,sBnBUgB;CkBGf;;AjBlDC;EkBwCA,YDS4C;ECR5C,0BnBMc;EmBLd,sBnBKc;CC/CO;;AkB6CvB;EAEE,4CnBAc;CmBCf;;AAED;EAEE,enBLc;EmBMd,8BAA6B;CAC9B;;AAED;;EAGE,YDV4C;ECW5C,0BnBbc;EmBcd,sBnBdc;CmBef;;ADHH;EACE,oBlB0KyB;EkBzKzB,elBEe;EkBDf,iBAAgB;CA8BjB;;AAjCD;EASI,8BAA6B;CAE9B;;AAXH;EAeI,0BAAyB;EACzB,iBAAgB;CACjB;;AjB5EC;EiB8EA,0BAAyB;CjB9EJ;;AAWrB;EiBsEA,elB0E4C;EkBzE5C,2BlB0E6B;EkBzE7B,8BAA6B;CjBrE5B;;AiB6CL;EA2BI,elBzCc;CkB8Cf;;AjBhFC;EiB8EE,sBAAqB;CjB3EtB;;AiBqFL;EChCE,qBnBgQ8B;EmB/P9B,mBnB+JsB;EmB9JtB,iBnBkI0B;EM9MxB,sBNoN0B;CkBxG7B;;AAED;ECpCE,wBnB4P+B;EmB3P/B,oBnBgKsB;EmB/JtB,iBnBmI0B;EM/MxB,sBNqN0B;CkBrG7B;;AAOD;EACE,eAAc;EACd,YAAW;CACZ;;AAGD;EACE,mBlBsNoC;CkBrNrC;;AAGD;;;EAII,YAAW;CACZ;;AE3IH;EACE,WAAU;EbIN,iCP4NsC;CoB1N3C;;AAPD;EAKI,WAAU;CACX;;AAGH;EACE,cAAa;CAId;;AALD;EAGI,eAAc;CACf;;AAGH;EAEI,mBAAkB;CACnB;;AAGH;EAEI,yBAAwB;CACzB;;AAGH;EACE,mBAAkB;EAClB,UAAS;EACT,iBAAgB;Eb1BZ,8BP6NmC;CoBjMxC;;AChCD;;EAEE,mBAAkB;CACnB;;AAED;EAGI,sBAAqB;EACrB,SAAQ;EACR,UAAS;EACT,qBAA+B;EAC/B,wBAAkC;EAClC,YAAW;EACX,wBAA8B;EAC9B,sCAA4C;EAC5C,qCAA2C;CAC5C;;AAZH;EAeI,eAAc;CACf;;AAKH;EAEI,cAAa;EACb,wBrB+coC;CqB9crC;;AAJH;EAQM,cAAa;EACb,2BAAiC;CAClC;;AAKL;EACE,mBAAkB;EAClB,UAAS;EACT,QAAO;EACP,crB0d8B;EqBzd9B,cAAa;EACb,YAAW;EACX,iBrB0boC;EqBzbpC,kBAA8B;EAC9B,qBAA4B;EAC5B,gBrByLmB;EqBxLnB,erBMgB;EqBLhB,iBAAgB;EAChB,iBAAgB;EAChB,uBrBNW;EqBOX,6BAA4B;EAC5B,sCrBEW;EMxDT,uBNmN2B;CqB1J9B;;AAGD;EC3DE,UAAS;EACT,iBAAuB;EACvB,iBAAgB;EAChB,8BtB4CgB;CqBcjB;;AAKD;EACE,eAAc;EACd,YAAW;EACX,wBrBobqC;EqBnbrC,YAAW;EACX,oBrBqKyB;EqBpKzB,erBlBgB;EqBmBhB,oBAAmB;EACnB,oBAAmB;EACnB,iBAAgB;EAChB,UAAS;CAwBV;;ApBnFG;EoB8DA,erBiakD;EqBhalD,sBAAqB;EACrB,0BrBnCc;CC1Bb;;AoB8CL;EAoBI,YrBzCS;EqB0CT,sBAAqB;EACrB,0BrBnBa;CqBoBd;;AAvBH;EA2BI,erB1Cc;EqB2Cd,8BAA6B;CAK9B;;AAIH;EAGI,WAAU;CACX;;AAGH;EACE,eAAc;CACf;;AAGD;EACE,eAAc;EACd,uBrBoYqC;EqBnYrC,iBAAgB;EAChB,oBrBmHsB;EqBlHtB,erBrEgB;EqBsEhB,oBAAmB;CACpB;;AE5HD;;EAEE,mBAAkB;EAClB,4BAAoB;EAApB,qBAAoB;EACpB,uBAAsB;CA0BvB;;AA9BD;;EAOI,mBAAkB;EAClB,mBAAc;MAAd,eAAc;EACd,iBAAgB;CAYjB;;AArBH;;EAcM,WAAU;CtBNS;;AsBRzB;;;;EAmBM,WAAU;CACX;;AApBL;;;;;;;;EA4BI,kBvBsLc;CuBrLf;;AAIH;EACE,qBAAa;EAAb,cAAa;EACb,oBAAe;MAAf,gBAAe;EACf,qBAA2B;MAA3B,4BAA2B;CAK5B;;AARD;EAMI,YAAW;CACZ;;AAGH;EACE,iBAAgB;CACjB;;AAGD;EACE,eAAc;CAKf;;AAND;EjBlCI,2BiBsC8B;EjBrC9B,8BiBqC8B;CAC/B;;AAGH;;EjB5BI,0BiB8B2B;EjB7B3B,6BiB6B2B;CAC9B;;AAGD;EACE,YAAW;CACZ;;AACD;EACE,iBAAgB;CACjB;;AACD;;EjBtDI,2BiByD8B;EjBxD9B,8BiBwD8B;CAC/B;;AAEH;EjB9CI,0BiB+C2B;EjB9C3B,6BiB8C2B;CAC9B;;AAeD;EACE,yBAAyC;EACzC,wBAAwC;CAKzC;;AAPD;EAKI,eAAc;CACf;;AAGH;EACE,wBAA4C;EAC5C,uBAA2C;CAC5C;;AAED;EACE,uBAA4C;EAC5C,sBAA2C;CAC5C;;AAmBD;EACE,4BAAoB;EAApB,qBAAoB;EACpB,2BAAsB;MAAtB,uBAAsB;EACtB,sBAAuB;MAAvB,wBAAuB;EACvB,sBAAuB;MAAvB,wBAAuB;CAcxB;;AAlBD;;EAQI,YAAW;CACZ;;AATH;;;;EAeI,iBvBoEc;EuBnEd,eAAc;CACf;;AAGH;EAEI,iBAAgB;CACjB;;AAHH;EjB9HI,8BiBmI+B;EjBlI/B,6BiBkI+B;CAChC;;AANH;EjB5II,0BiBoJ4B;EjBnJ5B,2BiBmJ4B;CAC7B;;AAEH;EACE,iBAAgB;CACjB;;AACD;;EjB5II,8BiB+I+B;EjB9I/B,6BiB8I+B;CAChC;;AAEH;EjBhKI,0BiBiK0B;EjBhK1B,2BiBgK0B;CAC7B;;AzBu5ED;;;;EyBn4EM,mBAAkB;EAClB,uBAAmB;EACnB,qBAAoB;CACrB;;AC/LL;EACE,mBAAkB;EAClB,qBAAa;EAAb,cAAa;EACb,YAAW;CAkBZ;;AArBD;EAQI,mBAAkB;EAClB,WAAU;EACV,mBAAc;MAAd,eAAc;EAGd,UAAS;EACT,iBAAgB;CAMjB;;AApBH;EAkBM,WAAU;CvBmCX;;AuB9BL;;;EAIE,qBAAa;EAAb,cAAa;EACb,uBAAmB;MAAnB,oBAAmB;CAKpB;;AAVD;;;ElBvBI,iBkB+BwB;CACzB;;AAGH;;EAEE,oBAAmB;EACnB,uBAAsB;CACvB;;AAwBD;EACE,wBxBkQgC;EwBjQhC,iBAAgB;EAChB,gBxBuKmB;EwBtKnB,oBxB0KyB;EwBzKzB,kBxB+P8B;EwB9P9B,exBhBgB;EwBiBhB,mBAAkB;EAClB,0BxBvBgB;EwBwBhB,sCxBhBW;EMxDT,uBNmN2B;CwBpH9B;;AAhCD;;;EAcI,wBxByP6B;EwBxP7B,oBxB6JoB;EM3OpB,sBNqN0B;CwBrI3B;;AAjBH;;;EAoBI,qBxBuP4B;EwBtP5B,mBxBsJoB;EM1OpB,sBNoN0B;CwB9H3B;;AAvBH;;EA6BI,cAAa;CACd;;AASH;;;;;;;ElBzFI,2BkBgG4B;ElB/F5B,8BkB+F4B;CAC/B;;AACD;EACE,gBAAe;CAChB;;AACD;;;;;;;ElBvFI,0BkB8F2B;ElB7F3B,6BkB6F2B;CAC9B;;AACD;EACE,eAAc;CACf;;AAMD;EACE,mBAAkB;EAGlB,aAAY;EACZ,oBAAmB;CAmCpB;;AAxCD;EAUI,mBAAkB;CAUnB;;AApBH;EAaM,kBxBiEY;CwBhEb;;AAdL;EAkBM,WAAU;CvBhGX;;AuB8EL;;EA0BM,mBxBoDY;CwBnDb;;AA3BL;;EAgCM,WAAU;EACV,kBxB6CY;CwBxCb;;AAtCL;;;;EAoCQ,WAAU;CvBlHb;;AwB9CL;EACE,mBAAkB;EAClB,4BAAoB;EAApB,qBAAoB;EACpB,mBAAsC;EACtC,qBzBmY8B;EyBlY9B,mBzBoY4B;CyBnY7B;;AAED;EACE,mBAAkB;EAClB,YAAW;EACX,WAAU;CA4BX;;AA/BD;EAMI,YzByBS;EyBxBT,0BzBgDa;CyB9Cd;;AATH;EAaI,8CzB0Ca;CyBzCd;;AAdH;EAiBI,YzBcS;EyBbT,0BzBgY6E;CyB9X9E;;AApBH;EAwBM,0BzBSY;CyBRb;;AAzBL;EA4BM,ezBSY;CyBRb;;AAQL;EACE,mBAAkB;EAClB,aAA+D;EAC/D,QAAO;EACP,eAAc;EACd,YzByVwC;EyBxVxC,azBwVwC;EyBvVxC,qBAAoB;EACpB,0BAAiB;KAAjB,uBAAiB;MAAjB,sBAAiB;UAAjB,kBAAiB;EACjB,uBzBsVwC;EyBrVxC,6BAA4B;EAC5B,mCAAkC;EAClC,yBzBoV2C;CyBlV5C;;AAMD;EnBxEI,uBNmN2B;CyBxI5B;;AAHH;EAMI,2NVtCuI;CUuCxI;;AAPH;EAUI,0BzBZa;EyBab,wKV3CuI;CU6CxI;;AAOH;EAEI,mBzB8UsC;CyB7UvC;;AAHH;EAMI,qKV1DuI;CU2DxI;;AASH;EACE,qBAAa;EAAb,cAAa;EACb,2BAAsB;MAAtB,uBAAsB;CASvB;;AAXD;EAKI,uBzB8R4B;CyBzR7B;;AAVH;EAQM,eAAc;CACf;;AAWL;EACE,sBAAqB;EACrB,gBAAe;EACf,4BzBmPoF;EyBlPpF,2CzB4SuC;EyB3SvC,kBzB8L8B;EyB7L9B,ezBjFgB;EyBkFhB,uBAAsB;EACtB,oNAAsG;EACtG,0BzB+SoC;EyB9SpC,sCzBlFW;EyBoFT,uBzBuE2B;EyBnE7B,yBAAgB;KAAhB,sBAAgB;UAAhB,iBAAgB;CA2BjB;;AA3CD;EAmBI,sBzB4SmE;EyB3SnE,cAAa;CAYd;;AAhCH;EA6BM,ezBxGY;EyByGZ,uBzBhHO;CyBiHR;;AA/BL;EAmCI,ezB/Gc;EyBgHd,0BzBpHc;CyBqHf;;AArCH;EAyCI,WAAU;CACX;;AAGH;EACE,8BzB2MuF;EyB1MvF,sBzBgQwC;EyB/PxC,yBzB+PwC;EyB9PxC,ezBiR+B;CyBhRhC;;AAOD;EACE,mBAAkB;EAClB,sBAAqB;EACrB,gBAAe;EACf,ezBwQmC;EyBvQnC,iBAAgB;CACjB;;AAED;EACE,iBzBoQkC;EyBnQlC,gBAAe;EACf,ezBiQmC;EyBhQnC,UAAS;EACT,WAAU;CAKX;;AAED;EACE,mBAAkB;EAClB,OAAM;EACN,SAAQ;EACR,QAAO;EACP,WAAU;EACV,ezBkPmC;EyBjPnC,qBzBqP8B;EyBpP9B,iBzBsP6B;EyBrP7B,ezBjKgB;EyBkKhB,qBAAoB;EACpB,0BAAiB;KAAjB,uBAAiB;MAAjB,sBAAiB;UAAjB,kBAAiB;EACjB,uBzB3KW;EyB4KX,sCzBlKW;EMxDT,uBNmN2B;CyBsC9B;;AA5CD;EAmBM,0BzBsPkB;CyBrPnB;;AApBL;EAwBI,mBAAkB;EAClB,UzBrBc;EyBsBd,YzBtBc;EyBuBd,azBvBc;EyBwBd,WAAU;EACV,eAAc;EACd,ezB0NiC;EyBzNjC,qBzB6N4B;EyB5N5B,iBzB8N2B;EyB7N3B,ezBzLc;EyB0Ld,0BzB/Lc;EyBgMd,sCzBxLS;EMxDT,mCmBiPgF;CACjF;;AArCH;EAyCM,kBzBmOU;CyBlOX;;ACtPL;EACE,qBAAa;EAAb,cAAa;EACb,oBAAe;MAAf,gBAAe;EACf,gBAAe;EACf,iBAAgB;EAChB,iBAAgB;CACjB;;AAED;EACE,eAAc;EACd,qB1BogBkC;C0B1fnC;;AzBHG;EyBJA,sBAAqB;CzBOpB;;AyBZL;EAUI,e1BiCc;C0BhCf;;AAOH;EACE,8B1BsfgD;C0BpdjD;;AAnCD;EAII,oB1BkLc;C0BjLf;;AALH;EAQI,8BAAgD;EpB7BhD,gCN6M2B;EM5M3B,iCN4M2B;C0BpK5B;;AApBH;EAYM,mC1B2e4C;CC7f7C;;AyBML;EAgBM,e1BSY;E0BRZ,8BAA6B;EAC7B,0BAAyB;CAC1B;;AAnBL;;EAwBI,e1BEc;E0BDd,uB1BNS;E0BOT,6B1BPS;C0BQV;;AA3BH;EA+BI,iB1BuJc;EM3Md,0BoBsD4B;EpBrD5B,2BoBqD4B;CAC7B;;AAQH;EpBrEI,uBNmN2B;C0BrI5B;;AATH;;EAMM,Y1B7BO;E0B8BP,0B1BNW;C0BOZ;;AASL;EAEI,mBAAc;MAAd,eAAc;EACd,mBAAkB;CACnB;;AAGH;EAEI,2BAAa;MAAb,cAAa;EACb,qBAAY;MAAZ,aAAY;EACZ,mBAAkB;CACnB;;AAQH;EAEI,cAAa;CACd;;AAHH;EAKI,eAAc;CACf;;ACnGH;EACE,mBAAkB;EAClB,qBAAa;EAAb,cAAa;EACb,oBAAe;MAAf,gBAAe;EACf,uBAAmB;MAAnB,oBAAmB;EACnB,uBAA8B;MAA9B,+BAA8B;EAC9B,qB3BgHW;C2BrGZ;;AAjBD;;EAYI,qBAAa;EAAb,cAAa;EACb,oBAAe;MAAf,gBAAe;EACf,uBAAmB;MAAnB,oBAAmB;EACnB,uBAA8B;MAA9B,+BAA8B;CAC/B;;AAQH;EACE,sBAAqB;EACrB,uB3BggB+E;E2B/f/E,0B3B+f+E;E2B9f/E,mB3B0FW;E2BzFX,mB3BgMsB;E2B/LtB,qBAAoB;EACpB,oBAAmB;CAKpB;;A1B/BG;E0B6BA,sBAAqB;C1B1BpB;;A0BmCL;EACE,qBAAa;EAAb,cAAa;EACb,2BAAsB;MAAtB,uBAAsB;EACtB,gBAAe;EACf,iBAAgB;EAChB,iBAAgB;CAWjB;;AAhBD;EAQI,iBAAgB;EAChB,gBAAe;CAChB;;AAVH;EAaI,iBAAgB;EAChB,YAAW;CACZ;;AAQH;EACE,sBAAqB;EACrB,oB3B6bmC;E2B5bnC,uB3B4bmC;C2B3bpC;;AAWD;EACE,8BAAgB;MAAhB,iBAAgB;EAGhB,uBAAmB;MAAnB,oBAAmB;CACpB;;AAGD;EACE,yB3BmcyC;E2BlczC,mB3BkIsB;E2BjItB,eAAc;EACd,wBAAuB;EACvB,8BAAuC;ErB3GrC,uBNmN2B;C2BlG9B;;A1B/FG;E0B6FA,sBAAqB;C1B1FpB;;A0BgGL;EACE,sBAAqB;EACrB,aAAY;EACZ,cAAa;EACb,uBAAsB;EACtB,YAAW;EACX,oCAAmC;EACnC,2BAA0B;CAC3B;;AhB5DG;EgBqEA;;IAIM,iBAAgB;IAChB,gBAAe;GAChB;C7B46FR;;AapgGG;EgBkFA;IAUI,wBAAmB;QAAnB,oBAAmB;IACnB,sBAAiB;QAAjB,kBAAiB;IACjB,qBAA2B;QAA3B,4BAA2B;GAoC9B;EAhDD;IAeM,wBAAmB;QAAnB,oBAAmB;GAepB;EA9BL;IAkBQ,mBAAkB;GACnB;EAnBP;IAsBQ,SAAQ;IACR,WAAU;GACX;EAxBP;IA2BQ,qBAAoB;IACpB,oBAAmB;GACpB;EA7BP;;IAmCM,sBAAiB;QAAjB,kBAAiB;GAClB;EApCL;IAwCM,gCAAwB;IAAxB,yBAAwB;GACzB;EAzCL;IA6CM,cAAa;GACd;C7Bo6FR;;AavhGG;EgBqEA;;IAIM,iBAAgB;IAChB,gBAAe;GAChB;C7Bo9FR;;Aa5iGG;EgBkFA;IAUI,wBAAmB;QAAnB,oBAAmB;IACnB,sBAAiB;QAAjB,kBAAiB;IACjB,qBAA2B;QAA3B,4BAA2B;GAoC9B;EAhDD;IAeM,wBAAmB;QAAnB,oBAAmB;GAepB;EA9BL;IAkBQ,mBAAkB;GACnB;EAnBP;IAsBQ,SAAQ;IACR,WAAU;GACX;EAxBP;IA2BQ,qBAAoB;IACpB,oBAAmB;GACpB;EA7BP;;IAmCM,sBAAiB;QAAjB,kBAAiB;GAClB;EApCL;IAwCM,gCAAwB;IAAxB,yBAAwB;GACzB;EAzCL;IA6CM,cAAa;GACd;C7B48FR;;Aa/jGG;EgBqEA;;IAIM,iBAAgB;IAChB,gBAAe;GAChB;C7B4/FR;;AaplGG;EgBkFA;IAUI,wBAAmB;QAAnB,oBAAmB;IACnB,sBAAiB;QAAjB,kBAAiB;IACjB,qBAA2B;QAA3B,4BAA2B;GAoC9B;EAhDD;IAeM,wBAAmB;QAAnB,oBAAmB;GAepB;EA9BL;IAkBQ,mBAAkB;GACnB;EAnBP;IAsBQ,SAAQ;IACR,WAAU;GACX;EAxBP;IA2BQ,qBAAoB;IACpB,oBAAmB;GACpB;EA7BP;;IAmCM,sBAAiB;QAAjB,kBAAiB;GAClB;EApCL;IAwCM,gCAAwB;IAAxB,yBAAwB;GACzB;EAzCL;IA6CM,cAAa;GACd;C7Bo/FR;;AavmGG;EgBqEA;;IAIM,iBAAgB;IAChB,gBAAe;GAChB;C7BoiGR;;Aa5nGG;EgBkFA;IAUI,wBAAmB;QAAnB,oBAAmB;IACnB,sBAAiB;QAAjB,kBAAiB;IACjB,qBAA2B;QAA3B,4BAA2B;GAoC9B;EAhDD;IAeM,wBAAmB;QAAnB,oBAAmB;GAepB;EA9BL;IAkBQ,mBAAkB;GACnB;EAnBP;IAsBQ,SAAQ;IACR,WAAU;GACX;EAxBP;IA2BQ,qBAAoB;IACpB,oBAAmB;GACpB;EA7BP;;IAmCM,sBAAiB;QAAjB,kBAAiB;GAClB;EApCL;IAwCM,gCAAwB;IAAxB,yBAAwB;GACzB;EAzCL;IA6CM,cAAa;GACd;C7B4hGR;;A6B/kGD;EAeQ,wBAAmB;MAAnB,oBAAmB;EACnB,sBAAiB;MAAjB,kBAAiB;EACjB,qBAA2B;MAA3B,4BAA2B;CAoC9B;;AArDL;;EASU,iBAAgB;EAChB,gBAAe;CAChB;;AAXT;EAoBU,wBAAmB;MAAnB,oBAAmB;CAepB;;AAnCT;EAuBY,mBAAkB;CACnB;;AAxBX;EA2BY,SAAQ;EACR,WAAU;CACX;;AA7BX;EAgCY,qBAAoB;EACpB,oBAAmB;CACpB;;AAlCX;;EAwCU,sBAAiB;MAAjB,kBAAiB;CAClB;;AAzCT;EA6CU,gCAAwB;EAAxB,yBAAwB;CACzB;;AA9CT;EAkDU,cAAa;CACd;;AAYT;EAEI,0B3B1IS;C2B+IV;;AAPH;EAKM,0B3B7IO;CCnCR;;A0B2KL;EAWM,0B3BnJO;C2B4JR;;AApBL;EAcQ,0B3BtJK;CCnCR;;A0B2KL;EAkBQ,0B3B1JK;C2B2JN;;AAnBP;;;;EA0BM,0B3BlKO;C2BmKR;;AA3BL;EA+BI,0B3BvKS;E2BwKT,iC3BxKS;C2ByKV;;AAjCH;EAoCI,sQ3BqV8R;C2BpV/R;;AArCH;EAwCI,0B3BhLS;C2BiLV;;AAIH;EAEI,a3BjMS;C2BsMV;;AAPH;EAKM,a3BpMO;CCzBR;;A0BwNL;EAWM,gC3B1MO;C2BmNR;;AApBL;EAcQ,iC3B7MK;CCzBR;;A0BwNL;EAkBQ,iC3BjNK;C2BkNN;;AAnBP;;;;EA0BM,a3BzNO;C2B0NR;;AA3BL;EA+BI,gC3B9NS;E2B+NT,uC3B/NS;C2BgOV;;AAjCH;EAoCI,4Q3BiS4R;C2BhS7R;;AArCH;EAwCI,gC3BvOS;C2BwOV;;ACtRH;EACE,mBAAkB;EAClB,qBAAa;EAAb,cAAa;EACb,2BAAsB;MAAtB,uBAAsB;EACtB,aAAY;EACZ,sBAAqB;EACrB,uB5BwCW;E4BvCX,4BAA2B;EAC3B,uC5BgDW;EMxDT,uBNmN2B;C4BzM9B;;AAED;EAGE,mBAAc;MAAd,eAAc;EACd,iB5BilBgC;C4BhlBjC;;AAED;EACE,uB5B4kB+B;C4B3kBhC;;AAED;EACE,sBAAgC;EAChC,iBAAgB;CACjB;;AAED;EACE,iBAAgB;CACjB;;A3BvBG;E2B2BA,sBAAqB;C3B3BA;;A2ByBzB;EAMI,qB5B2jB8B;C4B1jB/B;;AAGH;EtBpCI,gCN6M2B;EM5M3B,iCN4M2B;C4BrK1B;;AAJL;EtBtBI,oCN+L2B;EM9L3B,mCN8L2B;C4B/J1B;;AASL;EACE,yB5BmiBgC;E4BliBhC,iBAAgB;EAChB,sC5BRW;E4BSX,8C5BTW;C4BcZ;;AATD;EtB7DI,2DsBoE8E;CAC/E;;AAGH;EACE,yB5BwhBgC;E4BvhBhC,sC5BlBW;E4BmBX,2C5BnBW;C4BwBZ;;AARD;EtBxEI,2DNqmB2E;C4BthB5E;;AAQH;EACE,wBAAkC;EAClC,wB5BugB+B;E4BtgB/B,uBAAiC;EACjC,iBAAgB;CACjB;;AAED;EACE,wBAAkC;EAClC,uBAAiC;CAClC;;AAGD;EACE,mBAAkB;EAClB,OAAM;EACN,SAAQ;EACR,UAAS;EACT,QAAO;EACP,iB5B+fgC;C4B9fjC;;AAED;EACE,YAAW;EtB9GT,mCNqmB2E;C4Brf9E;;AAGD;EACE,YAAW;EtB9GT,4CN+lB2E;EM9lB3E,6CN8lB2E;C4B/e9E;;AAED;EACE,YAAW;EtBrGT,gDNilB2E;EMhlB3E,+CNglB2E;C4B1e9E;;AjBvEG;EiB6EF;IACE,qBAAa;IAAb,cAAa;IACb,wBAAmB;QAAnB,oBAAmB;IACnB,oB5BuegD;I4BtehD,mB5BsegD;G4B7djD;EAbD;IAOI,qBAAa;IAAb,cAAa;IACb,iBAAY;QAAZ,aAAY;IACZ,2BAAsB;QAAtB,uBAAsB;IACtB,mB5Bge8C;I4B/d9C,kB5B+d8C;G4B9d/C;C9ByzGJ;;Aal5GG;EiBmGF;IACE,qBAAa;IAAb,cAAa;IACb,wBAAmB;QAAnB,oBAAmB;GA2CpB;EA7CD;IAKI,iBAAY;QAAZ,aAAY;GAuCb;EA5CH;IAQM,eAAc;IACd,eAAc;GACf;EAVL;ItB1IE,2BsByJoC;ItBxJpC,8BsBwJoC;GAQ/B;EAvBP;IAkBU,2BAA0B;GAC3B;EAnBT;IAqBU,8BAA6B;GAC9B;EAtBT;ItB5HE,0BsBqJmC;ItBpJnC,6BsBoJmC;GAQ9B;EAjCP;IA4BU,0BAAyB;GAC1B;EA7BT;IA+BU,6BAA4B;GAC7B;EAhCT;IAoCQ,iBAAgB;GAMjB;EA1CP;;IAwCU,iBAAgB;GACjB;C9B+yGV;;A8BnyGD;EAEI,uB5BkZ6B;C4BjZ9B;;AjB3JC;EiBwJJ;IAMI,wB5B2ZyB;Y4B3ZzB,gB5B2ZyB;I4B1ZzB,4B5B2Z+B;Y4B3Z/B,oB5B2Z+B;G4BpZlC;EAdD;IAUM,sBAAqB;IACrB,YAAW;GACZ;C9BsyGJ;;A+BlgHD;EACE,sB7BixBkC;E6BhxBlC,oBAAmB;EACnB,iBAAgB;EAChB,0B7BgDgB;EMhDd,uBNmN2B;C6BhN9B;;ACNC;EACE,eAAc;EACd,YAAW;EACX,YAAW;CACZ;;ADIH;EACE,YAAW;CA2BZ;;AA5BD;EAKI,sBAAqB;EACrB,sB7BowBiC;E6BnwBjC,qB7BmwBiC;E6BlwBjC,e7BuCc;E6BtCd,aAAiC;CAClC;;AAVH;EAmBI,2BAA0B;CAC3B;;AApBH;EAsBI,sBAAqB;CACtB;;AAvBH;EA0BI,e7BqBc;C6BpBf;;AEpCH;EACE,qBAAa;EAAb,cAAa;EAEb,gBAAe;EACf,iBAAgB;EzBAd,uBNmN2B;C+BjN9B;;AAED;EAGM,eAAc;EzBoBhB,gCNwL2B;EMvL3B,mCNuL2B;C+B1M1B;;AALL;EzBSI,iCNsM2B;EMrM3B,oCNqM2B;C+BrM1B;;AAVL;EAcI,WAAU;EACV,Y/B2BS;E+B1BT,0B/BkDa;E+BjDb,sB/BiDa;C+BhDd;;AAlBH;EAqBI,e/B2Bc;E+B1Bd,qBAAoB;EACpB,uB/BmBS;E+BlBT,mB/ByjBuC;C+BxjBxC;;AAGH;EACE,mBAAkB;EAClB,eAAc;EACd,wB/B4hB0C;E+B3hB1C,kBAAiB;EACjB,kB/B+hBwC;E+B9hBxC,e/BgCe;E+B/Bf,uB/BOW;E+BNX,uB/BiiByC;C+BzhB1C;;A9B9BG;E8ByBA,e/BuH4C;E+BtH5C,sBAAqB;EACrB,0B/BGc;E+BFd,mB/B+hBuC;CCxjBtC;;A+BtBH;EACE,wBhCmkBwC;EgClkBxC,mBhCyOoB;EgCxOpB,iBhC4MwB;CgC3MzB;;AAIG;E1BoBF,+BNyL0B;EMxL1B,kCNwL0B;CgC3MvB;;AAGD;E1BCF,gCNuM0B;EMtM1B,mCNsM0B;CgCtMvB;;AAfL;EACE,wBhCikBuC;EgChkBvC,oBhC0OoB;EgCzOpB,iBhC6MwB;CgC5MzB;;AAIG;E1BoBF,+BN0L0B;EMzL1B,kCNyL0B;CgC5MvB;;AAGD;E1BCF,gCNwM0B;EMvM1B,mCNuM0B;CgCvMvB;;ACbP;EACE,sBAAqB;EACrB,sBjC+pBgC;EiC9pBhC,ejC2pB+B;EiC1pB/B,kBjCyOqB;EiCxOrB,eAAc;EACd,YjCuCW;EiCtCX,mBAAkB;EAClB,oBAAmB;EACnB,yBAAwB;E3BVtB,uBNmN2B;CiClM9B;;AAhBD;EAcI,cAAa;CACd;;AAIH;EACE,mBAAkB;EAClB,UAAS;CACV;;AAMD;EACE,qBjCsoBgC;EiCroBhC,oBjCqoBgC;EMpqB9B,qBNuqB+B;CiCtoBlC;;AAOC;ElBiBE,YAAW;EmB3Db,0BlCwEe;CiC5Bd;;AhCxBC;EcuCA,YAAW;EmBtDT,sBAAqB;EACrB,0BAAkC;CjCiBnC;;AgCmBH;ElBiBE,YAAW;EmB3Db,0BlCsDgB;CiCVf;;AhCxBC;EcuCA,YAAW;EmBtDT,sBAAqB;EACrB,0BAAkC;CjCiBnC;;AgCmBH;ElBiBE,YAAW;EmB3Db,0BlC+Ee;CiCnCd;;AhCxBC;EcuCA,YAAW;EmBtDT,sBAAqB;EACrB,0BAAkC;CjCiBnC;;AgCmBH;ElBiBE,YAAW;EmB3Db,0BlCiFe;CiCrCd;;AhCxBC;EcuCA,YAAW;EmBtDT,sBAAqB;EACrB,0BAAkC;CjCiBnC;;AgCmBH;ElBeE,YAAW;EmBzDb,0BlC8Ee;CiClCd;;AhCxBC;EcqCA,YAAW;EmBpDT,sBAAqB;EACrB,0BAAkC;CjCiBnC;;AgCmBH;ElBiBE,YAAW;EmB3Db,0BlC4Ee;CiChCd;;AhCxBC;EcuCA,YAAW;EmBtDT,sBAAqB;EACrB,0BAAkC;CjCiBnC;;AgCmBH;ElBeE,YAAW;EmBzDb,0BlCiDgB;CiCLf;;AhCxBC;EcqCA,YAAW;EmBpDT,sBAAqB;EACrB,0BAAkC;CjCiBnC;;AgCmBH;ElBiBE,YAAW;EmB3Db,0BlCwDgB;CiCZf;;AhCxBC;EcuCA,YAAW;EmBtDT,sBAAqB;EACrB,0BAAkC;CjCiBnC;;AkCzBL;EACE,mBAAoD;EACpD,oBnC4lBmC;EmC3lBnC,0BnCiDgB;EMhDd,sBNoN0B;CmC/M7B;;AxB+CG;EwBxDJ;IAOI,mBnCulBiC;GmCrlBpC;CrCkvHA;;AqChvHD;EACE,iBAAgB;EAChB,gBAAe;E7BTb,iB6BUsB;CACzB;;ACXD;EACE,yBpC6sBmC;EoC5sBnC,oBpC6sBgC;EoC5sBhC,8BAA6C;E9BH3C,uBNmN2B;CoC9M9B;;AAGD;EAEE,eAAc;CACf;;AAGD;EACE,kBpC+NqB;CoC9NtB;;AAOD;EAGI,mBAAkB;EAClB,cpCkrBgC;EoCjrBhC,gBpCkrBiC;EoCjrBjC,yBpCirBiC;EoChrBjC,eAAc;CACf;;AASD;EC3CA,etBsFkE;EsBrFlE,0BtBmFuE;EsBlFvE,sBtBkFuE;CqBvCtE;;ACzCD;EACE,0BAAqC;CACtC;;AAED;EACE,eAA0B;CAC3B;;ADiCD;EC3CA,etBsFkE;EsBrFlE,0BtBmFuE;EsBlFvE,sBtBkFuE;CqBvCtE;;ACzCD;EACE,0BAAqC;CACtC;;AAED;EACE,eAA0B;CAC3B;;ADiCD;EC3CA,etBsFkE;EsBrFlE,0BtBmFuE;EsBlFvE,sBtBkFuE;CqBvCtE;;ACzCD;EACE,0BAAqC;CACtC;;AAED;EACE,eAA0B;CAC3B;;ADiCD;EC3CA,etBsFkE;EsBrFlE,0BtBmFuE;EsBlFvE,sBtBkFuE;CqBvCtE;;ACzCD;EACE,0BAAqC;CACtC;;AAED;EACE,eAA0B;CAC3B;;ADiCD;EC3CA,etBsFkE;EsBrFlE,0BtBmFuE;EsBlFvE,sBtBkFuE;CqBvCtE;;ACzCD;EACE,0BAAqC;CACtC;;AAED;EACE,eAA0B;CAC3B;;ADiCD;EC3CA,etBsFkE;EsBrFlE,0BtBmFuE;EsBlFvE,sBtBkFuE;CqBvCtE;;ACzCD;EACE,0BAAqC;CACtC;;AAED;EACE,eAA0B;CAC3B;;ADiCD;EC3CA,etBsFkE;EsBrFlE,0BtBmFuE;EsBlFvE,sBtBkFuE;CqBvCtE;;ACzCD;EACE,0BAAqC;CACtC;;AAED;EACE,eAA0B;CAC3B;;ADiCD;EC3CA,etBsFkE;EsBrFlE,0BtBmFuE;EsBlFvE,sBtBkFuE;CqBvCtE;;ACzCD;EACE,0BAAqC;CACtC;;AAED;EACE,eAA0B;CAC3B;;ACXH;EACE;IAAO,4BAAuC;GxC44H7C;EwC34HD;IAAK,yBAAwB;GxC84H5B;CACF;;AwCj5HD;EACE;IAAO,4BAAuC;GxC44H7C;EwC34HD;IAAK,yBAAwB;GxC84H5B;CACF;;AwC54HD;EACE,qBAAa;EAAb,cAAa;EACb,iBAAgB;EAChB,mBtCotBoC;EsCntBpC,kBtCktBkC;EsCjtBlC,mBAAkB;EAClB,0BtCyCgB;EMhDd,uBNmN2B;CsCzM9B;;AAED;EACE,atC0sBkC;EsCzsBlC,kBtCysBkC;EsCxsBlC,YtC+BW;EsC9BX,0BtCsDe;EOrEX,4BP8tBwC;CsC7sB7C;;AAED;ECWE,sMAA6I;EDT7I,2BtCisBkC;CsChsBnC;;AAED;EACE,2DtCosBgD;UsCpsBhD,mDtCosBgD;CsCnsBjD;;AE/BD;EACE,qBAAa;EAAb,cAAa;EACb,sBAAuB;MAAvB,wBAAuB;CACxB;;AAED;EACE,YAAO;MAAP,QAAO;CACR;;ACHD;EACE,qBAAa;EAAb,cAAa;EACb,2BAAsB;MAAtB,uBAAsB;EAGtB,gBAAe;EACf,iBAAgB;CACjB;;AAQD;EACE,YAAW;EACX,ezCoCgB;EyCnChB,oBAAmB;CAapB;;AxCbG;EwCIA,ezC+Bc;EyC9Bd,sBAAqB;EACrB,0BzCuBc;CC1Bb;;AwCNL;EAaI,ezC2Bc;EyC1Bd,0BzCmBc;CyClBf;;AAQH;EACE,mBAAkB;EAClB,eAAc;EACd,yBzCgsBsC;EyC9rBtC,oBzCsKgB;EyCrKhB,uBzCEW;EyCDX,uCzCWW;CyCiBZ;;AAnCD;EnChCI,gCN6M2B;EM5M3B,iCN4M2B;CyClK5B;;AAXH;EAcI,iBAAgB;EnChChB,oCN+L2B;EM9L3B,mCN8L2B;CyC7J5B;;AxCpCC;EwCuCA,sBAAqB;CxCpCpB;;AwCiBL;EAwBI,ezCVc;EyCWd,uBzCjBS;CyCkBV;;AA1BH;EA8BI,WAAU;EACV,YzCvBS;EyCwBT,0BzCAa;EyCCb,sBzCDa;CyCEd;;AASH;EAEI,gBAAe;EACf,eAAc;EACd,iBAAgB;CACjB;;AALH;EASM,cAAa;CACd;;AAVL;EAeM,iBAAgB;CACjB;;AClGH;EACE,e3BmFgE;E2BlFhE,0B3BgFqE;C2B/EtE;;AAGD;;EAEE,e3B4EgE;C2BhEjE;;AzCDC;;;EyCRE,e3ByE8D;E2BxE9D,0BAAyC;CzCU1C;;AyChBH;;EAUI,YAAW;EACX,0B3BmE8D;E2BlE9D,sB3BkE8D;C2BjE/D;;AAnBH;EACE,e3BmFgE;E2BlFhE,0B3BgFqE;C2B/EtE;;AAGD;;EAEE,e3B4EgE;C2BhEjE;;AzCDC;;;EyCRE,e3ByE8D;E2BxE9D,0BAAyC;CzCU1C;;AyChBH;;EAUI,YAAW;EACX,0B3BmE8D;E2BlE9D,sB3BkE8D;C2BjE/D;;AAnBH;EACE,e3BmFgE;E2BlFhE,0B3BgFqE;C2B/EtE;;AAGD;;EAEE,e3B4EgE;C2BhEjE;;AzCDC;;;EyCRE,e3ByE8D;E2BxE9D,0BAAyC;CzCU1C;;AyChBH;;EAUI,YAAW;EACX,0B3BmE8D;E2BlE9D,sB3BkE8D;C2BjE/D;;AAnBH;EACE,e3BmFgE;E2BlFhE,0B3BgFqE;C2B/EtE;;AAGD;;EAEE,e3B4EgE;C2BhEjE;;AzCDC;;;EyCRE,e3ByE8D;E2BxE9D,0BAAyC;CzCU1C;;AyChBH;;EAUI,YAAW;EACX,0B3BmE8D;E2BlE9D,sB3BkE8D;C2BjE/D;;AAnBH;EACE,e3BmFgE;E2BlFhE,0B3BgFqE;C2B/EtE;;AAGD;;EAEE,e3B4EgE;C2BhEjE;;AzCDC;;;EyCRE,e3ByE8D;E2BxE9D,0BAAyC;CzCU1C;;AyChBH;;EAUI,YAAW;EACX,0B3BmE8D;E2BlE9D,sB3BkE8D;C2BjE/D;;AAnBH;EACE,e3BmFgE;E2BlFhE,0B3BgFqE;C2B/EtE;;AAGD;;EAEE,e3B4EgE;C2BhEjE;;AzCDC;;;EyCRE,e3ByE8D;E2BxE9D,0BAAyC;CzCU1C;;AyChBH;;EAUI,YAAW;EACX,0B3BmE8D;E2BlE9D,sB3BkE8D;C2BjE/D;;AAnBH;EACE,e3BmFgE;E2BlFhE,0B3BgFqE;C2B/EtE;;AAGD;;EAEE,e3B4EgE;C2BhEjE;;AzCDC;;;EyCRE,e3ByE8D;E2BxE9D,0BAAyC;CzCU1C;;AyChBH;;EAUI,YAAW;EACX,0B3BmE8D;E2BlE9D,sB3BkE8D;C2BjE/D;;AAnBH;EACE,e3BmFgE;E2BlFhE,0B3BgFqE;C2B/EtE;;AAGD;;EAEE,e3B4EgE;C2BhEjE;;AzCDC;;;EyCRE,e3ByE8D;E2BxE9D,0BAAyC;CzCU1C;;AyChBH;;EAUI,YAAW;EACX,0B3BmE8D;E2BlE9D,sB3BkE8D;C2BjE/D;;ACtBL;EACE,aAAY;EACZ,kB3CizBiD;E2ChzBjD,kB3C+OqB;E2C9OrB,eAAc;EACd,Y3CuDW;E2CtDX,0B3C4CW;E2C3CX,YAAW;CAOZ;;A1CQG;E0CZA,Y3CkDS;E2CjDT,sBAAqB;EACrB,aAAY;C1CaX;;A0CHL;EACE,WAAU;EACV,wBAAuB;EACvB,UAAS;EACT,yBAAwB;CACzB;;ACpBD;EACE,iBAAgB;CACjB;;AAGD;EACE,gBAAe;EACf,OAAM;EACN,SAAQ;EACR,UAAS;EACT,QAAO;EACP,c5C0f8B;E4Czf9B,cAAa;EACb,iBAAgB;EAGhB,WAAU;CAWX;;AAtBD;ErCPM,4CPqsB8C;EOrsB9C,oCPqsB8C;EOrsB9C,qEPqsB8C;E4C3qBhD,sCAA6B;UAA7B,8BAA6B;CAC9B;;AApBH;EAqByB,mCAA0B;UAA1B,2BAA0B;CAAI;;AAEvD;EACE,mBAAkB;EAClB,iBAAgB;CACjB;;AAGD;EACE,mBAAkB;EAClB,YAAW;EACX,a5CuoBgC;C4CtoBjC;;AAGD;EACE,mBAAkB;EAClB,qBAAa;EAAb,cAAa;EACb,2BAAsB;MAAtB,uBAAsB;EACtB,uB5CFW;E4CGX,6BAA4B;EAC5B,qC5CMW;EMxDT,sBNoN0B;E4C9J5B,WAAU;CACX;;AAGD;EACE,gBAAe;EACf,OAAM;EACN,SAAQ;EACR,UAAS;EACT,QAAO;EACP,c5Cuc8B;E4Ctc9B,uB5CTW;C4CcZ;;AAZD;EAUW,WAAU;CAAK;;AAV1B;EAWW,a5CsnBqB;C4CtnBe;;AAK/C;EACE,qBAAa;EAAb,cAAa;EACb,uBAAmB;MAAnB,oBAAmB;EACnB,uBAA8B;MAA9B,+BAA8B;EAC9B,c5CknBgC;E4CjnBhC,iC5C/BgB;C4CgCjB;;AAGD;EACE,iBAAgB;EAChB,iB5C4JoB;C4C3JrB;;AAID;EACE,mBAAkB;EAGlB,mBAAc;MAAd,eAAc;EACd,c5C8kBgC;C4C7kBjC;;AAGD;EACE,qBAAa;EAAb,cAAa;EACb,uBAAmB;MAAnB,oBAAmB;EACnB,mBAAyB;MAAzB,0BAAyB;EACzB,c5CskBgC;E4CrkBhC,8B5CxDgB;C4C6DjB;;AAVD;EAQyB,oBAAmB;CAAK;;AARjD;EASwB,qBAAoB;CAAK;;AAIjD;EACE,mBAAkB;EAClB,aAAY;EACZ,YAAW;EACX,aAAY;EACZ,iBAAgB;CACjB;;AjClEG;EiCuEF;IACE,iB5CukB+B;I4CtkB/B,kBAAyC;GAC1C;EAMD;IAAY,iB5CgkBqB;G4ChkBG;C9CosIrC;;AapxIG;EiCoFF;IAAY,iB5C0jBqB;G4C1jBG;C9CssIrC;;A+Cj1ID;EACE,mBAAkB;EAClB,c7C2gB8B;E6C1gB9B,eAAc;EACd,U7CynB6B;E8C5nB7B,wG9CuOiH;E8CrOjH,mBAAkB;EAClB,oB9C4OyB;E8C3OzB,iB9C+OoB;E8C9OpB,iBAAgB;EAChB,kBAAiB;EACjB,sBAAqB;EACrB,kBAAiB;EACjB,qBAAoB;EACpB,uBAAsB;EACtB,mBAAkB;EAClB,qBAAoB;EACpB,oBAAmB;EACnB,iBAAgB;EDPhB,oB7CsOsB;E6CpOtB,sBAAqB;EACrB,WAAU;CAoFX;;AA/FD;EAaW,a7C6mBqB;C6C7mBQ;;AAbxC;EAgBI,mBAAkB;EAClB,eAAc;EACd,W7C8mB6B;E6C7mB7B,Y7C8mB6B;C6C7mB9B;;AApBH;EAuBI,eAA+B;CAWhC;;AAlCH;EAyBM,UAAS;CACV;;AA1BL;EA6BM,kBAAuC;EACvC,YAAW;EACX,wBAAyD;EACzD,uB7C2BO;C6C1BR;;AAjCL;EAoCI,e7C4lB6B;C6CjlB9B;;AA/CH;EAsCM,QAAO;CACR;;AAvCL;EA0CM,iBAAsC;EACtC,YAAW;EACX,4BAA8E;EAC9E,yB7CcO;C6CbR;;AA9CL;EAiDI,eAA+B;CAWhC;;AA5DH;EAmDM,OAAM;CACP;;AApDL;EAuDM,kBAAuC;EACvC,YAAW;EACX,wB7CukB2B;E6CtkB3B,0B7CCO;C6CAR;;AA3DL;EA8DI,e7CkkB6B;C6CtjB9B;;AA1EH;EAgEM,SAAQ;CACT;;AAjEL;EAoEM,SAAQ;EACR,iBAAsC;EACtC,YAAW;EACX,4B7CyjB2B;E6CxjB3B,wB7CbO;C6CcR;;AAzEL;EA2FI,mBAAkB;EAClB,0BAAyB;EACzB,oBAAmB;CACpB;;AAIH;EACE,iB7CohBiC;E6CnhBjC,iB7CwhB+B;E6CvhB/B,Y7CpDW;E6CqDX,mBAAkB;EAClB,uB7C5CW;EMxDT,uBNmN2B;C6C7G9B;;AE1GD;EACE,mBAAkB;EAClB,OAAM;EACN,QAAO;EACP,c/CygB8B;E+CxgB9B,eAAc;EACd,iB/CooByC;E+CnoBzC,a/CioBuC;E8CtoBvC,wG9CuOiH;E8CrOjH,mBAAkB;EAClB,oB9C4OyB;E8C3OzB,iB9C+OoB;E8C9OpB,iBAAgB;EAChB,kBAAiB;EACjB,sBAAqB;EACrB,kBAAiB;EACjB,qBAAoB;EACpB,uBAAsB;EACtB,mBAAkB;EAClB,qBAAoB;EACpB,oBAAmB;EACnB,iBAAgB;ECLhB,oB/CoOsB;E+ClOtB,sBAAqB;EACrB,uB/CoCW;E+CnCX,6BAA4B;EAC5B,qC/C4CW;EMxDT,sBNoN0B;C+C5C7B;;AA5KD;EAyBI,mBAAkB;EAClB,eAAc;EACd,Y/C6nBsC;E+C5nBtC,Y/C6nBqC;C+C5nBtC;;AA7BH;;EAiCI,mBAAkB;EAClB,eAAc;EACd,0BAAyB;EACzB,oBAAmB;CACpB;;AArCH;EAwCI,YAAW;EACX,mB/CmnB8D;C+ClnB/D;;AA1CH;EA4CI,YAAW;EACX,mB/C+mB8D;C+C9mB/D;;AA9CH;EAmDI,oB/CqmBsC;C+C/kBvC;;AAzEH;EAsDM,UAAS;CACV;;AAvDL;;EA2DM,uBAAsB;CACvB;;AA5DL;EA+DM,c/C6lB4D;E+C5lB5D,kBAA6C;EAC7C,sC/C4lBmE;C+C3lBpE;;AAlEL;EAqEM,cAAwC;EACxC,kBAA6C;EAC7C,uB/CrBO;C+CsBR;;AAxEL;EA4EI,kB/C4kBsC;C+CvjBvC;;AAjGH;EA+EM,QAAO;CACR;;AAhFL;;EAoFM,iBAA4C;EAC5C,qBAAoB;CACrB;;AAtFL;EAyFM,Y/CmkB4D;E+ClkB5D,wC/CmkBmE;C+ClkBpE;;AA3FL;EA8FM,YAAsC;EACtC,yB/C7CO;C+C8CR;;AAhGL;EAoGI,iB/CojBsC;C+CnhBvC;;AArIH;EAuGM,OAAM;CACP;;AAxGL;;EA4GM,kBAAuC;EACvC,oBAAmB;CACpB;;AA9GL;EAiHM,W/C2iB4D;E+C1iB5D,yC/C2iBmE;C+C1iBpE;;AAnHL;EAsHM,WAAqC;EACrC,0B/CrEO;C+CsER;;AAxHL;EA4HM,mBAAkB;EAClB,OAAM;EACN,UAAS;EACT,eAAc;EACd,YAAW;EACX,mBAAkB;EAClB,YAAW;EACX,iC/C4gBwD;C+C3gBzD;;AApIL;EAwII,mB/CghBsC;C+C3fvC;;AA7JH;EA2IM,SAAQ;CACT;;AA5IL;;EAgJM,iBAA4C;EAC5C,sBAAqB;CACtB;;AAlJL;EAqJM,a/CugB4D;E+CtgB5D,uC/CugBmE;C+CtgBpE;;AAvJL;EA0JM,aAAuC;EACvC,wB/CzGO;C+C0GR;;AAoBL;EACE,kB/CieyC;E+ChezC,iBAAgB;EAChB,gB/C0DmB;E+CzDnB,e/C8E8B;E+C7E9B,0B/C0d4D;E+Czd5D,iCAAyE;EzC5KvE,2CyC6KyE;EzC5KzE,4CyC4KyE;CAM5E;;AAbD;EAWI,cAAa;CACd;;AAGH;EACE,kB/CsdqC;E+CrdrC,e/CtIgB;C+CuIjB;;ACjMD;EACE,mBAAkB;CACnB;;AAED;EACE,mBAAkB;EAClB,YAAW;EACX,iBAAgB;CACjB;;AAED;EACE,mBAAkB;EAClB,cAAa;EACb,uBAAmB;MAAnB,oBAAmB;EACnB,YAAW;EzCVP,wCPyyB4C;EOzyB5C,gCPyyB4C;EOzyB5C,6DPyyB4C;EgD7xBhD,oCAA2B;UAA3B,4BAA2B;EAC3B,4BAAmB;UAAnB,oBAAmB;CACpB;;AAED;;;EAGE,eAAc;CACf;;AAED;;EAEE,mBAAkB;EAClB,OAAM;CACP;;AAGD;;EAEE,iCAAwB;UAAxB,yBAAwB;CAKzB;;AAHyC;EAJ1C;;IAKI,wCAA+B;YAA/B,gCAA+B;GAElC;ClD2nJA;;AkDznJD;;EAEE,oCAA2B;UAA3B,4BAA2B;CAK5B;;AAHyC;EAJ1C;;IAKI,2CAAkC;YAAlC,mCAAkC;GAErC;ClD8nJA;;AkD5nJD;;EAEE,qCAA4B;UAA5B,6BAA4B;CAK7B;;AAHyC;EAJ1C;;IAKI,4CAAmC;YAAnC,oCAAmC;GAEtC;ClDioJA;;AkD1nJD;;EAEE,mBAAkB;EAClB,OAAM;EACN,UAAS;EAET,qBAAa;EAAb,cAAa;EACb,uBAAmB;MAAnB,oBAAmB;EACnB,sBAAuB;MAAvB,wBAAuB;EACvB,WhDmtB+C;EgDltB/C,YhD1BW;EgD2BX,mBAAkB;EAClB,ahDitB8C;CgDtsB/C;;A/CnEG;;;E+C8DA,YhDlCS;EgDmCT,sBAAqB;EACrB,WAAU;EACV,YAAW;C/C9DV;;A+CiEL;EACE,QAAO;CACR;;AACD;EACE,SAAQ;CACT;;AAGD;;EAEE,sBAAqB;EACrB,YhDosBgD;EgDnsBhD,ahDmsBgD;EgDlsBhD,gDAA+C;EAC/C,2BAA0B;CAC3B;;AACD;EACE,8MjC/DyI;CiCgE1I;;AACD;EACE,gNjClEyI;CiCmE1I;;AAQD;EACE,mBAAkB;EAClB,SAAQ;EACR,aAAY;EACZ,QAAO;EACP,YAAW;EACX,qBAAa;EAAb,cAAa;EACb,sBAAuB;MAAvB,wBAAuB;EACvB,gBAAe;EAEf,kBhD6pB+C;EgD5pB/C,iBhD4pB+C;EgD3pB/C,iBAAgB;CAoCjB;;AAhDD;EAeI,mBAAkB;EAClB,mBAAc;MAAd,eAAc;EACd,YhDypB8C;EgDxpB9C,YhDypB6C;EgDxpB7C,kBhDypB6C;EgDxpB7C,iBhDwpB6C;EgDvpB7C,oBAAmB;EACnB,2ChD3FS;CgDgHV;;AA3CH;EA0BM,mBAAkB;EAClB,WAAU;EACV,QAAO;EACP,sBAAqB;EACrB,YAAW;EACX,aAAY;EACZ,YAAW;CACZ;;AAjCL;EAmCM,mBAAkB;EAClB,cAAa;EACb,QAAO;EACP,sBAAqB;EACrB,YAAW;EACX,aAAY;EACZ,YAAW;CACZ;;AA1CL;EA8CI,uBhDnHS;CgDoHV;;AAQH;EACE,mBAAkB;EAClB,WAA6C;EAC7C,aAAY;EACZ,UAA4C;EAC5C,YAAW;EACX,kBAAiB;EACjB,qBAAoB;EACpB,YhDpIW;EgDqIX,mBAAkB;CACnB;;ACxLD;EAAqB,oCAAmC;CAAK;;AAC7D;EAAqB,+BAA8B;CAAK;;AACxD;EAAqB,kCAAiC;CAAK;;AAC3D;EAAqB,kCAAiC;CAAK;;AAC3D;EAAqB,uCAAsC;CAAK;;AAChE;EAAqB,oCAAmC;CAAK;;ACF3D;EACE,qCAAmC;CACpC;;AjDiBC;EiDdE,qCAAgD;CjDiBjD;;AiDtBH;EACE,qCAAmC;CACpC;;AjDiBC;EiDdE,qCAAgD;CjDiBjD;;AiDtBH;EACE,qCAAmC;CACpC;;AjDiBC;EiDdE,qCAAgD;CjDiBjD;;AiDtBH;EACE,qCAAmC;CACpC;;AjDiBC;EiDdE,qCAAgD;CjDiBjD;;AiDtBH;EACE,qCAAmC;CACpC;;AjDiBC;EiDdE,qCAAgD;CjDiBjD;;AiDtBH;EACE,qCAAmC;CACpC;;AjDiBC;EiDdE,qCAAgD;CjDiBjD;;AiDtBH;EACE,qCAAmC;CACpC;;AjDiBC;EiDdE,qCAAgD;CjDiBjD;;AiDtBH;EACE,qCAAmC;CACpC;;AjDiBC;EiDdE,qCAAgD;CjDiBjD;;AkDrBL;EAAY,kCAAmC;CAAI;;AACnD;EAAkB,yCAAwC;CAAK;;ACD/D;EAAmB,qCAAsC;CAAI;;AAC7D;EAAmB,qBAAoB;CAAK;;AAC5C;EAAmB,yBAAwB;CAAK;;AAChD;EAAmB,2BAA0B;CAAK;;AAClD;EAAmB,4BAA2B;CAAK;;AACnD;EAAmB,0BAAyB;CAAK;;AAG/C;EACE,iCAA+B;CAChC;;AAFD;EACE,iCAA+B;CAChC;;AAFD;EACE,iCAA+B;CAChC;;AAFD;EACE,iCAA+B;CAChC;;AAFD;EACE,iCAA+B;CAChC;;AAFD;EACE,iCAA+B;CAChC;;AAFD;EACE,iCAA+B;CAChC;;AAFD;EACE,iCAA+B;CAChC;;AAGH;EACE,8BAA+B;CAChC;;AAMD;EACE,kCAAwC;CACzC;;AACD;EACE,2CAAiD;EACjD,4CAAkD;CACnD;;AACD;EACE,4CAAkD;EAClD,+CAAqD;CACtD;;AACD;EACE,+CAAqD;EACrD,8CAAoD;CACrD;;AACD;EACE,2CAAiD;EACjD,8CAAoD;CACrD;;AAED;EACE,mBAAkB;CACnB;;AAED;EACE,iBAAgB;CACjB;;AtBlDC;EACE,eAAc;EACd,YAAW;EACX,YAAW;CACZ;;AuBGC;EAA2B,yBAAwB;CAAK;;AACxD;EAA2B,2BAA0B;CAAK;;AAC1D;EAA2B,iCAAgC;CAAK;;AAChE;EAA2B,0BAAyB;CAAK;;AACzD;EAA2B,0BAAyB;CAAK;;AACzD;EAA2B,+BAA8B;CAAK;;AAC9D;EAA2B,gCAAwB;EAAxB,yBAAwB;CAAK;;AACxD;EAA2B,uCAA+B;EAA/B,gCAA+B;CAAK;;A1CyC/D;E0ChDA;IAA2B,yBAAwB;GAAK;EACxD;IAA2B,2BAA0B;GAAK;EAC1D;IAA2B,iCAAgC;GAAK;EAChE;IAA2B,0BAAyB;GAAK;EACzD;IAA2B,0BAAyB;GAAK;EACzD;IAA2B,+BAA8B;GAAK;EAC9D;IAA2B,gCAAwB;IAAxB,yBAAwB;GAAK;EACxD;IAA2B,uCAA+B;IAA/B,gCAA+B;GAAK;CvDuhKlE;;Aa9+JG;E0ChDA;IAA2B,yBAAwB;GAAK;EACxD;IAA2B,2BAA0B;GAAK;EAC1D;IAA2B,iCAAgC;GAAK;EAChE;IAA2B,0BAAyB;GAAK;EACzD;IAA2B,0BAAyB;GAAK;EACzD;IAA2B,+BAA8B;GAAK;EAC9D;IAA2B,gCAAwB;IAAxB,yBAAwB;GAAK;EACxD;IAA2B,uCAA+B;IAA/B,gCAA+B;GAAK;CvDkjKlE;;AazgKG;E0ChDA;IAA2B,yBAAwB;GAAK;EACxD;IAA2B,2BAA0B;GAAK;EAC1D;IAA2B,iCAAgC;GAAK;EAChE;IAA2B,0BAAyB;GAAK;EACzD;IAA2B,0BAAyB;GAAK;EACzD;IAA2B,+BAA8B;GAAK;EAC9D;IAA2B,gCAAwB;IAAxB,yBAAwB;GAAK;EACxD;IAA2B,uCAA+B;IAA/B,gCAA+B;GAAK;CvD6kKlE;;AapiKG;E0ChDA;IAA2B,yBAAwB;GAAK;EACxD;IAA2B,2BAA0B;GAAK;EAC1D;IAA2B,iCAAgC;GAAK;EAChE;IAA2B,0BAAyB;GAAK;EACzD;IAA2B,0BAAyB;GAAK;EACzD;IAA2B,+BAA8B;GAAK;EAC9D;IAA2B,gCAAwB;IAAxB,yBAAwB;GAAK;EACxD;IAA2B,uCAA+B;IAA/B,gCAA+B;GAAK;CvDwmKlE;;AuD/lKD;EACE,yBAAwB;CAKzB;;AAHC;EAHF;IAII,0BAAyB;GAE5B;CvDmmKA;;AuDjmKD;EACE,yBAAwB;CAKzB;;AAHC;EAHF;IAII,2BAA0B;GAE7B;CvDqmKA;;AuDnmKD;EACE,yBAAwB;CAKzB;;AAHC;EAHF;IAII,iCAAgC;GAEnC;CvDumKA;;AuDpmKC;EADF;IAEI,yBAAwB;GAE3B;CvDumKA;;AwDzpKD;EACE,mBAAkB;EAClB,eAAc;EACd,YAAW;EACX,WAAU;EACV,iBAAgB;CAoBjB;;AAzBD;EAQI,eAAc;EACd,YAAW;CACZ;;AAVH;;;;;EAiBI,mBAAkB;EAClB,OAAM;EACN,UAAS;EACT,QAAO;EACP,YAAW;EACX,aAAY;EACZ,UAAS;CACV;;AAGH;EAEI,wBAA+B;CAChC;;AAGH;EAEI,oBAA+B;CAChC;;AAGH;EAEI,iBAA8B;CAC/B;;AAGH;EAEI,kBAA8B;CAC/B;;AC1CC;EAAgC,mCAA8B;MAA9B,+BAA8B;CAAK;;AACnE;EAAgC,sCAAiC;MAAjC,kCAAiC;CAAK;;AACtE;EAAgC,2CAAsC;MAAtC,uCAAsC;CAAK;;AAC3E;EAAgC,8CAAyC;MAAzC,0CAAyC;CAAK;;AAE9E;EAA8B,+BAA0B;MAA1B,2BAA0B;CAAK;;AAC7D;EAA8B,iCAA4B;MAA5B,6BAA4B;CAAK;;AAC/D;EAA8B,uCAAkC;MAAlC,mCAAkC;CAAK;;AAErE;EAAoC,gCAAsC;MAAtC,uCAAsC;CAAK;;AAC/E;EAAoC,8BAAoC;MAApC,qCAAoC;CAAK;;AAC7E;EAAoC,iCAAkC;MAAlC,mCAAkC;CAAK;;AAC3E;EAAoC,kCAAyC;MAAzC,0CAAyC;CAAK;;AAClF;EAAoC,qCAAwC;MAAxC,yCAAwC;CAAK;;AAEjF;EAAiC,iCAAkC;MAAlC,mCAAkC;CAAK;;AACxE;EAAiC,+BAAgC;MAAhC,iCAAgC;CAAK;;AACtE;EAAiC,kCAA8B;MAA9B,+BAA8B;CAAK;;AACpE;EAAiC,oCAAgC;MAAhC,iCAAgC;CAAK;;AACtE;EAAiC,mCAA+B;MAA/B,gCAA+B;CAAK;;AAErE;EAAkC,qCAAoC;MAApC,qCAAoC;CAAK;;AAC3E;EAAkC,mCAAkC;MAAlC,mCAAkC;CAAK;;AACzE;EAAkC,sCAAgC;MAAhC,iCAAgC;CAAK;;AACvE;EAAkC,uCAAuC;MAAvC,wCAAuC;CAAK;;AAC9E;EAAkC,0CAAsC;MAAtC,uCAAsC;CAAK;;AAC7E;EAAkC,uCAAiC;MAAjC,kCAAiC;CAAK;;AAExE;EAAgC,qCAA2B;MAA3B,4BAA2B;CAAK;;AAChE;EAAgC,sCAAiC;MAAjC,kCAAiC;CAAK;;AACtE;EAAgC,oCAA+B;MAA/B,gCAA+B;CAAK;;AACpE;EAAgC,uCAA6B;MAA7B,8BAA6B;CAAK;;AAClE;EAAgC,yCAA+B;MAA/B,gCAA+B;CAAK;;AACpE;EAAgC,wCAA8B;MAA9B,+BAA8B;CAAK;;A5CenE;E4ChDA;IAAgC,mCAA8B;QAA9B,+BAA8B;GAAK;EACnE;IAAgC,sCAAiC;QAAjC,kCAAiC;GAAK;EACtE;IAAgC,2CAAsC;QAAtC,uCAAsC;GAAK;EAC3E;IAAgC,8CAAyC;QAAzC,0CAAyC;GAAK;EAE9E;IAA8B,+BAA0B;QAA1B,2BAA0B;GAAK;EAC7D;IAA8B,iCAA4B;QAA5B,6BAA4B;GAAK;EAC/D;IAA8B,uCAAkC;QAAlC,mCAAkC;GAAK;EAErE;IAAoC,gCAAsC;QAAtC,uCAAsC;GAAK;EAC/E;IAAoC,8BAAoC;QAApC,qCAAoC;GAAK;EAC7E;IAAoC,iCAAkC;QAAlC,mCAAkC;GAAK;EAC3E;IAAoC,kCAAyC;QAAzC,0CAAyC;GAAK;EAClF;IAAoC,qCAAwC;QAAxC,yCAAwC;GAAK;EAEjF;IAAiC,iCAAkC;QAAlC,mCAAkC;GAAK;EACxE;IAAiC,+BAAgC;QAAhC,iCAAgC;GAAK;EACtE;IAAiC,kCAA8B;QAA9B,+BAA8B;GAAK;EACpE;IAAiC,oCAAgC;QAAhC,iCAAgC;GAAK;EACtE;IAAiC,mCAA+B;QAA/B,gCAA+B;GAAK;EAErE;IAAkC,qCAAoC;QAApC,qCAAoC;GAAK;EAC3E;IAAkC,mCAAkC;QAAlC,mCAAkC;GAAK;EACzE;IAAkC,sCAAgC;QAAhC,iCAAgC;GAAK;EACvE;IAAkC,uCAAuC;QAAvC,wCAAuC;GAAK;EAC9E;IAAkC,0CAAsC;QAAtC,uCAAsC;GAAK;EAC7E;IAAkC,uCAAiC;QAAjC,kCAAiC;GAAK;EAExE;IAAgC,qCAA2B;QAA3B,4BAA2B;GAAK;EAChE;IAAgC,sCAAiC;QAAjC,kCAAiC;GAAK;EACtE;IAAgC,oCAA+B;QAA/B,gCAA+B;GAAK;EACpE;IAAgC,uCAA6B;QAA7B,8BAA6B;GAAK;EAClE;IAAgC,yCAA+B;QAA/B,gCAA+B;GAAK;EACpE;IAAgC,wCAA8B;QAA9B,+BAA8B;GAAK;CzD22KtE;;Aa51KG;E4ChDA;IAAgC,mCAA8B;QAA9B,+BAA8B;GAAK;EACnE;IAAgC,sCAAiC;QAAjC,kCAAiC;GAAK;EACtE;IAAgC,2CAAsC;QAAtC,uCAAsC;GAAK;EAC3E;IAAgC,8CAAyC;QAAzC,0CAAyC;GAAK;EAE9E;IAA8B,+BAA0B;QAA1B,2BAA0B;GAAK;EAC7D;IAA8B,iCAA4B;QAA5B,6BAA4B;GAAK;EAC/D;IAA8B,uCAAkC;QAAlC,mCAAkC;GAAK;EAErE;IAAoC,gCAAsC;QAAtC,uCAAsC;GAAK;EAC/E;IAAoC,8BAAoC;QAApC,qCAAoC;GAAK;EAC7E;IAAoC,iCAAkC;QAAlC,mCAAkC;GAAK;EAC3E;IAAoC,kCAAyC;QAAzC,0CAAyC;GAAK;EAClF;IAAoC,qCAAwC;QAAxC,yCAAwC;GAAK;EAEjF;IAAiC,iCAAkC;QAAlC,mCAAkC;GAAK;EACxE;IAAiC,+BAAgC;QAAhC,iCAAgC;GAAK;EACtE;IAAiC,kCAA8B;QAA9B,+BAA8B;GAAK;EACpE;IAAiC,oCAAgC;QAAhC,iCAAgC;GAAK;EACtE;IAAiC,mCAA+B;QAA/B,gCAA+B;GAAK;EAErE;IAAkC,qCAAoC;QAApC,qCAAoC;GAAK;EAC3E;IAAkC,mCAAkC;QAAlC,mCAAkC;GAAK;EACzE;IAAkC,sCAAgC;QAAhC,iCAAgC;GAAK;EACvE;IAAkC,uCAAuC;QAAvC,wCAAuC;GAAK;EAC9E;IAAkC,0CAAsC;QAAtC,uCAAsC;GAAK;EAC7E;IAAkC,uCAAiC;QAAjC,kCAAiC;GAAK;EAExE;IAAgC,qCAA2B;QAA3B,4BAA2B;GAAK;EAChE;IAAgC,sCAAiC;QAAjC,kCAAiC;GAAK;EACtE;IAAgC,oCAA+B;QAA/B,gCAA+B;GAAK;EACpE;IAAgC,uCAA6B;QAA7B,8BAA6B;GAAK;EAClE;IAAgC,yCAA+B;QAA/B,gCAA+B;GAAK;EACpE;IAAgC,wCAA8B;QAA9B,+BAA8B;GAAK;CzDq8KtE;;Aat7KG;E4ChDA;IAAgC,mCAA8B;QAA9B,+BAA8B;GAAK;EACnE;IAAgC,sCAAiC;QAAjC,kCAAiC;GAAK;EACtE;IAAgC,2CAAsC;QAAtC,uCAAsC;GAAK;EAC3E;IAAgC,8CAAyC;QAAzC,0CAAyC;GAAK;EAE9E;IAA8B,+BAA0B;QAA1B,2BAA0B;GAAK;EAC7D;IAA8B,iCAA4B;QAA5B,6BAA4B;GAAK;EAC/D;IAA8B,uCAAkC;QAAlC,mCAAkC;GAAK;EAErE;IAAoC,gCAAsC;QAAtC,uCAAsC;GAAK;EAC/E;IAAoC,8BAAoC;QAApC,qCAAoC;GAAK;EAC7E;IAAoC,iCAAkC;QAAlC,mCAAkC;GAAK;EAC3E;IAAoC,kCAAyC;QAAzC,0CAAyC;GAAK;EAClF;IAAoC,qCAAwC;QAAxC,yCAAwC;GAAK;EAEjF;IAAiC,iCAAkC;QAAlC,mCAAkC;GAAK;EACxE;IAAiC,+BAAgC;QAAhC,iCAAgC;GAAK;EACtE;IAAiC,kCAA8B;QAA9B,+BAA8B;GAAK;EACpE;IAAiC,oCAAgC;QAAhC,iCAAgC;GAAK;EACtE;IAAiC,mCAA+B;QAA/B,gCAA+B;GAAK;EAErE;IAAkC,qCAAoC;QAApC,qCAAoC;GAAK;EAC3E;IAAkC,mCAAkC;QAAlC,mCAAkC;GAAK;EACzE;IAAkC,sCAAgC;QAAhC,iCAAgC;GAAK;EACvE;IAAkC,uCAAuC;QAAvC,wCAAuC;GAAK;EAC9E;IAAkC,0CAAsC;QAAtC,uCAAsC;GAAK;EAC7E;IAAkC,uCAAiC;QAAjC,kCAAiC;GAAK;EAExE;IAAgC,qCAA2B;QAA3B,4BAA2B;GAAK;EAChE;IAAgC,sCAAiC;QAAjC,kCAAiC;GAAK;EACtE;IAAgC,oCAA+B;QAA/B,gCAA+B;GAAK;EACpE;IAAgC,uCAA6B;QAA7B,8BAA6B;GAAK;EAClE;IAAgC,yCAA+B;QAA/B,gCAA+B;GAAK;EACpE;IAAgC,wCAA8B;QAA9B,+BAA8B;GAAK;CzD+hLtE;;AahhLG;E4ChDA;IAAgC,mCAA8B;QAA9B,+BAA8B;GAAK;EACnE;IAAgC,sCAAiC;QAAjC,kCAAiC;GAAK;EACtE;IAAgC,2CAAsC;QAAtC,uCAAsC;GAAK;EAC3E;IAAgC,8CAAyC;QAAzC,0CAAyC;GAAK;EAE9E;IAA8B,+BAA0B;QAA1B,2BAA0B;GAAK;EAC7D;IAA8B,iCAA4B;QAA5B,6BAA4B;GAAK;EAC/D;IAA8B,uCAAkC;QAAlC,mCAAkC;GAAK;EAErE;IAAoC,gCAAsC;QAAtC,uCAAsC;GAAK;EAC/E;IAAoC,8BAAoC;QAApC,qCAAoC;GAAK;EAC7E;IAAoC,iCAAkC;QAAlC,mCAAkC;GAAK;EAC3E;IAAoC,kCAAyC;QAAzC,0CAAyC;GAAK;EAClF;IAAoC,qCAAwC;QAAxC,yCAAwC;GAAK;EAEjF;IAAiC,iCAAkC;QAAlC,mCAAkC;GAAK;EACxE;IAAiC,+BAAgC;QAAhC,iCAAgC;GAAK;EACtE;IAAiC,kCAA8B;QAA9B,+BAA8B;GAAK;EACpE;IAAiC,oCAAgC;QAAhC,iCAAgC;GAAK;EACtE;IAAiC,mCAA+B;QAA/B,gCAA+B;GAAK;EAErE;IAAkC,qCAAoC;QAApC,qCAAoC;GAAK;EAC3E;IAAkC,mCAAkC;QAAlC,mCAAkC;GAAK;EACzE;IAAkC,sCAAgC;QAAhC,iCAAgC;GAAK;EACvE;IAAkC,uCAAuC;QAAvC,wCAAuC;GAAK;EAC9E;IAAkC,0CAAsC;QAAtC,uCAAsC;GAAK;EAC7E;IAAkC,uCAAiC;QAAjC,kCAAiC;GAAK;EAExE;IAAgC,qCAA2B;QAA3B,4BAA2B;GAAK;EAChE;IAAgC,sCAAiC;QAAjC,kCAAiC;GAAK;EACtE;IAAgC,oCAA+B;QAA/B,gCAA+B;GAAK;EACpE;IAAgC,uCAA6B;QAA7B,8BAA6B;GAAK;EAClE;IAAgC,yCAA+B;QAA/B,gCAA+B;GAAK;EACpE;IAAgC,wCAA8B;QAA9B,+BAA8B;GAAK;CzDynLtE;;A0D9pLG;ECHF,uBAAsB;CDG2B;;AAC/C;ECDF,wBAAuB;CDC2B;;AAChD;ECCF,uBAAsB;CDD2B;;A7CkD/C;E6CpDA;ICHF,uBAAsB;GDG2B;EAC/C;ICDF,wBAAuB;GDC2B;EAChD;ICCF,uBAAsB;GDD2B;C1DorLlD;;AaloLG;E6CpDA;ICHF,uBAAsB;GDG2B;EAC/C;ICDF,wBAAuB;GDC2B;EAChD;ICCF,uBAAsB;GDD2B;C1DgsLlD;;Aa9oLG;E6CpDA;ICHF,uBAAsB;GDG2B;EAC/C;ICDF,wBAAuB;GDC2B;EAChD;ICCF,uBAAsB;GDD2B;C1D4sLlD;;Aa1pLG;E6CpDA;ICHF,uBAAsB;GDG2B;EAC/C;ICDF,wBAAuB;GDC2B;EAChD;ICCF,uBAAsB;GDD2B;C1DwtLlD;;A4D5tLD;EACE,gBAAe;EACf,OAAM;EACN,SAAQ;EACR,QAAO;EACP,c1DmgB8B;C0DlgB/B;;AAED;EACE,gBAAe;EACf,SAAQ;EACR,UAAS;EACT,QAAO;EACP,c1D2f8B;C0D1f/B;;AAG6B;EAD9B;IAEI,yBAAgB;IAAhB,iBAAgB;IAChB,OAAM;IACN,c1Dmf4B;G0Djf/B;C5D8tLA;;A6DlvLD;ECEE,mBAAkB;EAClB,WAAU;EACV,YAAW;EACX,WAAU;EACV,iBAAgB;EAChB,uBAAmB;EACnB,oBAAmB;EACnB,8BAAqB;UAArB,sBAAqB;EACrB,UAAS;CDRV;;ACkBC;EAEE,iBAAgB;EAChB,YAAW;EACX,aAAY;EACZ,kBAAiB;EACjB,WAAU;EACV,oBAAmB;EACnB,wBAAe;UAAf,gBAAe;CAChB;;AC7BC;EAAuB,sBAA4B;CAAI;;AAAvD;EAAuB,sBAA4B;CAAI;;AAAvD;EAAuB,sBAA4B;CAAI;;AAAvD;EAAuB,uBAA4B;CAAI;;AAAvD;EAAuB,uBAA4B;CAAI;;AAAvD;EAAuB,uBAA4B;CAAI;;AAAvD;EAAuB,uBAA4B;CAAI;;AAAvD;EAAuB,wBAA4B;CAAI;;AAI3D;EAAU,2BAA0B;CAAK;;AACzC;EAAU,4BAA2B;CAAK;;ACAlC;EAAiC,qBAAmC;CAAI;;AACxE;EAAiC,yBAAuC;CAAI;;AAC5E;EAAiC,2BAAyC;CAAI;;AAC9E;EAAiC,4BAA0C;CAAI;;AAC/E;EAAiC,0BAAwC;CAAI;;AAC7E;EACE,2BAAwC;EACxC,0BAAuC;CACxC;;AACD;EACE,yBAAuC;EACvC,4BAA0C;CAC3C;;AAZD;EAAiC,2BAAmC;CAAI;;AACxE;EAAiC,+BAAuC;CAAI;;AAC5E;EAAiC,iCAAyC;CAAI;;AAC9E;EAAiC,kCAA0C;CAAI;;AAC/E;EAAiC,gCAAwC;CAAI;;AAC7E;EACE,iCAAwC;EACxC,gCAAuC;CACxC;;AACD;EACE,+BAAuC;EACvC,kCAA0C;CAC3C;;AAZD;EAAiC,0BAAmC;CAAI;;AACxE;EAAiC,8BAAuC;CAAI;;AAC5E;EAAiC,gCAAyC;CAAI;;AAC9E;EAAiC,iCAA0C;CAAI;;AAC/E;EAAiC,+BAAwC;CAAI;;AAC7E;EACE,gCAAwC;EACxC,+BAAuC;CACxC;;AACD;EACE,8BAAuC;EACvC,iCAA0C;CAC3C;;AAZD;EAAiC,wBAAmC;CAAI;;AACxE;EAAiC,4BAAuC;CAAI;;AAC5E;EAAiC,8BAAyC;CAAI;;AAC9E;EAAiC,+BAA0C;CAAI;;AAC/E;EAAiC,6BAAwC;CAAI;;AAC7E;EACE,8BAAwC;EACxC,6BAAuC;CACxC;;AACD;EACE,4BAAuC;EACvC,+BAA0C;CAC3C;;AAZD;EAAiC,0BAAmC;CAAI;;AACxE;EAAiC,8BAAuC;CAAI;;AAC5E;EAAiC,gCAAyC;CAAI;;AAC9E;EAAiC,iCAA0C;CAAI;;AAC/E;EAAiC,+BAAwC;CAAI;;AAC7E;EACE,gCAAwC;EACxC,+BAAuC;CACxC;;AACD;EACE,8BAAuC;EACvC,iCAA0C;CAC3C;;AAZD;EAAiC,wBAAmC;CAAI;;AACxE;EAAiC,4BAAuC;CAAI;;AAC5E;EAAiC,8BAAyC;CAAI;;AAC9E;EAAiC,+BAA0C;CAAI;;AAC/E;EAAiC,6BAAwC;CAAI;;AAC7E;EACE,8BAAwC;EACxC,6BAAuC;CACxC;;AACD;EACE,4BAAuC;EACvC,+BAA0C;CAC3C;;AAZD;EAAiC,sBAAmC;CAAI;;AACxE;EAAiC,0BAAuC;CAAI;;AAC5E;EAAiC,4BAAyC;CAAI;;AAC9E;EAAiC,6BAA0C;CAAI;;AAC/E;EAAiC,2BAAwC;CAAI;;AAC7E;EACE,4BAAwC;EACxC,2BAAuC;CACxC;;AACD;EACE,0BAAuC;EACvC,6BAA0C;CAC3C;;AAZD;EAAiC,4BAAmC;CAAI;;AACxE;EAAiC,gCAAuC;CAAI;;AAC5E;EAAiC,kCAAyC;CAAI;;AAC9E;EAAiC,mCAA0C;CAAI;;AAC/E;EAAiC,iCAAwC;CAAI;;AAC7E;EACE,kCAAwC;EACxC,iCAAuC;CACxC;;AACD;EACE,gCAAuC;EACvC,mCAA0C;CAC3C;;AAZD;EAAiC,2BAAmC;CAAI;;AACxE;EAAiC,+BAAuC;CAAI;;AAC5E;EAAiC,iCAAyC;CAAI;;AAC9E;EAAiC,kCAA0C;CAAI;;AAC/E;EAAiC,gCAAwC;CAAI;;AAC7E;EACE,iCAAwC;EACxC,gCAAuC;CACxC;;AACD;EACE,+BAAuC;EACvC,kCAA0C;CAC3C;;AAZD;EAAiC,yBAAmC;CAAI;;AACxE;EAAiC,6BAAuC;CAAI;;AAC5E;EAAiC,+BAAyC;CAAI;;AAC9E;EAAiC,gCAA0C;CAAI;;AAC/E;EAAiC,8BAAwC;CAAI;;AAC7E;EACE,+BAAwC;EACxC,8BAAuC;CACxC;;AACD;EACE,6BAAuC;EACvC,gCAA0C;CAC3C;;AAZD;EAAiC,2BAAmC;CAAI;;AACxE;EAAiC,+BAAuC;CAAI;;AAC5E;EAAiC,iCAAyC;CAAI;;AAC9E;EAAiC,kCAA0C;CAAI;;AAC/E;EAAiC,gCAAwC;CAAI;;AAC7E;EACE,iCAAwC;EACxC,gCAAuC;CACxC;;AACD;EACE,+BAAuC;EACvC,kCAA0C;CAC3C;;AAZD;EAAiC,yBAAmC;CAAI;;AACxE;EAAiC,6BAAuC;CAAI;;AAC5E;EAAiC,+BAAyC;CAAI;;AAC9E;EAAiC,gCAA0C;CAAI;;AAC/E;EAAiC,8BAAwC;CAAI;;AAC7E;EACE,+BAAwC;EACxC,8BAAuC;CACxC;;AACD;EACE,6BAAuC;EACvC,gCAA0C;CAC3C;;AAKL;EAAoB,wBAA8B;CAAK;;AACvD;EAAoB,4BAA8B;CAAK;;AACvD;EAAoB,8BAA8B;CAAK;;AACvD;EAAoB,+BAA8B;CAAK;;AACvD;EAAoB,6BAA8B;CAAK;;AACvD;EACE,8BAA6B;EAC7B,6BAA6B;CAC9B;;AACD;EACE,4BAA8B;EAC9B,+BAA8B;CAC/B;;AnDkBD;EmD/CI;IAAiC,qBAAmC;GAAI;EACxE;IAAiC,yBAAuC;GAAI;EAC5E;IAAiC,2BAAyC;GAAI;EAC9E;IAAiC,4BAA0C;GAAI;EAC/E;IAAiC,0BAAwC;GAAI;EAC7E;IACE,2BAAwC;IACxC,0BAAuC;GACxC;EACD;IACE,yBAAuC;IACvC,4BAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,0BAAmC;GAAI;EACxE;IAAiC,8BAAuC;GAAI;EAC5E;IAAiC,gCAAyC;GAAI;EAC9E;IAAiC,iCAA0C;GAAI;EAC/E;IAAiC,+BAAwC;GAAI;EAC7E;IACE,gCAAwC;IACxC,+BAAuC;GACxC;EACD;IACE,8BAAuC;IACvC,iCAA0C;GAC3C;EAZD;IAAiC,wBAAmC;GAAI;EACxE;IAAiC,4BAAuC;GAAI;EAC5E;IAAiC,8BAAyC;GAAI;EAC9E;IAAiC,+BAA0C;GAAI;EAC/E;IAAiC,6BAAwC;GAAI;EAC7E;IACE,8BAAwC;IACxC,6BAAuC;GACxC;EACD;IACE,4BAAuC;IACvC,+BAA0C;GAC3C;EAZD;IAAiC,0BAAmC;GAAI;EACxE;IAAiC,8BAAuC;GAAI;EAC5E;IAAiC,gCAAyC;GAAI;EAC9E;IAAiC,iCAA0C;GAAI;EAC/E;IAAiC,+BAAwC;GAAI;EAC7E;IACE,gCAAwC;IACxC,+BAAuC;GACxC;EACD;IACE,8BAAuC;IACvC,iCAA0C;GAC3C;EAZD;IAAiC,wBAAmC;GAAI;EACxE;IAAiC,4BAAuC;GAAI;EAC5E;IAAiC,8BAAyC;GAAI;EAC9E;IAAiC,+BAA0C;GAAI;EAC/E;IAAiC,6BAAwC;GAAI;EAC7E;IACE,8BAAwC;IACxC,6BAAuC;GACxC;EACD;IACE,4BAAuC;IACvC,+BAA0C;GAC3C;EAZD;IAAiC,sBAAmC;GAAI;EACxE;IAAiC,0BAAuC;GAAI;EAC5E;IAAiC,4BAAyC;GAAI;EAC9E;IAAiC,6BAA0C;GAAI;EAC/E;IAAiC,2BAAwC;GAAI;EAC7E;IACE,4BAAwC;IACxC,2BAAuC;GACxC;EACD;IACE,0BAAuC;IACvC,6BAA0C;GAC3C;EAZD;IAAiC,4BAAmC;GAAI;EACxE;IAAiC,gCAAuC;GAAI;EAC5E;IAAiC,kCAAyC;GAAI;EAC9E;IAAiC,mCAA0C;GAAI;EAC/E;IAAiC,iCAAwC;GAAI;EAC7E;IACE,kCAAwC;IACxC,iCAAuC;GACxC;EACD;IACE,gCAAuC;IACvC,mCAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,yBAAmC;GAAI;EACxE;IAAiC,6BAAuC;GAAI;EAC5E;IAAiC,+BAAyC;GAAI;EAC9E;IAAiC,gCAA0C;GAAI;EAC/E;IAAiC,8BAAwC;GAAI;EAC7E;IACE,+BAAwC;IACxC,8BAAuC;GACxC;EACD;IACE,6BAAuC;IACvC,gCAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,yBAAmC;GAAI;EACxE;IAAiC,6BAAuC;GAAI;EAC5E;IAAiC,+BAAyC;GAAI;EAC9E;IAAiC,gCAA0C;GAAI;EAC/E;IAAiC,8BAAwC;GAAI;EAC7E;IACE,+BAAwC;IACxC,8BAAuC;GACxC;EACD;IACE,6BAAuC;IACvC,gCAA0C;GAC3C;EAKL;IAAoB,wBAA8B;GAAK;EACvD;IAAoB,4BAA8B;GAAK;EACvD;IAAoB,8BAA8B;GAAK;EACvD;IAAoB,+BAA8B;GAAK;EACvD;IAAoB,6BAA8B;GAAK;EACvD;IACE,8BAA6B;IAC7B,6BAA6B;GAC9B;EACD;IACE,4BAA8B;IAC9B,+BAA8B;GAC/B;ChEk8MJ;;Aah7MG;EmD/CI;IAAiC,qBAAmC;GAAI;EACxE;IAAiC,yBAAuC;GAAI;EAC5E;IAAiC,2BAAyC;GAAI;EAC9E;IAAiC,4BAA0C;GAAI;EAC/E;IAAiC,0BAAwC;GAAI;EAC7E;IACE,2BAAwC;IACxC,0BAAuC;GACxC;EACD;IACE,yBAAuC;IACvC,4BAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,0BAAmC;GAAI;EACxE;IAAiC,8BAAuC;GAAI;EAC5E;IAAiC,gCAAyC;GAAI;EAC9E;IAAiC,iCAA0C;GAAI;EAC/E;IAAiC,+BAAwC;GAAI;EAC7E;IACE,gCAAwC;IACxC,+BAAuC;GACxC;EACD;IACE,8BAAuC;IACvC,iCAA0C;GAC3C;EAZD;IAAiC,wBAAmC;GAAI;EACxE;IAAiC,4BAAuC;GAAI;EAC5E;IAAiC,8BAAyC;GAAI;EAC9E;IAAiC,+BAA0C;GAAI;EAC/E;IAAiC,6BAAwC;GAAI;EAC7E;IACE,8BAAwC;IACxC,6BAAuC;GACxC;EACD;IACE,4BAAuC;IACvC,+BAA0C;GAC3C;EAZD;IAAiC,0BAAmC;GAAI;EACxE;IAAiC,8BAAuC;GAAI;EAC5E;IAAiC,gCAAyC;GAAI;EAC9E;IAAiC,iCAA0C;GAAI;EAC/E;IAAiC,+BAAwC;GAAI;EAC7E;IACE,gCAAwC;IACxC,+BAAuC;GACxC;EACD;IACE,8BAAuC;IACvC,iCAA0C;GAC3C;EAZD;IAAiC,wBAAmC;GAAI;EACxE;IAAiC,4BAAuC;GAAI;EAC5E;IAAiC,8BAAyC;GAAI;EAC9E;IAAiC,+BAA0C;GAAI;EAC/E;IAAiC,6BAAwC;GAAI;EAC7E;IACE,8BAAwC;IACxC,6BAAuC;GACxC;EACD;IACE,4BAAuC;IACvC,+BAA0C;GAC3C;EAZD;IAAiC,sBAAmC;GAAI;EACxE;IAAiC,0BAAuC;GAAI;EAC5E;IAAiC,4BAAyC;GAAI;EAC9E;IAAiC,6BAA0C;GAAI;EAC/E;IAAiC,2BAAwC;GAAI;EAC7E;IACE,4BAAwC;IACxC,2BAAuC;GACxC;EACD;IACE,0BAAuC;IACvC,6BAA0C;GAC3C;EAZD;IAAiC,4BAAmC;GAAI;EACxE;IAAiC,gCAAuC;GAAI;EAC5E;IAAiC,kCAAyC;GAAI;EAC9E;IAAiC,mCAA0C;GAAI;EAC/E;IAAiC,iCAAwC;GAAI;EAC7E;IACE,kCAAwC;IACxC,iCAAuC;GACxC;EACD;IACE,gCAAuC;IACvC,mCAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,yBAAmC;GAAI;EACxE;IAAiC,6BAAuC;GAAI;EAC5E;IAAiC,+BAAyC;GAAI;EAC9E;IAAiC,gCAA0C;GAAI;EAC/E;IAAiC,8BAAwC;GAAI;EAC7E;IACE,+BAAwC;IACxC,8BAAuC;GACxC;EACD;IACE,6BAAuC;IACvC,gCAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,yBAAmC;GAAI;EACxE;IAAiC,6BAAuC;GAAI;EAC5E;IAAiC,+BAAyC;GAAI;EAC9E;IAAiC,gCAA0C;GAAI;EAC/E;IAAiC,8BAAwC;GAAI;EAC7E;IACE,+BAAwC;IACxC,8BAAuC;GACxC;EACD;IACE,6BAAuC;IACvC,gCAA0C;GAC3C;EAKL;IAAoB,wBAA8B;GAAK;EACvD;IAAoB,4BAA8B;GAAK;EACvD;IAAoB,8BAA8B;GAAK;EACvD;IAAoB,+BAA8B;GAAK;EACvD;IAAoB,6BAA8B;GAAK;EACvD;IACE,8BAA6B;IAC7B,6BAA6B;GAC9B;EACD;IACE,4BAA8B;IAC9B,+BAA8B;GAC/B;ChEgvNJ;;Aa9tNG;EmD/CI;IAAiC,qBAAmC;GAAI;EACxE;IAAiC,yBAAuC;GAAI;EAC5E;IAAiC,2BAAyC;GAAI;EAC9E;IAAiC,4BAA0C;GAAI;EAC/E;IAAiC,0BAAwC;GAAI;EAC7E;IACE,2BAAwC;IACxC,0BAAuC;GACxC;EACD;IACE,yBAAuC;IACvC,4BAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,0BAAmC;GAAI;EACxE;IAAiC,8BAAuC;GAAI;EAC5E;IAAiC,gCAAyC;GAAI;EAC9E;IAAiC,iCAA0C;GAAI;EAC/E;IAAiC,+BAAwC;GAAI;EAC7E;IACE,gCAAwC;IACxC,+BAAuC;GACxC;EACD;IACE,8BAAuC;IACvC,iCAA0C;GAC3C;EAZD;IAAiC,wBAAmC;GAAI;EACxE;IAAiC,4BAAuC;GAAI;EAC5E;IAAiC,8BAAyC;GAAI;EAC9E;IAAiC,+BAA0C;GAAI;EAC/E;IAAiC,6BAAwC;GAAI;EAC7E;IACE,8BAAwC;IACxC,6BAAuC;GACxC;EACD;IACE,4BAAuC;IACvC,+BAA0C;GAC3C;EAZD;IAAiC,0BAAmC;GAAI;EACxE;IAAiC,8BAAuC;GAAI;EAC5E;IAAiC,gCAAyC;GAAI;EAC9E;IAAiC,iCAA0C;GAAI;EAC/E;IAAiC,+BAAwC;GAAI;EAC7E;IACE,gCAAwC;IACxC,+BAAuC;GACxC;EACD;IACE,8BAAuC;IACvC,iCAA0C;GAC3C;EAZD;IAAiC,wBAAmC;GAAI;EACxE;IAAiC,4BAAuC;GAAI;EAC5E;IAAiC,8BAAyC;GAAI;EAC9E;IAAiC,+BAA0C;GAAI;EAC/E;IAAiC,6BAAwC;GAAI;EAC7E;IACE,8BAAwC;IACxC,6BAAuC;GACxC;EACD;IACE,4BAAuC;IACvC,+BAA0C;GAC3C;EAZD;IAAiC,sBAAmC;GAAI;EACxE;IAAiC,0BAAuC;GAAI;EAC5E;IAAiC,4BAAyC;GAAI;EAC9E;IAAiC,6BAA0C;GAAI;EAC/E;IAAiC,2BAAwC;GAAI;EAC7E;IACE,4BAAwC;IACxC,2BAAuC;GACxC;EACD;IACE,0BAAuC;IACvC,6BAA0C;GAC3C;EAZD;IAAiC,4BAAmC;GAAI;EACxE;IAAiC,gCAAuC;GAAI;EAC5E;IAAiC,kCAAyC;GAAI;EAC9E;IAAiC,mCAA0C;GAAI;EAC/E;IAAiC,iCAAwC;GAAI;EAC7E;IACE,kCAAwC;IACxC,iCAAuC;GACxC;EACD;IACE,gCAAuC;IACvC,mCAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,yBAAmC;GAAI;EACxE;IAAiC,6BAAuC;GAAI;EAC5E;IAAiC,+BAAyC;GAAI;EAC9E;IAAiC,gCAA0C;GAAI;EAC/E;IAAiC,8BAAwC;GAAI;EAC7E;IACE,+BAAwC;IACxC,8BAAuC;GACxC;EACD;IACE,6BAAuC;IACvC,gCAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,yBAAmC;GAAI;EACxE;IAAiC,6BAAuC;GAAI;EAC5E;IAAiC,+BAAyC;GAAI;EAC9E;IAAiC,gCAA0C;GAAI;EAC/E;IAAiC,8BAAwC;GAAI;EAC7E;IACE,+BAAwC;IACxC,8BAAuC;GACxC;EACD;IACE,6BAAuC;IACvC,gCAA0C;GAC3C;EAKL;IAAoB,wBAA8B;GAAK;EACvD;IAAoB,4BAA8B;GAAK;EACvD;IAAoB,8BAA8B;GAAK;EACvD;IAAoB,+BAA8B;GAAK;EACvD;IAAoB,6BAA8B;GAAK;EACvD;IACE,8BAA6B;IAC7B,6BAA6B;GAC9B;EACD;IACE,4BAA8B;IAC9B,+BAA8B;GAC/B;ChE8hOJ;;Aa5gOG;EmD/CI;IAAiC,qBAAmC;GAAI;EACxE;IAAiC,yBAAuC;GAAI;EAC5E;IAAiC,2BAAyC;GAAI;EAC9E;IAAiC,4BAA0C;GAAI;EAC/E;IAAiC,0BAAwC;GAAI;EAC7E;IACE,2BAAwC;IACxC,0BAAuC;GACxC;EACD;IACE,yBAAuC;IACvC,4BAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,0BAAmC;GAAI;EACxE;IAAiC,8BAAuC;GAAI;EAC5E;IAAiC,gCAAyC;GAAI;EAC9E;IAAiC,iCAA0C;GAAI;EAC/E;IAAiC,+BAAwC;GAAI;EAC7E;IACE,gCAAwC;IACxC,+BAAuC;GACxC;EACD;IACE,8BAAuC;IACvC,iCAA0C;GAC3C;EAZD;IAAiC,wBAAmC;GAAI;EACxE;IAAiC,4BAAuC;GAAI;EAC5E;IAAiC,8BAAyC;GAAI;EAC9E;IAAiC,+BAA0C;GAAI;EAC/E;IAAiC,6BAAwC;GAAI;EAC7E;IACE,8BAAwC;IACxC,6BAAuC;GACxC;EACD;IACE,4BAAuC;IACvC,+BAA0C;GAC3C;EAZD;IAAiC,0BAAmC;GAAI;EACxE;IAAiC,8BAAuC;GAAI;EAC5E;IAAiC,gCAAyC;GAAI;EAC9E;IAAiC,iCAA0C;GAAI;EAC/E;IAAiC,+BAAwC;GAAI;EAC7E;IACE,gCAAwC;IACxC,+BAAuC;GACxC;EACD;IACE,8BAAuC;IACvC,iCAA0C;GAC3C;EAZD;IAAiC,wBAAmC;GAAI;EACxE;IAAiC,4BAAuC;GAAI;EAC5E;IAAiC,8BAAyC;GAAI;EAC9E;IAAiC,+BAA0C;GAAI;EAC/E;IAAiC,6BAAwC;GAAI;EAC7E;IACE,8BAAwC;IACxC,6BAAuC;GACxC;EACD;IACE,4BAAuC;IACvC,+BAA0C;GAC3C;EAZD;IAAiC,sBAAmC;GAAI;EACxE;IAAiC,0BAAuC;GAAI;EAC5E;IAAiC,4BAAyC;GAAI;EAC9E;IAAiC,6BAA0C;GAAI;EAC/E;IAAiC,2BAAwC;GAAI;EAC7E;IACE,4BAAwC;IACxC,2BAAuC;GACxC;EACD;IACE,0BAAuC;IACvC,6BAA0C;GAC3C;EAZD;IAAiC,4BAAmC;GAAI;EACxE;IAAiC,gCAAuC;GAAI;EAC5E;IAAiC,kCAAyC;GAAI;EAC9E;IAAiC,mCAA0C;GAAI;EAC/E;IAAiC,iCAAwC;GAAI;EAC7E;IACE,kCAAwC;IACxC,iCAAuC;GACxC;EACD;IACE,gCAAuC;IACvC,mCAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,yBAAmC;GAAI;EACxE;IAAiC,6BAAuC;GAAI;EAC5E;IAAiC,+BAAyC;GAAI;EAC9E;IAAiC,gCAA0C;GAAI;EAC/E;IAAiC,8BAAwC;GAAI;EAC7E;IACE,+BAAwC;IACxC,8BAAuC;GACxC;EACD;IACE,6BAAuC;IACvC,gCAA0C;GAC3C;EAZD;IAAiC,2BAAmC;GAAI;EACxE;IAAiC,+BAAuC;GAAI;EAC5E;IAAiC,iCAAyC;GAAI;EAC9E;IAAiC,kCAA0C;GAAI;EAC/E;IAAiC,gCAAwC;GAAI;EAC7E;IACE,iCAAwC;IACxC,gCAAuC;GACxC;EACD;IACE,+BAAuC;IACvC,kCAA0C;GAC3C;EAZD;IAAiC,yBAAmC;GAAI;EACxE;IAAiC,6BAAuC;GAAI;EAC5E;IAAiC,+BAAyC;GAAI;EAC9E;IAAiC,gCAA0C;GAAI;EAC/E;IAAiC,8BAAwC;GAAI;EAC7E;IACE,+BAAwC;IACxC,8BAAuC;GACxC;EACD;IACE,6BAAuC;IACvC,gCAA0C;GAC3C;EAKL;IAAoB,wBAA8B;GAAK;EACvD;IAAoB,4BAA8B;GAAK;EACvD;IAAoB,8BAA8B;GAAK;EACvD;IAAoB,+BAA8B;GAAK;EACvD;IAAoB,6BAA8B;GAAK;EACvD;IACE,8BAA6B;IAC7B,6BAA6B;GAC9B;EACD;IACE,4BAA8B;IAC9B,+BAA8B;GAC/B;ChE40OJ;;AiE52OD;EAAiB,+BAA8B;CAAK;;AACpD;EAAiB,+BAA8B;CAAK;;AACpD;ECJE,iBAAgB;EAChB,wBAAuB;EACvB,oBAAmB;CDEsB;;AAQvC;EAAwB,4BAA2B;CAAK;;AACxD;EAAwB,6BAA4B;CAAK;;AACzD;EAAwB,8BAA6B;CAAK;;ApDsC1D;EoDxCA;IAAwB,4BAA2B;GAAK;EACxD;IAAwB,6BAA4B;GAAK;EACzD;IAAwB,8BAA6B;GAAK;CjEs4O7D;;Aah2OG;EoDxCA;IAAwB,4BAA2B;GAAK;EACxD;IAAwB,6BAA4B;GAAK;EACzD;IAAwB,8BAA6B;GAAK;CjEk5O7D;;Aa52OG;EoDxCA;IAAwB,4BAA2B;GAAK;EACxD;IAAwB,6BAA4B;GAAK;EACzD;IAAwB,8BAA6B;GAAK;CjE85O7D;;Aax3OG;EoDxCA;IAAwB,4BAA2B;GAAK;EACxD;IAAwB,6BAA4B;GAAK;EACzD;IAAwB,8BAA6B;GAAK;CjE06O7D;;AiEp6OD;EAAmB,qCAAoC;CAAK;;AAC5D;EAAmB,qCAAoC;CAAK;;AAC5D;EAAmB,sCAAqC;CAAK;;AAI7D;EAAsB,oB/DmNK;C+DnN+B;;AAC1D;EAAsB,kB/DmNC;C+DnNiC;;AACxD;EAAsB,mBAAkB;CAAK;;AAI7C;EAAc,uBAAsB;CAAK;;AEjCvC;EACE,0BAAwB;CACzB;;AhEiBC;EgEdE,0BAAqC;ChEiBtC;;AgEtBH;EACE,0BAAwB;CACzB;;AhEiBC;EgEdE,0BAAqC;ChEiBtC;;AgEtBH;EACE,0BAAwB;CACzB;;AhEiBC;EgEdE,0BAAqC;ChEiBtC;;AgEtBH;EACE,0BAAwB;CACzB;;AhEiBC;EgEdE,0BAAqC;ChEiBtC;;AgEtBH;EACE,0BAAwB;CACzB;;AhEiBC;EgEdE,0BAAqC;ChEiBtC;;AgEtBH;EACE,0BAAwB;CACzB;;AhEiBC;EgEdE,0BAAqC;ChEiBtC;;AgEtBH;EACE,0BAAwB;CACzB;;AhEiBC;EgEdE,0BAAqC;ChEiBtC;;AgEtBH;EACE,0BAAwB;CACzB;;AhEiBC;EgEdE,0BAAqC;ChEiBtC;;A8DiBL;EAAc,0BAA6B;CAAI;;AAI/C;EG5CE,YAAW;EACX,mBAAkB;EAClB,kBAAiB;EACjB,8BAA6B;EAC7B,UAAS;CH0CV;;AI5CD;ECDE,+BAAkC;CDGnC;;AAED;ECLE,8BAAkC;CDOnC", "file": "bootstrap.css", "sourcesContent": ["/*!\n * Bootstrap v4.0.0-beta (https://getbootstrap.com)\n * Copyright 2011-2017 The Bootstrap Authors\n * Copyright 2011-2017 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n\n@import \"functions\";\n@import \"variables\";\n@import \"mixins\";\n@import \"print\";\n@import \"reboot\";\n@import \"type\";\n@import \"images\";\n@import \"code\";\n@import \"grid\";\n@import \"tables\";\n@import \"forms\";\n@import \"buttons\";\n@import \"transitions\";\n@import \"dropdown\";\n@import \"button-group\";\n@import \"input-group\";\n@import \"custom-forms\";\n@import \"nav\";\n@import \"navbar\";\n@import \"card\";\n@import \"breadcrumb\";\n@import \"pagination\";\n@import \"badge\";\n@import \"jumbotron\";\n@import \"alert\";\n@import \"progress\";\n@import \"media\";\n@import \"list-group\";\n@import \"close\";\n@import \"modal\";\n@import \"tooltip\";\n@import \"popover\";\n@import \"carousel\";\n@import \"utilities\";\n", "// scss-lint:disable QualifyingElement\n\n// Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css\n\n// ==========================================================================\n// Print styles.\n// Inlined to avoid the additional HTTP request:\n// http://www.phpied.com/delay-loading-your-print-css/\n// ==========================================================================\n\n@if $enable-print-styles {\n  @media print {\n    *,\n    *::before,\n    *::after {\n      // Bootstrap specific; comment out `color` and `background`\n      //color: #000 !important; // Black prints faster:\n                                //   http://www.sanbeiji.com/archives/953\n      text-shadow: none !important;\n      //background: transparent !important;\n      box-shadow: none !important;\n    }\n\n    a,\n    a:visited {\n      text-decoration: underline;\n    }\n\n    // Bootstrap specific; comment the following selector out\n    //a[href]::after {\n    //  content: \" (\" attr(href) \")\";\n    //}\n\n    abbr[title]::after {\n      content: \" (\" attr(title) \")\";\n    }\n\n    // Bootstrap specific; comment the following selector out\n    //\n    // Don't show links that are fragment identifiers,\n    // or use the `javascript:` pseudo protocol\n    //\n\n    //a[href^=\"#\"]::after,\n    //a[href^=\"javascript:\"]::after {\n    // content: \"\";\n    //}\n\n    pre {\n      white-space: pre-wrap !important;\n    }\n    pre,\n    blockquote {\n      border: $border-width solid #999;   // Bootstrap custom code; using `$border-width` instead of 1px\n      page-break-inside: avoid;\n    }\n\n    //\n    // Printing Tables:\n    // http://css-discuss.incutio.com/wiki/Printing_Tables\n    //\n\n    thead {\n      display: table-header-group;\n    }\n\n    tr,\n    img {\n      page-break-inside: avoid;\n    }\n\n    p,\n    h2,\n    h3 {\n      orphans: 3;\n      widows: 3;\n    }\n\n    h2,\n    h3 {\n      page-break-after: avoid;\n    }\n\n    // Bootstrap specific changes start\n\n    // Bootstrap components\n    .navbar {\n      display: none;\n    }\n    .badge {\n      border: $border-width solid #000;\n    }\n\n    .table {\n      border-collapse: collapse !important;\n\n      td,\n      th {\n        background-color: #fff !important;\n      }\n    }\n    .table-bordered {\n      th,\n      td {\n        border: 1px solid #ddd !important;\n      }\n    }\n\n    // Bootstrap specific changes end\n  }\n}\n", "/*!\n * Bootstrap v4.0.0-beta (https://getbootstrap.com)\n * Copyright 2011-2017 The Bootstrap Authors\n * Copyright 2011-2017 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n@media print {\n  *,\n  *::before,\n  *::after {\n    text-shadow: none !important;\n    box-shadow: none !important;\n  }\n  a,\n  a:visited {\n    text-decoration: underline;\n  }\n  abbr[title]::after {\n    content: \" (\" attr(title) \")\";\n  }\n  pre {\n    white-space: pre-wrap !important;\n  }\n  pre,\n  blockquote {\n    border: 1px solid #999;\n    page-break-inside: avoid;\n  }\n  thead {\n    display: table-header-group;\n  }\n  tr,\n  img {\n    page-break-inside: avoid;\n  }\n  p,\n  h2,\n  h3 {\n    orphans: 3;\n    widows: 3;\n  }\n  h2,\n  h3 {\n    page-break-after: avoid;\n  }\n  .navbar {\n    display: none;\n  }\n  .badge {\n    border: 1px solid #000;\n  }\n  .table {\n    border-collapse: collapse !important;\n  }\n  .table td,\n  .table th {\n    background-color: #fff !important;\n  }\n  .table-bordered th,\n  .table-bordered td {\n    border: 1px solid #ddd !important;\n  }\n}\n\nhtml {\n  box-sizing: border-box;\n  font-family: sans-serif;\n  line-height: 1.15;\n  -webkit-text-size-adjust: 100%;\n  -ms-text-size-adjust: 100%;\n  -ms-overflow-style: scrollbar;\n  -webkit-tap-highlight-color: transparent;\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: inherit;\n}\n\n@-ms-viewport {\n  width: device-width;\n}\n\narticle, aside, dialog, figcaption, figure, footer, header, hgroup, main, nav, section {\n  display: block;\n}\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n  font-size: 1rem;\n  font-weight: normal;\n  line-height: 1.5;\n  color: #212529;\n  background-color: #fff;\n}\n\n[tabindex=\"-1\"]:focus {\n  outline: none !important;\n}\n\nhr {\n  box-sizing: content-box;\n  height: 0;\n  overflow: visible;\n}\n\nh1, h2, h3, h4, h5, h6 {\n  margin-top: 0;\n  margin-bottom: .5rem;\n}\n\np {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nabbr[title],\nabbr[data-original-title] {\n  text-decoration: underline;\n  text-decoration: underline dotted;\n  cursor: help;\n  border-bottom: 0;\n}\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: bold;\n}\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0;\n}\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\ndfn {\n  font-style: italic;\n}\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\nsmall {\n  font-size: 80%;\n}\n\nsub,\nsup {\n  position: relative;\n  font-size: 75%;\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -.25em;\n}\n\nsup {\n  top: -.5em;\n}\n\na {\n  color: #007bff;\n  text-decoration: none;\n  background-color: transparent;\n  -webkit-text-decoration-skip: objects;\n}\n\na:hover {\n  color: #0056b3;\n  text-decoration: underline;\n}\n\na:not([href]):not([tabindex]) {\n  color: inherit;\n  text-decoration: none;\n}\n\na:not([href]):not([tabindex]):focus, a:not([href]):not([tabindex]):hover {\n  color: inherit;\n  text-decoration: none;\n}\n\na:not([href]):not([tabindex]):focus {\n  outline: 0;\n}\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: monospace, monospace;\n  font-size: 1em;\n}\n\npre {\n  margin-top: 0;\n  margin-bottom: 1rem;\n  overflow: auto;\n}\n\nfigure {\n  margin: 0 0 1rem;\n}\n\nimg {\n  vertical-align: middle;\n  border-style: none;\n}\n\nsvg:not(:root) {\n  overflow: hidden;\n}\n\na,\narea,\nbutton,\n[role=\"button\"],\ninput,\nlabel,\nselect,\nsummary,\ntextarea {\n  touch-action: manipulation;\n}\n\ntable {\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  color: #868e96;\n  text-align: left;\n  caption-side: bottom;\n}\n\nth {\n  text-align: left;\n}\n\nlabel {\n  display: inline-block;\n  margin-bottom: .5rem;\n}\n\nbutton:focus {\n  outline: 1px dotted;\n  outline: 5px auto -webkit-focus-ring-color;\n}\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n\nbutton,\ninput {\n  overflow: visible;\n}\n\nbutton,\nselect {\n  text-transform: none;\n}\n\nbutton,\nhtml [type=\"button\"],\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button;\n}\n\nbutton::-moz-focus-inner,\n[type=\"button\"]::-moz-focus-inner,\n[type=\"reset\"]::-moz-focus-inner,\n[type=\"submit\"]::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\ninput[type=\"radio\"],\ninput[type=\"checkbox\"] {\n  box-sizing: border-box;\n  padding: 0;\n}\n\ninput[type=\"date\"],\ninput[type=\"time\"],\ninput[type=\"datetime-local\"],\ninput[type=\"month\"] {\n  -webkit-appearance: listbox;\n}\n\ntextarea {\n  overflow: auto;\n  resize: vertical;\n}\n\nfieldset {\n  min-width: 0;\n  padding: 0;\n  margin: 0;\n  border: 0;\n}\n\nlegend {\n  display: block;\n  width: 100%;\n  max-width: 100%;\n  padding: 0;\n  margin-bottom: .5rem;\n  font-size: 1.5rem;\n  line-height: inherit;\n  color: inherit;\n  white-space: normal;\n}\n\nprogress {\n  vertical-align: baseline;\n}\n\n[type=\"number\"]::-webkit-inner-spin-button,\n[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n[type=\"search\"] {\n  outline-offset: -2px;\n  -webkit-appearance: none;\n}\n\n[type=\"search\"]::-webkit-search-cancel-button,\n[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n::-webkit-file-upload-button {\n  font: inherit;\n  -webkit-appearance: button;\n}\n\noutput {\n  display: inline-block;\n}\n\nsummary {\n  display: list-item;\n}\n\ntemplate {\n  display: none;\n}\n\n[hidden] {\n  display: none !important;\n}\n\nh1, h2, h3, h4, h5, h6,\n.h1, .h2, .h3, .h4, .h5, .h6 {\n  margin-bottom: 0.5rem;\n  font-family: inherit;\n  font-weight: 500;\n  line-height: 1.1;\n  color: inherit;\n}\n\nh1, .h1 {\n  font-size: 2.5rem;\n}\n\nh2, .h2 {\n  font-size: 2rem;\n}\n\nh3, .h3 {\n  font-size: 1.75rem;\n}\n\nh4, .h4 {\n  font-size: 1.5rem;\n}\n\nh5, .h5 {\n  font-size: 1.25rem;\n}\n\nh6, .h6 {\n  font-size: 1rem;\n}\n\n.lead {\n  font-size: 1.25rem;\n  font-weight: 300;\n}\n\n.display-1 {\n  font-size: 6rem;\n  font-weight: 300;\n  line-height: 1.1;\n}\n\n.display-2 {\n  font-size: 5.5rem;\n  font-weight: 300;\n  line-height: 1.1;\n}\n\n.display-3 {\n  font-size: 4.5rem;\n  font-weight: 300;\n  line-height: 1.1;\n}\n\n.display-4 {\n  font-size: 3.5rem;\n  font-weight: 300;\n  line-height: 1.1;\n}\n\nhr {\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n  border: 0;\n  border-top: 1px solid rgba(0, 0, 0, 0.1);\n}\n\nsmall,\n.small {\n  font-size: 80%;\n  font-weight: normal;\n}\n\nmark,\n.mark {\n  padding: 0.2em;\n  background-color: #fcf8e3;\n}\n\n.list-unstyled {\n  padding-left: 0;\n  list-style: none;\n}\n\n.list-inline {\n  padding-left: 0;\n  list-style: none;\n}\n\n.list-inline-item {\n  display: inline-block;\n}\n\n.list-inline-item:not(:last-child) {\n  margin-right: 5px;\n}\n\n.initialism {\n  font-size: 90%;\n  text-transform: uppercase;\n}\n\n.blockquote {\n  margin-bottom: 1rem;\n  font-size: 1.25rem;\n}\n\n.blockquote-footer {\n  display: block;\n  font-size: 80%;\n  color: #868e96;\n}\n\n.blockquote-footer::before {\n  content: \"\\2014 \\00A0\";\n}\n\n.img-fluid {\n  max-width: 100%;\n  height: auto;\n}\n\n.img-thumbnail {\n  padding: 0.25rem;\n  background-color: #fff;\n  border: 1px solid #ddd;\n  border-radius: 0.25rem;\n  transition: all 0.2s ease-in-out;\n  max-width: 100%;\n  height: auto;\n}\n\n.figure {\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: 0.5rem;\n  line-height: 1;\n}\n\n.figure-caption {\n  font-size: 90%;\n  color: #868e96;\n}\n\ncode,\nkbd,\npre,\nsamp {\n  font-family: Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\n\ncode {\n  padding: 0.2rem 0.4rem;\n  font-size: 90%;\n  color: #bd4147;\n  background-color: #f8f9fa;\n  border-radius: 0.25rem;\n}\n\na > code {\n  padding: 0;\n  color: inherit;\n  background-color: inherit;\n}\n\nkbd {\n  padding: 0.2rem 0.4rem;\n  font-size: 90%;\n  color: #fff;\n  background-color: #212529;\n  border-radius: 0.2rem;\n}\n\nkbd kbd {\n  padding: 0;\n  font-size: 100%;\n  font-weight: bold;\n}\n\npre {\n  display: block;\n  margin-top: 0;\n  margin-bottom: 1rem;\n  font-size: 90%;\n  color: #212529;\n}\n\npre code {\n  padding: 0;\n  font-size: inherit;\n  color: inherit;\n  background-color: transparent;\n  border-radius: 0;\n}\n\n.pre-scrollable {\n  max-height: 340px;\n  overflow-y: scroll;\n}\n\n.container {\n  margin-right: auto;\n  margin-left: auto;\n  padding-right: 15px;\n  padding-left: 15px;\n  width: 100%;\n}\n\n@media (min-width: 576px) {\n  .container {\n    max-width: 540px;\n  }\n}\n\n@media (min-width: 768px) {\n  .container {\n    max-width: 720px;\n  }\n}\n\n@media (min-width: 992px) {\n  .container {\n    max-width: 960px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .container {\n    max-width: 1140px;\n  }\n}\n\n.container-fluid {\n  width: 100%;\n  margin-right: auto;\n  margin-left: auto;\n  padding-right: 15px;\n  padding-left: 15px;\n  width: 100%;\n}\n\n.row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -15px;\n  margin-left: -15px;\n}\n\n.no-gutters {\n  margin-right: 0;\n  margin-left: 0;\n}\n\n.no-gutters > .col,\n.no-gutters > [class*=\"col-\"] {\n  padding-right: 0;\n  padding-left: 0;\n}\n\n.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col,\n.col-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm,\n.col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md,\n.col-md-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg,\n.col-lg-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl,\n.col-xl-auto {\n  position: relative;\n  width: 100%;\n  min-height: 1px;\n  padding-right: 15px;\n  padding-left: 15px;\n}\n\n.col {\n  flex-basis: 0;\n  flex-grow: 1;\n  max-width: 100%;\n}\n\n.col-auto {\n  flex: 0 0 auto;\n  width: auto;\n  max-width: none;\n}\n\n.col-1 {\n  flex: 0 0 8.333333%;\n  max-width: 8.333333%;\n}\n\n.col-2 {\n  flex: 0 0 16.666667%;\n  max-width: 16.666667%;\n}\n\n.col-3 {\n  flex: 0 0 25%;\n  max-width: 25%;\n}\n\n.col-4 {\n  flex: 0 0 33.333333%;\n  max-width: 33.333333%;\n}\n\n.col-5 {\n  flex: 0 0 41.666667%;\n  max-width: 41.666667%;\n}\n\n.col-6 {\n  flex: 0 0 50%;\n  max-width: 50%;\n}\n\n.col-7 {\n  flex: 0 0 58.333333%;\n  max-width: 58.333333%;\n}\n\n.col-8 {\n  flex: 0 0 66.666667%;\n  max-width: 66.666667%;\n}\n\n.col-9 {\n  flex: 0 0 75%;\n  max-width: 75%;\n}\n\n.col-10 {\n  flex: 0 0 83.333333%;\n  max-width: 83.333333%;\n}\n\n.col-11 {\n  flex: 0 0 91.666667%;\n  max-width: 91.666667%;\n}\n\n.col-12 {\n  flex: 0 0 100%;\n  max-width: 100%;\n}\n\n.order-1 {\n  order: 1;\n}\n\n.order-2 {\n  order: 2;\n}\n\n.order-3 {\n  order: 3;\n}\n\n.order-4 {\n  order: 4;\n}\n\n.order-5 {\n  order: 5;\n}\n\n.order-6 {\n  order: 6;\n}\n\n.order-7 {\n  order: 7;\n}\n\n.order-8 {\n  order: 8;\n}\n\n.order-9 {\n  order: 9;\n}\n\n.order-10 {\n  order: 10;\n}\n\n.order-11 {\n  order: 11;\n}\n\n.order-12 {\n  order: 12;\n}\n\n@media (min-width: 576px) {\n  .col-sm {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-sm-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: none;\n  }\n  .col-sm-1 {\n    flex: 0 0 8.333333%;\n    max-width: 8.333333%;\n  }\n  .col-sm-2 {\n    flex: 0 0 16.666667%;\n    max-width: 16.666667%;\n  }\n  .col-sm-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-sm-4 {\n    flex: 0 0 33.333333%;\n    max-width: 33.333333%;\n  }\n  .col-sm-5 {\n    flex: 0 0 41.666667%;\n    max-width: 41.666667%;\n  }\n  .col-sm-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-sm-7 {\n    flex: 0 0 58.333333%;\n    max-width: 58.333333%;\n  }\n  .col-sm-8 {\n    flex: 0 0 66.666667%;\n    max-width: 66.666667%;\n  }\n  .col-sm-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-sm-10 {\n    flex: 0 0 83.333333%;\n    max-width: 83.333333%;\n  }\n  .col-sm-11 {\n    flex: 0 0 91.666667%;\n    max-width: 91.666667%;\n  }\n  .col-sm-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-sm-1 {\n    order: 1;\n  }\n  .order-sm-2 {\n    order: 2;\n  }\n  .order-sm-3 {\n    order: 3;\n  }\n  .order-sm-4 {\n    order: 4;\n  }\n  .order-sm-5 {\n    order: 5;\n  }\n  .order-sm-6 {\n    order: 6;\n  }\n  .order-sm-7 {\n    order: 7;\n  }\n  .order-sm-8 {\n    order: 8;\n  }\n  .order-sm-9 {\n    order: 9;\n  }\n  .order-sm-10 {\n    order: 10;\n  }\n  .order-sm-11 {\n    order: 11;\n  }\n  .order-sm-12 {\n    order: 12;\n  }\n}\n\n@media (min-width: 768px) {\n  .col-md {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-md-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: none;\n  }\n  .col-md-1 {\n    flex: 0 0 8.333333%;\n    max-width: 8.333333%;\n  }\n  .col-md-2 {\n    flex: 0 0 16.666667%;\n    max-width: 16.666667%;\n  }\n  .col-md-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-md-4 {\n    flex: 0 0 33.333333%;\n    max-width: 33.333333%;\n  }\n  .col-md-5 {\n    flex: 0 0 41.666667%;\n    max-width: 41.666667%;\n  }\n  .col-md-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-md-7 {\n    flex: 0 0 58.333333%;\n    max-width: 58.333333%;\n  }\n  .col-md-8 {\n    flex: 0 0 66.666667%;\n    max-width: 66.666667%;\n  }\n  .col-md-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-md-10 {\n    flex: 0 0 83.333333%;\n    max-width: 83.333333%;\n  }\n  .col-md-11 {\n    flex: 0 0 91.666667%;\n    max-width: 91.666667%;\n  }\n  .col-md-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-md-1 {\n    order: 1;\n  }\n  .order-md-2 {\n    order: 2;\n  }\n  .order-md-3 {\n    order: 3;\n  }\n  .order-md-4 {\n    order: 4;\n  }\n  .order-md-5 {\n    order: 5;\n  }\n  .order-md-6 {\n    order: 6;\n  }\n  .order-md-7 {\n    order: 7;\n  }\n  .order-md-8 {\n    order: 8;\n  }\n  .order-md-9 {\n    order: 9;\n  }\n  .order-md-10 {\n    order: 10;\n  }\n  .order-md-11 {\n    order: 11;\n  }\n  .order-md-12 {\n    order: 12;\n  }\n}\n\n@media (min-width: 992px) {\n  .col-lg {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-lg-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: none;\n  }\n  .col-lg-1 {\n    flex: 0 0 8.333333%;\n    max-width: 8.333333%;\n  }\n  .col-lg-2 {\n    flex: 0 0 16.666667%;\n    max-width: 16.666667%;\n  }\n  .col-lg-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-lg-4 {\n    flex: 0 0 33.333333%;\n    max-width: 33.333333%;\n  }\n  .col-lg-5 {\n    flex: 0 0 41.666667%;\n    max-width: 41.666667%;\n  }\n  .col-lg-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-lg-7 {\n    flex: 0 0 58.333333%;\n    max-width: 58.333333%;\n  }\n  .col-lg-8 {\n    flex: 0 0 66.666667%;\n    max-width: 66.666667%;\n  }\n  .col-lg-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-lg-10 {\n    flex: 0 0 83.333333%;\n    max-width: 83.333333%;\n  }\n  .col-lg-11 {\n    flex: 0 0 91.666667%;\n    max-width: 91.666667%;\n  }\n  .col-lg-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-lg-1 {\n    order: 1;\n  }\n  .order-lg-2 {\n    order: 2;\n  }\n  .order-lg-3 {\n    order: 3;\n  }\n  .order-lg-4 {\n    order: 4;\n  }\n  .order-lg-5 {\n    order: 5;\n  }\n  .order-lg-6 {\n    order: 6;\n  }\n  .order-lg-7 {\n    order: 7;\n  }\n  .order-lg-8 {\n    order: 8;\n  }\n  .order-lg-9 {\n    order: 9;\n  }\n  .order-lg-10 {\n    order: 10;\n  }\n  .order-lg-11 {\n    order: 11;\n  }\n  .order-lg-12 {\n    order: 12;\n  }\n}\n\n@media (min-width: 1200px) {\n  .col-xl {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-xl-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: none;\n  }\n  .col-xl-1 {\n    flex: 0 0 8.333333%;\n    max-width: 8.333333%;\n  }\n  .col-xl-2 {\n    flex: 0 0 16.666667%;\n    max-width: 16.666667%;\n  }\n  .col-xl-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-xl-4 {\n    flex: 0 0 33.333333%;\n    max-width: 33.333333%;\n  }\n  .col-xl-5 {\n    flex: 0 0 41.666667%;\n    max-width: 41.666667%;\n  }\n  .col-xl-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-xl-7 {\n    flex: 0 0 58.333333%;\n    max-width: 58.333333%;\n  }\n  .col-xl-8 {\n    flex: 0 0 66.666667%;\n    max-width: 66.666667%;\n  }\n  .col-xl-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-xl-10 {\n    flex: 0 0 83.333333%;\n    max-width: 83.333333%;\n  }\n  .col-xl-11 {\n    flex: 0 0 91.666667%;\n    max-width: 91.666667%;\n  }\n  .col-xl-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-xl-1 {\n    order: 1;\n  }\n  .order-xl-2 {\n    order: 2;\n  }\n  .order-xl-3 {\n    order: 3;\n  }\n  .order-xl-4 {\n    order: 4;\n  }\n  .order-xl-5 {\n    order: 5;\n  }\n  .order-xl-6 {\n    order: 6;\n  }\n  .order-xl-7 {\n    order: 7;\n  }\n  .order-xl-8 {\n    order: 8;\n  }\n  .order-xl-9 {\n    order: 9;\n  }\n  .order-xl-10 {\n    order: 10;\n  }\n  .order-xl-11 {\n    order: 11;\n  }\n  .order-xl-12 {\n    order: 12;\n  }\n}\n\n.table {\n  width: 100%;\n  max-width: 100%;\n  margin-bottom: 1rem;\n  background-color: transparent;\n}\n\n.table th,\n.table td {\n  padding: 0.75rem;\n  vertical-align: top;\n  border-top: 1px solid #e9ecef;\n}\n\n.table thead th {\n  vertical-align: bottom;\n  border-bottom: 2px solid #e9ecef;\n}\n\n.table tbody + tbody {\n  border-top: 2px solid #e9ecef;\n}\n\n.table .table {\n  background-color: #fff;\n}\n\n.table-sm th,\n.table-sm td {\n  padding: 0.3rem;\n}\n\n.table-bordered {\n  border: 1px solid #e9ecef;\n}\n\n.table-bordered th,\n.table-bordered td {\n  border: 1px solid #e9ecef;\n}\n\n.table-bordered thead th,\n.table-bordered thead td {\n  border-bottom-width: 2px;\n}\n\n.table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n.table-hover tbody tr:hover {\n  background-color: rgba(0, 0, 0, 0.075);\n}\n\n.table-primary,\n.table-primary > th,\n.table-primary > td {\n  background-color: #b8daff;\n}\n\n.table-hover .table-primary:hover {\n  background-color: #9fcdff;\n}\n\n.table-hover .table-primary:hover > td,\n.table-hover .table-primary:hover > th {\n  background-color: #9fcdff;\n}\n\n.table-secondary,\n.table-secondary > th,\n.table-secondary > td {\n  background-color: #dddfe2;\n}\n\n.table-hover .table-secondary:hover {\n  background-color: #cfd2d6;\n}\n\n.table-hover .table-secondary:hover > td,\n.table-hover .table-secondary:hover > th {\n  background-color: #cfd2d6;\n}\n\n.table-success,\n.table-success > th,\n.table-success > td {\n  background-color: #c3e6cb;\n}\n\n.table-hover .table-success:hover {\n  background-color: #b1dfbb;\n}\n\n.table-hover .table-success:hover > td,\n.table-hover .table-success:hover > th {\n  background-color: #b1dfbb;\n}\n\n.table-info,\n.table-info > th,\n.table-info > td {\n  background-color: #bee5eb;\n}\n\n.table-hover .table-info:hover {\n  background-color: #abdde5;\n}\n\n.table-hover .table-info:hover > td,\n.table-hover .table-info:hover > th {\n  background-color: #abdde5;\n}\n\n.table-warning,\n.table-warning > th,\n.table-warning > td {\n  background-color: #ffeeba;\n}\n\n.table-hover .table-warning:hover {\n  background-color: #ffe8a1;\n}\n\n.table-hover .table-warning:hover > td,\n.table-hover .table-warning:hover > th {\n  background-color: #ffe8a1;\n}\n\n.table-danger,\n.table-danger > th,\n.table-danger > td {\n  background-color: #f5c6cb;\n}\n\n.table-hover .table-danger:hover {\n  background-color: #f1b0b7;\n}\n\n.table-hover .table-danger:hover > td,\n.table-hover .table-danger:hover > th {\n  background-color: #f1b0b7;\n}\n\n.table-light,\n.table-light > th,\n.table-light > td {\n  background-color: #fdfdfe;\n}\n\n.table-hover .table-light:hover {\n  background-color: #ececf6;\n}\n\n.table-hover .table-light:hover > td,\n.table-hover .table-light:hover > th {\n  background-color: #ececf6;\n}\n\n.table-dark,\n.table-dark > th,\n.table-dark > td {\n  background-color: #c6c8ca;\n}\n\n.table-hover .table-dark:hover {\n  background-color: #b9bbbe;\n}\n\n.table-hover .table-dark:hover > td,\n.table-hover .table-dark:hover > th {\n  background-color: #b9bbbe;\n}\n\n.table-active,\n.table-active > th,\n.table-active > td {\n  background-color: rgba(0, 0, 0, 0.075);\n}\n\n.table-hover .table-active:hover {\n  background-color: rgba(0, 0, 0, 0.075);\n}\n\n.table-hover .table-active:hover > td,\n.table-hover .table-active:hover > th {\n  background-color: rgba(0, 0, 0, 0.075);\n}\n\n.thead-inverse th {\n  color: #fff;\n  background-color: #212529;\n}\n\n.thead-default th {\n  color: #495057;\n  background-color: #e9ecef;\n}\n\n.table-inverse {\n  color: #fff;\n  background-color: #212529;\n}\n\n.table-inverse th,\n.table-inverse td,\n.table-inverse thead th {\n  border-color: #32383e;\n}\n\n.table-inverse.table-bordered {\n  border: 0;\n}\n\n.table-inverse.table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n\n.table-inverse.table-hover tbody tr:hover {\n  background-color: rgba(255, 255, 255, 0.075);\n}\n\n@media (max-width: 991px) {\n  .table-responsive {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -ms-overflow-style: -ms-autohiding-scrollbar;\n  }\n  .table-responsive.table-bordered {\n    border: 0;\n  }\n}\n\n.form-control {\n  display: block;\n  width: 100%;\n  padding: 0.5rem 0.75rem;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #495057;\n  background-color: #fff;\n  background-image: none;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 0.25rem;\n  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;\n}\n\n.form-control::-ms-expand {\n  background-color: transparent;\n  border: 0;\n}\n\n.form-control:focus {\n  color: #495057;\n  background-color: #fff;\n  border-color: #80bdff;\n  outline: none;\n}\n\n.form-control::placeholder {\n  color: #868e96;\n  opacity: 1;\n}\n\n.form-control:disabled, .form-control[readonly] {\n  background-color: #e9ecef;\n  opacity: 1;\n}\n\nselect.form-control:not([size]):not([multiple]) {\n  height: calc(2.25rem + 2px);\n}\n\nselect.form-control:focus::-ms-value {\n  color: #495057;\n  background-color: #fff;\n}\n\n.form-control-file,\n.form-control-range {\n  display: block;\n}\n\n.col-form-label {\n  padding-top: calc(0.5rem - 1px * 2);\n  padding-bottom: calc(0.5rem - 1px * 2);\n  margin-bottom: 0;\n}\n\n.col-form-label-lg {\n  padding-top: calc(0.5rem - 1px * 2);\n  padding-bottom: calc(0.5rem - 1px * 2);\n  font-size: 1.25rem;\n}\n\n.col-form-label-sm {\n  padding-top: calc(0.25rem - 1px * 2);\n  padding-bottom: calc(0.25rem - 1px * 2);\n  font-size: 0.875rem;\n}\n\n.col-form-legend {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  margin-bottom: 0;\n  font-size: 1rem;\n}\n\n.form-control-plaintext {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  margin-bottom: 0;\n  line-height: 1.25;\n  border: solid transparent;\n  border-width: 1px 0;\n}\n\n.form-control-plaintext.form-control-sm, .input-group-sm > .form-control-plaintext.form-control,\n.input-group-sm > .form-control-plaintext.input-group-addon,\n.input-group-sm > .input-group-btn > .form-control-plaintext.btn, .form-control-plaintext.form-control-lg, .input-group-lg > .form-control-plaintext.form-control,\n.input-group-lg > .form-control-plaintext.input-group-addon,\n.input-group-lg > .input-group-btn > .form-control-plaintext.btn {\n  padding-right: 0;\n  padding-left: 0;\n}\n\n.form-control-sm, .input-group-sm > .form-control,\n.input-group-sm > .input-group-addon,\n.input-group-sm > .input-group-btn > .btn {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 0.2rem;\n}\n\nselect.form-control-sm:not([size]):not([multiple]), .input-group-sm > select.form-control:not([size]):not([multiple]),\n.input-group-sm > select.input-group-addon:not([size]):not([multiple]),\n.input-group-sm > .input-group-btn > select.btn:not([size]):not([multiple]) {\n  height: calc(1.8125rem + 2px);\n}\n\n.form-control-lg, .input-group-lg > .form-control,\n.input-group-lg > .input-group-addon,\n.input-group-lg > .input-group-btn > .btn {\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n  border-radius: 0.3rem;\n}\n\nselect.form-control-lg:not([size]):not([multiple]), .input-group-lg > select.form-control:not([size]):not([multiple]),\n.input-group-lg > select.input-group-addon:not([size]):not([multiple]),\n.input-group-lg > .input-group-btn > select.btn:not([size]):not([multiple]) {\n  height: calc(2.3125rem + 2px);\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-text {\n  display: block;\n  margin-top: 0.25rem;\n}\n\n.form-row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -5px;\n  margin-left: -5px;\n}\n\n.form-row > .col,\n.form-row > [class*=\"col-\"] {\n  padding-right: 5px;\n  padding-left: 5px;\n}\n\n.form-check {\n  position: relative;\n  display: block;\n  margin-bottom: 0.5rem;\n}\n\n.form-check.disabled .form-check-label {\n  color: #868e96;\n}\n\n.form-check-label {\n  padding-left: 1.25rem;\n  margin-bottom: 0;\n}\n\n.form-check-input {\n  position: absolute;\n  margin-top: 0.25rem;\n  margin-left: -1.25rem;\n}\n\n.form-check-input:only-child {\n  position: static;\n}\n\n.form-check-inline {\n  display: inline-block;\n}\n\n.form-check-inline .form-check-label {\n  vertical-align: middle;\n}\n\n.form-check-inline + .form-check-inline {\n  margin-left: 0.75rem;\n}\n\n.invalid-feedback {\n  display: none;\n  margin-top: .25rem;\n  font-size: .875rem;\n  color: #dc3545;\n}\n\n.invalid-tooltip {\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n  display: none;\n  width: 250px;\n  padding: .5rem;\n  margin-top: .1rem;\n  font-size: .875rem;\n  line-height: 1;\n  color: #fff;\n  background-color: rgba(220, 53, 69, 0.8);\n  border-radius: .2rem;\n}\n\n.was-validated .form-control:valid, .form-control.is-valid, .was-validated\n.custom-select:valid,\n.custom-select.is-valid {\n  border-color: #28a745;\n}\n\n.was-validated .form-control:valid:focus, .form-control.is-valid:focus, .was-validated\n.custom-select:valid:focus,\n.custom-select.is-valid:focus {\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.was-validated .form-control:valid ~ .invalid-feedback,\n.was-validated .form-control:valid ~ .invalid-tooltip, .form-control.is-valid ~ .invalid-feedback,\n.form-control.is-valid ~ .invalid-tooltip, .was-validated\n.custom-select:valid ~ .invalid-feedback,\n.was-validated\n.custom-select:valid ~ .invalid-tooltip,\n.custom-select.is-valid ~ .invalid-feedback,\n.custom-select.is-valid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated .form-check-input:valid + .form-check-label, .form-check-input.is-valid + .form-check-label {\n  color: #28a745;\n}\n\n.was-validated .custom-control-input:valid ~ .custom-control-indicator, .custom-control-input.is-valid ~ .custom-control-indicator {\n  background-color: rgba(40, 167, 69, 0.25);\n}\n\n.was-validated .custom-control-input:valid ~ .custom-control-description, .custom-control-input.is-valid ~ .custom-control-description {\n  color: #28a745;\n}\n\n.was-validated .custom-file-input:valid ~ .custom-file-control, .custom-file-input.is-valid ~ .custom-file-control {\n  border-color: #28a745;\n}\n\n.was-validated .custom-file-input:valid ~ .custom-file-control::before, .custom-file-input.is-valid ~ .custom-file-control::before {\n  border-color: inherit;\n}\n\n.was-validated .custom-file-input:valid:focus, .custom-file-input.is-valid:focus {\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.was-validated .form-control:invalid, .form-control.is-invalid, .was-validated\n.custom-select:invalid,\n.custom-select.is-invalid {\n  border-color: #dc3545;\n}\n\n.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus, .was-validated\n.custom-select:invalid:focus,\n.custom-select.is-invalid:focus {\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.was-validated .form-control:invalid ~ .invalid-feedback,\n.was-validated .form-control:invalid ~ .invalid-tooltip, .form-control.is-invalid ~ .invalid-feedback,\n.form-control.is-invalid ~ .invalid-tooltip, .was-validated\n.custom-select:invalid ~ .invalid-feedback,\n.was-validated\n.custom-select:invalid ~ .invalid-tooltip,\n.custom-select.is-invalid ~ .invalid-feedback,\n.custom-select.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated .form-check-input:invalid + .form-check-label, .form-check-input.is-invalid + .form-check-label {\n  color: #dc3545;\n}\n\n.was-validated .custom-control-input:invalid ~ .custom-control-indicator, .custom-control-input.is-invalid ~ .custom-control-indicator {\n  background-color: rgba(220, 53, 69, 0.25);\n}\n\n.was-validated .custom-control-input:invalid ~ .custom-control-description, .custom-control-input.is-invalid ~ .custom-control-description {\n  color: #dc3545;\n}\n\n.was-validated .custom-file-input:invalid ~ .custom-file-control, .custom-file-input.is-invalid ~ .custom-file-control {\n  border-color: #dc3545;\n}\n\n.was-validated .custom-file-input:invalid ~ .custom-file-control::before, .custom-file-input.is-invalid ~ .custom-file-control::before {\n  border-color: inherit;\n}\n\n.was-validated .custom-file-input:invalid:focus, .custom-file-input.is-invalid:focus {\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.form-inline {\n  display: flex;\n  flex-flow: row wrap;\n  align-items: center;\n}\n\n.form-inline .form-check {\n  width: 100%;\n}\n\n@media (min-width: 576px) {\n  .form-inline label {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 0;\n  }\n  .form-inline .form-group {\n    display: flex;\n    flex: 0 0 auto;\n    flex-flow: row wrap;\n    align-items: center;\n    margin-bottom: 0;\n  }\n  .form-inline .form-control {\n    display: inline-block;\n    width: auto;\n    vertical-align: middle;\n  }\n  .form-inline .form-control-plaintext {\n    display: inline-block;\n  }\n  .form-inline .input-group {\n    width: auto;\n  }\n  .form-inline .form-control-label {\n    margin-bottom: 0;\n    vertical-align: middle;\n  }\n  .form-inline .form-check {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: auto;\n    margin-top: 0;\n    margin-bottom: 0;\n  }\n  .form-inline .form-check-label {\n    padding-left: 0;\n  }\n  .form-inline .form-check-input {\n    position: relative;\n    margin-top: 0;\n    margin-right: 0.25rem;\n    margin-left: 0;\n  }\n  .form-inline .custom-control {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding-left: 0;\n  }\n  .form-inline .custom-control-indicator {\n    position: static;\n    display: inline-block;\n    margin-right: 0.25rem;\n    vertical-align: text-bottom;\n  }\n  .form-inline .has-feedback .form-control-feedback {\n    top: 0;\n  }\n}\n\n.btn {\n  display: inline-block;\n  font-weight: normal;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: middle;\n  user-select: none;\n  border: 1px solid transparent;\n  padding: 0.5rem 0.75rem;\n  font-size: 1rem;\n  line-height: 1.25;\n  border-radius: 0.25rem;\n  transition: all 0.15s ease-in-out;\n}\n\n.btn:focus, .btn:hover {\n  text-decoration: none;\n}\n\n.btn:focus, .btn.focus {\n  outline: 0;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);\n}\n\n.btn.disabled, .btn:disabled {\n  opacity: .65;\n}\n\n.btn:active, .btn.active {\n  background-image: none;\n}\n\na.btn.disabled,\nfieldset[disabled] a.btn {\n  pointer-events: none;\n}\n\n.btn-primary {\n  color: #fff;\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.btn-primary:hover {\n  color: #fff;\n  background-color: #0069d9;\n  border-color: #0062cc;\n}\n\n.btn-primary:focus, .btn-primary.focus {\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5);\n}\n\n.btn-primary.disabled, .btn-primary:disabled {\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.btn-primary:active, .btn-primary.active,\n.show > .btn-primary.dropdown-toggle {\n  background-color: #0069d9;\n  background-image: none;\n  border-color: #0062cc;\n}\n\n.btn-secondary {\n  color: #fff;\n  background-color: #868e96;\n  border-color: #868e96;\n}\n\n.btn-secondary:hover {\n  color: #fff;\n  background-color: #727b84;\n  border-color: #6c757d;\n}\n\n.btn-secondary:focus, .btn-secondary.focus {\n  box-shadow: 0 0 0 3px rgba(134, 142, 150, 0.5);\n}\n\n.btn-secondary.disabled, .btn-secondary:disabled {\n  background-color: #868e96;\n  border-color: #868e96;\n}\n\n.btn-secondary:active, .btn-secondary.active,\n.show > .btn-secondary.dropdown-toggle {\n  background-color: #727b84;\n  background-image: none;\n  border-color: #6c757d;\n}\n\n.btn-success {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #28a745;\n}\n\n.btn-success:hover {\n  color: #fff;\n  background-color: #218838;\n  border-color: #1e7e34;\n}\n\n.btn-success:focus, .btn-success.focus {\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.5);\n}\n\n.btn-success.disabled, .btn-success:disabled {\n  background-color: #28a745;\n  border-color: #28a745;\n}\n\n.btn-success:active, .btn-success.active,\n.show > .btn-success.dropdown-toggle {\n  background-color: #218838;\n  background-image: none;\n  border-color: #1e7e34;\n}\n\n.btn-info {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n\n.btn-info:hover {\n  color: #fff;\n  background-color: #138496;\n  border-color: #117a8b;\n}\n\n.btn-info:focus, .btn-info.focus {\n  box-shadow: 0 0 0 3px rgba(23, 162, 184, 0.5);\n}\n\n.btn-info.disabled, .btn-info:disabled {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n\n.btn-info:active, .btn-info.active,\n.show > .btn-info.dropdown-toggle {\n  background-color: #138496;\n  background-image: none;\n  border-color: #117a8b;\n}\n\n.btn-warning {\n  color: #111;\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n\n.btn-warning:hover {\n  color: #111;\n  background-color: #e0a800;\n  border-color: #d39e00;\n}\n\n.btn-warning:focus, .btn-warning.focus {\n  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.5);\n}\n\n.btn-warning.disabled, .btn-warning:disabled {\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n\n.btn-warning:active, .btn-warning.active,\n.show > .btn-warning.dropdown-toggle {\n  background-color: #e0a800;\n  background-image: none;\n  border-color: #d39e00;\n}\n\n.btn-danger {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.btn-danger:hover {\n  color: #fff;\n  background-color: #c82333;\n  border-color: #bd2130;\n}\n\n.btn-danger:focus, .btn-danger.focus {\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.5);\n}\n\n.btn-danger.disabled, .btn-danger:disabled {\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.btn-danger:active, .btn-danger.active,\n.show > .btn-danger.dropdown-toggle {\n  background-color: #c82333;\n  background-image: none;\n  border-color: #bd2130;\n}\n\n.btn-light {\n  color: #111;\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n\n.btn-light:hover {\n  color: #111;\n  background-color: #e2e6ea;\n  border-color: #dae0e5;\n}\n\n.btn-light:focus, .btn-light.focus {\n  box-shadow: 0 0 0 3px rgba(248, 249, 250, 0.5);\n}\n\n.btn-light.disabled, .btn-light:disabled {\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n\n.btn-light:active, .btn-light.active,\n.show > .btn-light.dropdown-toggle {\n  background-color: #e2e6ea;\n  background-image: none;\n  border-color: #dae0e5;\n}\n\n.btn-dark {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.btn-dark:hover {\n  color: #fff;\n  background-color: #23272b;\n  border-color: #1d2124;\n}\n\n.btn-dark:focus, .btn-dark.focus {\n  box-shadow: 0 0 0 3px rgba(52, 58, 64, 0.5);\n}\n\n.btn-dark.disabled, .btn-dark:disabled {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.btn-dark:active, .btn-dark.active,\n.show > .btn-dark.dropdown-toggle {\n  background-color: #23272b;\n  background-image: none;\n  border-color: #1d2124;\n}\n\n.btn-outline-primary {\n  color: #007bff;\n  background-color: transparent;\n  background-image: none;\n  border-color: #007bff;\n}\n\n.btn-outline-primary:hover {\n  color: #fff;\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.btn-outline-primary:focus, .btn-outline-primary.focus {\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5);\n}\n\n.btn-outline-primary.disabled, .btn-outline-primary:disabled {\n  color: #007bff;\n  background-color: transparent;\n}\n\n.btn-outline-primary:active, .btn-outline-primary.active,\n.show > .btn-outline-primary.dropdown-toggle {\n  color: #fff;\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.btn-outline-secondary {\n  color: #868e96;\n  background-color: transparent;\n  background-image: none;\n  border-color: #868e96;\n}\n\n.btn-outline-secondary:hover {\n  color: #fff;\n  background-color: #868e96;\n  border-color: #868e96;\n}\n\n.btn-outline-secondary:focus, .btn-outline-secondary.focus {\n  box-shadow: 0 0 0 3px rgba(134, 142, 150, 0.5);\n}\n\n.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {\n  color: #868e96;\n  background-color: transparent;\n}\n\n.btn-outline-secondary:active, .btn-outline-secondary.active,\n.show > .btn-outline-secondary.dropdown-toggle {\n  color: #fff;\n  background-color: #868e96;\n  border-color: #868e96;\n}\n\n.btn-outline-success {\n  color: #28a745;\n  background-color: transparent;\n  background-image: none;\n  border-color: #28a745;\n}\n\n.btn-outline-success:hover {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #28a745;\n}\n\n.btn-outline-success:focus, .btn-outline-success.focus {\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.5);\n}\n\n.btn-outline-success.disabled, .btn-outline-success:disabled {\n  color: #28a745;\n  background-color: transparent;\n}\n\n.btn-outline-success:active, .btn-outline-success.active,\n.show > .btn-outline-success.dropdown-toggle {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #28a745;\n}\n\n.btn-outline-info {\n  color: #17a2b8;\n  background-color: transparent;\n  background-image: none;\n  border-color: #17a2b8;\n}\n\n.btn-outline-info:hover {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n\n.btn-outline-info:focus, .btn-outline-info.focus {\n  box-shadow: 0 0 0 3px rgba(23, 162, 184, 0.5);\n}\n\n.btn-outline-info.disabled, .btn-outline-info:disabled {\n  color: #17a2b8;\n  background-color: transparent;\n}\n\n.btn-outline-info:active, .btn-outline-info.active,\n.show > .btn-outline-info.dropdown-toggle {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n\n.btn-outline-warning {\n  color: #ffc107;\n  background-color: transparent;\n  background-image: none;\n  border-color: #ffc107;\n}\n\n.btn-outline-warning:hover {\n  color: #fff;\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n\n.btn-outline-warning:focus, .btn-outline-warning.focus {\n  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.5);\n}\n\n.btn-outline-warning.disabled, .btn-outline-warning:disabled {\n  color: #ffc107;\n  background-color: transparent;\n}\n\n.btn-outline-warning:active, .btn-outline-warning.active,\n.show > .btn-outline-warning.dropdown-toggle {\n  color: #fff;\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n\n.btn-outline-danger {\n  color: #dc3545;\n  background-color: transparent;\n  background-image: none;\n  border-color: #dc3545;\n}\n\n.btn-outline-danger:hover {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.btn-outline-danger:focus, .btn-outline-danger.focus {\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.5);\n}\n\n.btn-outline-danger.disabled, .btn-outline-danger:disabled {\n  color: #dc3545;\n  background-color: transparent;\n}\n\n.btn-outline-danger:active, .btn-outline-danger.active,\n.show > .btn-outline-danger.dropdown-toggle {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.btn-outline-light {\n  color: #f8f9fa;\n  background-color: transparent;\n  background-image: none;\n  border-color: #f8f9fa;\n}\n\n.btn-outline-light:hover {\n  color: #fff;\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n\n.btn-outline-light:focus, .btn-outline-light.focus {\n  box-shadow: 0 0 0 3px rgba(248, 249, 250, 0.5);\n}\n\n.btn-outline-light.disabled, .btn-outline-light:disabled {\n  color: #f8f9fa;\n  background-color: transparent;\n}\n\n.btn-outline-light:active, .btn-outline-light.active,\n.show > .btn-outline-light.dropdown-toggle {\n  color: #fff;\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n\n.btn-outline-dark {\n  color: #343a40;\n  background-color: transparent;\n  background-image: none;\n  border-color: #343a40;\n}\n\n.btn-outline-dark:hover {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.btn-outline-dark:focus, .btn-outline-dark.focus {\n  box-shadow: 0 0 0 3px rgba(52, 58, 64, 0.5);\n}\n\n.btn-outline-dark.disabled, .btn-outline-dark:disabled {\n  color: #343a40;\n  background-color: transparent;\n}\n\n.btn-outline-dark:active, .btn-outline-dark.active,\n.show > .btn-outline-dark.dropdown-toggle {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.btn-link {\n  font-weight: normal;\n  color: #007bff;\n  border-radius: 0;\n}\n\n.btn-link, .btn-link:active, .btn-link.active, .btn-link:disabled {\n  background-color: transparent;\n}\n\n.btn-link, .btn-link:focus, .btn-link:active {\n  border-color: transparent;\n  box-shadow: none;\n}\n\n.btn-link:hover {\n  border-color: transparent;\n}\n\n.btn-link:focus, .btn-link:hover {\n  color: #0056b3;\n  text-decoration: underline;\n  background-color: transparent;\n}\n\n.btn-link:disabled {\n  color: #868e96;\n}\n\n.btn-link:disabled:focus, .btn-link:disabled:hover {\n  text-decoration: none;\n}\n\n.btn-lg, .btn-group-lg > .btn {\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n  border-radius: 0.3rem;\n}\n\n.btn-sm, .btn-group-sm > .btn {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 0.2rem;\n}\n\n.btn-block {\n  display: block;\n  width: 100%;\n}\n\n.btn-block + .btn-block {\n  margin-top: 0.5rem;\n}\n\ninput[type=\"submit\"].btn-block,\ninput[type=\"reset\"].btn-block,\ninput[type=\"button\"].btn-block {\n  width: 100%;\n}\n\n.fade {\n  opacity: 0;\n  transition: opacity 0.15s linear;\n}\n\n.fade.show {\n  opacity: 1;\n}\n\n.collapse {\n  display: none;\n}\n\n.collapse.show {\n  display: block;\n}\n\ntr.collapse.show {\n  display: table-row;\n}\n\ntbody.collapse.show {\n  display: table-row-group;\n}\n\n.collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  transition: height 0.35s ease;\n}\n\n.dropup,\n.dropdown {\n  position: relative;\n}\n\n.dropdown-toggle::after {\n  display: inline-block;\n  width: 0;\n  height: 0;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0.3em solid;\n  border-right: 0.3em solid transparent;\n  border-left: 0.3em solid transparent;\n}\n\n.dropdown-toggle:empty::after {\n  margin-left: 0;\n}\n\n.dropup .dropdown-menu {\n  margin-top: 0;\n  margin-bottom: 0.125rem;\n}\n\n.dropup .dropdown-toggle::after {\n  border-top: 0;\n  border-bottom: 0.3em solid;\n}\n\n.dropdown-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: 1000;\n  display: none;\n  float: left;\n  min-width: 10rem;\n  padding: 0.5rem 0;\n  margin: 0.125rem 0 0;\n  font-size: 1rem;\n  color: #212529;\n  text-align: left;\n  list-style: none;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 0.25rem;\n}\n\n.dropdown-divider {\n  height: 0;\n  margin: 0.5rem 0;\n  overflow: hidden;\n  border-top: 1px solid #e9ecef;\n}\n\n.dropdown-item {\n  display: block;\n  width: 100%;\n  padding: 0.25rem 1.5rem;\n  clear: both;\n  font-weight: normal;\n  color: #212529;\n  text-align: inherit;\n  white-space: nowrap;\n  background: none;\n  border: 0;\n}\n\n.dropdown-item:focus, .dropdown-item:hover {\n  color: #16181b;\n  text-decoration: none;\n  background-color: #f8f9fa;\n}\n\n.dropdown-item.active, .dropdown-item:active {\n  color: #fff;\n  text-decoration: none;\n  background-color: #007bff;\n}\n\n.dropdown-item.disabled, .dropdown-item:disabled {\n  color: #868e96;\n  background-color: transparent;\n}\n\n.show > a {\n  outline: 0;\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n.dropdown-header {\n  display: block;\n  padding: 0.5rem 1.5rem;\n  margin-bottom: 0;\n  font-size: 0.875rem;\n  color: #868e96;\n  white-space: nowrap;\n}\n\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle;\n}\n\n.btn-group > .btn,\n.btn-group-vertical > .btn {\n  position: relative;\n  flex: 0 1 auto;\n  margin-bottom: 0;\n}\n\n.btn-group > .btn:hover,\n.btn-group-vertical > .btn:hover {\n  z-index: 2;\n}\n\n.btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active,\n.btn-group-vertical > .btn:focus,\n.btn-group-vertical > .btn:active,\n.btn-group-vertical > .btn.active {\n  z-index: 2;\n}\n\n.btn-group .btn + .btn,\n.btn-group .btn + .btn-group,\n.btn-group .btn-group + .btn,\n.btn-group .btn-group + .btn-group,\n.btn-group-vertical .btn + .btn,\n.btn-group-vertical .btn + .btn-group,\n.btn-group-vertical .btn-group + .btn,\n.btn-group-vertical .btn-group + .btn-group {\n  margin-left: -1px;\n}\n\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n}\n\n.btn-toolbar .input-group {\n  width: auto;\n}\n\n.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {\n  border-radius: 0;\n}\n\n.btn-group > .btn:first-child {\n  margin-left: 0;\n}\n\n.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.btn-group > .btn:last-child:not(:first-child),\n.btn-group > .dropdown-toggle:not(:first-child) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.btn-group > .btn-group {\n  float: left;\n}\n\n.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {\n  border-radius: 0;\n}\n\n.btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child,\n.btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.btn + .dropdown-toggle-split {\n  padding-right: 0.5625rem;\n  padding-left: 0.5625rem;\n}\n\n.btn + .dropdown-toggle-split::after {\n  margin-left: 0;\n}\n\n.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {\n  padding-right: 0.375rem;\n  padding-left: 0.375rem;\n}\n\n.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {\n  padding-right: 0.75rem;\n  padding-left: 0.75rem;\n}\n\n.btn-group-vertical {\n  display: inline-flex;\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n}\n\n.btn-group-vertical .btn,\n.btn-group-vertical .btn-group {\n  width: 100%;\n}\n\n.btn-group-vertical > .btn + .btn,\n.btn-group-vertical > .btn + .btn-group,\n.btn-group-vertical > .btn-group + .btn,\n.btn-group-vertical > .btn-group + .btn-group {\n  margin-top: -1px;\n  margin-left: 0;\n}\n\n.btn-group-vertical > .btn:not(:first-child):not(:last-child) {\n  border-radius: 0;\n}\n\n.btn-group-vertical > .btn:first-child:not(:last-child) {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.btn-group-vertical > .btn:last-child:not(:first-child) {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {\n  border-radius: 0;\n}\n\n.btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child,\n.btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n\n[data-toggle=\"buttons\"] > .btn input[type=\"radio\"],\n[data-toggle=\"buttons\"] > .btn input[type=\"checkbox\"],\n[data-toggle=\"buttons\"] > .btn-group > .btn input[type=\"radio\"],\n[data-toggle=\"buttons\"] > .btn-group > .btn input[type=\"checkbox\"] {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n  pointer-events: none;\n}\n\n.input-group {\n  position: relative;\n  display: flex;\n  width: 100%;\n}\n\n.input-group .form-control {\n  position: relative;\n  z-index: 2;\n  flex: 1 1 auto;\n  width: 1%;\n  margin-bottom: 0;\n}\n\n.input-group .form-control:focus, .input-group .form-control:active, .input-group .form-control:hover {\n  z-index: 3;\n}\n\n.input-group-addon,\n.input-group-btn,\n.input-group .form-control {\n  display: flex;\n  align-items: center;\n}\n\n.input-group-addon:not(:first-child):not(:last-child),\n.input-group-btn:not(:first-child):not(:last-child),\n.input-group .form-control:not(:first-child):not(:last-child) {\n  border-radius: 0;\n}\n\n.input-group-addon,\n.input-group-btn {\n  white-space: nowrap;\n  vertical-align: middle;\n}\n\n.input-group-addon {\n  padding: 0.5rem 0.75rem;\n  margin-bottom: 0;\n  font-size: 1rem;\n  font-weight: normal;\n  line-height: 1.25;\n  color: #495057;\n  text-align: center;\n  background-color: #e9ecef;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 0.25rem;\n}\n\n.input-group-addon.form-control-sm,\n.input-group-sm > .input-group-addon,\n.input-group-sm > .input-group-btn > .input-group-addon.btn {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  border-radius: 0.2rem;\n}\n\n.input-group-addon.form-control-lg,\n.input-group-lg > .input-group-addon,\n.input-group-lg > .input-group-btn > .input-group-addon.btn {\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  border-radius: 0.3rem;\n}\n\n.input-group-addon input[type=\"radio\"],\n.input-group-addon input[type=\"checkbox\"] {\n  margin-top: 0;\n}\n\n.input-group .form-control:not(:last-child),\n.input-group-addon:not(:last-child),\n.input-group-btn:not(:last-child) > .btn,\n.input-group-btn:not(:last-child) > .btn-group > .btn,\n.input-group-btn:not(:last-child) > .dropdown-toggle,\n.input-group-btn:not(:first-child) > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group-btn:not(:first-child) > .btn-group:not(:last-child) > .btn {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.input-group-addon:not(:last-child) {\n  border-right: 0;\n}\n\n.input-group .form-control:not(:first-child),\n.input-group-addon:not(:first-child),\n.input-group-btn:not(:first-child) > .btn,\n.input-group-btn:not(:first-child) > .btn-group > .btn,\n.input-group-btn:not(:first-child) > .dropdown-toggle,\n.input-group-btn:not(:last-child) > .btn:not(:first-child),\n.input-group-btn:not(:last-child) > .btn-group:not(:first-child) > .btn {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.form-control + .input-group-addon:not(:first-child) {\n  border-left: 0;\n}\n\n.input-group-btn {\n  position: relative;\n  font-size: 0;\n  white-space: nowrap;\n}\n\n.input-group-btn > .btn {\n  position: relative;\n}\n\n.input-group-btn > .btn + .btn {\n  margin-left: -1px;\n}\n\n.input-group-btn > .btn:focus, .input-group-btn > .btn:active, .input-group-btn > .btn:hover {\n  z-index: 3;\n}\n\n.input-group-btn:not(:last-child) > .btn,\n.input-group-btn:not(:last-child) > .btn-group {\n  margin-right: -1px;\n}\n\n.input-group-btn:not(:first-child) > .btn,\n.input-group-btn:not(:first-child) > .btn-group {\n  z-index: 2;\n  margin-left: -1px;\n}\n\n.input-group-btn:not(:first-child) > .btn:focus, .input-group-btn:not(:first-child) > .btn:active, .input-group-btn:not(:first-child) > .btn:hover,\n.input-group-btn:not(:first-child) > .btn-group:focus,\n.input-group-btn:not(:first-child) > .btn-group:active,\n.input-group-btn:not(:first-child) > .btn-group:hover {\n  z-index: 3;\n}\n\n.custom-control {\n  position: relative;\n  display: inline-flex;\n  min-height: 1.5rem;\n  padding-left: 1.5rem;\n  margin-right: 1rem;\n}\n\n.custom-control-input {\n  position: absolute;\n  z-index: -1;\n  opacity: 0;\n}\n\n.custom-control-input:checked ~ .custom-control-indicator {\n  color: #fff;\n  background-color: #007bff;\n}\n\n.custom-control-input:focus ~ .custom-control-indicator {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 3px #007bff;\n}\n\n.custom-control-input:active ~ .custom-control-indicator {\n  color: #fff;\n  background-color: #b3d7ff;\n}\n\n.custom-control-input:disabled ~ .custom-control-indicator {\n  background-color: #e9ecef;\n}\n\n.custom-control-input:disabled ~ .custom-control-description {\n  color: #868e96;\n}\n\n.custom-control-indicator {\n  position: absolute;\n  top: 0.25rem;\n  left: 0;\n  display: block;\n  width: 1rem;\n  height: 1rem;\n  pointer-events: none;\n  user-select: none;\n  background-color: #ddd;\n  background-repeat: no-repeat;\n  background-position: center center;\n  background-size: 50% 50%;\n}\n\n.custom-checkbox .custom-control-indicator {\n  border-radius: 0.25rem;\n}\n\n.custom-checkbox .custom-control-input:checked ~ .custom-control-indicator {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\");\n}\n\n.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-indicator {\n  background-color: #007bff;\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='%23fff' d='M0 2h4'/%3E%3C/svg%3E\");\n}\n\n.custom-radio .custom-control-indicator {\n  border-radius: 50%;\n}\n\n.custom-radio .custom-control-input:checked ~ .custom-control-indicator {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E\");\n}\n\n.custom-controls-stacked {\n  display: flex;\n  flex-direction: column;\n}\n\n.custom-controls-stacked .custom-control {\n  margin-bottom: 0.25rem;\n}\n\n.custom-controls-stacked .custom-control + .custom-control {\n  margin-left: 0;\n}\n\n.custom-select {\n  display: inline-block;\n  max-width: 100%;\n  height: calc(2.25rem + 2px);\n  padding: 0.375rem 1.75rem 0.375rem 0.75rem;\n  line-height: 1.25;\n  color: #495057;\n  vertical-align: middle;\n  background: #fff url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E\") no-repeat right 0.75rem center;\n  background-size: 8px 10px;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 0.25rem;\n  appearance: none;\n}\n\n.custom-select:focus {\n  border-color: #80bdff;\n  outline: none;\n}\n\n.custom-select:focus::-ms-value {\n  color: #495057;\n  background-color: #fff;\n}\n\n.custom-select:disabled {\n  color: #868e96;\n  background-color: #e9ecef;\n}\n\n.custom-select::-ms-expand {\n  opacity: 0;\n}\n\n.custom-select-sm {\n  height: calc(1.8125rem + 2px);\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n  font-size: 75%;\n}\n\n.custom-file {\n  position: relative;\n  display: inline-block;\n  max-width: 100%;\n  height: 2.5rem;\n  margin-bottom: 0;\n}\n\n.custom-file-input {\n  min-width: 14rem;\n  max-width: 100%;\n  height: 2.5rem;\n  margin: 0;\n  opacity: 0;\n}\n\n.custom-file-control {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 5;\n  height: 2.5rem;\n  padding: 0.5rem 1rem;\n  line-height: 1.5;\n  color: #495057;\n  pointer-events: none;\n  user-select: none;\n  background-color: #fff;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 0.25rem;\n}\n\n.custom-file-control:lang(en):empty::after {\n  content: \"Choose file...\";\n}\n\n.custom-file-control::before {\n  position: absolute;\n  top: -1px;\n  right: -1px;\n  bottom: -1px;\n  z-index: 6;\n  display: block;\n  height: 2.5rem;\n  padding: 0.5rem 1rem;\n  line-height: 1.5;\n  color: #495057;\n  background-color: #e9ecef;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 0 0.25rem 0.25rem 0;\n}\n\n.custom-file-control:lang(en)::before {\n  content: \"Browse\";\n}\n\n.nav {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.nav-link {\n  display: block;\n  padding: 0.5rem 1rem;\n}\n\n.nav-link:focus, .nav-link:hover {\n  text-decoration: none;\n}\n\n.nav-link.disabled {\n  color: #868e96;\n}\n\n.nav-tabs {\n  border-bottom: 1px solid #ddd;\n}\n\n.nav-tabs .nav-item {\n  margin-bottom: -1px;\n}\n\n.nav-tabs .nav-link {\n  border: 1px solid transparent;\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n\n.nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover {\n  border-color: #e9ecef #e9ecef #ddd;\n}\n\n.nav-tabs .nav-link.disabled {\n  color: #868e96;\n  background-color: transparent;\n  border-color: transparent;\n}\n\n.nav-tabs .nav-link.active,\n.nav-tabs .nav-item.show .nav-link {\n  color: #495057;\n  background-color: #fff;\n  border-color: #ddd #ddd #fff;\n}\n\n.nav-tabs .dropdown-menu {\n  margin-top: -1px;\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.nav-pills .nav-link {\n  border-radius: 0.25rem;\n}\n\n.nav-pills .nav-link.active,\n.show > .nav-pills .nav-link {\n  color: #fff;\n  background-color: #007bff;\n}\n\n.nav-fill .nav-item {\n  flex: 1 1 auto;\n  text-align: center;\n}\n\n.nav-justified .nav-item {\n  flex-basis: 0;\n  flex-grow: 1;\n  text-align: center;\n}\n\n.tab-content > .tab-pane {\n  display: none;\n}\n\n.tab-content > .active {\n  display: block;\n}\n\n.navbar {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0.5rem 1rem;\n}\n\n.navbar > .container,\n.navbar > .container-fluid {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.navbar-brand {\n  display: inline-block;\n  padding-top: 0.3125rem;\n  padding-bottom: 0.3125rem;\n  margin-right: 1rem;\n  font-size: 1.25rem;\n  line-height: inherit;\n  white-space: nowrap;\n}\n\n.navbar-brand:focus, .navbar-brand:hover {\n  text-decoration: none;\n}\n\n.navbar-nav {\n  display: flex;\n  flex-direction: column;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.navbar-nav .nav-link {\n  padding-right: 0;\n  padding-left: 0;\n}\n\n.navbar-nav .dropdown-menu {\n  position: static;\n  float: none;\n}\n\n.navbar-text {\n  display: inline-block;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n\n.navbar-collapse {\n  flex-basis: 100%;\n  align-items: center;\n}\n\n.navbar-toggler {\n  padding: 0.25rem 0.75rem;\n  font-size: 1.25rem;\n  line-height: 1;\n  background: transparent;\n  border: 1px solid transparent;\n  border-radius: 0.25rem;\n}\n\n.navbar-toggler:focus, .navbar-toggler:hover {\n  text-decoration: none;\n}\n\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  content: \"\";\n  background: no-repeat center center;\n  background-size: 100% 100%;\n}\n\n@media (max-width: 575px) {\n  .navbar-expand-sm > .container,\n  .navbar-expand-sm > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n@media (min-width: 576px) {\n  .navbar-expand-sm {\n    flex-direction: row;\n    flex-wrap: nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-sm .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-sm .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-sm .navbar-nav .dropdown-menu-right {\n    right: 0;\n    left: auto;\n  }\n  .navbar-expand-sm .navbar-nav .nav-link {\n    padding-right: .5rem;\n    padding-left: .5rem;\n  }\n  .navbar-expand-sm > .container,\n  .navbar-expand-sm > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-sm .navbar-collapse {\n    display: flex !important;\n  }\n  .navbar-expand-sm .navbar-toggler {\n    display: none;\n  }\n}\n\n@media (max-width: 767px) {\n  .navbar-expand-md > .container,\n  .navbar-expand-md > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n@media (min-width: 768px) {\n  .navbar-expand-md {\n    flex-direction: row;\n    flex-wrap: nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-md .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-md .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-md .navbar-nav .dropdown-menu-right {\n    right: 0;\n    left: auto;\n  }\n  .navbar-expand-md .navbar-nav .nav-link {\n    padding-right: .5rem;\n    padding-left: .5rem;\n  }\n  .navbar-expand-md > .container,\n  .navbar-expand-md > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-md .navbar-collapse {\n    display: flex !important;\n  }\n  .navbar-expand-md .navbar-toggler {\n    display: none;\n  }\n}\n\n@media (max-width: 991px) {\n  .navbar-expand-lg > .container,\n  .navbar-expand-lg > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n@media (min-width: 992px) {\n  .navbar-expand-lg {\n    flex-direction: row;\n    flex-wrap: nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-lg .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-lg .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-lg .navbar-nav .dropdown-menu-right {\n    right: 0;\n    left: auto;\n  }\n  .navbar-expand-lg .navbar-nav .nav-link {\n    padding-right: .5rem;\n    padding-left: .5rem;\n  }\n  .navbar-expand-lg > .container,\n  .navbar-expand-lg > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-lg .navbar-collapse {\n    display: flex !important;\n  }\n  .navbar-expand-lg .navbar-toggler {\n    display: none;\n  }\n}\n\n@media (max-width: 1199px) {\n  .navbar-expand-xl > .container,\n  .navbar-expand-xl > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n@media (min-width: 1200px) {\n  .navbar-expand-xl {\n    flex-direction: row;\n    flex-wrap: nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-xl .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-xl .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-xl .navbar-nav .dropdown-menu-right {\n    right: 0;\n    left: auto;\n  }\n  .navbar-expand-xl .navbar-nav .nav-link {\n    padding-right: .5rem;\n    padding-left: .5rem;\n  }\n  .navbar-expand-xl > .container,\n  .navbar-expand-xl > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-xl .navbar-collapse {\n    display: flex !important;\n  }\n  .navbar-expand-xl .navbar-toggler {\n    display: none;\n  }\n}\n\n.navbar-expand {\n  flex-direction: row;\n  flex-wrap: nowrap;\n  justify-content: flex-start;\n}\n\n.navbar-expand > .container,\n.navbar-expand > .container-fluid {\n  padding-right: 0;\n  padding-left: 0;\n}\n\n.navbar-expand .navbar-nav {\n  flex-direction: row;\n}\n\n.navbar-expand .navbar-nav .dropdown-menu {\n  position: absolute;\n}\n\n.navbar-expand .navbar-nav .dropdown-menu-right {\n  right: 0;\n  left: auto;\n}\n\n.navbar-expand .navbar-nav .nav-link {\n  padding-right: .5rem;\n  padding-left: .5rem;\n}\n\n.navbar-expand > .container,\n.navbar-expand > .container-fluid {\n  flex-wrap: nowrap;\n}\n\n.navbar-expand .navbar-collapse {\n  display: flex !important;\n}\n\n.navbar-expand .navbar-toggler {\n  display: none;\n}\n\n.navbar-light .navbar-brand {\n  color: rgba(0, 0, 0, 0.9);\n}\n\n.navbar-light .navbar-brand:focus, .navbar-light .navbar-brand:hover {\n  color: rgba(0, 0, 0, 0.9);\n}\n\n.navbar-light .navbar-nav .nav-link {\n  color: rgba(0, 0, 0, 0.5);\n}\n\n.navbar-light .navbar-nav .nav-link:focus, .navbar-light .navbar-nav .nav-link:hover {\n  color: rgba(0, 0, 0, 0.7);\n}\n\n.navbar-light .navbar-nav .nav-link.disabled {\n  color: rgba(0, 0, 0, 0.3);\n}\n\n.navbar-light .navbar-nav .show > .nav-link,\n.navbar-light .navbar-nav .active > .nav-link,\n.navbar-light .navbar-nav .nav-link.show,\n.navbar-light .navbar-nav .nav-link.active {\n  color: rgba(0, 0, 0, 0.9);\n}\n\n.navbar-light .navbar-toggler {\n  color: rgba(0, 0, 0, 0.5);\n  border-color: rgba(0, 0, 0, 0.1);\n}\n\n.navbar-light .navbar-toggler-icon {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\");\n}\n\n.navbar-light .navbar-text {\n  color: rgba(0, 0, 0, 0.5);\n}\n\n.navbar-dark .navbar-brand {\n  color: white;\n}\n\n.navbar-dark .navbar-brand:focus, .navbar-dark .navbar-brand:hover {\n  color: white;\n}\n\n.navbar-dark .navbar-nav .nav-link {\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-link:hover {\n  color: rgba(255, 255, 255, 0.75);\n}\n\n.navbar-dark .navbar-nav .nav-link.disabled {\n  color: rgba(255, 255, 255, 0.25);\n}\n\n.navbar-dark .navbar-nav .show > .nav-link,\n.navbar-dark .navbar-nav .active > .nav-link,\n.navbar-dark .navbar-nav .nav-link.show,\n.navbar-dark .navbar-nav .nav-link.active {\n  color: white;\n}\n\n.navbar-dark .navbar-toggler {\n  color: rgba(255, 255, 255, 0.5);\n  border-color: rgba(255, 255, 255, 0.1);\n}\n\n.navbar-dark .navbar-toggler-icon {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\");\n}\n\n.navbar-dark .navbar-text {\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.card {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0;\n  word-wrap: break-word;\n  background-color: #fff;\n  background-clip: border-box;\n  border: 1px solid rgba(0, 0, 0, 0.125);\n  border-radius: 0.25rem;\n}\n\n.card-body {\n  flex: 1 1 auto;\n  padding: 1.25rem;\n}\n\n.card-title {\n  margin-bottom: 0.75rem;\n}\n\n.card-subtitle {\n  margin-top: -0.375rem;\n  margin-bottom: 0;\n}\n\n.card-text:last-child {\n  margin-bottom: 0;\n}\n\n.card-link:hover {\n  text-decoration: none;\n}\n\n.card-link + .card-link {\n  margin-left: 1.25rem;\n}\n\n.card > .list-group:first-child .list-group-item:first-child {\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n\n.card > .list-group:last-child .list-group-item:last-child {\n  border-bottom-right-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\n\n.card-header {\n  padding: 0.75rem 1.25rem;\n  margin-bottom: 0;\n  background-color: rgba(0, 0, 0, 0.03);\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.card-header:first-child {\n  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;\n}\n\n.card-footer {\n  padding: 0.75rem 1.25rem;\n  background-color: rgba(0, 0, 0, 0.03);\n  border-top: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.card-footer:last-child {\n  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);\n}\n\n.card-header-tabs {\n  margin-right: -0.625rem;\n  margin-bottom: -0.75rem;\n  margin-left: -0.625rem;\n  border-bottom: 0;\n}\n\n.card-header-pills {\n  margin-right: -0.625rem;\n  margin-left: -0.625rem;\n}\n\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: 1.25rem;\n}\n\n.card-img {\n  width: 100%;\n  border-radius: calc(0.25rem - 1px);\n}\n\n.card-img-top {\n  width: 100%;\n  border-top-left-radius: calc(0.25rem - 1px);\n  border-top-right-radius: calc(0.25rem - 1px);\n}\n\n.card-img-bottom {\n  width: 100%;\n  border-bottom-right-radius: calc(0.25rem - 1px);\n  border-bottom-left-radius: calc(0.25rem - 1px);\n}\n\n@media (min-width: 576px) {\n  .card-deck {\n    display: flex;\n    flex-flow: row wrap;\n    margin-right: -15px;\n    margin-left: -15px;\n  }\n  .card-deck .card {\n    display: flex;\n    flex: 1 0 0%;\n    flex-direction: column;\n    margin-right: 15px;\n    margin-left: 15px;\n  }\n}\n\n@media (min-width: 576px) {\n  .card-group {\n    display: flex;\n    flex-flow: row wrap;\n  }\n  .card-group .card {\n    flex: 1 0 0%;\n  }\n  .card-group .card + .card {\n    margin-left: 0;\n    border-left: 0;\n  }\n  .card-group .card:first-child {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n  .card-group .card:first-child .card-img-top {\n    border-top-right-radius: 0;\n  }\n  .card-group .card:first-child .card-img-bottom {\n    border-bottom-right-radius: 0;\n  }\n  .card-group .card:last-child {\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n  .card-group .card:last-child .card-img-top {\n    border-top-left-radius: 0;\n  }\n  .card-group .card:last-child .card-img-bottom {\n    border-bottom-left-radius: 0;\n  }\n  .card-group .card:not(:first-child):not(:last-child) {\n    border-radius: 0;\n  }\n  .card-group .card:not(:first-child):not(:last-child) .card-img-top,\n  .card-group .card:not(:first-child):not(:last-child) .card-img-bottom {\n    border-radius: 0;\n  }\n}\n\n.card-columns .card {\n  margin-bottom: 0.75rem;\n}\n\n@media (min-width: 576px) {\n  .card-columns {\n    column-count: 3;\n    column-gap: 1.25rem;\n  }\n  .card-columns .card {\n    display: inline-block;\n    width: 100%;\n  }\n}\n\n.breadcrumb {\n  padding: 0.75rem 1rem;\n  margin-bottom: 1rem;\n  list-style: none;\n  background-color: #e9ecef;\n  border-radius: 0.25rem;\n}\n\n.breadcrumb::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.breadcrumb-item {\n  float: left;\n}\n\n.breadcrumb-item + .breadcrumb-item::before {\n  display: inline-block;\n  padding-right: 0.5rem;\n  padding-left: 0.5rem;\n  color: #868e96;\n  content: \"/\";\n}\n\n.breadcrumb-item + .breadcrumb-item:hover::before {\n  text-decoration: underline;\n}\n\n.breadcrumb-item + .breadcrumb-item:hover::before {\n  text-decoration: none;\n}\n\n.breadcrumb-item.active {\n  color: #868e96;\n}\n\n.pagination {\n  display: flex;\n  padding-left: 0;\n  list-style: none;\n  border-radius: 0.25rem;\n}\n\n.page-item:first-child .page-link {\n  margin-left: 0;\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\n\n.page-item:last-child .page-link {\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem;\n}\n\n.page-item.active .page-link {\n  z-index: 2;\n  color: #fff;\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.page-item.disabled .page-link {\n  color: #868e96;\n  pointer-events: none;\n  background-color: #fff;\n  border-color: #ddd;\n}\n\n.page-link {\n  position: relative;\n  display: block;\n  padding: 0.5rem 0.75rem;\n  margin-left: -1px;\n  line-height: 1.25;\n  color: #007bff;\n  background-color: #fff;\n  border: 1px solid #ddd;\n}\n\n.page-link:focus, .page-link:hover {\n  color: #0056b3;\n  text-decoration: none;\n  background-color: #e9ecef;\n  border-color: #ddd;\n}\n\n.pagination-lg .page-link {\n  padding: 0.75rem 1.5rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n}\n\n.pagination-lg .page-item:first-child .page-link {\n  border-top-left-radius: 0.3rem;\n  border-bottom-left-radius: 0.3rem;\n}\n\n.pagination-lg .page-item:last-child .page-link {\n  border-top-right-radius: 0.3rem;\n  border-bottom-right-radius: 0.3rem;\n}\n\n.pagination-sm .page-link {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.pagination-sm .page-item:first-child .page-link {\n  border-top-left-radius: 0.2rem;\n  border-bottom-left-radius: 0.2rem;\n}\n\n.pagination-sm .page-item:last-child .page-link {\n  border-top-right-radius: 0.2rem;\n  border-bottom-right-radius: 0.2rem;\n}\n\n.badge {\n  display: inline-block;\n  padding: 0.25em 0.4em;\n  font-size: 75%;\n  font-weight: bold;\n  line-height: 1;\n  color: #fff;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  border-radius: 0.25rem;\n}\n\n.badge:empty {\n  display: none;\n}\n\n.btn .badge {\n  position: relative;\n  top: -1px;\n}\n\n.badge-pill {\n  padding-right: 0.6em;\n  padding-left: 0.6em;\n  border-radius: 10rem;\n}\n\n.badge-primary {\n  color: #fff;\n  background-color: #007bff;\n}\n\n.badge-primary[href]:focus, .badge-primary[href]:hover {\n  color: #fff;\n  text-decoration: none;\n  background-color: #0062cc;\n}\n\n.badge-secondary {\n  color: #fff;\n  background-color: #868e96;\n}\n\n.badge-secondary[href]:focus, .badge-secondary[href]:hover {\n  color: #fff;\n  text-decoration: none;\n  background-color: #6c757d;\n}\n\n.badge-success {\n  color: #fff;\n  background-color: #28a745;\n}\n\n.badge-success[href]:focus, .badge-success[href]:hover {\n  color: #fff;\n  text-decoration: none;\n  background-color: #1e7e34;\n}\n\n.badge-info {\n  color: #fff;\n  background-color: #17a2b8;\n}\n\n.badge-info[href]:focus, .badge-info[href]:hover {\n  color: #fff;\n  text-decoration: none;\n  background-color: #117a8b;\n}\n\n.badge-warning {\n  color: #111;\n  background-color: #ffc107;\n}\n\n.badge-warning[href]:focus, .badge-warning[href]:hover {\n  color: #111;\n  text-decoration: none;\n  background-color: #d39e00;\n}\n\n.badge-danger {\n  color: #fff;\n  background-color: #dc3545;\n}\n\n.badge-danger[href]:focus, .badge-danger[href]:hover {\n  color: #fff;\n  text-decoration: none;\n  background-color: #bd2130;\n}\n\n.badge-light {\n  color: #111;\n  background-color: #f8f9fa;\n}\n\n.badge-light[href]:focus, .badge-light[href]:hover {\n  color: #111;\n  text-decoration: none;\n  background-color: #dae0e5;\n}\n\n.badge-dark {\n  color: #fff;\n  background-color: #343a40;\n}\n\n.badge-dark[href]:focus, .badge-dark[href]:hover {\n  color: #fff;\n  text-decoration: none;\n  background-color: #1d2124;\n}\n\n.jumbotron {\n  padding: 2rem 1rem;\n  margin-bottom: 2rem;\n  background-color: #e9ecef;\n  border-radius: 0.3rem;\n}\n\n@media (min-width: 576px) {\n  .jumbotron {\n    padding: 4rem 2rem;\n  }\n}\n\n.jumbotron-fluid {\n  padding-right: 0;\n  padding-left: 0;\n  border-radius: 0;\n}\n\n.alert {\n  padding: 0.75rem 1.25rem;\n  margin-bottom: 1rem;\n  border: 1px solid transparent;\n  border-radius: 0.25rem;\n}\n\n.alert-heading {\n  color: inherit;\n}\n\n.alert-link {\n  font-weight: bold;\n}\n\n.alert-dismissible .close {\n  position: relative;\n  top: -0.75rem;\n  right: -1.25rem;\n  padding: 0.75rem 1.25rem;\n  color: inherit;\n}\n\n.alert-primary {\n  color: #004085;\n  background-color: #cce5ff;\n  border-color: #b8daff;\n}\n\n.alert-primary hr {\n  border-top-color: #9fcdff;\n}\n\n.alert-primary .alert-link {\n  color: #002752;\n}\n\n.alert-secondary {\n  color: #464a4e;\n  background-color: #e7e8ea;\n  border-color: #dddfe2;\n}\n\n.alert-secondary hr {\n  border-top-color: #cfd2d6;\n}\n\n.alert-secondary .alert-link {\n  color: #2e3133;\n}\n\n.alert-success {\n  color: #155724;\n  background-color: #d4edda;\n  border-color: #c3e6cb;\n}\n\n.alert-success hr {\n  border-top-color: #b1dfbb;\n}\n\n.alert-success .alert-link {\n  color: #0b2e13;\n}\n\n.alert-info {\n  color: #0c5460;\n  background-color: #d1ecf1;\n  border-color: #bee5eb;\n}\n\n.alert-info hr {\n  border-top-color: #abdde5;\n}\n\n.alert-info .alert-link {\n  color: #062c33;\n}\n\n.alert-warning {\n  color: #856404;\n  background-color: #fff3cd;\n  border-color: #ffeeba;\n}\n\n.alert-warning hr {\n  border-top-color: #ffe8a1;\n}\n\n.alert-warning .alert-link {\n  color: #533f03;\n}\n\n.alert-danger {\n  color: #721c24;\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n\n.alert-danger hr {\n  border-top-color: #f1b0b7;\n}\n\n.alert-danger .alert-link {\n  color: #491217;\n}\n\n.alert-light {\n  color: #818182;\n  background-color: #fefefe;\n  border-color: #fdfdfe;\n}\n\n.alert-light hr {\n  border-top-color: #ececf6;\n}\n\n.alert-light .alert-link {\n  color: #686868;\n}\n\n.alert-dark {\n  color: #1b1e21;\n  background-color: #d6d8d9;\n  border-color: #c6c8ca;\n}\n\n.alert-dark hr {\n  border-top-color: #b9bbbe;\n}\n\n.alert-dark .alert-link {\n  color: #040505;\n}\n\n@keyframes progress-bar-stripes {\n  from {\n    background-position: 1rem 0;\n  }\n  to {\n    background-position: 0 0;\n  }\n}\n\n.progress {\n  display: flex;\n  overflow: hidden;\n  font-size: 0.75rem;\n  line-height: 1rem;\n  text-align: center;\n  background-color: #e9ecef;\n  border-radius: 0.25rem;\n}\n\n.progress-bar {\n  height: 1rem;\n  line-height: 1rem;\n  color: #fff;\n  background-color: #007bff;\n  transition: width 0.6s ease;\n}\n\n.progress-bar-striped {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\n  background-size: 1rem 1rem;\n}\n\n.progress-bar-animated {\n  animation: progress-bar-stripes 1s linear infinite;\n}\n\n.media {\n  display: flex;\n  align-items: flex-start;\n}\n\n.media-body {\n  flex: 1;\n}\n\n.list-group {\n  display: flex;\n  flex-direction: column;\n  padding-left: 0;\n  margin-bottom: 0;\n}\n\n.list-group-item-action {\n  width: 100%;\n  color: #495057;\n  text-align: inherit;\n}\n\n.list-group-item-action:focus, .list-group-item-action:hover {\n  color: #495057;\n  text-decoration: none;\n  background-color: #f8f9fa;\n}\n\n.list-group-item-action:active {\n  color: #212529;\n  background-color: #e9ecef;\n}\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: 0.75rem 1.25rem;\n  margin-bottom: -1px;\n  background-color: #fff;\n  border: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.list-group-item:first-child {\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n\n.list-group-item:last-child {\n  margin-bottom: 0;\n  border-bottom-right-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\n\n.list-group-item:focus, .list-group-item:hover {\n  text-decoration: none;\n}\n\n.list-group-item.disabled, .list-group-item:disabled {\n  color: #868e96;\n  background-color: #fff;\n}\n\n.list-group-item.active {\n  z-index: 2;\n  color: #fff;\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.list-group-flush .list-group-item {\n  border-right: 0;\n  border-left: 0;\n  border-radius: 0;\n}\n\n.list-group-flush:first-child .list-group-item:first-child {\n  border-top: 0;\n}\n\n.list-group-flush:last-child .list-group-item:last-child {\n  border-bottom: 0;\n}\n\n.list-group-item-primary {\n  color: #004085;\n  background-color: #b8daff;\n}\n\na.list-group-item-primary,\nbutton.list-group-item-primary {\n  color: #004085;\n}\n\na.list-group-item-primary:focus, a.list-group-item-primary:hover,\nbutton.list-group-item-primary:focus,\nbutton.list-group-item-primary:hover {\n  color: #004085;\n  background-color: #9fcdff;\n}\n\na.list-group-item-primary.active,\nbutton.list-group-item-primary.active {\n  color: #fff;\n  background-color: #004085;\n  border-color: #004085;\n}\n\n.list-group-item-secondary {\n  color: #464a4e;\n  background-color: #dddfe2;\n}\n\na.list-group-item-secondary,\nbutton.list-group-item-secondary {\n  color: #464a4e;\n}\n\na.list-group-item-secondary:focus, a.list-group-item-secondary:hover,\nbutton.list-group-item-secondary:focus,\nbutton.list-group-item-secondary:hover {\n  color: #464a4e;\n  background-color: #cfd2d6;\n}\n\na.list-group-item-secondary.active,\nbutton.list-group-item-secondary.active {\n  color: #fff;\n  background-color: #464a4e;\n  border-color: #464a4e;\n}\n\n.list-group-item-success {\n  color: #155724;\n  background-color: #c3e6cb;\n}\n\na.list-group-item-success,\nbutton.list-group-item-success {\n  color: #155724;\n}\n\na.list-group-item-success:focus, a.list-group-item-success:hover,\nbutton.list-group-item-success:focus,\nbutton.list-group-item-success:hover {\n  color: #155724;\n  background-color: #b1dfbb;\n}\n\na.list-group-item-success.active,\nbutton.list-group-item-success.active {\n  color: #fff;\n  background-color: #155724;\n  border-color: #155724;\n}\n\n.list-group-item-info {\n  color: #0c5460;\n  background-color: #bee5eb;\n}\n\na.list-group-item-info,\nbutton.list-group-item-info {\n  color: #0c5460;\n}\n\na.list-group-item-info:focus, a.list-group-item-info:hover,\nbutton.list-group-item-info:focus,\nbutton.list-group-item-info:hover {\n  color: #0c5460;\n  background-color: #abdde5;\n}\n\na.list-group-item-info.active,\nbutton.list-group-item-info.active {\n  color: #fff;\n  background-color: #0c5460;\n  border-color: #0c5460;\n}\n\n.list-group-item-warning {\n  color: #856404;\n  background-color: #ffeeba;\n}\n\na.list-group-item-warning,\nbutton.list-group-item-warning {\n  color: #856404;\n}\n\na.list-group-item-warning:focus, a.list-group-item-warning:hover,\nbutton.list-group-item-warning:focus,\nbutton.list-group-item-warning:hover {\n  color: #856404;\n  background-color: #ffe8a1;\n}\n\na.list-group-item-warning.active,\nbutton.list-group-item-warning.active {\n  color: #fff;\n  background-color: #856404;\n  border-color: #856404;\n}\n\n.list-group-item-danger {\n  color: #721c24;\n  background-color: #f5c6cb;\n}\n\na.list-group-item-danger,\nbutton.list-group-item-danger {\n  color: #721c24;\n}\n\na.list-group-item-danger:focus, a.list-group-item-danger:hover,\nbutton.list-group-item-danger:focus,\nbutton.list-group-item-danger:hover {\n  color: #721c24;\n  background-color: #f1b0b7;\n}\n\na.list-group-item-danger.active,\nbutton.list-group-item-danger.active {\n  color: #fff;\n  background-color: #721c24;\n  border-color: #721c24;\n}\n\n.list-group-item-light {\n  color: #818182;\n  background-color: #fdfdfe;\n}\n\na.list-group-item-light,\nbutton.list-group-item-light {\n  color: #818182;\n}\n\na.list-group-item-light:focus, a.list-group-item-light:hover,\nbutton.list-group-item-light:focus,\nbutton.list-group-item-light:hover {\n  color: #818182;\n  background-color: #ececf6;\n}\n\na.list-group-item-light.active,\nbutton.list-group-item-light.active {\n  color: #fff;\n  background-color: #818182;\n  border-color: #818182;\n}\n\n.list-group-item-dark {\n  color: #1b1e21;\n  background-color: #c6c8ca;\n}\n\na.list-group-item-dark,\nbutton.list-group-item-dark {\n  color: #1b1e21;\n}\n\na.list-group-item-dark:focus, a.list-group-item-dark:hover,\nbutton.list-group-item-dark:focus,\nbutton.list-group-item-dark:hover {\n  color: #1b1e21;\n  background-color: #b9bbbe;\n}\n\na.list-group-item-dark.active,\nbutton.list-group-item-dark.active {\n  color: #fff;\n  background-color: #1b1e21;\n  border-color: #1b1e21;\n}\n\n.close {\n  float: right;\n  font-size: 1.5rem;\n  font-weight: bold;\n  line-height: 1;\n  color: #000;\n  text-shadow: 0 1px 0 #fff;\n  opacity: .5;\n}\n\n.close:focus, .close:hover {\n  color: #000;\n  text-decoration: none;\n  opacity: .75;\n}\n\nbutton.close {\n  padding: 0;\n  background: transparent;\n  border: 0;\n  -webkit-appearance: none;\n}\n\n.modal-open {\n  overflow: hidden;\n}\n\n.modal {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1050;\n  display: none;\n  overflow: hidden;\n  outline: 0;\n}\n\n.modal.fade .modal-dialog {\n  transition: transform 0.3s ease-out;\n  transform: translate(0, -25%);\n}\n\n.modal.show .modal-dialog {\n  transform: translate(0, 0);\n}\n\n.modal-open .modal {\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: 10px;\n}\n\n.modal-content {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  border-radius: 0.3rem;\n  outline: 0;\n}\n\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1040;\n  background-color: #000;\n}\n\n.modal-backdrop.fade {\n  opacity: 0;\n}\n\n.modal-backdrop.show {\n  opacity: 0.5;\n}\n\n.modal-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 15px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.modal-title {\n  margin-bottom: 0;\n  line-height: 1.5;\n}\n\n.modal-body {\n  position: relative;\n  flex: 1 1 auto;\n  padding: 15px;\n}\n\n.modal-footer {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  padding: 15px;\n  border-top: 1px solid #e9ecef;\n}\n\n.modal-footer > :not(:first-child) {\n  margin-left: .25rem;\n}\n\n.modal-footer > :not(:last-child) {\n  margin-right: .25rem;\n}\n\n.modal-scrollbar-measure {\n  position: absolute;\n  top: -9999px;\n  width: 50px;\n  height: 50px;\n  overflow: scroll;\n}\n\n@media (min-width: 576px) {\n  .modal-dialog {\n    max-width: 500px;\n    margin: 30px auto;\n  }\n  .modal-sm {\n    max-width: 300px;\n  }\n}\n\n@media (min-width: 992px) {\n  .modal-lg {\n    max-width: 800px;\n  }\n}\n\n.tooltip {\n  position: absolute;\n  z-index: 1070;\n  display: block;\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n  font-style: normal;\n  font-weight: normal;\n  line-height: 1.5;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n  font-size: 0.875rem;\n  word-wrap: break-word;\n  opacity: 0;\n}\n\n.tooltip.show {\n  opacity: 0.9;\n}\n\n.tooltip .arrow {\n  position: absolute;\n  display: block;\n  width: 5px;\n  height: 5px;\n}\n\n.tooltip.bs-tooltip-top, .tooltip.bs-tooltip-auto[x-placement^=\"top\"] {\n  padding: 5px 0;\n}\n\n.tooltip.bs-tooltip-top .arrow, .tooltip.bs-tooltip-auto[x-placement^=\"top\"] .arrow {\n  bottom: 0;\n}\n\n.tooltip.bs-tooltip-top .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=\"top\"] .arrow::before {\n  margin-left: -3px;\n  content: \"\";\n  border-width: 5px 5px 0;\n  border-top-color: #000;\n}\n\n.tooltip.bs-tooltip-right, .tooltip.bs-tooltip-auto[x-placement^=\"right\"] {\n  padding: 0 5px;\n}\n\n.tooltip.bs-tooltip-right .arrow, .tooltip.bs-tooltip-auto[x-placement^=\"right\"] .arrow {\n  left: 0;\n}\n\n.tooltip.bs-tooltip-right .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=\"right\"] .arrow::before {\n  margin-top: -3px;\n  content: \"\";\n  border-width: 5px 5px 5px 0;\n  border-right-color: #000;\n}\n\n.tooltip.bs-tooltip-bottom, .tooltip.bs-tooltip-auto[x-placement^=\"bottom\"] {\n  padding: 5px 0;\n}\n\n.tooltip.bs-tooltip-bottom .arrow, .tooltip.bs-tooltip-auto[x-placement^=\"bottom\"] .arrow {\n  top: 0;\n}\n\n.tooltip.bs-tooltip-bottom .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=\"bottom\"] .arrow::before {\n  margin-left: -3px;\n  content: \"\";\n  border-width: 0 5px 5px;\n  border-bottom-color: #000;\n}\n\n.tooltip.bs-tooltip-left, .tooltip.bs-tooltip-auto[x-placement^=\"left\"] {\n  padding: 0 5px;\n}\n\n.tooltip.bs-tooltip-left .arrow, .tooltip.bs-tooltip-auto[x-placement^=\"left\"] .arrow {\n  right: 0;\n}\n\n.tooltip.bs-tooltip-left .arrow::before, .tooltip.bs-tooltip-auto[x-placement^=\"left\"] .arrow::before {\n  right: 0;\n  margin-top: -3px;\n  content: \"\";\n  border-width: 5px 0 5px 5px;\n  border-left-color: #000;\n}\n\n.tooltip .arrow::before {\n  position: absolute;\n  border-color: transparent;\n  border-style: solid;\n}\n\n.tooltip-inner {\n  max-width: 200px;\n  padding: 3px 8px;\n  color: #fff;\n  text-align: center;\n  background-color: #000;\n  border-radius: 0.25rem;\n}\n\n.popover {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 1060;\n  display: block;\n  max-width: 276px;\n  padding: 1px;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n  font-style: normal;\n  font-weight: normal;\n  line-height: 1.5;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n  font-size: 0.875rem;\n  word-wrap: break-word;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  border-radius: 0.3rem;\n}\n\n.popover .arrow {\n  position: absolute;\n  display: block;\n  width: 10px;\n  height: 5px;\n}\n\n.popover .arrow::before,\n.popover .arrow::after {\n  position: absolute;\n  display: block;\n  border-color: transparent;\n  border-style: solid;\n}\n\n.popover .arrow::before {\n  content: \"\";\n  border-width: 11px;\n}\n\n.popover .arrow::after {\n  content: \"\";\n  border-width: 11px;\n}\n\n.popover.bs-popover-top, .popover.bs-popover-auto[x-placement^=\"top\"] {\n  margin-bottom: 10px;\n}\n\n.popover.bs-popover-top .arrow, .popover.bs-popover-auto[x-placement^=\"top\"] .arrow {\n  bottom: 0;\n}\n\n.popover.bs-popover-top .arrow::before, .popover.bs-popover-auto[x-placement^=\"top\"] .arrow::before,\n.popover.bs-popover-top .arrow::after, .popover.bs-popover-auto[x-placement^=\"top\"] .arrow::after {\n  border-bottom-width: 0;\n}\n\n.popover.bs-popover-top .arrow::before, .popover.bs-popover-auto[x-placement^=\"top\"] .arrow::before {\n  bottom: -11px;\n  margin-left: -6px;\n  border-top-color: rgba(0, 0, 0, 0.25);\n}\n\n.popover.bs-popover-top .arrow::after, .popover.bs-popover-auto[x-placement^=\"top\"] .arrow::after {\n  bottom: -10px;\n  margin-left: -6px;\n  border-top-color: #fff;\n}\n\n.popover.bs-popover-right, .popover.bs-popover-auto[x-placement^=\"right\"] {\n  margin-left: 10px;\n}\n\n.popover.bs-popover-right .arrow, .popover.bs-popover-auto[x-placement^=\"right\"] .arrow {\n  left: 0;\n}\n\n.popover.bs-popover-right .arrow::before, .popover.bs-popover-auto[x-placement^=\"right\"] .arrow::before,\n.popover.bs-popover-right .arrow::after, .popover.bs-popover-auto[x-placement^=\"right\"] .arrow::after {\n  margin-top: -8px;\n  border-left-width: 0;\n}\n\n.popover.bs-popover-right .arrow::before, .popover.bs-popover-auto[x-placement^=\"right\"] .arrow::before {\n  left: -11px;\n  border-right-color: rgba(0, 0, 0, 0.25);\n}\n\n.popover.bs-popover-right .arrow::after, .popover.bs-popover-auto[x-placement^=\"right\"] .arrow::after {\n  left: -10px;\n  border-right-color: #fff;\n}\n\n.popover.bs-popover-bottom, .popover.bs-popover-auto[x-placement^=\"bottom\"] {\n  margin-top: 10px;\n}\n\n.popover.bs-popover-bottom .arrow, .popover.bs-popover-auto[x-placement^=\"bottom\"] .arrow {\n  top: 0;\n}\n\n.popover.bs-popover-bottom .arrow::before, .popover.bs-popover-auto[x-placement^=\"bottom\"] .arrow::before,\n.popover.bs-popover-bottom .arrow::after, .popover.bs-popover-auto[x-placement^=\"bottom\"] .arrow::after {\n  margin-left: -7px;\n  border-top-width: 0;\n}\n\n.popover.bs-popover-bottom .arrow::before, .popover.bs-popover-auto[x-placement^=\"bottom\"] .arrow::before {\n  top: -11px;\n  border-bottom-color: rgba(0, 0, 0, 0.25);\n}\n\n.popover.bs-popover-bottom .arrow::after, .popover.bs-popover-auto[x-placement^=\"bottom\"] .arrow::after {\n  top: -10px;\n  border-bottom-color: #fff;\n}\n\n.popover.bs-popover-bottom .popover-header::before, .popover.bs-popover-auto[x-placement^=\"bottom\"] .popover-header::before {\n  position: absolute;\n  top: 0;\n  left: 50%;\n  display: block;\n  width: 20px;\n  margin-left: -10px;\n  content: \"\";\n  border-bottom: 1px solid #f7f7f7;\n}\n\n.popover.bs-popover-left, .popover.bs-popover-auto[x-placement^=\"left\"] {\n  margin-right: 10px;\n}\n\n.popover.bs-popover-left .arrow, .popover.bs-popover-auto[x-placement^=\"left\"] .arrow {\n  right: 0;\n}\n\n.popover.bs-popover-left .arrow::before, .popover.bs-popover-auto[x-placement^=\"left\"] .arrow::before,\n.popover.bs-popover-left .arrow::after, .popover.bs-popover-auto[x-placement^=\"left\"] .arrow::after {\n  margin-top: -8px;\n  border-right-width: 0;\n}\n\n.popover.bs-popover-left .arrow::before, .popover.bs-popover-auto[x-placement^=\"left\"] .arrow::before {\n  right: -11px;\n  border-left-color: rgba(0, 0, 0, 0.25);\n}\n\n.popover.bs-popover-left .arrow::after, .popover.bs-popover-auto[x-placement^=\"left\"] .arrow::after {\n  right: -10px;\n  border-left-color: #fff;\n}\n\n.popover-header {\n  padding: 8px 14px;\n  margin-bottom: 0;\n  font-size: 1rem;\n  color: inherit;\n  background-color: #f7f7f7;\n  border-bottom: 1px solid #ebebeb;\n  border-top-left-radius: calc(0.3rem - 1px);\n  border-top-right-radius: calc(0.3rem - 1px);\n}\n\n.popover-header:empty {\n  display: none;\n}\n\n.popover-body {\n  padding: 9px 14px;\n  color: #212529;\n}\n\n.carousel {\n  position: relative;\n}\n\n.carousel-inner {\n  position: relative;\n  width: 100%;\n  overflow: hidden;\n}\n\n.carousel-item {\n  position: relative;\n  display: none;\n  align-items: center;\n  width: 100%;\n  transition: transform 0.6s ease;\n  backface-visibility: hidden;\n  perspective: 1000px;\n}\n\n.carousel-item.active,\n.carousel-item-next,\n.carousel-item-prev {\n  display: block;\n}\n\n.carousel-item-next,\n.carousel-item-prev {\n  position: absolute;\n  top: 0;\n}\n\n.carousel-item-next.carousel-item-left,\n.carousel-item-prev.carousel-item-right {\n  transform: translateX(0);\n}\n\n@supports (transform-style: preserve-3d) {\n  .carousel-item-next.carousel-item-left,\n  .carousel-item-prev.carousel-item-right {\n    transform: translate3d(0, 0, 0);\n  }\n}\n\n.carousel-item-next,\n.active.carousel-item-right {\n  transform: translateX(100%);\n}\n\n@supports (transform-style: preserve-3d) {\n  .carousel-item-next,\n  .active.carousel-item-right {\n    transform: translate3d(100%, 0, 0);\n  }\n}\n\n.carousel-item-prev,\n.active.carousel-item-left {\n  transform: translateX(-100%);\n}\n\n@supports (transform-style: preserve-3d) {\n  .carousel-item-prev,\n  .active.carousel-item-left {\n    transform: translate3d(-100%, 0, 0);\n  }\n}\n\n.carousel-control-prev,\n.carousel-control-next {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 15%;\n  color: #fff;\n  text-align: center;\n  opacity: 0.5;\n}\n\n.carousel-control-prev:focus, .carousel-control-prev:hover,\n.carousel-control-next:focus,\n.carousel-control-next:hover {\n  color: #fff;\n  text-decoration: none;\n  outline: 0;\n  opacity: .9;\n}\n\n.carousel-control-prev {\n  left: 0;\n}\n\n.carousel-control-next {\n  right: 0;\n}\n\n.carousel-control-prev-icon,\n.carousel-control-next-icon {\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  background: transparent no-repeat center center;\n  background-size: 100% 100%;\n}\n\n.carousel-control-prev-icon {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3E%3Cpath d='M4 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E\");\n}\n\n.carousel-control-next-icon {\n  background-image: url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3E%3Cpath d='M1.5 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E\");\n}\n\n.carousel-indicators {\n  position: absolute;\n  right: 0;\n  bottom: 10px;\n  left: 0;\n  z-index: 15;\n  display: flex;\n  justify-content: center;\n  padding-left: 0;\n  margin-right: 15%;\n  margin-left: 15%;\n  list-style: none;\n}\n\n.carousel-indicators li {\n  position: relative;\n  flex: 0 1 auto;\n  width: 30px;\n  height: 3px;\n  margin-right: 3px;\n  margin-left: 3px;\n  text-indent: -999px;\n  background-color: rgba(255, 255, 255, 0.5);\n}\n\n.carousel-indicators li::before {\n  position: absolute;\n  top: -10px;\n  left: 0;\n  display: inline-block;\n  width: 100%;\n  height: 10px;\n  content: \"\";\n}\n\n.carousel-indicators li::after {\n  position: absolute;\n  bottom: -10px;\n  left: 0;\n  display: inline-block;\n  width: 100%;\n  height: 10px;\n  content: \"\";\n}\n\n.carousel-indicators .active {\n  background-color: #fff;\n}\n\n.carousel-caption {\n  position: absolute;\n  right: 15%;\n  bottom: 20px;\n  left: 15%;\n  z-index: 10;\n  padding-top: 20px;\n  padding-bottom: 20px;\n  color: #fff;\n  text-align: center;\n}\n\n.align-baseline {\n  vertical-align: baseline !important;\n}\n\n.align-top {\n  vertical-align: top !important;\n}\n\n.align-middle {\n  vertical-align: middle !important;\n}\n\n.align-bottom {\n  vertical-align: bottom !important;\n}\n\n.align-text-bottom {\n  vertical-align: text-bottom !important;\n}\n\n.align-text-top {\n  vertical-align: text-top !important;\n}\n\n.bg-primary {\n  background-color: #007bff !important;\n}\n\na.bg-primary:focus, a.bg-primary:hover {\n  background-color: #0062cc !important;\n}\n\n.bg-secondary {\n  background-color: #868e96 !important;\n}\n\na.bg-secondary:focus, a.bg-secondary:hover {\n  background-color: #6c757d !important;\n}\n\n.bg-success {\n  background-color: #28a745 !important;\n}\n\na.bg-success:focus, a.bg-success:hover {\n  background-color: #1e7e34 !important;\n}\n\n.bg-info {\n  background-color: #17a2b8 !important;\n}\n\na.bg-info:focus, a.bg-info:hover {\n  background-color: #117a8b !important;\n}\n\n.bg-warning {\n  background-color: #ffc107 !important;\n}\n\na.bg-warning:focus, a.bg-warning:hover {\n  background-color: #d39e00 !important;\n}\n\n.bg-danger {\n  background-color: #dc3545 !important;\n}\n\na.bg-danger:focus, a.bg-danger:hover {\n  background-color: #bd2130 !important;\n}\n\n.bg-light {\n  background-color: #f8f9fa !important;\n}\n\na.bg-light:focus, a.bg-light:hover {\n  background-color: #dae0e5 !important;\n}\n\n.bg-dark {\n  background-color: #343a40 !important;\n}\n\na.bg-dark:focus, a.bg-dark:hover {\n  background-color: #1d2124 !important;\n}\n\n.bg-white {\n  background-color: #fff !important;\n}\n\n.bg-transparent {\n  background-color: transparent !important;\n}\n\n.border {\n  border: 1px solid #e9ecef !important;\n}\n\n.border-0 {\n  border: 0 !important;\n}\n\n.border-top-0 {\n  border-top: 0 !important;\n}\n\n.border-right-0 {\n  border-right: 0 !important;\n}\n\n.border-bottom-0 {\n  border-bottom: 0 !important;\n}\n\n.border-left-0 {\n  border-left: 0 !important;\n}\n\n.border-primary {\n  border-color: #007bff !important;\n}\n\n.border-secondary {\n  border-color: #868e96 !important;\n}\n\n.border-success {\n  border-color: #28a745 !important;\n}\n\n.border-info {\n  border-color: #17a2b8 !important;\n}\n\n.border-warning {\n  border-color: #ffc107 !important;\n}\n\n.border-danger {\n  border-color: #dc3545 !important;\n}\n\n.border-light {\n  border-color: #f8f9fa !important;\n}\n\n.border-dark {\n  border-color: #343a40 !important;\n}\n\n.border-white {\n  border-color: #fff !important;\n}\n\n.rounded {\n  border-radius: 0.25rem !important;\n}\n\n.rounded-top {\n  border-top-left-radius: 0.25rem !important;\n  border-top-right-radius: 0.25rem !important;\n}\n\n.rounded-right {\n  border-top-right-radius: 0.25rem !important;\n  border-bottom-right-radius: 0.25rem !important;\n}\n\n.rounded-bottom {\n  border-bottom-right-radius: 0.25rem !important;\n  border-bottom-left-radius: 0.25rem !important;\n}\n\n.rounded-left {\n  border-top-left-radius: 0.25rem !important;\n  border-bottom-left-radius: 0.25rem !important;\n}\n\n.rounded-circle {\n  border-radius: 50%;\n}\n\n.rounded-0 {\n  border-radius: 0;\n}\n\n.clearfix::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.d-none {\n  display: none !important;\n}\n\n.d-inline {\n  display: inline !important;\n}\n\n.d-inline-block {\n  display: inline-block !important;\n}\n\n.d-block {\n  display: block !important;\n}\n\n.d-table {\n  display: table !important;\n}\n\n.d-table-cell {\n  display: table-cell !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.d-inline-flex {\n  display: inline-flex !important;\n}\n\n@media (min-width: 576px) {\n  .d-sm-none {\n    display: none !important;\n  }\n  .d-sm-inline {\n    display: inline !important;\n  }\n  .d-sm-inline-block {\n    display: inline-block !important;\n  }\n  .d-sm-block {\n    display: block !important;\n  }\n  .d-sm-table {\n    display: table !important;\n  }\n  .d-sm-table-cell {\n    display: table-cell !important;\n  }\n  .d-sm-flex {\n    display: flex !important;\n  }\n  .d-sm-inline-flex {\n    display: inline-flex !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .d-md-none {\n    display: none !important;\n  }\n  .d-md-inline {\n    display: inline !important;\n  }\n  .d-md-inline-block {\n    display: inline-block !important;\n  }\n  .d-md-block {\n    display: block !important;\n  }\n  .d-md-table {\n    display: table !important;\n  }\n  .d-md-table-cell {\n    display: table-cell !important;\n  }\n  .d-md-flex {\n    display: flex !important;\n  }\n  .d-md-inline-flex {\n    display: inline-flex !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .d-lg-none {\n    display: none !important;\n  }\n  .d-lg-inline {\n    display: inline !important;\n  }\n  .d-lg-inline-block {\n    display: inline-block !important;\n  }\n  .d-lg-block {\n    display: block !important;\n  }\n  .d-lg-table {\n    display: table !important;\n  }\n  .d-lg-table-cell {\n    display: table-cell !important;\n  }\n  .d-lg-flex {\n    display: flex !important;\n  }\n  .d-lg-inline-flex {\n    display: inline-flex !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .d-xl-none {\n    display: none !important;\n  }\n  .d-xl-inline {\n    display: inline !important;\n  }\n  .d-xl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xl-block {\n    display: block !important;\n  }\n  .d-xl-table {\n    display: table !important;\n  }\n  .d-xl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xl-flex {\n    display: flex !important;\n  }\n  .d-xl-inline-flex {\n    display: inline-flex !important;\n  }\n}\n\n.d-print-block {\n  display: none !important;\n}\n\n@media print {\n  .d-print-block {\n    display: block !important;\n  }\n}\n\n.d-print-inline {\n  display: none !important;\n}\n\n@media print {\n  .d-print-inline {\n    display: inline !important;\n  }\n}\n\n.d-print-inline-block {\n  display: none !important;\n}\n\n@media print {\n  .d-print-inline-block {\n    display: inline-block !important;\n  }\n}\n\n@media print {\n  .d-print-none {\n    display: none !important;\n  }\n}\n\n.embed-responsive {\n  position: relative;\n  display: block;\n  width: 100%;\n  padding: 0;\n  overflow: hidden;\n}\n\n.embed-responsive::before {\n  display: block;\n  content: \"\";\n}\n\n.embed-responsive .embed-responsive-item,\n.embed-responsive iframe,\n.embed-responsive embed,\n.embed-responsive object,\n.embed-responsive video {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  border: 0;\n}\n\n.embed-responsive-21by9::before {\n  padding-top: 42.857143%;\n}\n\n.embed-responsive-16by9::before {\n  padding-top: 56.25%;\n}\n\n.embed-responsive-4by3::before {\n  padding-top: 75%;\n}\n\n.embed-responsive-1by1::before {\n  padding-top: 100%;\n}\n\n.flex-row {\n  flex-direction: row !important;\n}\n\n.flex-column {\n  flex-direction: column !important;\n}\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important;\n}\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important;\n}\n\n.flex-wrap {\n  flex-wrap: wrap !important;\n}\n\n.flex-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n.justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.justify-content-around {\n  justify-content: space-around !important;\n}\n\n.align-items-start {\n  align-items: flex-start !important;\n}\n\n.align-items-end {\n  align-items: flex-end !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n.align-items-baseline {\n  align-items: baseline !important;\n}\n\n.align-items-stretch {\n  align-items: stretch !important;\n}\n\n.align-content-start {\n  align-content: flex-start !important;\n}\n\n.align-content-end {\n  align-content: flex-end !important;\n}\n\n.align-content-center {\n  align-content: center !important;\n}\n\n.align-content-between {\n  align-content: space-between !important;\n}\n\n.align-content-around {\n  align-content: space-around !important;\n}\n\n.align-content-stretch {\n  align-content: stretch !important;\n}\n\n.align-self-auto {\n  align-self: auto !important;\n}\n\n.align-self-start {\n  align-self: flex-start !important;\n}\n\n.align-self-end {\n  align-self: flex-end !important;\n}\n\n.align-self-center {\n  align-self: center !important;\n}\n\n.align-self-baseline {\n  align-self: baseline !important;\n}\n\n.align-self-stretch {\n  align-self: stretch !important;\n}\n\n@media (min-width: 576px) {\n  .flex-sm-row {\n    flex-direction: row !important;\n  }\n  .flex-sm-column {\n    flex-direction: column !important;\n  }\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-sm-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-sm-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-sm-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-sm-center {\n    justify-content: center !important;\n  }\n  .justify-content-sm-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-sm-around {\n    justify-content: space-around !important;\n  }\n  .align-items-sm-start {\n    align-items: flex-start !important;\n  }\n  .align-items-sm-end {\n    align-items: flex-end !important;\n  }\n  .align-items-sm-center {\n    align-items: center !important;\n  }\n  .align-items-sm-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-sm-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-sm-start {\n    align-content: flex-start !important;\n  }\n  .align-content-sm-end {\n    align-content: flex-end !important;\n  }\n  .align-content-sm-center {\n    align-content: center !important;\n  }\n  .align-content-sm-between {\n    align-content: space-between !important;\n  }\n  .align-content-sm-around {\n    align-content: space-around !important;\n  }\n  .align-content-sm-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-sm-auto {\n    align-self: auto !important;\n  }\n  .align-self-sm-start {\n    align-self: flex-start !important;\n  }\n  .align-self-sm-end {\n    align-self: flex-end !important;\n  }\n  .align-self-sm-center {\n    align-self: center !important;\n  }\n  .align-self-sm-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-sm-stretch {\n    align-self: stretch !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .flex-md-row {\n    flex-direction: row !important;\n  }\n  .flex-md-column {\n    flex-direction: column !important;\n  }\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-md-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-md-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-md-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-md-center {\n    justify-content: center !important;\n  }\n  .justify-content-md-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-md-around {\n    justify-content: space-around !important;\n  }\n  .align-items-md-start {\n    align-items: flex-start !important;\n  }\n  .align-items-md-end {\n    align-items: flex-end !important;\n  }\n  .align-items-md-center {\n    align-items: center !important;\n  }\n  .align-items-md-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-md-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-md-start {\n    align-content: flex-start !important;\n  }\n  .align-content-md-end {\n    align-content: flex-end !important;\n  }\n  .align-content-md-center {\n    align-content: center !important;\n  }\n  .align-content-md-between {\n    align-content: space-between !important;\n  }\n  .align-content-md-around {\n    align-content: space-around !important;\n  }\n  .align-content-md-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-md-auto {\n    align-self: auto !important;\n  }\n  .align-self-md-start {\n    align-self: flex-start !important;\n  }\n  .align-self-md-end {\n    align-self: flex-end !important;\n  }\n  .align-self-md-center {\n    align-self: center !important;\n  }\n  .align-self-md-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-md-stretch {\n    align-self: stretch !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .flex-lg-row {\n    flex-direction: row !important;\n  }\n  .flex-lg-column {\n    flex-direction: column !important;\n  }\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-lg-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-lg-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-lg-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-lg-center {\n    justify-content: center !important;\n  }\n  .justify-content-lg-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-lg-around {\n    justify-content: space-around !important;\n  }\n  .align-items-lg-start {\n    align-items: flex-start !important;\n  }\n  .align-items-lg-end {\n    align-items: flex-end !important;\n  }\n  .align-items-lg-center {\n    align-items: center !important;\n  }\n  .align-items-lg-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-lg-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-lg-start {\n    align-content: flex-start !important;\n  }\n  .align-content-lg-end {\n    align-content: flex-end !important;\n  }\n  .align-content-lg-center {\n    align-content: center !important;\n  }\n  .align-content-lg-between {\n    align-content: space-between !important;\n  }\n  .align-content-lg-around {\n    align-content: space-around !important;\n  }\n  .align-content-lg-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-lg-auto {\n    align-self: auto !important;\n  }\n  .align-self-lg-start {\n    align-self: flex-start !important;\n  }\n  .align-self-lg-end {\n    align-self: flex-end !important;\n  }\n  .align-self-lg-center {\n    align-self: center !important;\n  }\n  .align-self-lg-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-lg-stretch {\n    align-self: stretch !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .flex-xl-row {\n    flex-direction: row !important;\n  }\n  .flex-xl-column {\n    flex-direction: column !important;\n  }\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-xl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xl-around {\n    justify-content: space-around !important;\n  }\n  .align-items-xl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xl-center {\n    align-items: center !important;\n  }\n  .align-items-xl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xl-center {\n    align-content: center !important;\n  }\n  .align-content-xl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xl-center {\n    align-self: center !important;\n  }\n  .align-self-xl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xl-stretch {\n    align-self: stretch !important;\n  }\n}\n\n.float-left {\n  float: left !important;\n}\n\n.float-right {\n  float: right !important;\n}\n\n.float-none {\n  float: none !important;\n}\n\n@media (min-width: 576px) {\n  .float-sm-left {\n    float: left !important;\n  }\n  .float-sm-right {\n    float: right !important;\n  }\n  .float-sm-none {\n    float: none !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .float-md-left {\n    float: left !important;\n  }\n  .float-md-right {\n    float: right !important;\n  }\n  .float-md-none {\n    float: none !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .float-lg-left {\n    float: left !important;\n  }\n  .float-lg-right {\n    float: right !important;\n  }\n  .float-lg-none {\n    float: none !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .float-xl-left {\n    float: left !important;\n  }\n  .float-xl-right {\n    float: right !important;\n  }\n  .float-xl-none {\n    float: none !important;\n  }\n}\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n@supports (position: sticky) {\n  .sticky-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n}\n\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  clip-path: inset(50%);\n  border: 0;\n}\n\n.sr-only-focusable:active, .sr-only-focusable:focus {\n  position: static;\n  width: auto;\n  height: auto;\n  overflow: visible;\n  clip: auto;\n  white-space: normal;\n  clip-path: none;\n}\n\n.w-25 {\n  width: 25% !important;\n}\n\n.w-50 {\n  width: 50% !important;\n}\n\n.w-75 {\n  width: 75% !important;\n}\n\n.w-100 {\n  width: 100% !important;\n}\n\n.h-25 {\n  height: 25% !important;\n}\n\n.h-50 {\n  height: 50% !important;\n}\n\n.h-75 {\n  height: 75% !important;\n}\n\n.h-100 {\n  height: 100% !important;\n}\n\n.mw-100 {\n  max-width: 100% !important;\n}\n\n.mh-100 {\n  max-height: 100% !important;\n}\n\n.m-0 {\n  margin: 0 !important;\n}\n\n.mt-0 {\n  margin-top: 0 !important;\n}\n\n.mr-0 {\n  margin-right: 0 !important;\n}\n\n.mb-0 {\n  margin-bottom: 0 !important;\n}\n\n.ml-0 {\n  margin-left: 0 !important;\n}\n\n.mx-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important;\n}\n\n.my-0 {\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n\n.m-1 {\n  margin: 0.25rem !important;\n}\n\n.mt-1 {\n  margin-top: 0.25rem !important;\n}\n\n.mr-1 {\n  margin-right: 0.25rem !important;\n}\n\n.mb-1 {\n  margin-bottom: 0.25rem !important;\n}\n\n.ml-1 {\n  margin-left: 0.25rem !important;\n}\n\n.mx-1 {\n  margin-right: 0.25rem !important;\n  margin-left: 0.25rem !important;\n}\n\n.my-1 {\n  margin-top: 0.25rem !important;\n  margin-bottom: 0.25rem !important;\n}\n\n.m-2 {\n  margin: 0.5rem !important;\n}\n\n.mt-2 {\n  margin-top: 0.5rem !important;\n}\n\n.mr-2 {\n  margin-right: 0.5rem !important;\n}\n\n.mb-2 {\n  margin-bottom: 0.5rem !important;\n}\n\n.ml-2 {\n  margin-left: 0.5rem !important;\n}\n\n.mx-2 {\n  margin-right: 0.5rem !important;\n  margin-left: 0.5rem !important;\n}\n\n.my-2 {\n  margin-top: 0.5rem !important;\n  margin-bottom: 0.5rem !important;\n}\n\n.m-3 {\n  margin: 1rem !important;\n}\n\n.mt-3 {\n  margin-top: 1rem !important;\n}\n\n.mr-3 {\n  margin-right: 1rem !important;\n}\n\n.mb-3 {\n  margin-bottom: 1rem !important;\n}\n\n.ml-3 {\n  margin-left: 1rem !important;\n}\n\n.mx-3 {\n  margin-right: 1rem !important;\n  margin-left: 1rem !important;\n}\n\n.my-3 {\n  margin-top: 1rem !important;\n  margin-bottom: 1rem !important;\n}\n\n.m-4 {\n  margin: 1.5rem !important;\n}\n\n.mt-4 {\n  margin-top: 1.5rem !important;\n}\n\n.mr-4 {\n  margin-right: 1.5rem !important;\n}\n\n.mb-4 {\n  margin-bottom: 1.5rem !important;\n}\n\n.ml-4 {\n  margin-left: 1.5rem !important;\n}\n\n.mx-4 {\n  margin-right: 1.5rem !important;\n  margin-left: 1.5rem !important;\n}\n\n.my-4 {\n  margin-top: 1.5rem !important;\n  margin-bottom: 1.5rem !important;\n}\n\n.m-5 {\n  margin: 3rem !important;\n}\n\n.mt-5 {\n  margin-top: 3rem !important;\n}\n\n.mr-5 {\n  margin-right: 3rem !important;\n}\n\n.mb-5 {\n  margin-bottom: 3rem !important;\n}\n\n.ml-5 {\n  margin-left: 3rem !important;\n}\n\n.mx-5 {\n  margin-right: 3rem !important;\n  margin-left: 3rem !important;\n}\n\n.my-5 {\n  margin-top: 3rem !important;\n  margin-bottom: 3rem !important;\n}\n\n.p-0 {\n  padding: 0 !important;\n}\n\n.pt-0 {\n  padding-top: 0 !important;\n}\n\n.pr-0 {\n  padding-right: 0 !important;\n}\n\n.pb-0 {\n  padding-bottom: 0 !important;\n}\n\n.pl-0 {\n  padding-left: 0 !important;\n}\n\n.px-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important;\n}\n\n.py-0 {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n.p-1 {\n  padding: 0.25rem !important;\n}\n\n.pt-1 {\n  padding-top: 0.25rem !important;\n}\n\n.pr-1 {\n  padding-right: 0.25rem !important;\n}\n\n.pb-1 {\n  padding-bottom: 0.25rem !important;\n}\n\n.pl-1 {\n  padding-left: 0.25rem !important;\n}\n\n.px-1 {\n  padding-right: 0.25rem !important;\n  padding-left: 0.25rem !important;\n}\n\n.py-1 {\n  padding-top: 0.25rem !important;\n  padding-bottom: 0.25rem !important;\n}\n\n.p-2 {\n  padding: 0.5rem !important;\n}\n\n.pt-2 {\n  padding-top: 0.5rem !important;\n}\n\n.pr-2 {\n  padding-right: 0.5rem !important;\n}\n\n.pb-2 {\n  padding-bottom: 0.5rem !important;\n}\n\n.pl-2 {\n  padding-left: 0.5rem !important;\n}\n\n.px-2 {\n  padding-right: 0.5rem !important;\n  padding-left: 0.5rem !important;\n}\n\n.py-2 {\n  padding-top: 0.5rem !important;\n  padding-bottom: 0.5rem !important;\n}\n\n.p-3 {\n  padding: 1rem !important;\n}\n\n.pt-3 {\n  padding-top: 1rem !important;\n}\n\n.pr-3 {\n  padding-right: 1rem !important;\n}\n\n.pb-3 {\n  padding-bottom: 1rem !important;\n}\n\n.pl-3 {\n  padding-left: 1rem !important;\n}\n\n.px-3 {\n  padding-right: 1rem !important;\n  padding-left: 1rem !important;\n}\n\n.py-3 {\n  padding-top: 1rem !important;\n  padding-bottom: 1rem !important;\n}\n\n.p-4 {\n  padding: 1.5rem !important;\n}\n\n.pt-4 {\n  padding-top: 1.5rem !important;\n}\n\n.pr-4 {\n  padding-right: 1.5rem !important;\n}\n\n.pb-4 {\n  padding-bottom: 1.5rem !important;\n}\n\n.pl-4 {\n  padding-left: 1.5rem !important;\n}\n\n.px-4 {\n  padding-right: 1.5rem !important;\n  padding-left: 1.5rem !important;\n}\n\n.py-4 {\n  padding-top: 1.5rem !important;\n  padding-bottom: 1.5rem !important;\n}\n\n.p-5 {\n  padding: 3rem !important;\n}\n\n.pt-5 {\n  padding-top: 3rem !important;\n}\n\n.pr-5 {\n  padding-right: 3rem !important;\n}\n\n.pb-5 {\n  padding-bottom: 3rem !important;\n}\n\n.pl-5 {\n  padding-left: 3rem !important;\n}\n\n.px-5 {\n  padding-right: 3rem !important;\n  padding-left: 3rem !important;\n}\n\n.py-5 {\n  padding-top: 3rem !important;\n  padding-bottom: 3rem !important;\n}\n\n.m-auto {\n  margin: auto !important;\n}\n\n.mt-auto {\n  margin-top: auto !important;\n}\n\n.mr-auto {\n  margin-right: auto !important;\n}\n\n.mb-auto {\n  margin-bottom: auto !important;\n}\n\n.ml-auto {\n  margin-left: auto !important;\n}\n\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important;\n}\n\n.my-auto {\n  margin-top: auto !important;\n  margin-bottom: auto !important;\n}\n\n@media (min-width: 576px) {\n  .m-sm-0 {\n    margin: 0 !important;\n  }\n  .mt-sm-0 {\n    margin-top: 0 !important;\n  }\n  .mr-sm-0 {\n    margin-right: 0 !important;\n  }\n  .mb-sm-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-sm-0 {\n    margin-left: 0 !important;\n  }\n  .mx-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .my-sm-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .m-sm-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-sm-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-sm-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-sm-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-sm-1 {\n    margin-left: 0.25rem !important;\n  }\n  .mx-sm-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .my-sm-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .m-sm-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-sm-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-sm-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-sm-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-sm-2 {\n    margin-left: 0.5rem !important;\n  }\n  .mx-sm-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .my-sm-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .m-sm-3 {\n    margin: 1rem !important;\n  }\n  .mt-sm-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-sm-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-sm-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-sm-3 {\n    margin-left: 1rem !important;\n  }\n  .mx-sm-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .my-sm-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .m-sm-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-sm-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-sm-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-sm-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-sm-4 {\n    margin-left: 1.5rem !important;\n  }\n  .mx-sm-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .my-sm-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .m-sm-5 {\n    margin: 3rem !important;\n  }\n  .mt-sm-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-sm-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-sm-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-sm-5 {\n    margin-left: 3rem !important;\n  }\n  .mx-sm-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .my-sm-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .p-sm-0 {\n    padding: 0 !important;\n  }\n  .pt-sm-0 {\n    padding-top: 0 !important;\n  }\n  .pr-sm-0 {\n    padding-right: 0 !important;\n  }\n  .pb-sm-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-sm-0 {\n    padding-left: 0 !important;\n  }\n  .px-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .py-sm-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .p-sm-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-sm-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-sm-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-sm-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-sm-1 {\n    padding-left: 0.25rem !important;\n  }\n  .px-sm-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .py-sm-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .p-sm-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-sm-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-sm-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-sm-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-sm-2 {\n    padding-left: 0.5rem !important;\n  }\n  .px-sm-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .py-sm-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .p-sm-3 {\n    padding: 1rem !important;\n  }\n  .pt-sm-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-sm-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-sm-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-sm-3 {\n    padding-left: 1rem !important;\n  }\n  .px-sm-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .py-sm-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .p-sm-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-sm-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-sm-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-sm-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-sm-4 {\n    padding-left: 1.5rem !important;\n  }\n  .px-sm-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .py-sm-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .p-sm-5 {\n    padding: 3rem !important;\n  }\n  .pt-sm-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-sm-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-sm-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-sm-5 {\n    padding-left: 3rem !important;\n  }\n  .px-sm-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-sm-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .m-sm-auto {\n    margin: auto !important;\n  }\n  .mt-sm-auto {\n    margin-top: auto !important;\n  }\n  .mr-sm-auto {\n    margin-right: auto !important;\n  }\n  .mb-sm-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-sm-auto {\n    margin-left: auto !important;\n  }\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-sm-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .m-md-0 {\n    margin: 0 !important;\n  }\n  .mt-md-0 {\n    margin-top: 0 !important;\n  }\n  .mr-md-0 {\n    margin-right: 0 !important;\n  }\n  .mb-md-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-md-0 {\n    margin-left: 0 !important;\n  }\n  .mx-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .my-md-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .m-md-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-md-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-md-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-md-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-md-1 {\n    margin-left: 0.25rem !important;\n  }\n  .mx-md-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .my-md-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .m-md-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-md-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-md-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-md-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-md-2 {\n    margin-left: 0.5rem !important;\n  }\n  .mx-md-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .my-md-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .m-md-3 {\n    margin: 1rem !important;\n  }\n  .mt-md-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-md-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-md-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-md-3 {\n    margin-left: 1rem !important;\n  }\n  .mx-md-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .my-md-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .m-md-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-md-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-md-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-md-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-md-4 {\n    margin-left: 1.5rem !important;\n  }\n  .mx-md-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .my-md-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .m-md-5 {\n    margin: 3rem !important;\n  }\n  .mt-md-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-md-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-md-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-md-5 {\n    margin-left: 3rem !important;\n  }\n  .mx-md-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .my-md-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .p-md-0 {\n    padding: 0 !important;\n  }\n  .pt-md-0 {\n    padding-top: 0 !important;\n  }\n  .pr-md-0 {\n    padding-right: 0 !important;\n  }\n  .pb-md-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-md-0 {\n    padding-left: 0 !important;\n  }\n  .px-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .py-md-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .p-md-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-md-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-md-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-md-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-md-1 {\n    padding-left: 0.25rem !important;\n  }\n  .px-md-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .py-md-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .p-md-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-md-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-md-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-md-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-md-2 {\n    padding-left: 0.5rem !important;\n  }\n  .px-md-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .py-md-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .p-md-3 {\n    padding: 1rem !important;\n  }\n  .pt-md-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-md-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-md-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-md-3 {\n    padding-left: 1rem !important;\n  }\n  .px-md-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .py-md-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .p-md-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-md-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-md-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-md-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-md-4 {\n    padding-left: 1.5rem !important;\n  }\n  .px-md-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .py-md-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .p-md-5 {\n    padding: 3rem !important;\n  }\n  .pt-md-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-md-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-md-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-md-5 {\n    padding-left: 3rem !important;\n  }\n  .px-md-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-md-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .m-md-auto {\n    margin: auto !important;\n  }\n  .mt-md-auto {\n    margin-top: auto !important;\n  }\n  .mr-md-auto {\n    margin-right: auto !important;\n  }\n  .mb-md-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-md-auto {\n    margin-left: auto !important;\n  }\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-md-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .m-lg-0 {\n    margin: 0 !important;\n  }\n  .mt-lg-0 {\n    margin-top: 0 !important;\n  }\n  .mr-lg-0 {\n    margin-right: 0 !important;\n  }\n  .mb-lg-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-lg-0 {\n    margin-left: 0 !important;\n  }\n  .mx-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .my-lg-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .m-lg-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-lg-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-lg-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-lg-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-lg-1 {\n    margin-left: 0.25rem !important;\n  }\n  .mx-lg-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .my-lg-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .m-lg-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-lg-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-lg-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-lg-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-lg-2 {\n    margin-left: 0.5rem !important;\n  }\n  .mx-lg-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .my-lg-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .m-lg-3 {\n    margin: 1rem !important;\n  }\n  .mt-lg-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-lg-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-lg-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-lg-3 {\n    margin-left: 1rem !important;\n  }\n  .mx-lg-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .my-lg-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .m-lg-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-lg-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-lg-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-lg-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-lg-4 {\n    margin-left: 1.5rem !important;\n  }\n  .mx-lg-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .my-lg-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .m-lg-5 {\n    margin: 3rem !important;\n  }\n  .mt-lg-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-lg-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-lg-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-lg-5 {\n    margin-left: 3rem !important;\n  }\n  .mx-lg-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .my-lg-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .p-lg-0 {\n    padding: 0 !important;\n  }\n  .pt-lg-0 {\n    padding-top: 0 !important;\n  }\n  .pr-lg-0 {\n    padding-right: 0 !important;\n  }\n  .pb-lg-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-lg-0 {\n    padding-left: 0 !important;\n  }\n  .px-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .py-lg-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .p-lg-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-lg-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-lg-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-lg-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-lg-1 {\n    padding-left: 0.25rem !important;\n  }\n  .px-lg-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .py-lg-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .p-lg-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-lg-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-lg-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-lg-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-lg-2 {\n    padding-left: 0.5rem !important;\n  }\n  .px-lg-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .py-lg-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .p-lg-3 {\n    padding: 1rem !important;\n  }\n  .pt-lg-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-lg-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-lg-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-lg-3 {\n    padding-left: 1rem !important;\n  }\n  .px-lg-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .py-lg-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .p-lg-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-lg-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-lg-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-lg-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-lg-4 {\n    padding-left: 1.5rem !important;\n  }\n  .px-lg-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .py-lg-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .p-lg-5 {\n    padding: 3rem !important;\n  }\n  .pt-lg-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-lg-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-lg-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-lg-5 {\n    padding-left: 3rem !important;\n  }\n  .px-lg-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-lg-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .m-lg-auto {\n    margin: auto !important;\n  }\n  .mt-lg-auto {\n    margin-top: auto !important;\n  }\n  .mr-lg-auto {\n    margin-right: auto !important;\n  }\n  .mb-lg-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-lg-auto {\n    margin-left: auto !important;\n  }\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-lg-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .m-xl-0 {\n    margin: 0 !important;\n  }\n  .mt-xl-0 {\n    margin-top: 0 !important;\n  }\n  .mr-xl-0 {\n    margin-right: 0 !important;\n  }\n  .mb-xl-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-xl-0 {\n    margin-left: 0 !important;\n  }\n  .mx-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .my-xl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .m-xl-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-xl-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-xl-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-xl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-xl-1 {\n    margin-left: 0.25rem !important;\n  }\n  .mx-xl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .my-xl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .m-xl-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-xl-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-xl-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-xl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-xl-2 {\n    margin-left: 0.5rem !important;\n  }\n  .mx-xl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .my-xl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .m-xl-3 {\n    margin: 1rem !important;\n  }\n  .mt-xl-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-xl-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-xl-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-xl-3 {\n    margin-left: 1rem !important;\n  }\n  .mx-xl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .my-xl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .m-xl-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-xl-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-xl-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-xl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-xl-4 {\n    margin-left: 1.5rem !important;\n  }\n  .mx-xl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .my-xl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .m-xl-5 {\n    margin: 3rem !important;\n  }\n  .mt-xl-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-xl-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-xl-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-xl-5 {\n    margin-left: 3rem !important;\n  }\n  .mx-xl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .my-xl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .p-xl-0 {\n    padding: 0 !important;\n  }\n  .pt-xl-0 {\n    padding-top: 0 !important;\n  }\n  .pr-xl-0 {\n    padding-right: 0 !important;\n  }\n  .pb-xl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-xl-0 {\n    padding-left: 0 !important;\n  }\n  .px-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .py-xl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .p-xl-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-xl-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-xl-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-xl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-xl-1 {\n    padding-left: 0.25rem !important;\n  }\n  .px-xl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .py-xl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .p-xl-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-xl-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-xl-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-xl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-xl-2 {\n    padding-left: 0.5rem !important;\n  }\n  .px-xl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .py-xl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .p-xl-3 {\n    padding: 1rem !important;\n  }\n  .pt-xl-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-xl-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-xl-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-xl-3 {\n    padding-left: 1rem !important;\n  }\n  .px-xl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .py-xl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .p-xl-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-xl-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-xl-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-xl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-xl-4 {\n    padding-left: 1.5rem !important;\n  }\n  .px-xl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .py-xl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .p-xl-5 {\n    padding: 3rem !important;\n  }\n  .pt-xl-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-xl-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-xl-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-xl-5 {\n    padding-left: 3rem !important;\n  }\n  .px-xl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-xl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .m-xl-auto {\n    margin: auto !important;\n  }\n  .mt-xl-auto {\n    margin-top: auto !important;\n  }\n  .mr-xl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xl-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-xl-auto {\n    margin-left: auto !important;\n  }\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-xl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n}\n\n.text-justify {\n  text-align: justify !important;\n}\n\n.text-nowrap {\n  white-space: nowrap !important;\n}\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.text-left {\n  text-align: left !important;\n}\n\n.text-right {\n  text-align: right !important;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n@media (min-width: 576px) {\n  .text-sm-left {\n    text-align: left !important;\n  }\n  .text-sm-right {\n    text-align: right !important;\n  }\n  .text-sm-center {\n    text-align: center !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .text-md-left {\n    text-align: left !important;\n  }\n  .text-md-right {\n    text-align: right !important;\n  }\n  .text-md-center {\n    text-align: center !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .text-lg-left {\n    text-align: left !important;\n  }\n  .text-lg-right {\n    text-align: right !important;\n  }\n  .text-lg-center {\n    text-align: center !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .text-xl-left {\n    text-align: left !important;\n  }\n  .text-xl-right {\n    text-align: right !important;\n  }\n  .text-xl-center {\n    text-align: center !important;\n  }\n}\n\n.text-lowercase {\n  text-transform: lowercase !important;\n}\n\n.text-uppercase {\n  text-transform: uppercase !important;\n}\n\n.text-capitalize {\n  text-transform: capitalize !important;\n}\n\n.font-weight-normal {\n  font-weight: normal;\n}\n\n.font-weight-bold {\n  font-weight: bold;\n}\n\n.font-italic {\n  font-style: italic;\n}\n\n.text-white {\n  color: #fff !important;\n}\n\n.text-primary {\n  color: #007bff !important;\n}\n\na.text-primary:focus, a.text-primary:hover {\n  color: #0062cc !important;\n}\n\n.text-secondary {\n  color: #868e96 !important;\n}\n\na.text-secondary:focus, a.text-secondary:hover {\n  color: #6c757d !important;\n}\n\n.text-success {\n  color: #28a745 !important;\n}\n\na.text-success:focus, a.text-success:hover {\n  color: #1e7e34 !important;\n}\n\n.text-info {\n  color: #17a2b8 !important;\n}\n\na.text-info:focus, a.text-info:hover {\n  color: #117a8b !important;\n}\n\n.text-warning {\n  color: #ffc107 !important;\n}\n\na.text-warning:focus, a.text-warning:hover {\n  color: #d39e00 !important;\n}\n\n.text-danger {\n  color: #dc3545 !important;\n}\n\na.text-danger:focus, a.text-danger:hover {\n  color: #bd2130 !important;\n}\n\n.text-light {\n  color: #f8f9fa !important;\n}\n\na.text-light:focus, a.text-light:hover {\n  color: #dae0e5 !important;\n}\n\n.text-dark {\n  color: #343a40 !important;\n}\n\na.text-dark:focus, a.text-dark:hover {\n  color: #1d2124 !important;\n}\n\n.text-muted {\n  color: #868e96 !important;\n}\n\n.text-hide {\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n}\n\n.visible {\n  visibility: visible !important;\n}\n\n.invisible {\n  visibility: hidden !important;\n}\n\n/*# sourceMappingURL=bootstrap.css.map */", "// scss-lint:disable QualifyingElement, DuplicateProperty, VendorPrefix\n\n// Reboot\n//\n// Normalization of HTML elements, manually forked from Normalize.css to remove\n// styles targeting irrelevant browsers while applying new styles.\n//\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\n\n\n// Document\n//\n// 1. Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\n// 2. Change the default font family in all browsers.\n// 3. Correct the line height in all browsers.\n// 4. Prevent adjustments of font size after orientation changes in IE on Windows Phone and in iOS.\n// 5. Setting @viewport causes scrollbars to overlap content in IE11 and Edge, so\n//    we force a non-overlapping, non-auto-hiding scrollbar to counteract.\n// 6. Change the default tap highlight to be completely transparent in iOS.\n\nhtml {\n  box-sizing: border-box; // 1\n  font-family: sans-serif; // 2\n  line-height: 1.15; // 3\n  -webkit-text-size-adjust: 100%; // 4\n  -ms-text-size-adjust: 100%; // 4\n  -ms-overflow-style: scrollbar; // 5\n  -webkit-tap-highlight-color: rgba(0,0,0,0); // 6\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: inherit; // 1\n}\n\n// IE10+ doesn't honor `<meta name=\"viewport\">` in some cases.\n@at-root {\n  @-ms-viewport { width: device-width; }\n}\n\n// Shim for \"new\" HTML5 structural elements to display correctly (IE10, older browsers)\narticle, aside, dialog, figcaption, figure, footer, header, hgroup, main, nav, section {\n  display: block;\n}\n\n// Body\n//\n// 1. Remove the margin in all browsers.\n// 2. As a best practice, apply a default `background-color`.\n\nbody {\n  margin: 0; // 1\n  font-family: $font-family-base;\n  font-size: $font-size-base;\n  font-weight: $font-weight-base;\n  line-height: $line-height-base;\n  color: $body-color;\n  background-color: $body-bg; // 2\n}\n\n// Suppress the focus outline on elements that cannot be accessed via keyboard.\n// This prevents an unwanted focus outline from appearing around elements that\n// might still respond to pointer events.\n//\n// Credit: https://github.com/suitcss/base\n[tabindex=\"-1\"]:focus {\n  outline: none !important;\n}\n\n\n// Content grouping\n//\n// 1. Add the correct box sizing in Firefox.\n// 2. Show the overflow in Edge and IE.\n\nhr {\n  box-sizing: content-box; // 1\n  height: 0; // 1\n  overflow: visible; // 2\n}\n\n\n//\n// Typography\n//\n\n// Remove top margins from headings\n//\n// By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\n// margin for easier control within type scales as it avoids margin collapsing.\nh1, h2, h3, h4, h5, h6 {\n  margin-top: 0;\n  margin-bottom: .5rem;\n}\n\n// Reset margins on paragraphs\n//\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\n// bottom margin to use `rem` units instead of `em`.\np {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\n// Abbreviations\n//\n// 1. Remove the bottom border in Firefox 39-.\n// 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.\n// 3. Add explicit cursor to indicate changed behavior.\n// 4. Duplicate behavior to the data-* attribute for our tooltip plugin\n\nabbr[title],\nabbr[data-original-title] { // 4\n  text-decoration: underline; // 2\n  text-decoration: underline dotted; // 2\n  cursor: help; // 3\n  border-bottom: 0; // 1\n}\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: $dt-font-weight;\n}\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; // Undo browser default\n}\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\ndfn {\n  font-style: italic; // Add the correct font style in Android 4.3-\n}\n\nb,\nstrong {\n  font-weight: bolder; // Add the correct font weight in Chrome, Edge, and Safari\n}\n\nsmall {\n  font-size: 80%; // Add the correct font size in all browsers\n}\n\n//\n// Prevent `sub` and `sup` elements from affecting the line height in\n// all browsers.\n//\n\nsub,\nsup {\n  position: relative;\n  font-size: 75%;\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub { bottom: -.25em; }\nsup { top: -.5em; }\n\n\n//\n// Links\n//\n\na {\n  color: $link-color;\n  text-decoration: $link-decoration;\n  background-color: transparent; // Remove the gray background on active links in IE 10.\n  -webkit-text-decoration-skip: objects; // Remove gaps in links underline in iOS 8+ and Safari 8+.\n\n  @include hover {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n}\n\n// And undo these styles for placeholder links/named anchors (without href)\n// which have not been made explicitly keyboard-focusable (without tabindex).\n// It would be more straightforward to just use a[href] in previous block, but that\n// causes specificity issues in many other styles that are too complex to fix.\n// See https://github.com/twbs/bootstrap/issues/19402\n\na:not([href]):not([tabindex]) {\n  color: inherit;\n  text-decoration: none;\n\n  @include hover-focus {\n    color: inherit;\n    text-decoration: none;\n  }\n\n  &:focus {\n    outline: 0;\n  }\n}\n\n\n//\n// Code\n//\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: monospace, monospace; // Correct the inheritance and scaling of font size in all browsers.\n  font-size: 1em; // Correct the odd `em` font sizing in all browsers.\n}\n\npre {\n  // Remove browser default top margin\n  margin-top: 0;\n  // Reset browser default of `1em` to use `rem`s\n  margin-bottom: 1rem;\n  // Don't allow content to break outside\n  overflow: auto;\n}\n\n\n//\n// Figures\n//\n\nfigure {\n  // Apply a consistent margin strategy (matches our type styles).\n  margin: 0 0 1rem;\n}\n\n\n//\n// Images and content\n//\n\nimg {\n  vertical-align: middle;\n  border-style: none; // Remove the border on images inside links in IE 10-.\n}\n\nsvg:not(:root) {\n  overflow: hidden; // Hide the overflow in IE\n}\n\n\n// Avoid 300ms click delay on touch devices that support the `touch-action` CSS property.\n//\n// In particular, unlike most other browsers, IE11+Edge on Windows 10 on touch devices and IE Mobile 10-11\n// DON'T remove the click delay when `<meta name=\"viewport\" content=\"width=device-width\">` is present.\n// However, they DO support removing the click delay via `touch-action: manipulation`.\n// See:\n// * https://v4-alpha.getbootstrap.com/content/reboot/#click-delay-optimization-for-touch\n// * http://caniuse.com/#feat=css-touch-action\n// * https://patrickhlauke.github.io/touch/tests/results/#suppressing-300ms-delay\n\na,\narea,\nbutton,\n[role=\"button\"],\ninput,\nlabel,\nselect,\nsummary,\ntextarea {\n  touch-action: manipulation;\n}\n\n\n//\n// Tables\n//\n\ntable {\n  border-collapse: collapse; // Prevent double borders\n}\n\ncaption {\n  padding-top: $table-cell-padding;\n  padding-bottom: $table-cell-padding;\n  color: $text-muted;\n  text-align: left;\n  caption-side: bottom;\n}\n\nth {\n  // Matches default `<td>` alignment\n  text-align: left;\n}\n\n\n//\n// Forms\n//\n\nlabel {\n  // Allow labels to use `margin` for spacing.\n  display: inline-block;\n  margin-bottom: .5rem;\n}\n\n// Work around a Firefox/IE bug where the transparent `button` background\n// results in a loss of the default `button` focus styles.\n//\n// Credit: https://github.com/suitcss/base/\nbutton:focus {\n  outline: 1px dotted;\n  outline: 5px auto -webkit-focus-ring-color;\n}\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0; // Remove the margin in Firefox and Safari\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n\nbutton,\ninput {\n  overflow: visible; // Show the overflow in Edge\n}\n\nbutton,\nselect {\n  text-transform: none; // Remove the inheritance of text transform in Firefox\n}\n\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\n//    controls in Android 4.\n// 2. Correct the inability to style clickable types in iOS and Safari.\nbutton,\nhtml [type=\"button\"], // 1\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n}\n\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\nbutton::-moz-focus-inner,\n[type=\"button\"]::-moz-focus-inner,\n[type=\"reset\"]::-moz-focus-inner,\n[type=\"submit\"]::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\ninput[type=\"radio\"],\ninput[type=\"checkbox\"] {\n  box-sizing: border-box; // 1. Add the correct box sizing in IE 10-\n  padding: 0; // 2. Remove the padding in IE 10-\n}\n\n\ninput[type=\"date\"],\ninput[type=\"time\"],\ninput[type=\"datetime-local\"],\ninput[type=\"month\"] {\n  // Remove the default appearance of temporal inputs to avoid a Mobile Safari\n  // bug where setting a custom line-height prevents text from being vertically\n  // centered within the input.\n  // See https://bugs.webkit.org/show_bug.cgi?id=139848\n  // and https://github.com/twbs/bootstrap/issues/11266\n  -webkit-appearance: listbox;\n}\n\ntextarea {\n  overflow: auto; // Remove the default vertical scrollbar in IE.\n  // Textareas should really only resize vertically so they don't break their (horizontal) containers.\n  resize: vertical;\n}\n\nfieldset {\n  // Browsers set a default `min-width: min-content;` on fieldsets,\n  // unlike e.g. `<div>`s, which have `min-width: 0;` by default.\n  // So we reset that to ensure fieldsets behave more like a standard block element.\n  // See https://github.com/twbs/bootstrap/issues/12359\n  // and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\n  min-width: 0;\n  // Reset the default outline behavior of fieldsets so they don't affect page layout.\n  padding: 0;\n  margin: 0;\n  border: 0;\n}\n\n// 1. Correct the text wrapping in Edge and IE.\n// 2. Correct the color inheritance from `fieldset` elements in IE.\nlegend {\n  display: block;\n  width: 100%;\n  max-width: 100%; // 1\n  padding: 0;\n  margin-bottom: .5rem;\n  font-size: 1.5rem;\n  line-height: inherit;\n  color: inherit; // 2\n  white-space: normal; // 1\n}\n\nprogress {\n  vertical-align: baseline; // Add the correct vertical alignment in Chrome, Firefox, and Opera.\n}\n\n// Correct the cursor style of increment and decrement buttons in Chrome.\n[type=\"number\"]::-webkit-inner-spin-button,\n[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n[type=\"search\"] {\n  // This overrides the extra rounded corners on search inputs in iOS so that our\n  // `.form-control` class can properly style them. Note that this cannot simply\n  // be added to `.form-control` as it's not specific enough. For details, see\n  // https://github.com/twbs/bootstrap/issues/11586.\n  outline-offset: -2px; // 2. Correct the outline style in Safari.\n  -webkit-appearance: none;\n}\n\n//\n// Remove the inner padding and cancel buttons in Chrome and Safari on macOS.\n//\n\n[type=\"search\"]::-webkit-search-cancel-button,\n[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n//\n// 1. Correct the inability to style clickable types in iOS and Safari.\n// 2. Change font properties to `inherit` in Safari.\n//\n\n::-webkit-file-upload-button {\n  font: inherit; // 2\n  -webkit-appearance: button; // 1\n}\n\n//\n// Correct element displays\n//\n\noutput {\n  display: inline-block;\n}\n\nsummary {\n  display: list-item; // Add the correct display in all browsers\n}\n\ntemplate {\n  display: none; // Add the correct display in IE\n}\n\n// Always hide an element with the `hidden` HTML attribute (from PureCSS).\n// Needed for proper display in IE 10-.\n[hidden] {\n  display: none !important;\n}\n", "// Variables\n//\n// Copy settings from this file into the provided `_custom.scss` to override\n// the Bootstrap defaults without modifying key, versioned files.\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Table of Contents\n//\n// Color system\n// Options\n// Spacing\n// Body\n// Links\n// Grid breakpoints\n// Grid containers\n// Grid columns\n// Fonts\n// Components\n// Tables\n// Buttons\n// Forms\n// Dropdowns\n// Z-index master list\n// Navs\n// Navbar\n// Pagination\n// Jumbotron\n// Form states and alerts\n// Cards\n// Tooltips\n// Popovers\n// Badges\n// Modals\n// Alerts\n// Progress bars\n// List group\n// Image thumbnails\n// Figures\n// Breadcrumbs\n// Carousel\n// Close\n// Code\n\n\n//\n// Color system\n//\n\n$white:  #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #868e96 !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:  #000 !default;\n\n$grays: (\n  100: $gray-100,\n  200: $gray-200,\n  300: $gray-300,\n  400: $gray-400,\n  500: $gray-500,\n  600: $gray-600,\n  700: $gray-700,\n  800: $gray-800,\n  900: $gray-900\n) !default;\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: (\n  blue: $blue,\n  indigo: $indigo,\n  purple: $purple,\n  pink: $pink,\n  red: $red,\n  orange: $orange,\n  yellow: $yellow,\n  green: $green,\n  teal: $teal,\n  cyan: $cyan,\n  white: $white,\n  gray: $gray-600,\n  gray-dark: $gray-800\n) !default;\n\n$theme-colors: (\n  primary: $blue,\n  secondary: $gray-600,\n  success: $green,\n  info: $cyan,\n  warning: $yellow,\n  danger: $red,\n  light: $gray-100,\n  dark: $gray-800\n) !default;\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval: 8% !default;\n\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-rounded:            true !default;\n$enable-shadows:            false !default;\n$enable-gradients:          false !default;\n$enable-transitions:        true !default;\n$enable-hover-media-query:  false !default;\n$enable-grid-classes:       true !default;\n$enable-print-styles:       true !default;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n$spacer: 1rem !default;\n$spacers: (\n  0: 0,\n  1: ($spacer * .25),\n  2: ($spacer * .5),\n  3: $spacer,\n  4: ($spacer * 1.5),\n  5: ($spacer * 3)\n) !default;\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: (\n  25: 25%,\n  50: 50%,\n  75: 75%,\n  100: 100%\n) !default;\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:       $white !default;\n$body-color:    $gray-900 !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:            theme-color(\"primary\") !default;\n$link-decoration:       none !default;\n$link-hover-color:      darken($link-color, 15%) !default;\n$link-hover-decoration: underline !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints);\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns: 12 !default;\n$grid-gutter-width: 30px !default;\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:         1.5 !default;\n$line-height-sm:         1.5 !default;\n\n$border-width: 1px !default;\n\n$border-radius:          .25rem !default;\n$border-radius-lg:       .3rem !default;\n$border-radius-sm:       .2rem !default;\n\n$component-active-color: $white !default;\n$component-active-bg:    theme-color(\"primary\") !default;\n\n$caret-width:            .3em !default;\n\n$transition-base:        all .2s ease-in-out !default;\n$transition-fade:        opacity .15s linear !default;\n$transition-collapse:    height .35s ease !default;\n\n\n// Fonts\n//\n// Font, line-height, and color for body text, headings, and more.\n\n$font-family-sans-serif: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif !default;\n$font-family-monospace:  Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:       $font-family-sans-serif !default;\n\n$font-size-base: 1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:   1.25rem !default;\n$font-size-sm:   .875rem !default;\n\n$font-weight-normal: normal !default;\n$font-weight-bold: bold !default;\n\n$font-weight-base: $font-weight-normal !default;\n$line-height-base: 1.5 !default;\n\n$h1-font-size: 2.5rem !default;\n$h2-font-size: 2rem !default;\n$h3-font-size: 1.75rem !default;\n$h4-font-size: 1.5rem !default;\n$h5-font-size: 1.25rem !default;\n$h6-font-size: 1rem !default;\n\n$headings-margin-bottom: ($spacer / 2) !default;\n$headings-font-family:   inherit !default;\n$headings-font-weight:   500 !default;\n$headings-line-height:   1.1 !default;\n$headings-color:         inherit !default;\n\n$display1-size: 6rem !default;\n$display2-size: 5.5rem !default;\n$display3-size: 4.5rem !default;\n$display4-size: 3.5rem !default;\n\n$display1-weight:     300 !default;\n$display2-weight:     300 !default;\n$display3-weight:     300 !default;\n$display4-weight:     300 !default;\n$display-line-height: $headings-line-height !default;\n\n$lead-font-size:   1.25rem !default;\n$lead-font-weight: 300 !default;\n\n$small-font-size: 80% !default;\n\n$text-muted: $gray-600 !default;\n\n$blockquote-small-color:  $gray-600 !default;\n$blockquote-font-size:    ($font-size-base * 1.25) !default;\n\n$hr-border-color: rgba($black,.1) !default;\n$hr-border-width: $border-width !default;\n\n$mark-padding: .2em !default;\n\n$dt-font-weight: $font-weight-bold !default;\n\n$kbd-box-shadow:         inset 0 -.1rem 0 rgba($black,.25) !default;\n$nested-kbd-font-weight: $font-weight-bold !default;\n\n$list-inline-padding: 5px !default;\n\n$mark-bg: #fcf8e3 !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:            .75rem !default;\n$table-cell-padding-sm:         .3rem !default;\n\n$table-bg:                      transparent !default;\n$table-accent-bg:               rgba($black,.05) !default;\n$table-hover-bg:                rgba($black,.075) !default;\n$table-active-bg:               $table-hover-bg !default;\n\n$table-border-width:            $border-width !default;\n$table-border-color:            $gray-200 !default;\n\n$table-head-bg:                 $gray-200 !default;\n$table-head-color:              $gray-700 !default;\n\n$table-inverse-bg:              $gray-900 !default;\n$table-inverse-accent-bg:       rgba($white, .05) !default;\n$table-inverse-hover-bg:        rgba($white, .075) !default;\n$table-inverse-border-color:    lighten($gray-900, 7.5%) !default;\n$table-inverse-color:           $body-bg !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background and border color.\n\n$input-btn-padding-y:       .5rem !default;\n$input-btn-padding-x:       .75rem !default;\n$input-btn-line-height:     1.25 !default;\n\n$input-btn-padding-y-sm:    .25rem !default;\n$input-btn-padding-x-sm:    .5rem !default;\n$input-btn-line-height-sm:  1.5 !default;\n\n$input-btn-padding-y-lg:    .5rem !default;\n$input-btn-padding-x-lg:    1rem !default;\n$input-btn-line-height-lg:  1.5 !default;\n\n$btn-font-weight:                $font-weight-normal !default;\n$btn-box-shadow:                 inset 0 1px 0 rgba($white,.15), 0 1px 1px rgba($black,.075) !default;\n$btn-focus-box-shadow:           0 0 0 3px rgba(theme-color(\"primary\"), .25) !default;\n$btn-active-box-shadow:          inset 0 3px 5px rgba($black,.125) !default;\n\n$btn-link-disabled-color:        $gray-600 !default;\n\n$btn-block-spacing-y:            .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:              $border-radius !default;\n$btn-border-radius-lg:           $border-radius-lg !default;\n$btn-border-radius-sm:           $border-radius-sm !default;\n\n$btn-transition:                 all .15s ease-in-out !default;\n\n\n// Forms\n\n$input-bg:                       $white !default;\n$input-disabled-bg:              $gray-200 !default;\n\n$input-color:                    $gray-700 !default;\n$input-border-color:             rgba($black,.15) !default;\n$input-btn-border-width:         $border-width !default; // For form controls and buttons\n$input-box-shadow:               inset 0 1px 1px rgba($black,.075) !default;\n\n$input-border-radius:            $border-radius !default;\n$input-border-radius-lg:         $border-radius-lg !default;\n$input-border-radius-sm:         $border-radius-sm !default;\n\n$input-focus-bg:                 $input-bg !default;\n$input-focus-border-color:       lighten(theme-color(\"primary\"), 25%) !default;\n$input-focus-box-shadow:         $input-box-shadow, $btn-focus-box-shadow !default;\n$input-focus-color:              $input-color !default;\n\n$input-placeholder-color:        $gray-600 !default;\n\n$input-height-border:           $input-btn-border-width * 2 !default;\n\n$input-height-inner:            ($font-size-base * $input-btn-line-height) + ($input-btn-padding-y * 2) !default;\n$input-height:                  calc(#{$input-height-inner} + #{$input-height-border}) !default;\n\n$input-height-inner-sm:         ($font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2) !default;\n$input-height-sm:               calc(#{$input-height-inner-sm} + #{$input-height-border}) !default;\n\n$input-height-inner-lg:         ($font-size-sm * $input-btn-line-height-lg) + ($input-btn-padding-y-lg * 2) !default;\n$input-height-lg:               calc(#{$input-height-inner-lg} + #{$input-height-border}) !default;\n\n$input-transition:               border-color ease-in-out .15s, box-shadow ease-in-out .15s !default;\n\n$form-text-margin-top:     .25rem !default;\n\n$form-check-margin-bottom:  .5rem !default;\n$form-check-input-gutter:   1.25rem !default;\n$form-check-input-margin-y: .25rem !default;\n$form-check-input-margin-x: .25rem !default;\n\n$form-check-inline-margin-x: .75rem !default;\n\n$form-group-margin-bottom:       1rem !default;\n\n$input-group-addon-bg:           $gray-200 !default;\n$input-group-addon-border-color: $input-border-color !default;\n\n$custom-control-gutter:   1.5rem !default;\n$custom-control-spacer-y: .25rem !default;\n$custom-control-spacer-x: 1rem !default;\n\n$custom-control-indicator-size:       1rem !default;\n$custom-control-indicator-bg:         #ddd !default;\n$custom-control-indicator-bg-size:    50% 50% !default;\n$custom-control-indicator-box-shadow: inset 0 .25rem .25rem rgba($black,.1) !default;\n\n$custom-control-indicator-disabled-bg:       $gray-200 !default;\n$custom-control-description-disabled-color:  $gray-600 !default;\n\n$custom-control-indicator-checked-color:      $white !default;\n$custom-control-indicator-checked-bg:         theme-color(\"primary\") !default;\n$custom-control-indicator-checked-box-shadow: none !default;\n\n$custom-control-indicator-focus-box-shadow: 0 0 0 1px $body-bg, 0 0 0 3px theme-color(\"primary\") !default;\n\n$custom-control-indicator-active-color:      $white !default;\n$custom-control-indicator-active-bg:         lighten(theme-color(\"primary\"), 35%) !default;\n$custom-control-indicator-active-box-shadow: none !default;\n\n$custom-checkbox-indicator-border-radius: $border-radius !default;\n$custom-checkbox-indicator-icon-checked: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg: theme-color(\"primary\") !default;\n$custom-checkbox-indicator-indeterminate-color: $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow: none !default;\n\n$custom-radio-indicator-border-radius: 50% !default;\n$custom-radio-indicator-icon-checked: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-select-padding-y:          .375rem !default;\n$custom-select-padding-x:          .75rem  !default;\n$custom-select-height:              $input-height  !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-line-height:         $input-btn-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:            $white !default;\n$custom-select-disabled-bg:   $gray-200 !default;\n$custom-select-bg-size:       8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color: #333 !default;\n$custom-select-indicator:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-select-border-width:  $input-btn-border-width !default;\n$custom-select-border-color:  $input-border-color !default;\n$custom-select-border-radius: $border-radius !default;\n\n$custom-select-focus-border-color: lighten(theme-color(\"primary\"), 25%) !default;\n$custom-select-focus-box-shadow:   inset 0 1px 2px rgba($black, .075), 0 0 5px rgba($custom-select-focus-border-color, .5) !default;\n\n$custom-select-font-size-sm:  75% !default;\n$custom-select-height-sm: $input-height-sm !default;\n\n$custom-file-height:           2.5rem !default;\n$custom-file-width:            14rem !default;\n$custom-file-focus-box-shadow: 0 0 0 .075rem $white, 0 0 0 .2rem theme-color(\"primary\") !default;\n\n$custom-file-padding-y:     1rem !default;\n$custom-file-padding-x:     .5rem !default;\n$custom-file-line-height:   1.5 !default;\n$custom-file-color:         $gray-700 !default;\n$custom-file-bg:            $white !default;\n$custom-file-border-width:  $border-width !default;\n$custom-file-border-color:  $input-border-color !default;\n$custom-file-border-radius: $border-radius !default;\n$custom-file-box-shadow:    inset 0 .2rem .4rem rgba($black,.05) !default;\n$custom-file-button-color:  $custom-file-color !default;\n$custom-file-button-bg:     $gray-200 !default;\n$custom-file-text: (\n  placeholder: (\n    en: \"Choose file...\"\n  ),\n  button-label: (\n    en: \"Browse\"\n  )\n) !default;\n\n\n// Form validation\n$form-feedback-valid-color:   theme-color(\"success\") !default;\n$form-feedback-invalid-color: theme-color(\"danger\") !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:             10rem !default;\n$dropdown-padding-y:             .5rem !default;\n$dropdown-spacer:                .125rem !default;\n$dropdown-bg:                    $white !default;\n$dropdown-border-color:          rgba($black,.15) !default;\n$dropdown-border-width:          $border-width !default;\n$dropdown-divider-bg:            $gray-200 !default;\n$dropdown-box-shadow:            0 .5rem 1rem rgba($black,.175) !default;\n\n$dropdown-link-color:            $gray-900 !default;\n$dropdown-link-hover-color:      darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:         $gray-100 !default;\n\n$dropdown-link-active-color:     $component-active-color !default;\n$dropdown-link-active-bg:        $component-active-bg !default;\n\n$dropdown-link-disabled-color:   $gray-600 !default;\n\n$dropdown-item-padding-y:        .25rem !default;\n$dropdown-item-padding-x:        1.5rem !default;\n\n$dropdown-header-color:          $gray-600 !default;\n\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:           1000 !default;\n$zindex-sticky:             1020 !default;\n$zindex-fixed:              1030 !default;\n$zindex-modal-backdrop:     1040 !default;\n$zindex-modal:              1050 !default;\n$zindex-popover:            1060 !default;\n$zindex-tooltip:            1070 !default;\n\n// Navs\n\n$nav-link-padding-y:            .5rem !default;\n$nav-link-padding-x:            1rem !default;\n$nav-link-disabled-color:       $gray-600 !default;\n\n$nav-tabs-border-color:                       #ddd !default;\n$nav-tabs-border-width:                       $border-width !default;\n$nav-tabs-border-radius:                      $border-radius !default;\n$nav-tabs-link-hover-border-color:            $gray-200 !default;\n$nav-tabs-link-active-color:                  $gray-700 !default;\n$nav-tabs-link-active-bg:                     $body-bg !default;\n$nav-tabs-link-active-border-color:           #ddd !default;\n\n$nav-pills-border-radius:     $border-radius !default;\n$nav-pills-link-active-color: $component-active-color !default;\n$nav-pills-link-active-bg:    $component-active-bg !default;\n\n// Navbar\n\n$navbar-padding-y:                  ($spacer / 2) !default;\n$navbar-padding-x:                  $spacer !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-height:               ($font-size-base * $line-height-base + $nav-link-padding-y * 2) !default;\n$navbar-brand-padding-y:            ($navbar-brand-height - $nav-link-height) / 2 !default;\n\n$navbar-toggler-padding-y:           .25rem !default;\n$navbar-toggler-padding-x:           .75rem !default;\n$navbar-toggler-font-size:           $font-size-lg !default;\n$navbar-toggler-border-radius:       $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white,.5) !default;\n$navbar-dark-hover-color:           rgba($white,.75) !default;\n$navbar-dark-active-color:          rgba($white,1) !default;\n$navbar-dark-disabled-color:        rgba($white,.25) !default;\n$navbar-dark-toggler-icon-bg: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-dark-toggler-border-color:  rgba($white,.1) !default;\n\n$navbar-light-color:                rgba($black,.5) !default;\n$navbar-light-hover-color:          rgba($black,.7) !default;\n$navbar-light-active-color:         rgba($black,.9) !default;\n$navbar-light-disabled-color:       rgba($black,.3) !default;\n$navbar-light-toggler-icon-bg: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-light-toggler-border-color: rgba($black,.1) !default;\n\n// Pagination\n\n$pagination-padding-y:                .5rem !default;\n$pagination-padding-x:                .75rem !default;\n$pagination-padding-y-sm:             .25rem !default;\n$pagination-padding-x-sm:             .5rem !default;\n$pagination-padding-y-lg:             .75rem !default;\n$pagination-padding-x-lg:             1.5rem !default;\n$pagination-line-height:              1.25 !default;\n\n$pagination-color:                     $link-color !default;\n$pagination-bg:                        $white !default;\n$pagination-border-width:              $border-width !default;\n$pagination-border-color:              #ddd !default;\n\n$pagination-hover-color:               $link-hover-color !default;\n$pagination-hover-bg:                  $gray-200 !default;\n$pagination-hover-border-color:        #ddd !default;\n\n$pagination-active-color:              $white !default;\n$pagination-active-bg:                 theme-color(\"primary\") !default;\n$pagination-active-border-color:       theme-color(\"primary\") !default;\n\n$pagination-disabled-color:            $gray-600 !default;\n$pagination-disabled-bg:               $white !default;\n$pagination-disabled-border-color:     #ddd !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:              2rem !default;\n$jumbotron-bg:                   $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:            .75rem !default;\n$card-spacer-x:            1.25rem !default;\n$card-border-width:        1px !default;\n$card-border-radius:       $border-radius !default;\n$card-border-color:        rgba($black,.125) !default;\n$card-inner-border-radius: calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg:              rgba($black, .03) !default;\n$card-bg:                  $white !default;\n\n$card-img-overlay-padding: 1.25rem !default;\n\n$card-deck-margin:          ($grid-gutter-width / 2) !default;\n\n$card-columns-count:        3 !default;\n$card-columns-gap:          1.25rem !default;\n$card-columns-margin:       $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-max-width:           200px !default;\n$tooltip-color:               $white !default;\n$tooltip-bg:                  $black !default;\n$tooltip-opacity:             .9 !default;\n$tooltip-padding-y:           3px !default;\n$tooltip-padding-x:           8px !default;\n$tooltip-margin:              0 !default;\n\n\n$tooltip-arrow-width:         5px !default;\n$tooltip-arrow-height:        5px !default;\n$tooltip-arrow-color:         $tooltip-bg !default;\n\n\n// Popovers\n\n$popover-inner-padding:               1px !default;\n$popover-bg:                          $white !default;\n$popover-max-width:                   276px !default;\n$popover-border-width:                $border-width !default;\n$popover-border-color:                rgba($black,.2) !default;\n$popover-box-shadow:                  0 5px 10px rgba($black,.2) !default;\n\n$popover-header-bg:                    darken($popover-bg, 3%) !default;\n$popover-header-color:                 $headings-color !default;\n$popover-header-padding-y:             8px !default;\n$popover-header-padding-x:             14px !default;\n\n$popover-body-color:               $body-color !default;\n$popover-body-padding-y:           9px !default;\n$popover-body-padding-x:           14px !default;\n\n$popover-arrow-width:                 10px !default;\n$popover-arrow-height:                5px !default;\n$popover-arrow-color:                 $popover-bg !default;\n\n$popover-arrow-outer-width:           ($popover-arrow-width + 1px) !default;\n$popover-arrow-outer-color:           fade-in($popover-border-color, .05) !default;\n\n\n// Badges\n\n$badge-color:                 $white !default;\n$badge-font-size:             75% !default;\n$badge-font-weight:           $font-weight-bold !default;\n$badge-padding-y:             .25em !default;\n$badge-padding-x:             .4em !default;\n\n$badge-pill-padding-x:        .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:    10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:         15px !default;\n\n$modal-dialog-margin:         10px !default;\n$modal-dialog-margin-y-sm-up: 30px !default;\n\n$modal-title-line-height:     $line-height-base !default;\n\n$modal-content-bg:               $white !default;\n$modal-content-border-color:     rgba($black,.2) !default;\n$modal-content-border-width:     $border-width !default;\n$modal-content-box-shadow-xs:    0 3px 9px rgba($black,.5) !default;\n$modal-content-box-shadow-sm-up: 0 5px 15px rgba($black,.5) !default;\n\n$modal-backdrop-bg:           $black !default;\n$modal-backdrop-opacity:      .5 !default;\n$modal-header-border-color:   $gray-200 !default;\n$modal-footer-border-color:   $modal-header-border-color !default;\n$modal-header-border-width:   $modal-content-border-width !default;\n$modal-footer-border-width:   $modal-header-border-width !default;\n$modal-header-padding:        15px !default;\n\n$modal-lg:                    800px !default;\n$modal-md:                    500px !default;\n$modal-sm:                    300px !default;\n\n$modal-transition:            transform .3s ease-out !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:             .75rem !default;\n$alert-padding-x:             1.25rem !default;\n$alert-margin-bottom:         1rem !default;\n$alert-border-radius:         $border-radius !default;\n$alert-link-font-weight:      $font-weight-bold !default;\n$alert-border-width:          $border-width !default;\n\n\n// Progress bars\n\n$progress-height:               1rem !default;\n$progress-font-size:            .75rem !default;\n$progress-bg:                   $gray-200 !default;\n$progress-border-radius:        $border-radius !default;\n$progress-box-shadow:           inset 0 .1rem .1rem rgba($black,.1) !default;\n$progress-bar-color:            $white !default;\n$progress-bar-bg:               theme-color(\"primary\") !default;\n$progress-bar-animation-timing: 1s linear infinite !default;\n$progress-bar-transition:       width .6s ease !default;\n\n// List group\n\n$list-group-bg:                  $white !default;\n$list-group-border-color:        rgba($black,.125) !default;\n$list-group-border-width:        $border-width !default;\n$list-group-border-radius:       $border-radius !default;\n\n$list-group-item-padding-y:      .75rem !default;\n$list-group-item-padding-x:      1.25rem !default;\n\n$list-group-hover-bg:                 $gray-100 !default;\n$list-group-active-color:             $component-active-color !default;\n$list-group-active-bg:                $component-active-bg !default;\n$list-group-active-border-color:      $list-group-active-bg !default;\n\n$list-group-disabled-color:      $gray-600 !default;\n$list-group-disabled-bg:         $list-group-bg !default;\n\n$list-group-action-color:             $gray-700 !default;\n$list-group-action-hover-color:       $list-group-action-color !default;\n\n$list-group-action-active-color:      $body-color !default;\n$list-group-action-active-bg:         $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:           .25rem !default;\n$thumbnail-bg:                $body-bg !default;\n$thumbnail-border-width:      $border-width !default;\n$thumbnail-border-color:      #ddd !default;\n$thumbnail-border-radius:     $border-radius !default;\n$thumbnail-box-shadow:        0 1px 2px rgba($black,.075) !default;\n$thumbnail-transition:        all .2s ease-in-out !default;\n\n\n// Figures\n\n$figure-caption-font-size: 90% !default;\n$figure-caption-color:     $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y:          .75rem !default;\n$breadcrumb-padding-x:          1rem !default;\n$breadcrumb-item-padding:       .5rem !default;\n\n$breadcrumb-bg:                 $gray-200 !default;\n$breadcrumb-divider-color:      $gray-600 !default;\n$breadcrumb-active-color:       $gray-600 !default;\n$breadcrumb-divider:            \"/\" !default;\n\n\n// Carousel\n\n$carousel-control-color:                      $white !default;\n$carousel-control-width:                      15% !default;\n$carousel-control-opacity:                    .5 !default;\n\n$carousel-indicator-width:                    30px !default;\n$carousel-indicator-height:                   3px !default;\n$carousel-indicator-spacer:                   3px !default;\n$carousel-indicator-active-bg:                $white !default;\n\n$carousel-caption-width:                      70% !default;\n$carousel-caption-color:                      $white !default;\n\n$carousel-control-icon-width:                 20px !default;\n\n$carousel-control-prev-icon-bg: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M4 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$carousel-control-next-icon-bg: str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M1.5 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$carousel-transition:           transform .6s ease !default;\n\n\n// Close\n\n$close-font-size:             $font-size-base * 1.5 !default;\n$close-font-weight:           $font-weight-bold !default;\n$close-color:                 $black !default;\n$close-text-shadow:           0 1px 0 $white !default;\n\n// Code\n\n$code-font-size:              90% !default;\n$code-padding-y:              .2rem !default;\n$code-padding-x:              .4rem !default;\n$code-color:                  #bd4147 !default;\n$code-bg:                     $gray-100 !default;\n\n$kbd-color:                   $white !default;\n$kbd-bg:                      $gray-900 !default;\n\n$pre-color:                   $gray-900 !default;\n$pre-scrollable-max-height:   340px !default;\n", "@mixin hover {\n  // TODO: re-enable along with mq4-hover-shim\n//  @if $enable-hover-media-query {\n//    // See Media Queries Level 4: https://drafts.csswg.org/mediaqueries/#hover\n//    // Currently shimmed by https://github.com/twbs/mq4-hover-shim\n//    @media (hover: hover) {\n//      &:hover { @content }\n//    }\n//  }\n//  @else {\n// scss-lint:disable Indentation\n    &:hover { @content }\n// scss-lint:enable Indentation\n//  }\n}\n\n\n@mixin hover-focus {\n  @if $enable-hover-media-query {\n    &:focus { @content }\n    @include hover { @content }\n  } @else {\n    &:focus,\n    &:hover {\n      @content\n    }\n  }\n}\n\n@mixin plain-hover-focus {\n  @if $enable-hover-media-query {\n    &,\n    &:focus {\n      @content\n    }\n    @include hover { @content }\n  } @else {\n    &,\n    &:focus,\n    &:hover {\n      @content\n    }\n  }\n}\n\n@mixin hover-focus-active {\n  @if $enable-hover-media-query {\n    &:focus,\n    &:active {\n      @content\n    }\n    @include hover { @content }\n  } @else {\n    &:focus,\n    &:active,\n    &:hover {\n      @content\n    }\n  }\n}\n", "//\n// Headings\n//\n\nh1, h2, h3, h4, h5, h6,\n.h1, .h2, .h3, .h4, .h5, .h6 {\n  margin-bottom: $headings-margin-bottom;\n  font-family: $headings-font-family;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: $headings-color;\n}\n\nh1, .h1 { font-size: $h1-font-size; }\nh2, .h2 { font-size: $h2-font-size; }\nh3, .h3 { font-size: $h3-font-size; }\nh4, .h4 { font-size: $h4-font-size; }\nh5, .h5 { font-size: $h5-font-size; }\nh6, .h6 { font-size: $h6-font-size; }\n\n.lead {\n  font-size: $lead-font-size;\n  font-weight: $lead-font-weight;\n}\n\n// Type display classes\n.display-1 {\n  font-size: $display1-size;\n  font-weight: $display1-weight;\n  line-height: $display-line-height;\n}\n.display-2 {\n  font-size: $display2-size;\n  font-weight: $display2-weight;\n  line-height: $display-line-height;\n}\n.display-3 {\n  font-size: $display3-size;\n  font-weight: $display3-weight;\n  line-height: $display-line-height;\n}\n.display-4 {\n  font-size: $display4-size;\n  font-weight: $display4-weight;\n  line-height: $display-line-height;\n}\n\n\n//\n// Horizontal rules\n//\n\nhr {\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n  border: 0;\n  border-top: $hr-border-width solid $hr-border-color;\n}\n\n\n//\n// Emphasis\n//\n\nsmall,\n.small {\n  font-size: $small-font-size;\n  font-weight: $font-weight-normal;\n}\n\nmark,\n.mark {\n  padding: $mark-padding;\n  background-color: $mark-bg;\n}\n\n\n//\n// Lists\n//\n\n.list-unstyled {\n  @include list-unstyled;\n}\n\n// Inline turns list items into inline-block\n.list-inline {\n  @include list-unstyled;\n}\n.list-inline-item {\n  display: inline-block;\n\n  &:not(:last-child) {\n    margin-right: $list-inline-padding;\n  }\n}\n\n\n//\n// Misc\n//\n\n// Builds on `abbr`\n.initialism {\n  font-size: 90%;\n  text-transform: uppercase;\n}\n\n// Blockquotes\n.blockquote {\n  margin-bottom: $spacer;\n  font-size: $blockquote-font-size;\n}\n\n.blockquote-footer {\n  display: block;\n  font-size: 80%; // back to default font-size\n  color: $blockquote-small-color;\n\n  &::before {\n    content: \"\\2014 \\00A0\"; // em dash, nbsp\n  }\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled {\n  padding-left: 0;\n  list-style: none;\n}\n", "// Responsive images (ensure images don't scale beyond their parents)\n//\n// This is purposefully opt-in via an explicit class rather than being the default for all `<img>`s.\n// We previously tried the \"images are responsive by default\" approach in Bootstrap v2,\n// and abandoned it in Bootstrap v3 because it breaks lots of third-party widgets (including Google Maps)\n// which weren't expecting the images within themselves to be involuntarily resized.\n// See also https://github.com/twbs/bootstrap/issues/18178\n.img-fluid {\n  @include img-fluid;\n}\n\n\n// Image thumbnails\n.img-thumbnail {\n  padding: $thumbnail-padding;\n  background-color: $thumbnail-bg;\n  border: $thumbnail-border-width solid $thumbnail-border-color;\n  @include border-radius($thumbnail-border-radius);\n  @include transition($thumbnail-transition);\n  @include box-shadow($thumbnail-box-shadow);\n\n  // Keep them at most 100% wide\n  @include img-fluid;\n}\n\n//\n// Figures\n//\n\n.figure {\n  // Ensures the caption's text aligns with the image.\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: ($spacer / 2);\n  line-height: 1;\n}\n\n.figure-caption {\n  font-size: $figure-caption-font-size;\n  color: $figure-caption-color;\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n\n\n// Retina image\n//\n// Short retina mixin for setting background-image and -size.\n\n@mixin img-retina($file-1x, $file-2x, $width-1x, $height-1x) {\n  background-image: url($file-1x);\n\n  // Autoprefixer takes care of adding -webkit-min-device-pixel-ratio and -o-min-device-pixel-ratio,\n  // but doesn't convert dppx=>dpi.\n  // There's no such thing as unprefixed min-device-pixel-ratio since it's nonstandard.\n  // Compatibility info: http://caniuse.com/#feat=css-media-resolution\n  @media\n  only screen and (min-resolution: 192dpi), // IE9-11 don't support dppx\n  only screen and (min-resolution: 2dppx) { // Standardized\n    background-image: url($file-2x);\n    background-size: $width-1x $height-1x;\n  }\n}\n", "// Single side border-radius\n\n@mixin border-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-radius: $radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n", "@mixin transition($transition...) {\n  @if $enable-transitions {\n    @if length($transition) == 0 {\n      transition: $transition-base;\n    } @else {\n      transition: $transition;\n    }\n  }\n}\n", "// Inline and block code styles\ncode,\nkbd,\npre,\nsamp {\n  font-family: $font-family-monospace;\n}\n\n// Inline code\ncode {\n  padding: $code-padding-y $code-padding-x;\n  font-size: $code-font-size;\n  color: $code-color;\n  background-color: $code-bg;\n  @include border-radius($border-radius);\n\n  // Streamline the style when inside anchors to avoid broken underline and more\n  a > & {\n    padding: 0;\n    color: inherit;\n    background-color: inherit;\n  }\n}\n\n// User input typically entered via keyboard\nkbd {\n  padding: $code-padding-y $code-padding-x;\n  font-size: $code-font-size;\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  @include border-radius($border-radius-sm);\n  @include box-shadow($kbd-box-shadow);\n\n  kbd {\n    padding: 0;\n    font-size: 100%;\n    font-weight: $nested-kbd-font-weight;\n    @include box-shadow(none);\n  }\n}\n\n// Blocks of code\npre {\n  display: block;\n  margin-top: 0;\n  margin-bottom: 1rem;\n  font-size: $code-font-size;\n  color: $pre-color;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    padding: 0;\n    font-size: inherit;\n    color: inherit;\n    background-color: transparent;\n    border-radius: 0;\n  }\n}\n\n// Enable scrollable blocks of code\n.pre-scrollable {\n  max-height: $pre-scrollable-max-height;\n  overflow-y: scroll;\n}\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-grid-classes {\n  .container {\n    @include make-container();\n    @include make-container-max-widths();\n  }\n}\n\n// Fluid container\n//\n// Utilizes the mixin meant for fixed width containers, but with 100% width for\n// fluid, full width layouts.\n\n@if $enable-grid-classes {\n  .container-fluid {\n    width: 100%;\n    @include make-container();\n  }\n}\n\n// Row\n//\n// Rows contain and clear the floats of your columns.\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n  }\n\n  // Remove the negative margin from default .row, then the horizontal padding\n  // from all immediate children columns (to prevent runaway style inheritance).\n  .no-gutters {\n    margin-right: 0;\n    margin-left: 0;\n\n    > .col,\n    > [class*=\"col-\"] {\n      padding-right: 0;\n      padding-left: 0;\n    }\n  }\n}\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "/// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-container() {\n  margin-right: auto;\n  margin-left: auto;\n  padding-right: ($grid-gutter-width / 2);\n  padding-left:  ($grid-gutter-width / 2);\n  width: 100%;\n}\n\n\n// For each breakpoint, define the maximum width of the container in a media query\n@mixin make-container-max-widths($max-widths: $container-max-widths, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint, $container-max-width in $max-widths {\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      max-width: $container-max-width;\n    }\n  }\n}\n\n@mixin make-row() {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: ($grid-gutter-width / -2);\n  margin-left:  ($grid-gutter-width / -2);\n}\n\n@mixin make-col-ready() {\n  position: relative;\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we use `flex` values\n  // later on to override this initial width.\n  width: 100%;\n  min-height: 1px; // Prevent collapsing\n  padding-right: ($grid-gutter-width / 2);\n  padding-left:  ($grid-gutter-width / 2);\n}\n\n@mixin make-col($size, $columns: $grid-columns) {\n  flex: 0 0 percentage($size / $columns);\n  // Add a `max-width` to ensure content within each column does not blow out\n  // the width of the column. Applies to IE10+ and Firefox. Chrome and Safari\n  // do not appear to require this.\n  max-width: percentage($size / $columns);\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.1.\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - 1px, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @media (min-width: $min) and (max-width: $max) {\n    @content;\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name)\n  } @else if $min == null {\n    @include media-breakpoint-down($name)\n  }\n}\n", "// Framework grid generation\n//\n// Used only by <PERSON><PERSON><PERSON> to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  // Common properties for all breakpoints\n  %grid-column {\n    position: relative;\n    width: 100%;\n    min-height: 1px; // Prevent columns from collapsing when empty\n    padding-right: ($gutter / 2);\n    padding-left:  ($gutter / 2);\n  }\n\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    // Allow columns to stretch full width below their breakpoints\n    @for $i from 1 through $columns {\n      .col#{$infix}-#{$i} {\n        @extend %grid-column;\n      }\n    }\n    .col#{$infix},\n    .col#{$infix}-auto {\n      @extend %grid-column;\n    }\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex-basis: 0;\n        flex-grow: 1;\n        max-width: 100%;\n      }\n      .col#{$infix}-auto {\n        flex: 0 0 auto;\n        width: auto;\n        max-width: none; // Reset earlier grid tiers\n      }\n\n      @for $i from 1 through $columns {\n        .col#{$infix}-#{$i} {\n          @include make-col($i, $columns);\n        }\n      }\n\n      @for $i from 1 through $columns {\n        .order#{$infix}-#{$i} {\n          order: $i;\n        }\n      }\n    }\n  }\n}\n", "//\n// Basic Bootstrap table\n//\n\n.table {\n  width: 100%;\n  max-width: 100%;\n  margin-bottom: $spacer;\n  background-color: $table-bg; // Reset for nesting within parents with `background-color`.\n\n  th,\n  td {\n    padding: $table-cell-padding;\n    vertical-align: top;\n    border-top: $table-border-width solid $table-border-color;\n  }\n\n  thead th {\n    vertical-align: bottom;\n    border-bottom: (2 * $table-border-width) solid $table-border-color;\n  }\n\n  tbody + tbody {\n    border-top: (2 * $table-border-width) solid $table-border-color;\n  }\n\n  .table {\n    background-color: $body-bg;\n  }\n}\n\n\n//\n// Condensed table w/ half padding\n//\n\n.table-sm {\n  th,\n  td {\n    padding: $table-cell-padding-sm;\n  }\n}\n\n\n// Bordered version\n//\n// Add borders all around the table and between all the columns.\n\n.table-bordered {\n  border: $table-border-width solid $table-border-color;\n\n  th,\n  td {\n    border: $table-border-width solid $table-border-color;\n  }\n\n  thead {\n    th,\n    td {\n      border-bottom-width: (2 * $table-border-width);\n    }\n  }\n}\n\n\n// Zebra-striping\n//\n// Default zebra-stripe styles (alternating gray and transparent backgrounds)\n\n.table-striped {\n  tbody tr:nth-of-type(odd) {\n    background-color: $table-accent-bg;\n  }\n}\n\n\n// Hover effect\n//\n// Placed here since it has to come after the potential zebra striping\n\n.table-hover {\n  tbody tr {\n    @include hover {\n      background-color: $table-hover-bg;\n    }\n  }\n}\n\n\n// Table backgrounds\n//\n// Exact selectors below required to override `.table-striped` and prevent\n// inheritance to nested tables.\n\n@each $color, $value in $theme-colors {\n  @include table-row-variant($color, theme-color-level($color, -9));\n}\n\n@include table-row-variant(active, $table-active-bg);\n\n\n// Inverse styles\n//\n// Same table markup, but inverted color scheme: dark background and light text.\n\n.thead-inverse {\n  th {\n    color: $table-inverse-color;\n    background-color: $table-inverse-bg;\n  }\n}\n\n.thead-default {\n  th {\n    color: $table-head-color;\n    background-color: $table-head-bg;\n  }\n}\n\n.table-inverse {\n  color: $table-inverse-color;\n  background-color: $table-inverse-bg;\n\n  th,\n  td,\n  thead th {\n    border-color: $table-inverse-border-color;\n  }\n\n  &.table-bordered {\n    border: 0;\n  }\n\n  &.table-striped {\n    tbody tr:nth-of-type(odd) {\n      background-color: $table-inverse-accent-bg;\n    }\n  }\n\n  &.table-hover {\n    tbody tr {\n      @include hover {\n        background-color: $table-inverse-hover-bg;\n      }\n    }\n  }\n}\n\n\n// Responsive tables\n//\n// Add `.table-responsive` to `.table`s and we'll make them mobile friendly by\n// enabling horizontal scrolling. Only applies <768px. Everything above that\n// will display normally.\n\n.table-responsive {\n  @include media-breakpoint-down(md) {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -ms-overflow-style: -ms-autohiding-scrollbar; // See https://github.com/twbs/bootstrap/pull/10057\n\n    // Prevent double border on horizontal scroll due to use of `display: block;`\n    &.table-bordered {\n      border: 0;\n    }\n  }\n}\n", "// Tables\n\n@mixin table-row-variant($state, $background) {\n  // Exact selectors below required to override `.table-striped` and prevent\n  // inheritance to nested tables.\n  .table-#{$state} {\n    &,\n    > th,\n    > td {\n      background-color: $background;\n    }\n  }\n\n  // Hover states for `.table-hover`\n  // Note: this is not available for cells or rows within `thead` or `tfoot`.\n  .table-hover {\n    $hover-background: darken($background, 5%);\n\n    .table-#{$state} {\n      @include hover {\n        background-color: $hover-background;\n\n        > td,\n        > th {\n          background-color: $hover-background;\n        }\n      }\n    }\n  }\n}\n", "// Bootstrap functions\n//\n// Utility mixins and functions for evalutating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Another grid mixin that ensures the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map) {\n  $values: map-values($map);\n  $first-value: nth($values, 1);\n  @if $first-value != 0 {\n    @warn \"First breakpoint in `$grid-breakpoints` must start at 0, but starts at #{$first-value}.\";\n  }\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// Color contrast\n@mixin color-yiq($color) {\n  $r: red($color);\n  $g: green($color);\n  $b: blue($color);\n\n  $yiq: (($r * 299) + ($g * 587) + ($b * 114)) / 1000;\n\n  @if ($yiq >= 150) {\n    color: #111;\n  } @else {\n    color: #fff;\n  }\n}\n\n// Retreive color Sass maps\n@function color($key: \"blue\") {\n  @return map-get($colors, $key);\n}\n\n@function theme-color($key: \"primary\") {\n  @return map-get($theme-colors, $key);\n}\n\n@function grayscale($key: \"100\") {\n  @return map-get($grays, $key);\n}\n\n// Request a theme color level\n@function theme-color-level($color-name: \"primary\", $level: 0) {\n  $color: theme-color($color-name);\n  $color-base: if($level > 0, #000, #fff);\n\n  @if $level < 0 {\n    // Lighter values need a quick double negative for the Sass math to work\n    @return mix($color-base, $color, $level * -1 * $theme-color-interval);\n  } @else {\n    @return mix($color-base, $color, $level * $theme-color-interval);\n  }\n}\n", "// scss-lint:disable QualifyingElement, VendorPrefix\n\n//\n// Textual form controls\n//\n\n.form-control {\n  display: block;\n  width: 100%;\n  // // Make inputs at least the height of their button counterpart (base line-height + padding + border)\n  // height: $input-height;\n  padding: $input-btn-padding-y $input-btn-padding-x;\n  font-size: $font-size-base;\n  line-height: $input-btn-line-height;\n  color: $input-color;\n  background-color: $input-bg;\n  // Reset unusual Firefox-on-Android default style; see https://github.com/necolas/normalize.css/issues/214.\n  background-image: none;\n  background-clip: padding-box;\n  border: $input-btn-border-width solid $input-border-color;\n\n  // Note: This has no effect on <select>s in some browsers, due to the limited stylability of `<select>`s in CSS.\n  @if $enable-rounded {\n    // Manually use the if/else instead of the mixin to account for iOS override\n    border-radius: $input-border-radius;\n  } @else {\n    // Otherwise undo the iOS default\n    border-radius: 0;\n  }\n\n  @include box-shadow($input-box-shadow);\n  @include transition($input-transition);\n\n  // Unstyle the caret on `<select>`s in IE10+.\n  &::-ms-expand {\n    background-color: transparent;\n    border: 0;\n  }\n\n  // Customize the `:focus` state to imitate native WebKit styles.\n  @include form-control-focus();\n\n  // Placeholder\n  &::placeholder {\n    color: $input-placeholder-color;\n    // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526.\n    opacity: 1;\n  }\n\n  // Disabled and read-only inputs\n  //\n  // HTML5 says that controls under a fieldset > legend:first-child won't be\n  // disabled if the fieldset is disabled. Due to implementation difficulty, we\n  // don't honor that edge case; we style them as disabled anyway.\n  &:disabled,\n  &[readonly] {\n    background-color: $input-disabled-bg;\n    // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655.\n    opacity: 1;\n  }\n}\n\nselect.form-control {\n  &:not([size]):not([multiple]) {\n    height: $input-height;\n  }\n\n  &:focus::-ms-value {\n    // Suppress the nested default white text on blue background highlight given to\n    // the selected option text when the (still closed) <select> receives focus\n    // in IE and (under certain conditions) Edge, as it looks bad and cannot be made to\n    // match the appearance of the native widget.\n    // See https://github.com/twbs/bootstrap/issues/19398.\n    color: $input-color;\n    background-color: $input-bg;\n  }\n}\n\n// Make file inputs better match text inputs by forcing them to new lines.\n.form-control-file,\n.form-control-range {\n  display: block;\n}\n\n\n//\n// Labels\n//\n\n// For use with horizontal and inline forms, when you need the label text to\n// align with the form controls.\n.col-form-label {\n  padding-top: calc(#{$input-btn-padding-y} - #{$input-btn-border-width} * 2);\n  padding-bottom: calc(#{$input-btn-padding-y} - #{$input-btn-border-width} * 2);\n  margin-bottom: 0; // Override the `<label>` default\n}\n\n.col-form-label-lg {\n  padding-top: calc(#{$input-btn-padding-y-lg} - #{$input-btn-border-width} * 2);\n  padding-bottom: calc(#{$input-btn-padding-y-lg} - #{$input-btn-border-width} * 2);\n  font-size: $font-size-lg;\n}\n\n.col-form-label-sm {\n  padding-top: calc(#{$input-btn-padding-y-sm} - #{$input-btn-border-width} * 2);\n  padding-bottom: calc(#{$input-btn-padding-y-sm} - #{$input-btn-border-width} * 2);\n  font-size: $font-size-sm;\n}\n\n\n//\n// Legends\n//\n\n// For use with horizontal and inline forms, when you need the legend text to\n// be the same size as regular labels, and to align with the form controls.\n.col-form-legend {\n  padding-top: $input-btn-padding-y;\n  padding-bottom: $input-btn-padding-y;\n  margin-bottom: 0;\n  font-size: $font-size-base;\n}\n\n\n// Readonly controls as plain text\n//\n// Apply class to a readonly input to make it appear like regular plain\n// text (without any border, background color, focus indicator)\n\n.form-control-plaintext {\n  padding-top: $input-btn-padding-y;\n  padding-bottom: $input-btn-padding-y;\n  margin-bottom: 0; // match inputs if this class comes on inputs with default margins\n  line-height: $input-btn-line-height;\n  border: solid transparent;\n  border-width: $input-btn-border-width 0;\n\n  &.form-control-sm,\n  &.form-control-lg {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n\n// Form control sizing\n//\n// Build on `.form-control` with modifier classes to decrease or increase the\n// height and font-size of form controls.\n//\n// The `.form-group-* form-control` variations are sadly duplicated to avoid the\n// issue documented in https://github.com/twbs/bootstrap/issues/15074.\n\n.form-control-sm {\n  padding: $input-btn-padding-y-sm $input-btn-padding-x-sm;\n  font-size: $font-size-sm;\n  line-height: $input-btn-line-height-sm;\n  @include border-radius($input-border-radius-sm);\n}\n\nselect.form-control-sm {\n  &:not([size]):not([multiple]) {\n    height: $input-height-sm;\n  }\n}\n\n.form-control-lg {\n  padding: $input-btn-padding-y-lg $input-btn-padding-x-lg;\n  font-size: $font-size-lg;\n  line-height: $input-btn-line-height-lg;\n  @include border-radius($input-border-radius-lg);\n}\n\nselect.form-control-lg {\n  &:not([size]):not([multiple]) {\n    height: $input-height-lg;\n  }\n}\n\n\n// Form groups\n//\n// Designed to help with the organization and spacing of vertical forms. For\n// horizontal forms, use the predefined grid classes.\n\n.form-group {\n  margin-bottom: $form-group-margin-bottom;\n}\n\n.form-text {\n  display: block;\n  margin-top: $form-text-margin-top;\n}\n\n\n// Form grid\n//\n// Special replacement for our grid system's `.row` for tighter form layouts.\n\n.form-row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -5px;\n  margin-left: -5px;\n\n  > .col,\n  > [class*=\"col-\"] {\n    padding-right: 5px;\n    padding-left: 5px;\n  }\n}\n\n\n// Checkboxes and radios\n//\n// Indent the labels to position radios/checkboxes as hanging controls.\n\n.form-check {\n  position: relative;\n  display: block;\n  margin-bottom: $form-check-margin-bottom;\n\n  &.disabled {\n    .form-check-label {\n      color: $text-muted;\n    }\n  }\n}\n\n.form-check-label {\n  padding-left: $form-check-input-gutter;\n  margin-bottom: 0; // Override default `<label>` bottom margin\n}\n\n.form-check-input {\n  position: absolute;\n  margin-top: $form-check-input-margin-y;\n  margin-left: -$form-check-input-gutter;\n\n  &:only-child {\n    position: static;\n  }\n}\n\n// Radios and checkboxes on same line\n.form-check-inline {\n  display: inline-block;\n\n  .form-check-label {\n    vertical-align: middle;\n  }\n\n  + .form-check-inline {\n    margin-left: $form-check-inline-margin-x;\n  }\n}\n\n\n// Form validation\n//\n// Provide feedback to users when form field values are valid or invalid. Works\n// primarily for client-side validation via scoped `:invalid` and `:valid`\n// pseudo-classes but also includes `.is-invalid` and `.is-valid` classes for\n// server side validation.\n\n.invalid-feedback {\n  display: none;\n  margin-top: .25rem;\n  font-size: .875rem;\n  color: $form-feedback-invalid-color;\n}\n\n.invalid-tooltip {\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n  display: none;\n  width: 250px;\n  padding: .5rem;\n  margin-top: .1rem;\n  font-size: .875rem;\n  line-height: 1;\n  color: #fff;\n  background-color: rgba($form-feedback-invalid-color,.8);\n  border-radius: .2rem;\n}\n\n@include form-validation-state(\"valid\", $form-feedback-valid-color);\n@include form-validation-state(\"invalid\", $form-feedback-invalid-color);\n\n// Inline forms\n//\n// Make forms appear inline(-block) by adding the `.form-inline` class. Inline\n// forms begin stacked on extra small (mobile) devices and then go inline when\n// viewports reach <768px.\n//\n// Requires wrapping inputs and labels with `.form-group` for proper display of\n// default HTML form controls and our custom form controls (e.g., input groups).\n\n.form-inline {\n  display: flex;\n  flex-flow: row wrap;\n  align-items: center; // Prevent shorter elements from growing to same height as others (e.g., small buttons growing to normal sized button height)\n\n  // Because we use flex, the initial sizing of checkboxes is collapsed and\n  // doesn't occupy the full-width (which is what we want for xs grid tier),\n  // so we force that here.\n  .form-check {\n    width: 100%;\n  }\n\n  // Kick in the inline\n  @include media-breakpoint-up(sm) {\n    label {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 0;\n    }\n\n    // Inline-block all the things for \"inline\"\n    .form-group {\n      display: flex;\n      flex: 0 0 auto;\n      flex-flow: row wrap;\n      align-items: center;\n      margin-bottom: 0;\n    }\n\n    // Allow folks to *not* use `.form-group`\n    .form-control {\n      display: inline-block;\n      width: auto; // Prevent labels from stacking above inputs in `.form-group`\n      vertical-align: middle;\n    }\n\n    // Make static controls behave like regular ones\n    .form-control-plaintext {\n      display: inline-block;\n    }\n\n    .input-group {\n      width: auto;\n    }\n\n    .form-control-label {\n      margin-bottom: 0;\n      vertical-align: middle;\n    }\n\n    // Remove default margin on radios/checkboxes that were used for stacking, and\n    // then undo the floating of radios and checkboxes to match.\n    .form-check {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: auto;\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n    .form-check-label {\n      padding-left: 0;\n    }\n    .form-check-input {\n      position: relative;\n      margin-top: 0;\n      margin-right: $form-check-input-margin-x;\n      margin-left: 0;\n    }\n\n    // Custom form controls\n    .custom-control {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding-left: 0;\n    }\n    .custom-control-indicator {\n      position: static;\n      display: inline-block;\n      margin-right: $form-check-input-margin-x; // Flexbox alignment means we lose our HTML space here, so we compensate.\n      vertical-align: text-bottom;\n    }\n\n    // Re-override the feedback icon.\n    .has-feedback .form-control-feedback {\n      top: 0;\n    }\n  }\n}\n", "// Form control focus state\n//\n// Generate a customized focus state and for any input with the specified color,\n// which defaults to the `@input-border-color-focus` variable.\n//\n// We highly encourage you to not customize the default value, but instead use\n// this to tweak colors on an as-needed basis. This aesthetic change is based on\n// WebKit's default styles, but applicable to a wider range of browsers. Its\n// usability and accessibility should be taken into account with any change.\n//\n// Example usage: change the default blue border and shadow to white for better\n// contrast against a dark gray background.\n@mixin form-control-focus() {\n  &:focus {\n    color: $input-focus-color;\n    background-color: $input-focus-bg;\n    border-color: $input-focus-border-color;\n    outline: none;\n    @include box-shadow($input-focus-box-shadow);\n  }\n}\n\n\n@mixin form-validation-state($state, $color) {\n\n  .form-control,\n  .custom-select {\n    .was-validated &:#{$state},\n    &.is-#{$state} {\n      border-color: $color;\n\n      &:focus {\n        box-shadow: 0 0 0 .2rem rgba($color,.25);\n      }\n\n      ~ .invalid-feedback,\n      ~ .invalid-tooltip {\n        display: block;\n      }\n    }\n  }\n\n\n  // TODO: redo check markup lol crap\n  .form-check-input {\n    .was-validated &:#{$state},\n    &.is-#{$state} {\n      + .form-check-label {\n        color: $color;\n      }\n    }\n  }\n\n  // custom radios and checks\n  .custom-control-input {\n    .was-validated &:#{$state},\n    &.is-#{$state} {\n      ~ .custom-control-indicator {\n        background-color: rgba($color, .25);\n      }\n      ~ .custom-control-description {\n        color: $color;\n      }\n    }\n  }\n\n  // custom file\n  .custom-file-input {\n    .was-validated &:#{$state},\n    &.is-#{$state} {\n      ~ .custom-file-control {\n        border-color: $color;\n\n        &::before { border-color: inherit; }\n      }\n      &:focus {\n        box-shadow: 0 0 0 .2rem rgba($color,.25);\n      }\n    }\n  }\n}\n", "// scss-lint:disable QualifyingElement\n\n//\n// Base styles\n//\n\n.btn {\n  display: inline-block;\n  font-weight: $btn-font-weight;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: middle;\n  user-select: none;\n  border: $input-btn-border-width solid transparent;\n  @include button-size($input-btn-padding-y, $input-btn-padding-x, $font-size-base, $input-btn-line-height, $btn-border-radius);\n  @include transition($btn-transition);\n\n  // Share hover and focus styles\n  @include hover-focus {\n    text-decoration: none;\n  }\n  &:focus,\n  &.focus {\n    outline: 0;\n    box-shadow: $btn-focus-box-shadow;\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    opacity: .65;\n    @include box-shadow(none);\n  }\n\n  &:active,\n  &.active {\n    background-image: none;\n    @include box-shadow($btn-focus-box-shadow, $btn-active-box-shadow);\n  }\n}\n\n// Future-proof disabling of clicks on `<a>` elements\na.btn.disabled,\nfieldset[disabled] a.btn {\n  pointer-events: none;\n}\n\n\n//\n// Alternate buttons\n//\n\n@each $color, $value in $theme-colors {\n  .btn-#{$color} {\n    @include button-variant($value, $value);\n  }\n}\n\n@each $color, $value in $theme-colors {\n  .btn-outline-#{$color} {\n    @include button-outline-variant($value, #fff);\n  }\n}\n\n\n//\n// Link buttons\n//\n\n// Make a button look and behave like a link\n.btn-link {\n  font-weight: $font-weight-normal;\n  color: $link-color;\n  border-radius: 0;\n\n  &,\n  &:active,\n  &.active,\n  &:disabled {\n    background-color: transparent;\n    @include box-shadow(none);\n  }\n  &,\n  &:focus,\n  &:active {\n    border-color: transparent;\n    box-shadow: none;\n  }\n  @include hover {\n    border-color: transparent;\n  }\n  @include hover-focus {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n    background-color: transparent;\n  }\n  &:disabled {\n    color: $btn-link-disabled-color;\n\n    @include hover-focus {\n      text-decoration: none;\n    }\n  }\n}\n\n\n//\n// Button Sizes\n//\n\n.btn-lg {\n  @include button-size($input-btn-padding-y-lg, $input-btn-padding-x-lg, $font-size-lg, $line-height-lg, $btn-border-radius-lg);\n}\n\n.btn-sm {\n  @include button-size($input-btn-padding-y-sm, $input-btn-padding-x-sm, $font-size-sm, $line-height-sm, $btn-border-radius-sm);\n}\n\n\n//\n// Block button\n//\n\n.btn-block {\n  display: block;\n  width: 100%;\n}\n\n// Vertically space out multiple block buttons\n.btn-block + .btn-block {\n  margin-top: $btn-block-spacing-y;\n}\n\n// Specificity overrides\ninput[type=\"submit\"],\ninput[type=\"reset\"],\ninput[type=\"button\"] {\n  &.btn-block {\n    width: 100%;\n  }\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n@mixin button-variant($background, $border, $active-background: darken($background, 7.5%), $active-border: darken($border, 10%)) {\n  @include color-yiq($background);\n  background-color: $background;\n  border-color: $border;\n  @include box-shadow($btn-box-shadow);\n\n  &:hover {\n    @include color-yiq($background);\n    background-color: $active-background;\n    border-color: $active-border;\n  }\n\n  &:focus,\n  &.focus {\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: $btn-box-shadow, 0 0 0 3px rgba($border, .5);\n    } @else {\n      box-shadow: 0 0 0 3px rgba($border, .5);\n    }\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    background-color: $background;\n    border-color: $border;\n  }\n\n  &:active,\n  &.active,\n  .show > &.dropdown-toggle {\n    background-color: $active-background;\n    background-image: none; // Remove the gradient for the pressed/active state\n    border-color: $active-border;\n    @include box-shadow($btn-active-box-shadow);\n  }\n}\n\n@mixin button-outline-variant($color, $color-hover: #fff) {\n  color: $color;\n  background-color: transparent;\n  background-image: none;\n  border-color: $color;\n\n  @include hover {\n    color: $color-hover;\n    background-color: $color;\n    border-color: $color;\n  }\n\n  &:focus,\n  &.focus {\n    box-shadow: 0 0 0 3px rgba($color, .5);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $color;\n    background-color: transparent;\n  }\n\n  &:active,\n  &.active,\n  .show > &.dropdown-toggle {\n    color: $color-hover;\n    background-color: $color;\n    border-color: $color;\n  }\n}\n\n// Button sizes\n@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  padding: $padding-y $padding-x;\n  font-size: $font-size;\n  line-height: $line-height;\n  @include border-radius($border-radius);\n}\n", ".fade {\n  opacity: 0;\n  @include transition($transition-fade);\n\n  &.show {\n    opacity: 1;\n  }\n}\n\n.collapse {\n  display: none;\n  &.show {\n    display: block;\n  }\n}\n\ntr {\n  &.collapse.show {\n    display: table-row;\n  }\n}\n\ntbody {\n  &.collapse.show {\n    display: table-row-group;\n  }\n}\n\n.collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  @include transition($transition-collapse);\n}\n", "// The dropdown wrapper (`<div>`)\n.dropup,\n.dropdown {\n  position: relative;\n}\n\n.dropdown-toggle {\n  // Generate the caret automatically\n  &::after {\n    display: inline-block;\n    width: 0;\n    height: 0;\n    margin-left: $caret-width * .85;\n    vertical-align: $caret-width * .85;\n    content: \"\";\n    border-top: $caret-width solid;\n    border-right: $caret-width solid transparent;\n    border-left: $caret-width solid transparent;\n  }\n\n  &:empty::after {\n    margin-left: 0;\n  }\n}\n\n// Allow for dropdowns to go bottom up (aka, dropup-menu)\n// Just add .dropup after the standard .dropdown class and you're set.\n.dropup {\n  .dropdown-menu {\n    margin-top: 0;\n    margin-bottom: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    &::after {\n      border-top: 0;\n      border-bottom: $caret-width solid;\n    }\n  }\n}\n\n// The dropdown menu\n.dropdown-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: $zindex-dropdown;\n  display: none; // none by default, but block on \"open\" of the menu\n  float: left;\n  min-width: $dropdown-min-width;\n  padding: $dropdown-padding-y 0;\n  margin: $dropdown-spacer 0 0; // override default ul\n  font-size: $font-size-base; // Redeclare because nesting can cause inheritance issues\n  color: $body-color;\n  text-align: left; // Ensures proper alignment if parent has it changed (e.g., modal footer)\n  list-style: none;\n  background-color: $dropdown-bg;\n  background-clip: padding-box;\n  border: $dropdown-border-width solid $dropdown-border-color;\n  @include border-radius($border-radius);\n  @include box-shadow($dropdown-box-shadow);\n}\n\n// Dividers (basically an `<hr>`) within the dropdown\n.dropdown-divider {\n  @include nav-divider($dropdown-divider-bg);\n}\n\n// Links, buttons, and more within the dropdown menu\n//\n// `<button>`-specific styles are denoted with `// For <button>s`\n.dropdown-item {\n  display: block;\n  width: 100%; // For `<button>`s\n  padding: $dropdown-item-padding-y $dropdown-item-padding-x;\n  clear: both;\n  font-weight: $font-weight-normal;\n  color: $dropdown-link-color;\n  text-align: inherit; // For `<button>`s\n  white-space: nowrap; // prevent links from randomly breaking onto new lines\n  background: none; // For `<button>`s\n  border: 0; // For `<button>`s\n\n  @include hover-focus {\n    color: $dropdown-link-hover-color;\n    text-decoration: none;\n    background-color: $dropdown-link-hover-bg;\n  }\n\n  &.active,\n  &:active {\n    color: $dropdown-link-active-color;\n    text-decoration: none;\n    background-color: $dropdown-link-active-bg;\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $dropdown-link-disabled-color;\n    background-color: transparent;\n    // Remove CSS gradients if they're enabled\n    @if $enable-gradients {\n      background-image: none;\n    }\n  }\n}\n\n// Open state for the dropdown\n.show {\n  // Remove the outline when :focus is triggered\n  > a {\n    outline: 0;\n  }\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n// Dropdown section headers\n.dropdown-header {\n  display: block;\n  padding: $dropdown-padding-y $dropdown-item-padding-x;\n  margin-bottom: 0; // for use with heading elements\n  font-size: $font-size-sm;\n  color: $dropdown-header-color;\n  white-space: nowrap; // as with > li > a\n}\n", "// Horizontal dividers\n//\n// Dividers (basically an hr) within dropdowns and nav lists\n\n@mixin nav-divider($color: #e5e5e5) {\n  height: 0;\n  margin: ($spacer / 2) 0;\n  overflow: hidden;\n  border-top: 1px solid $color;\n}\n", "// scss-lint:disable QualifyingElement\n\n// Make the div behave like a button\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle; // match .btn alignment given font-size hack above\n\n  > .btn {\n    position: relative;\n    flex: 0 1 auto;\n    margin-bottom: 0;\n\n    // Bring the hover, focused, and \"active\" buttons to the front to overlay\n    // the borders properly\n    @include hover {\n      z-index: 2;\n    }\n    &:focus,\n    &:active,\n    &.active {\n      z-index: 2;\n    }\n  }\n\n  // Prevent double borders when buttons are next to each other\n  .btn + .btn,\n  .btn + .btn-group,\n  .btn-group + .btn,\n  .btn-group + .btn-group {\n    margin-left: -$input-btn-border-width;\n  }\n}\n\n// Optional: Group multiple button groups together for a toolbar\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n\n  .input-group {\n    width: auto;\n  }\n}\n\n.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {\n  border-radius: 0;\n}\n\n// Set corners individual because sometimes a single button can be in a .btn-group and we need :first-child and :last-child to both match\n.btn-group > .btn:first-child {\n  margin-left: 0;\n\n  &:not(:last-child):not(.dropdown-toggle) {\n    @include border-right-radius(0);\n  }\n}\n// Need .dropdown-toggle since :last-child doesn't apply given a .dropdown-menu immediately after it\n.btn-group > .btn:last-child:not(:first-child),\n.btn-group > .dropdown-toggle:not(:first-child) {\n  @include border-left-radius(0);\n}\n\n// Custom edits for including btn-groups within btn-groups (useful for including dropdown buttons within a btn-group)\n.btn-group > .btn-group {\n  float: left;\n}\n.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {\n  border-radius: 0;\n}\n.btn-group > .btn-group:first-child:not(:last-child) {\n  > .btn:last-child,\n  > .dropdown-toggle {\n    @include border-right-radius(0);\n  }\n}\n.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {\n  @include border-left-radius(0);\n}\n\n\n// Sizing\n//\n// Remix the default button sizing classes into new ones for easier manipulation.\n\n.btn-group-sm > .btn { @extend .btn-sm; }\n.btn-group-lg > .btn { @extend .btn-lg; }\n\n\n//\n// Split button dropdowns\n//\n\n.btn + .dropdown-toggle-split {\n  padding-right: $input-btn-padding-x * .75;\n  padding-left: $input-btn-padding-x * .75;\n\n  &::after {\n    margin-left: 0;\n  }\n}\n\n.btn-sm + .dropdown-toggle-split {\n  padding-right: $input-btn-padding-x-sm * .75;\n  padding-left: $input-btn-padding-x-sm * .75;\n}\n\n.btn-lg + .dropdown-toggle-split {\n  padding-right: $input-btn-padding-x-lg * .75;\n  padding-left: $input-btn-padding-x-lg * .75;\n}\n\n\n// The clickable button for toggling the menu\n// Set the same inset shadow as the :active state\n.btn-group.show .dropdown-toggle {\n  @include box-shadow($btn-active-box-shadow);\n\n  // Show no shadow for `.btn-link` since it has no other button styles.\n  &.btn-link {\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Vertical button groups\n//\n\n.btn-group-vertical {\n  display: inline-flex;\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n\n  .btn,\n  .btn-group {\n    width: 100%;\n  }\n\n  > .btn + .btn,\n  > .btn + .btn-group,\n  > .btn-group + .btn,\n  > .btn-group + .btn-group {\n    margin-top: -$input-btn-border-width;\n    margin-left: 0;\n  }\n}\n\n.btn-group-vertical > .btn {\n  &:not(:first-child):not(:last-child) {\n    border-radius: 0;\n  }\n  &:first-child:not(:last-child) {\n    @include border-bottom-radius(0);\n  }\n  &:last-child:not(:first-child) {\n    @include border-top-radius(0);\n  }\n}\n.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {\n  border-radius: 0;\n}\n.btn-group-vertical > .btn-group:first-child:not(:last-child) {\n  > .btn:last-child,\n  > .dropdown-toggle {\n    @include border-bottom-radius(0);\n  }\n}\n.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {\n  @include border-top-radius(0);\n}\n\n\n// Checkbox and radio options\n//\n// In order to support the browser's form validation feedback, powered by the\n// `required` attribute, we have to \"hide\" the inputs via `clip`. We cannot use\n// `display: none;` or `visibility: hidden;` as that also hides the popover.\n// Simply visually hiding the inputs via `opacity` would leave them clickable in\n// certain cases which is prevented by using `clip` and `pointer-events`.\n// This way, we ensure a DOM element is visible to position the popover from.\n//\n// See https://github.com/twbs/bootstrap/pull/12794 and\n// https://github.com/twbs/bootstrap/pull/14559 for more information.\n\n[data-toggle=\"buttons\"] {\n  > .btn,\n  > .btn-group > .btn {\n    input[type=\"radio\"],\n    input[type=\"checkbox\"] {\n      position: absolute;\n      clip: rect(0,0,0,0);\n      pointer-events: none;\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.input-group {\n  position: relative;\n  display: flex;\n  width: 100%;\n\n  .form-control {\n    // Ensure that the input is always above the *appended* addon button for\n    // proper border colors.\n    position: relative;\n    z-index: 2;\n    flex: 1 1 auto;\n    // Add width 1% and flex-basis auto to ensure that button will not wrap out\n    // the column. Applies to IE Edge+ and Firefox. Chrome does not require this.\n    width: 1%;\n    margin-bottom: 0;\n\n    // Bring the \"active\" form control to the front\n    @include hover-focus-active {\n      z-index: 3;\n    }\n  }\n}\n\n.input-group-addon,\n.input-group-btn,\n.input-group .form-control {\n  // Vertically centers the content of the addons within the input group\n  display: flex;\n  align-items: center;\n\n  &:not(:first-child):not(:last-child) {\n    @include border-radius(0);\n  }\n}\n\n.input-group-addon,\n.input-group-btn {\n  white-space: nowrap;\n  vertical-align: middle; // Match the inputs\n}\n\n\n// Sizing options\n//\n// Remix the default form control sizing classes into new ones for easier\n// manipulation.\n\n.input-group-lg > .form-control,\n.input-group-lg > .input-group-addon,\n.input-group-lg > .input-group-btn > .btn {\n  @extend .form-control-lg;\n}\n.input-group-sm > .form-control,\n.input-group-sm > .input-group-addon,\n.input-group-sm > .input-group-btn > .btn {\n  @extend .form-control-sm;\n}\n\n\n//\n// Text input groups\n//\n\n.input-group-addon {\n  padding: $input-btn-padding-y $input-btn-padding-x;\n  margin-bottom: 0; // Allow use of <label> elements by overriding our default margin-bottom\n  font-size: $font-size-base; // Match inputs\n  font-weight: $font-weight-normal;\n  line-height: $input-btn-line-height;\n  color: $input-color;\n  text-align: center;\n  background-color: $input-group-addon-bg;\n  border: $input-btn-border-width solid $input-group-addon-border-color;\n  @include border-radius($input-border-radius);\n\n  // Sizing\n  &.form-control-sm {\n    padding: $input-btn-padding-y-sm $input-btn-padding-x-sm;\n    font-size: $font-size-sm;\n    @include border-radius($input-border-radius-sm);\n  }\n\n  &.form-control-lg {\n    padding: $input-btn-padding-y-lg $input-btn-padding-x-lg;\n    font-size: $font-size-lg;\n    @include border-radius($input-border-radius-lg);\n  }\n\n  // scss-lint:disable QualifyingElement\n  // Nuke default margins from checkboxes and radios to vertically center within.\n  input[type=\"radio\"],\n  input[type=\"checkbox\"] {\n    margin-top: 0;\n  }\n  // scss-lint:enable QualifyingElement\n}\n\n\n//\n// Reset rounded corners\n//\n\n.input-group .form-control:not(:last-child),\n.input-group-addon:not(:last-child),\n.input-group-btn:not(:last-child) > .btn,\n.input-group-btn:not(:last-child) > .btn-group > .btn,\n.input-group-btn:not(:last-child) > .dropdown-toggle,\n.input-group-btn:not(:first-child) > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group-btn:not(:first-child) > .btn-group:not(:last-child) > .btn {\n  @include border-right-radius(0);\n}\n.input-group-addon:not(:last-child) {\n  border-right: 0;\n}\n.input-group .form-control:not(:first-child),\n.input-group-addon:not(:first-child),\n.input-group-btn:not(:first-child) > .btn,\n.input-group-btn:not(:first-child) > .btn-group > .btn,\n.input-group-btn:not(:first-child) > .dropdown-toggle,\n.input-group-btn:not(:last-child) > .btn:not(:first-child),\n.input-group-btn:not(:last-child) > .btn-group:not(:first-child) > .btn {\n  @include border-left-radius(0);\n}\n.form-control + .input-group-addon:not(:first-child) {\n  border-left: 0;\n}\n\n//\n// Button input groups\n//\n\n.input-group-btn {\n  position: relative;\n  // Jankily prevent input button groups from wrapping with `white-space` and\n  // `font-size` in combination with `inline-block` on buttons.\n  font-size: 0;\n  white-space: nowrap;\n\n  // Negative margin for spacing, position for bringing hovered/focused/actived\n  // element above the siblings.\n  > .btn {\n    position: relative;\n\n    + .btn {\n      margin-left: (-$input-btn-border-width);\n    }\n\n    // Bring the \"active\" button to the front\n    @include hover-focus-active {\n      z-index: 3;\n    }\n  }\n\n  // Negative margin to only have a single, shared border between the two\n  &:not(:last-child) {\n    > .btn,\n    > .btn-group {\n      margin-right: (-$input-btn-border-width);\n    }\n  }\n  &:not(:first-child) {\n    > .btn,\n    > .btn-group {\n      z-index: 2;\n      margin-left: (-$input-btn-border-width);\n      // Because specificity\n      @include hover-focus-active {\n        z-index: 3;\n      }\n    }\n  }\n}\n", "// scss-lint:disable PropertyCount, VendorPrefix\n\n// Embedded icons from Open Iconic.\n// Released under MIT and copyright 2014 Waybury.\n// https://useiconic.com/open\n\n\n// Checkboxes and radios\n//\n// Base class takes care of all the key behavioral aspects.\n\n.custom-control {\n  position: relative;\n  display: inline-flex;\n  min-height: (1rem * $line-height-base);\n  padding-left: $custom-control-gutter;\n  margin-right: $custom-control-spacer-x;\n}\n\n.custom-control-input {\n  position: absolute;\n  z-index: -1; // Put the input behind the label so it doesn't overlay text\n  opacity: 0;\n\n  &:checked ~ .custom-control-indicator {\n    color: $custom-control-indicator-checked-color;\n    background-color: $custom-control-indicator-checked-bg;\n    @include box-shadow($custom-control-indicator-checked-box-shadow);\n  }\n\n  &:focus ~ .custom-control-indicator {\n    // the mixin is not used here to make sure there is feedback\n    box-shadow: $custom-control-indicator-focus-box-shadow;\n  }\n\n  &:active ~ .custom-control-indicator {\n    color: $custom-control-indicator-active-color;\n    background-color: $custom-control-indicator-active-bg;\n    @include box-shadow($custom-control-indicator-active-box-shadow);\n  }\n\n  &:disabled {\n    ~ .custom-control-indicator {\n      background-color: $custom-control-indicator-disabled-bg;\n    }\n\n    ~ .custom-control-description {\n      color: $custom-control-description-disabled-color;\n    }\n  }\n}\n\n// Custom indicator\n//\n// Generates a shadow element to create our makeshift checkbox/radio background.\n\n.custom-control-indicator {\n  position: absolute;\n  top: (($line-height-base - $custom-control-indicator-size) / 2);\n  left: 0;\n  display: block;\n  width: $custom-control-indicator-size;\n  height: $custom-control-indicator-size;\n  pointer-events: none;\n  user-select: none;\n  background-color: $custom-control-indicator-bg;\n  background-repeat: no-repeat;\n  background-position: center center;\n  background-size: $custom-control-indicator-bg-size;\n  @include box-shadow($custom-control-indicator-box-shadow);\n}\n\n// Checkboxes\n//\n// Tweak just a few things for checkboxes.\n\n.custom-checkbox {\n  .custom-control-indicator {\n    @include border-radius($custom-checkbox-indicator-border-radius);\n  }\n\n  .custom-control-input:checked ~ .custom-control-indicator {\n    background-image: $custom-checkbox-indicator-icon-checked;\n  }\n\n  .custom-control-input:indeterminate ~ .custom-control-indicator {\n    background-color: $custom-checkbox-indicator-indeterminate-bg;\n    background-image: $custom-checkbox-indicator-icon-indeterminate;\n    @include box-shadow($custom-checkbox-indicator-indeterminate-box-shadow);\n  }\n}\n\n// Radios\n//\n// Tweak just a few things for radios.\n\n.custom-radio {\n  .custom-control-indicator {\n    border-radius: $custom-radio-indicator-border-radius;\n  }\n\n  .custom-control-input:checked ~ .custom-control-indicator {\n    background-image: $custom-radio-indicator-icon-checked;\n  }\n}\n\n\n// Layout options\n//\n// By default radios and checkboxes are `inline-block` with no additional spacing\n// set. Use these optional classes to tweak the layout.\n\n.custom-controls-stacked {\n  display: flex;\n  flex-direction: column;\n\n  .custom-control {\n    margin-bottom: $custom-control-spacer-y;\n\n    + .custom-control {\n      margin-left: 0;\n    }\n  }\n}\n\n\n// Select\n//\n// Replaces the browser default select with a custom one, mostly pulled from\n// http://primercss.io.\n//\n\n.custom-select {\n  display: inline-block;\n  max-width: 100%;\n  height: $input-height;\n  padding: $custom-select-padding-y ($custom-select-padding-x + $custom-select-indicator-padding) $custom-select-padding-y $custom-select-padding-x;\n  line-height: $custom-select-line-height;\n  color: $custom-select-color;\n  vertical-align: middle;\n  background: $custom-select-bg $custom-select-indicator no-repeat right $custom-select-padding-x center;\n  background-size: $custom-select-bg-size;\n  border: $custom-select-border-width solid $custom-select-border-color;\n  @if $enable-rounded {\n    border-radius: $custom-select-border-radius;\n  } @else {\n    border-radius: 0;\n  }\n  appearance: none;\n\n  &:focus {\n    border-color: $custom-select-focus-border-color;\n    outline: none;\n    @include box-shadow($custom-select-focus-box-shadow);\n\n    &::-ms-value {\n      // For visual consistency with other platforms/browsers,\n      // supress the default white text on blue background highlight given to\n      // the selected option text when the (still closed) <select> receives focus\n      // in IE and (under certain conditions) Edge.\n      // See https://github.com/twbs/bootstrap/issues/19398.\n      color: $input-color;\n      background-color: $input-bg;\n    }\n  }\n\n  &:disabled {\n    color: $custom-select-disabled-color;\n    background-color: $custom-select-disabled-bg;\n  }\n\n  // Hides the default caret in IE11\n  &::-ms-expand {\n    opacity: 0;\n  }\n}\n\n.custom-select-sm {\n  height: $custom-select-height-sm;\n  padding-top: $custom-select-padding-y;\n  padding-bottom: $custom-select-padding-y;\n  font-size: $custom-select-font-size-sm;\n}\n\n\n// File\n//\n// Custom file input.\n\n.custom-file {\n  position: relative;\n  display: inline-block;\n  max-width: 100%;\n  height: $custom-file-height;\n  margin-bottom: 0;\n}\n\n.custom-file-input {\n  min-width: $custom-file-width;\n  max-width: 100%;\n  height: $custom-file-height;\n  margin: 0;\n  opacity: 0;\n\n  &:focus ~ .custom-file-control {\n    @include box-shadow($custom-file-focus-box-shadow);\n  }\n}\n\n.custom-file-control {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 5;\n  height: $custom-file-height;\n  padding: $custom-file-padding-x $custom-file-padding-y;\n  line-height: $custom-file-line-height;\n  color: $custom-file-color;\n  pointer-events: none;\n  user-select: none;\n  background-color: $custom-file-bg;\n  border: $custom-file-border-width solid $custom-file-border-color;\n  @include border-radius($custom-file-border-radius);\n  @include box-shadow($custom-file-box-shadow);\n\n  @each $lang, $text in map-get($custom-file-text, placeholder) {\n    &:lang(#{$lang}):empty::after {\n      content: $text;\n    }\n  }\n\n  &::before {\n    position: absolute;\n    top: -$custom-file-border-width;\n    right: -$custom-file-border-width;\n    bottom: -$custom-file-border-width;\n    z-index: 6;\n    display: block;\n    height: $custom-file-height;\n    padding: $custom-file-padding-x $custom-file-padding-y;\n    line-height: $custom-file-line-height;\n    color: $custom-file-button-color;\n    background-color: $custom-file-button-bg;\n    border: $custom-file-border-width solid $custom-file-border-color;\n    @include border-radius(0 $custom-file-border-radius $custom-file-border-radius 0);\n  }\n\n  @each $lang, $text in map-get($custom-file-text, button-label) {\n    &:lang(#{$lang})::before {\n      content: $text;\n    }\n  }\n}\n", "// Base class\n//\n// Kickstart any navigation component with a set of style resets. Works with\n// `<nav>`s or `<ul>`s.\n\n.nav {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.nav-link {\n  display: block;\n  padding: $nav-link-padding-y $nav-link-padding-x;\n\n  @include hover-focus {\n    text-decoration: none;\n  }\n\n  // Disabled state lightens text\n  &.disabled {\n    color: $nav-link-disabled-color;\n  }\n}\n\n//\n// Tabs\n//\n\n.nav-tabs {\n  border-bottom: $nav-tabs-border-width solid $nav-tabs-border-color;\n\n  .nav-item {\n    margin-bottom: -$nav-tabs-border-width;\n  }\n\n  .nav-link {\n    border: $nav-tabs-border-width solid transparent;\n    @include border-top-radius($nav-tabs-border-radius);\n\n    @include hover-focus {\n      border-color: $nav-tabs-link-hover-border-color $nav-tabs-link-hover-border-color $nav-tabs-border-color;\n    }\n\n    &.disabled {\n      color: $nav-link-disabled-color;\n      background-color: transparent;\n      border-color: transparent;\n    }\n  }\n\n  .nav-link.active,\n  .nav-item.show .nav-link {\n    color: $nav-tabs-link-active-color;\n    background-color: $nav-tabs-link-active-bg;\n    border-color: $nav-tabs-link-active-border-color $nav-tabs-link-active-border-color $nav-tabs-link-active-bg;\n  }\n\n  .dropdown-menu {\n    // Make dropdown border overlap tab border\n    margin-top: -$nav-tabs-border-width;\n    // Remove the top rounded corners here since there is a hard edge above the menu\n    @include border-top-radius(0);\n  }\n}\n\n\n//\n// Pills\n//\n\n.nav-pills {\n  .nav-link {\n    @include border-radius($nav-pills-border-radius);\n\n    &.active,\n    .show > & {\n      color: $nav-pills-link-active-color;\n      background-color: $nav-pills-link-active-bg;\n    }\n  }\n}\n\n\n//\n// Justified variants\n//\n\n.nav-fill {\n  .nav-item {\n    flex: 1 1 auto;\n    text-align: center;\n  }\n}\n\n.nav-justified {\n  .nav-item {\n    flex-basis: 0;\n    flex-grow: 1;\n    text-align: center;\n  }\n}\n\n\n// Tabbable tabs\n//\n// Hide tabbable panes to start, show them when `.active`\n\n.tab-content {\n  > .tab-pane {\n    display: none;\n  }\n  > .active {\n    display: block;\n  }\n}\n", "// Contents\n//\n// Navbar\n// Navbar brand\n// Navbar nav\n// Navbar text\n// Navbar divider\n// Responsive navbar\n// Navbar position\n// Navbar themes\n\n\n// Navbar\n//\n// Provide a static navbar from which we expand to create full-width, fixed, and\n// other navbar variations.\n\n.navbar {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // allow us to do the line break for collapsing content\n  align-items: center;\n  justify-content: space-between; // space out brand from logo\n  padding: $navbar-padding-y $navbar-padding-x;\n\n  // Because flex properties aren't inherited, we need to redeclare these first\n  // few properities so that content nested within behave properly.\n  > .container,\n  > .container-fluid {\n    display: flex;\n    flex-wrap: wrap;\n    align-items: center;\n    justify-content: space-between;\n  }\n}\n\n\n// Navbar brand\n//\n// Used for brand, project, or site names.\n\n.navbar-brand {\n  display: inline-block;\n  padding-top: $navbar-brand-padding-y;\n  padding-bottom: $navbar-brand-padding-y;\n  margin-right: $navbar-padding-x;\n  font-size: $navbar-brand-font-size;\n  line-height: inherit;\n  white-space: nowrap;\n\n  @include hover-focus {\n    text-decoration: none;\n  }\n}\n\n\n// Navbar nav\n//\n// Custom navbar navigation (doesn't require `.nav`, but does make use of `.nav-link`).\n\n.navbar-nav {\n  display: flex;\n  flex-direction: column; // cannot use `inherit` to get the `.navbar`s value\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n\n  .nav-link {\n    padding-right: 0;\n    padding-left: 0;\n  }\n\n  .dropdown-menu {\n    position: static;\n    float: none;\n  }\n}\n\n\n// Navbar text\n//\n//\n\n.navbar-text {\n  display: inline-block;\n  padding-top: $nav-link-padding-y;\n  padding-bottom: $nav-link-padding-y;\n}\n\n\n// Responsive navbar\n//\n// Custom styles for responsive collapsing and toggling of navbar contents.\n// Powered by the collapse Bootstrap JavaScript plugin.\n\n// When collapsed, prevent the toggleable navbar contents from appearing in\n// the default flexbox row orienation. Requires the use of `flex-wrap: wrap`\n// on the `.navbar` parent.\n.navbar-collapse {\n  flex-basis: 100%;\n  // For always expanded or extra full navbars, ensure content aligns itself\n  // properly vertically. Can be easily overridden with flex utilities.\n  align-items: center;\n}\n\n// Button for toggling the navbar when in its collapsed state\n.navbar-toggler {\n  padding: $navbar-toggler-padding-y $navbar-toggler-padding-x;\n  font-size: $navbar-toggler-font-size;\n  line-height: 1;\n  background: transparent; // remove default button style\n  border: $border-width solid transparent; // remove default button style\n  @include border-radius($navbar-toggler-border-radius);\n\n  @include hover-focus {\n    text-decoration: none;\n  }\n}\n\n// Keep as a separate element so folks can easily override it with another icon\n// or image file as needed.\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  content: \"\";\n  background: no-repeat center center;\n  background-size: 100% 100%;\n}\n\n// Generate series of `.navbar-expand-*` responsive classes for configuring\n// where your navbar collapses.\n.navbar-expand {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\n    $infix: breakpoint-infix($next, $grid-breakpoints);\n\n    &#{$infix} {\n      @include media-breakpoint-down($breakpoint) {\n        > .container,\n        > .container-fluid {\n          padding-right: 0;\n          padding-left: 0;\n        }\n      }\n\n      @include media-breakpoint-up($next) {\n        flex-direction: row;\n        flex-wrap: nowrap;\n        justify-content: flex-start;\n\n        .navbar-nav {\n          flex-direction: row;\n\n          .dropdown-menu {\n            position: absolute;\n          }\n\n          .dropdown-menu-right {\n            right: 0;\n            left: auto; // Reset the default from `.dropdown-menu`\n          }\n\n          .nav-link {\n            padding-right: .5rem;\n            padding-left: .5rem;\n          }\n        }\n\n        // For nesting containers, have to redeclare for alignment purposes\n        > .container,\n        > .container-fluid {\n          flex-wrap: nowrap;\n        }\n\n        // scss-lint:disable ImportantRule\n        .navbar-collapse {\n          display: flex !important;\n        }\n        // scss-lint:enable ImportantRule\n\n        .navbar-toggler {\n          display: none;\n        }\n      }\n    }\n  }\n}\n\n\n// Navbar themes\n//\n// Styles for switching between navbars with light or dark background.\n\n// Dark links against a light background\n.navbar-light {\n  .navbar-brand {\n    color: $navbar-light-active-color;\n\n    @include hover-focus {\n      color: $navbar-light-active-color;\n    }\n  }\n\n  .navbar-nav {\n    .nav-link {\n      color: $navbar-light-color;\n\n      @include hover-focus {\n        color: $navbar-light-hover-color;\n      }\n\n      &.disabled {\n        color: $navbar-light-disabled-color;\n      }\n    }\n\n    .show > .nav-link,\n    .active > .nav-link,\n    .nav-link.show,\n    .nav-link.active {\n      color: $navbar-light-active-color;\n    }\n  }\n\n  .navbar-toggler {\n    color: $navbar-light-color;\n    border-color: $navbar-light-toggler-border-color;\n  }\n\n  .navbar-toggler-icon {\n    background-image: $navbar-light-toggler-icon-bg;\n  }\n\n  .navbar-text {\n    color: $navbar-light-color;\n  }\n}\n\n// White links against a dark background\n.navbar-dark {\n  .navbar-brand {\n    color: $navbar-dark-active-color;\n\n    @include hover-focus {\n      color: $navbar-dark-active-color;\n    }\n  }\n\n  .navbar-nav {\n    .nav-link {\n      color: $navbar-dark-color;\n\n      @include hover-focus {\n        color: $navbar-dark-hover-color;\n      }\n\n      &.disabled {\n        color: $navbar-dark-disabled-color;\n      }\n    }\n\n    .show > .nav-link,\n    .active > .nav-link,\n    .nav-link.show,\n    .nav-link.active {\n      color: $navbar-dark-active-color;\n    }\n  }\n\n  .navbar-toggler {\n    color: $navbar-dark-color;\n    border-color: $navbar-dark-toggler-border-color;\n  }\n\n  .navbar-toggler-icon {\n    background-image: $navbar-dark-toggler-icon-bg;\n  }\n\n  .navbar-text {\n    color: $navbar-dark-color;\n  }\n}\n", "//\n// Base styles\n//\n\n.card {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0;\n  word-wrap: break-word;\n  background-color: $card-bg;\n  background-clip: border-box;\n  border: $card-border-width solid $card-border-color;\n  @include border-radius($card-border-radius);\n}\n\n.card-body {\n  // Enable `flex-grow: 1` for decks and groups so that card blocks take up\n  // as much space as possible, ensuring footers are aligned to the bottom.\n  flex: 1 1 auto;\n  padding: $card-spacer-x;\n}\n\n.card-title {\n  margin-bottom: $card-spacer-y;\n}\n\n.card-subtitle {\n  margin-top: -($card-spacer-y / 2);\n  margin-bottom: 0;\n}\n\n.card-text:last-child {\n  margin-bottom: 0;\n}\n\n.card-link {\n  @include hover {\n    text-decoration: none;\n  }\n\n  + .card-link {\n    margin-left: $card-spacer-x;\n  }\n}\n\n.card {\n  > .list-group:first-child {\n    .list-group-item:first-child {\n      @include border-top-radius($card-border-radius);\n    }\n  }\n\n  > .list-group:last-child {\n    .list-group-item:last-child {\n      @include border-bottom-radius($card-border-radius);\n    }\n  }\n}\n\n\n//\n// Optional textual caps\n//\n\n.card-header {\n  padding: $card-spacer-y $card-spacer-x;\n  margin-bottom: 0; // Removes the default margin-bottom of <hN>\n  background-color: $card-cap-bg;\n  border-bottom: $card-border-width solid $card-border-color;\n\n  &:first-child {\n    @include border-radius($card-inner-border-radius $card-inner-border-radius 0 0);\n  }\n}\n\n.card-footer {\n  padding: $card-spacer-y $card-spacer-x;\n  background-color: $card-cap-bg;\n  border-top: $card-border-width solid $card-border-color;\n\n  &:last-child {\n    @include border-radius(0 0 $card-inner-border-radius $card-inner-border-radius);\n  }\n}\n\n\n//\n// Header navs\n//\n\n.card-header-tabs {\n  margin-right: -($card-spacer-x / 2);\n  margin-bottom: -$card-spacer-y;\n  margin-left: -($card-spacer-x / 2);\n  border-bottom: 0;\n}\n\n.card-header-pills {\n  margin-right: -($card-spacer-x / 2);\n  margin-left: -($card-spacer-x / 2);\n}\n\n// Card image\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: $card-img-overlay-padding;\n}\n\n.card-img {\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\n  @include border-radius($card-inner-border-radius);\n}\n\n// Card image caps\n.card-img-top {\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\n  @include border-top-radius($card-inner-border-radius);\n}\n\n.card-img-bottom {\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\n  @include border-bottom-radius($card-inner-border-radius);\n}\n\n\n// Card deck\n\n@include media-breakpoint-up(sm) {\n  .card-deck {\n    display: flex;\n    flex-flow: row wrap;\n    margin-right: -$card-deck-margin;\n    margin-left: -$card-deck-margin;\n\n    .card {\n      display: flex;\n      flex: 1 0 0%;\n      flex-direction: column;\n      margin-right: $card-deck-margin;\n      margin-left: $card-deck-margin;\n    }\n  }\n}\n\n\n//\n// Card groups\n//\n\n@include media-breakpoint-up(sm) {\n  .card-group {\n    display: flex;\n    flex-flow: row wrap;\n\n    .card {\n      flex: 1 0 0%;\n\n      + .card {\n        margin-left: 0;\n        border-left: 0;\n      }\n\n      // Handle rounded corners\n      @if $enable-rounded {\n        &:first-child {\n          @include border-right-radius(0);\n\n          .card-img-top {\n            border-top-right-radius: 0;\n          }\n          .card-img-bottom {\n            border-bottom-right-radius: 0;\n          }\n        }\n        &:last-child {\n          @include border-left-radius(0);\n\n          .card-img-top {\n            border-top-left-radius: 0;\n          }\n          .card-img-bottom {\n            border-bottom-left-radius: 0;\n          }\n        }\n\n        &:not(:first-child):not(:last-child) {\n          border-radius: 0;\n\n          .card-img-top,\n          .card-img-bottom {\n            border-radius: 0;\n          }\n        }\n      }\n    }\n  }\n}\n\n\n//\n// Columns\n//\n\n.card-columns {\n  .card {\n    margin-bottom: $card-columns-margin;\n  }\n\n  @include media-breakpoint-up(sm) {\n    column-count: $card-columns-count;\n    column-gap: $card-columns-gap;\n\n    .card {\n      display: inline-block; // Don't let them vertically span multiple columns\n      width: 100%; // Don't let their width change\n    }\n  }\n}\n", ".breadcrumb {\n  padding: $breadcrumb-padding-y $breadcrumb-padding-x;\n  margin-bottom: 1rem;\n  list-style: none;\n  background-color: $breadcrumb-bg;\n  @include border-radius($border-radius);\n  @include clearfix;\n}\n\n.breadcrumb-item {\n  float: left;\n\n  // The separator between breadcrumbs (by default, a forward-slash: \"/\")\n  + .breadcrumb-item::before {\n    display: inline-block; // Suppress underlining of the separator in modern browsers\n    padding-right: $breadcrumb-item-padding;\n    padding-left: $breadcrumb-item-padding;\n    color: $breadcrumb-divider-color;\n    content: \"#{$breadcrumb-divider}\";\n  }\n\n  // IE9-11 hack to properly handle hyperlink underlines for breadcrumbs built\n  // without `<ul>`s. The `::before` pseudo-element generates an element\n  // *within* the .breadcrumb-item and thereby inherits the `text-decoration`.\n  //\n  // To trick IE into suppressing the underline, we give the pseudo-element an\n  // underline and then immediately remove it.\n  + .breadcrumb-item:hover::before {\n    text-decoration: underline;\n  }\n  + .breadcrumb-item:hover::before {\n    text-decoration: none;\n  }\n\n  &.active {\n    color: $breadcrumb-active-color;\n  }\n}\n", "@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n", ".pagination {\n  display: flex;\n  // 1-2: Disable browser default list styles\n  padding-left: 0; // 1\n  list-style: none; // 2\n  @include border-radius();\n}\n\n.page-item {\n  &:first-child {\n    .page-link {\n      margin-left: 0;\n      @include border-left-radius($border-radius);\n    }\n  }\n  &:last-child {\n    .page-link {\n      @include border-right-radius($border-radius);\n    }\n  }\n\n  &.active .page-link {\n    z-index: 2;\n    color: $pagination-active-color;\n    background-color: $pagination-active-bg;\n    border-color: $pagination-active-border-color;\n  }\n\n  &.disabled .page-link {\n    color: $pagination-disabled-color;\n    pointer-events: none;\n    background-color: $pagination-disabled-bg;\n    border-color: $pagination-disabled-border-color;\n  }\n}\n\n.page-link {\n  position: relative;\n  display: block;\n  padding: $pagination-padding-y $pagination-padding-x;\n  margin-left: -1px;\n  line-height: $pagination-line-height;\n  color: $pagination-color;\n  background-color: $pagination-bg;\n  border: $pagination-border-width solid $pagination-border-color;\n\n  @include hover-focus {\n    color: $pagination-hover-color;\n    text-decoration: none;\n    background-color: $pagination-hover-bg;\n    border-color: $pagination-hover-border-color;\n  }\n}\n\n\n//\n// Sizing\n//\n\n.pagination-lg {\n  @include pagination-size($pagination-padding-y-lg, $pagination-padding-x-lg, $font-size-lg, $line-height-lg, $border-radius-lg);\n}\n\n.pagination-sm {\n  @include pagination-size($pagination-padding-y-sm, $pagination-padding-x-sm, $font-size-sm, $line-height-sm, $border-radius-sm);\n}\n", "// Pagination\n\n@mixin pagination-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  .page-link {\n    padding: $padding-y $padding-x;\n    font-size: $font-size;\n    line-height: $line-height;\n  }\n\n  .page-item {\n    &:first-child {\n      .page-link {\n        @include border-left-radius($border-radius);\n      }\n    }\n    &:last-child {\n      .page-link {\n        @include border-right-radius($border-radius);\n      }\n    }\n  }\n}\n", "// Base class\n//\n// Requires one of the contextual, color modifier classes for `color` and\n// `background-color`.\n\n.badge {\n  display: inline-block;\n  padding: $badge-padding-y $badge-padding-x;\n  font-size: $badge-font-size;\n  font-weight: $badge-font-weight;\n  line-height: 1;\n  color: $badge-color;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  @include border-radius();\n\n  // Empty badges collapse automatically\n  &:empty {\n    display: none;\n  }\n}\n\n// Quick fix for badges in buttons\n.btn .badge {\n  position: relative;\n  top: -1px;\n}\n\n// Pill badges\n//\n// Make them extra rounded with a modifier to replace v3's badges.\n\n.badge-pill {\n  padding-right: $badge-pill-padding-x;\n  padding-left: $badge-pill-padding-x;\n  @include border-radius($badge-pill-border-radius);\n}\n\n// Colors\n//\n// Contextual variations (linked badges get darker on :hover).\n\n@each $color, $value in $theme-colors {\n  .badge-#{$color} {\n    @include badge-variant($value);\n  }\n}\n", "@mixin badge-variant($bg) {\n  @include color-yiq($bg);\n  background-color: $bg;\n\n  &[href] {\n    @include hover-focus {\n      @include color-yiq($bg);\n      text-decoration: none;\n      background-color: darken($bg, 10%);\n    }\n  }\n}\n", ".jumbotron {\n  padding: $jumbotron-padding ($jumbotron-padding / 2);\n  margin-bottom: $jumbotron-padding;\n  background-color: $jumbotron-bg;\n  @include border-radius($border-radius-lg);\n\n  @include media-breakpoint-up(sm) {\n    padding: ($jumbotron-padding * 2) $jumbotron-padding;\n  }\n}\n\n.jumbotron-fluid {\n  padding-right: 0;\n  padding-left: 0;\n  @include border-radius(0);\n}\n", "//\n// Base styles\n//\n\n.alert {\n  padding: $alert-padding-y $alert-padding-x;\n  margin-bottom: $alert-margin-bottom;\n  border: $alert-border-width solid transparent;\n  @include border-radius($alert-border-radius);\n}\n\n// Headings for larger alerts\n.alert-heading {\n  // Specified to prevent conflicts of changing $headings-color\n  color: inherit;\n}\n\n// Provide class for links that match alerts\n.alert-link {\n  font-weight: $alert-link-font-weight;\n}\n\n\n// Dismissible alerts\n//\n// Expand the right padding and account for the close button's positioning.\n\n.alert-dismissible {\n  // Adjust close link position\n  .close {\n    position: relative;\n    top: -$alert-padding-y;\n    right: -$alert-padding-x;\n    padding: $alert-padding-y $alert-padding-x;\n    color: inherit;\n  }\n}\n\n\n// Alternate styles\n//\n// Generate contextual modifier classes for colorizing the alert.\n\n@each $color, $value in $theme-colors {\n  .alert-#{$color} {\n    @include alert-variant(theme-color-level($color, -10), theme-color-level($color, -9), theme-color-level($color, 6));\n  }\n}\n", "@mixin alert-variant($background, $border, $color) {\n  color: $color;\n  background-color: $background;\n  border-color: $border;\n\n  hr {\n    border-top-color: darken($border, 5%);\n  }\n\n  .alert-link {\n    color: darken($color, 10%);\n  }\n}\n", "@keyframes progress-bar-stripes {\n  from { background-position: $progress-height 0; }\n  to { background-position: 0 0; }\n}\n\n.progress {\n  display: flex;\n  overflow: hidden; // force rounded corners by cropping it\n  font-size: $progress-font-size;\n  line-height: $progress-height;\n  text-align: center;\n  background-color: $progress-bg;\n  @include border-radius($progress-border-radius);\n  @include box-shadow($progress-box-shadow);\n}\n\n.progress-bar {\n  height: $progress-height;\n  line-height: $progress-height;\n  color: $progress-bar-color;\n  background-color: $progress-bar-bg;\n  @include transition($progress-bar-transition);\n}\n\n.progress-bar-striped {\n  @include gradient-striped();\n  background-size: $progress-height $progress-height;\n}\n\n.progress-bar-animated {\n  animation: progress-bar-stripes $progress-bar-animation-timing;\n}\n", "// Gradients\n\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: #555, $end-color: #333, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: #555, $end-color: #333, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n@mixin gradient-directional($start-color: #555, $end-color: #333, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n  background-repeat: repeat-x;\n}\n@mixin gradient-x-three-colors($start-color: #00b3ee, $mid-color: #7a43b6, $color-stop: 50%, $end-color: #c3325f) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-y-three-colors($start-color: #00b3ee, $mid-color: #7a43b6, $color-stop: 50%, $end-color: #c3325f) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-radial($inner-color: #555, $outer-color: #333) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-striped($color: rgba(255,255,255,.15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n", ".media {\n  display: flex;\n  align-items: flex-start;\n}\n\n.media-body {\n  flex: 1;\n}\n", "// Base class\n//\n// Easily usable on <ul>, <ol>, or <div>.\n\n.list-group {\n  display: flex;\n  flex-direction: column;\n\n  // No need to set list-style: none; since .list-group-item is block level\n  padding-left: 0; // reset padding because ul and ol\n  margin-bottom: 0;\n}\n\n\n// Interactive list items\n//\n// Use anchor or button elements instead of `li`s or `div`s to create interactive\n// list items. Includes an extra `.active` modifier class for selected items.\n\n.list-group-item-action {\n  width: 100%; // For `<button>`s (anchors become 100% by default though)\n  color: $list-group-action-color;\n  text-align: inherit; // For `<button>`s (anchors inherit)\n\n  // Hover state\n  @include hover-focus {\n    color: $list-group-action-hover-color;\n    text-decoration: none;\n    background-color: $list-group-hover-bg;\n  }\n\n  &:active {\n    color: $list-group-action-active-color;\n    background-color: $list-group-action-active-bg;\n  }\n}\n\n\n// Individual list items\n//\n// Use on `li`s or `div`s within the `.list-group` parent.\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: $list-group-item-padding-y $list-group-item-padding-x;\n  // Place the border on the list items and negative margin up for better styling\n  margin-bottom: -$list-group-border-width;\n  background-color: $list-group-bg;\n  border: $list-group-border-width solid $list-group-border-color;\n\n  &:first-child {\n    @include border-top-radius($list-group-border-radius);\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n    @include border-bottom-radius($list-group-border-radius);\n  }\n\n  @include hover-focus {\n    text-decoration: none;\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $list-group-disabled-color;\n    background-color: $list-group-disabled-bg;\n  }\n\n  // Include both here for `<a>`s and `<button>`s\n  &.active {\n    z-index: 2; // Place active items above their siblings for proper border styling\n    color: $list-group-active-color;\n    background-color: $list-group-active-bg;\n    border-color: $list-group-active-border-color;\n  }\n}\n\n\n// Flush list items\n//\n// Remove borders and border-radius to keep list group items edge-to-edge. Most\n// useful within other components (e.g., cards).\n\n.list-group-flush {\n  .list-group-item {\n    border-right: 0;\n    border-left: 0;\n    border-radius: 0;\n  }\n\n  &:first-child {\n    .list-group-item:first-child {\n      border-top: 0;\n    }\n  }\n\n  &:last-child {\n    .list-group-item:last-child {\n      border-bottom: 0;\n    }\n  }\n}\n\n\n// Contextual variants\n//\n// Add modifier classes to change text and background color on individual items.\n// Organizationally, this must come after the `:hover` states.\n\n@each $color, $value in $theme-colors {\n  @include list-group-item-variant($color, theme-color-level($color, -9), theme-color-level($color, 6));\n}\n", "// List Groups\n\n@mixin list-group-item-variant($state, $background, $color) {\n  .list-group-item-#{$state} {\n    color: $color;\n    background-color: $background;\n  }\n\n  //scss-lint:disable QualifyingElement\n  a.list-group-item-#{$state},\n  button.list-group-item-#{$state} {\n    color: $color;\n\n    @include hover-focus {\n      color: $color;\n      background-color: darken($background, 5%);\n    }\n\n    &.active {\n      color: #fff;\n      background-color: $color;\n      border-color: $color;\n    }\n  }\n  // scss-lint:enable QualifyingElement\n}\n", ".close {\n  float: right;\n  font-size: $close-font-size;\n  font-weight: $close-font-weight;\n  line-height: 1;\n  color: $close-color;\n  text-shadow: $close-text-shadow;\n  opacity: .5;\n\n  @include hover-focus {\n    color: $close-color;\n    text-decoration: none;\n    opacity: .75;\n  }\n}\n\n// Additional properties for button version\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n// scss-lint:disable QualifyingElement\nbutton.close {\n  padding: 0;\n  background: transparent;\n  border: 0;\n  -webkit-appearance: none;\n}\n// scss-lint:enable QualifyingElement\n", "// .modal-open      - body class for killing the scroll\n// .modal           - container to scroll within\n// .modal-dialog    - positioning shell for the actual modal\n// .modal-content   - actual modal w/ bg and corners and stuff\n\n\n// Kill the scroll on the body\n.modal-open {\n  overflow: hidden;\n}\n\n// Container that the modal scrolls within\n.modal {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-modal;\n  display: none;\n  overflow: hidden;\n  // Prevent Chrome on Windows from adding a focus outline. For details, see\n  // https://github.com/twbs/bootstrap/pull/10951.\n  outline: 0;\n  // We deliberately don't use `-webkit-overflow-scrolling: touch;` due to a\n  // gnarly iOS Safari bug: https://bugs.webkit.org/show_bug.cgi?id=158342\n  // See also https://github.com/twbs/bootstrap/issues/17695\n\n  // When fading in the modal, animate it to slide down\n  &.fade .modal-dialog {\n    @include transition($modal-transition);\n    transform: translate(0, -25%);\n  }\n  &.show .modal-dialog { transform: translate(0, 0); }\n}\n.modal-open .modal {\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n// Shell div to position the modal with bottom padding\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: $modal-dialog-margin;\n}\n\n// Actual modal\n.modal-content {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  background-color: $modal-content-bg;\n  background-clip: padding-box;\n  border: $modal-content-border-width solid $modal-content-border-color;\n  @include border-radius($border-radius-lg);\n  @include box-shadow($modal-content-box-shadow-xs);\n  // Remove focus outline from opened modal\n  outline: 0;\n}\n\n// Modal background\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-modal-backdrop;\n  background-color: $modal-backdrop-bg;\n\n  // Fade for backdrop\n  &.fade { opacity: 0; }\n  &.show { opacity: $modal-backdrop-opacity; }\n}\n\n// Modal header\n// Top section of the modal w/ title and dismiss\n.modal-header {\n  display: flex;\n  align-items: center; // vertically center it\n  justify-content: space-between; // Put modal header elements (title and dismiss) on opposite ends\n  padding: $modal-header-padding;\n  border-bottom: $modal-header-border-width solid $modal-header-border-color;\n}\n\n// Title text within header\n.modal-title {\n  margin-bottom: 0;\n  line-height: $modal-title-line-height;\n}\n\n// Modal body\n// Where all modal content resides (sibling of .modal-header and .modal-footer)\n.modal-body {\n  position: relative;\n  // Enable `flex-grow: 1` so that the body take up as much space as possible\n  // when should there be a fixed height on `.modal-dialog`.\n  flex: 1 1 auto;\n  padding: $modal-inner-padding;\n}\n\n// Footer (for actions)\n.modal-footer {\n  display: flex;\n  align-items: center; // vertically center\n  justify-content: flex-end; // Right align buttons with flex property because text-align doesn't work on flex items\n  padding: $modal-inner-padding;\n  border-top: $modal-footer-border-width solid $modal-footer-border-color;\n\n  // Easily place margin between footer elements\n  > :not(:first-child) { margin-left: .25rem; }\n  > :not(:last-child) { margin-right: .25rem; }\n}\n\n// Measure scrollbar width for padding body during modal show/hide\n.modal-scrollbar-measure {\n  position: absolute;\n  top: -9999px;\n  width: 50px;\n  height: 50px;\n  overflow: scroll;\n}\n\n// Scale up the modal\n@include media-breakpoint-up(sm) {\n  // Automatically set modal's width for larger viewports\n  .modal-dialog {\n    max-width: $modal-md;\n    margin: $modal-dialog-margin-y-sm-up auto;\n  }\n\n  .modal-content {\n    @include box-shadow($modal-content-box-shadow-sm-up);\n  }\n\n  .modal-sm { max-width: $modal-sm; }\n}\n\n@include media-breakpoint-up(lg) {\n  .modal-lg { max-width: $modal-lg; }\n}\n", "// Base class\n.tooltip {\n  position: absolute;\n  z-index: $zindex-tooltip;\n  display: block;\n  margin: $tooltip-margin;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  font-size: $font-size-sm;\n  // Allow breaking very long words so they don't overflow the tooltip's bounds\n  word-wrap: break-word;\n  opacity: 0;\n\n  &.show { opacity: $tooltip-opacity; }\n\n  .arrow {\n    position: absolute;\n    display: block;\n    width: $tooltip-arrow-width;\n    height: $tooltip-arrow-height;\n  }\n\n  &.bs-tooltip-top {\n    padding: $tooltip-arrow-width 0;\n    .arrow {\n      bottom: 0;\n    }\n\n    .arrow::before {\n      margin-left: -($tooltip-arrow-width - 2);\n      content: \"\";\n      border-width: $tooltip-arrow-width $tooltip-arrow-width 0;\n      border-top-color: $tooltip-arrow-color;\n    }\n  }\n  &.bs-tooltip-right {\n    padding: 0 $tooltip-arrow-width;\n    .arrow {\n      left: 0;\n    }\n\n    .arrow::before {\n      margin-top: -($tooltip-arrow-width - 2);\n      content: \"\";\n      border-width: $tooltip-arrow-width $tooltip-arrow-width $tooltip-arrow-width 0;\n      border-right-color: $tooltip-arrow-color;\n    }\n  }\n  &.bs-tooltip-bottom {\n    padding: $tooltip-arrow-width 0;\n    .arrow {\n      top: 0;\n    }\n\n    .arrow::before {\n      margin-left: -($tooltip-arrow-width - 2);\n      content: \"\";\n      border-width: 0 $tooltip-arrow-width $tooltip-arrow-width;\n      border-bottom-color: $tooltip-arrow-color;\n    }\n  }\n  &.bs-tooltip-left {\n    padding: 0 $tooltip-arrow-width;\n    .arrow {\n      right: 0;\n    }\n\n    .arrow::before {\n      right: 0;\n      margin-top: -($tooltip-arrow-width - 2);\n      content: \"\";\n      border-width: $tooltip-arrow-width 0 $tooltip-arrow-width $tooltip-arrow-width;\n      border-left-color: $tooltip-arrow-color;\n    }\n  }\n  &.bs-tooltip-auto {\n    &[x-placement^=\"top\"] {\n      @extend .bs-tooltip-top;\n    }\n    &[x-placement^=\"right\"] {\n      @extend .bs-tooltip-right;\n    }\n    &[x-placement^=\"bottom\"] {\n      @extend .bs-tooltip-bottom;\n    }\n    &[x-placement^=\"left\"] {\n      @extend .bs-tooltip-left;\n    }\n  }\n\n  .arrow::before {\n    position: absolute;\n    border-color: transparent;\n    border-style: solid;\n  }\n}\n\n// Wrapper for the tooltip content\n.tooltip-inner {\n  max-width: $tooltip-max-width;\n  padding: $tooltip-padding-y $tooltip-padding-x;\n  color: $tooltip-color;\n  text-align: center;\n  background-color: $tooltip-bg;\n  @include border-radius($border-radius);\n}\n", "// scss-lint:disable DuplicateProperty\n@mixin reset-text {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n}\n", ".popover {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: $zindex-popover;\n  display: block;\n  max-width: $popover-max-width;\n  padding: $popover-inner-padding;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  font-size: $font-size-sm;\n  // Allow breaking very long words so they don't overflow the popover's bounds\n  word-wrap: break-word;\n  background-color: $popover-bg;\n  background-clip: padding-box;\n  border: $popover-border-width solid $popover-border-color;\n  @include border-radius($border-radius-lg);\n  @include box-shadow($popover-box-shadow);\n\n  // Arrows\n  //\n  // .arrow is outer, .arrow::after is inner\n\n  .arrow {\n    position: absolute;\n    display: block;\n    width: $popover-arrow-width;\n    height: $popover-arrow-height;\n  }\n\n  .arrow::before,\n  .arrow::after {\n    position: absolute;\n    display: block;\n    border-color: transparent;\n    border-style: solid;\n  }\n\n  .arrow::before {\n    content: \"\";\n    border-width: $popover-arrow-outer-width;\n  }\n  .arrow::after {\n    content: \"\";\n    border-width: $popover-arrow-outer-width;\n  }\n\n  // Popover directions\n\n  &.bs-popover-top {\n    margin-bottom: $popover-arrow-width;\n\n    .arrow {\n      bottom: 0;\n    }\n\n    .arrow::before,\n    .arrow::after {\n      border-bottom-width: 0;\n    }\n\n    .arrow::before {\n      bottom: -$popover-arrow-outer-width;\n      margin-left: -($popover-arrow-outer-width - 5);\n      border-top-color: $popover-arrow-outer-color;\n    }\n\n    .arrow::after {\n      bottom: -($popover-arrow-outer-width - 1);\n      margin-left: -($popover-arrow-outer-width - 5);\n      border-top-color: $popover-arrow-color;\n    }\n  }\n\n  &.bs-popover-right {\n    margin-left: $popover-arrow-width;\n\n    .arrow {\n      left: 0;\n    }\n\n    .arrow::before,\n    .arrow::after {\n      margin-top: -($popover-arrow-outer-width - 3);\n      border-left-width: 0;\n    }\n\n    .arrow::before {\n      left: -$popover-arrow-outer-width;\n      border-right-color: $popover-arrow-outer-color;\n    }\n\n    .arrow::after {\n      left: -($popover-arrow-outer-width - 1);\n      border-right-color: $popover-arrow-color;\n    }\n  }\n\n  &.bs-popover-bottom {\n    margin-top: $popover-arrow-width;\n\n    .arrow {\n      top: 0;\n    }\n\n    .arrow::before,\n    .arrow::after {\n      margin-left: -($popover-arrow-width - 3);\n      border-top-width: 0;\n    }\n\n    .arrow::before {\n      top: -$popover-arrow-outer-width;\n      border-bottom-color: $popover-arrow-outer-color;\n    }\n\n    .arrow::after {\n      top: -($popover-arrow-outer-width - 1);\n      border-bottom-color: $popover-arrow-color;\n    }\n\n    // This will remove the popover-header's border just below the arrow\n    .popover-header::before {\n      position: absolute;\n      top: 0;\n      left: 50%;\n      display: block;\n      width: 20px;\n      margin-left: -10px;\n      content: \"\";\n      border-bottom: 1px solid $popover-header-bg;\n    }\n  }\n\n  &.bs-popover-left {\n    margin-right: $popover-arrow-width;\n\n    .arrow {\n      right: 0;\n    }\n\n    .arrow::before,\n    .arrow::after {\n      margin-top: -($popover-arrow-outer-width - 3);\n      border-right-width: 0;\n    }\n\n    .arrow::before {\n      right: -$popover-arrow-outer-width;\n      border-left-color: $popover-arrow-outer-color;\n    }\n\n    .arrow::after {\n      right: -($popover-arrow-outer-width - 1);\n      border-left-color: $popover-arrow-color;\n    }\n  }\n  &.bs-popover-auto {\n    &[x-placement^=\"top\"] {\n      @extend .bs-popover-top;\n    }\n    &[x-placement^=\"right\"] {\n      @extend .bs-popover-right;\n    }\n    &[x-placement^=\"bottom\"] {\n      @extend .bs-popover-bottom;\n    }\n    &[x-placement^=\"left\"] {\n      @extend .bs-popover-left;\n    }\n  }\n}\n\n\n// Offset the popover to account for the popover arrow\n.popover-header {\n  padding: $popover-header-padding-y $popover-header-padding-x;\n  margin-bottom: 0; // Reset the default from Reboot\n  font-size: $font-size-base;\n  color: $popover-header-color;\n  background-color: $popover-header-bg;\n  border-bottom: $popover-border-width solid darken($popover-header-bg, 5%);\n  $offset-border-width: calc(#{$border-radius-lg} - #{$popover-border-width});\n  @include border-top-radius($offset-border-width);\n\n  &:empty {\n    display: none;\n  }\n}\n\n.popover-body {\n  padding: $popover-body-padding-y $popover-body-padding-x;\n  color: $popover-body-color;\n}\n", "// Wrapper for the slide container and indicators\n.carousel {\n  position: relative;\n}\n\n.carousel-inner {\n  position: relative;\n  width: 100%;\n  overflow: hidden;\n}\n\n.carousel-item {\n  position: relative;\n  display: none;\n  align-items: center;\n  width: 100%;\n  @include transition($carousel-transition);\n  backface-visibility: hidden;\n  perspective: 1000px;\n}\n\n.carousel-item.active,\n.carousel-item-next,\n.carousel-item-prev {\n  display: block;\n}\n\n.carousel-item-next,\n.carousel-item-prev {\n  position: absolute;\n  top: 0;\n}\n\n// CSS3 transforms when supported by the browser\n.carousel-item-next.carousel-item-left,\n.carousel-item-prev.carousel-item-right {\n  transform: translateX(0);\n\n  @supports (transform-style: preserve-3d) {\n    transform: translate3d(0, 0, 0);\n  }\n}\n\n.carousel-item-next,\n.active.carousel-item-right {\n  transform: translateX(100%);\n\n  @supports (transform-style: preserve-3d) {\n    transform: translate3d(100%, 0, 0);\n  }\n}\n\n.carousel-item-prev,\n.active.carousel-item-left {\n  transform: translateX(-100%);\n\n  @supports (transform-style: preserve-3d) {\n    transform: translate3d(-100%, 0, 0);\n  }\n}\n\n\n//\n// Left/right controls for nav\n//\n\n.carousel-control-prev,\n.carousel-control-next {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  // Use flex for alignment (1-3)\n  display: flex; // 1. allow flex styles\n  align-items: center; // 2. vertically center contents\n  justify-content: center; // 3. horizontally center contents\n  width: $carousel-control-width;\n  color: $carousel-control-color;\n  text-align: center;\n  opacity: $carousel-control-opacity;\n  // We can't have a transition here because WebKit cancels the carousel\n  // animation if you trip this while in the middle of another animation.\n\n  // Hover/focus state\n  @include hover-focus {\n    color: $carousel-control-color;\n    text-decoration: none;\n    outline: 0;\n    opacity: .9;\n  }\n}\n.carousel-control-prev {\n  left: 0;\n}\n.carousel-control-next {\n  right: 0;\n}\n\n// Icons for within\n.carousel-control-prev-icon,\n.carousel-control-next-icon {\n  display: inline-block;\n  width: $carousel-control-icon-width;\n  height: $carousel-control-icon-width;\n  background: transparent no-repeat center center;\n  background-size: 100% 100%;\n}\n.carousel-control-prev-icon {\n  background-image: $carousel-control-prev-icon-bg;\n}\n.carousel-control-next-icon {\n  background-image: $carousel-control-next-icon-bg;\n}\n\n\n// Optional indicator pips\n//\n// Add an ordered list with the following class and add a list item for each\n// slide your carousel holds.\n\n.carousel-indicators {\n  position: absolute;\n  right: 0;\n  bottom: 10px;\n  left: 0;\n  z-index: 15;\n  display: flex;\n  justify-content: center;\n  padding-left: 0; // override <ol> default\n  // Use the .carousel-control's width as margin so we don't overlay those\n  margin-right: $carousel-control-width;\n  margin-left: $carousel-control-width;\n  list-style: none;\n\n  li {\n    position: relative;\n    flex: 0 1 auto;\n    width: $carousel-indicator-width;\n    height: $carousel-indicator-height;\n    margin-right: $carousel-indicator-spacer;\n    margin-left: $carousel-indicator-spacer;\n    text-indent: -999px;\n    background-color: rgba($carousel-indicator-active-bg, .5);\n\n    // Use pseudo classes to increase the hit area by 10px on top and bottom.\n    &::before {\n      position: absolute;\n      top: -10px;\n      left: 0;\n      display: inline-block;\n      width: 100%;\n      height: 10px;\n      content: \"\";\n    }\n    &::after {\n      position: absolute;\n      bottom: -10px;\n      left: 0;\n      display: inline-block;\n      width: 100%;\n      height: 10px;\n      content: \"\";\n    }\n  }\n\n  .active {\n    background-color: $carousel-indicator-active-bg;\n  }\n}\n\n\n// Optional captions\n//\n//\n\n.carousel-caption {\n  position: absolute;\n  right: ((100% - $carousel-caption-width) / 2);\n  bottom: 20px;\n  left: ((100% - $carousel-caption-width) / 2);\n  z-index: 10;\n  padding-top: 20px;\n  padding-bottom: 20px;\n  color: $carousel-caption-color;\n  text-align: center;\n}\n", ".align-baseline    { vertical-align: baseline !important; } // <PERSON><PERSON><PERSON> default\n.align-top         { vertical-align: top !important; }\n.align-middle      { vertical-align: middle !important; }\n.align-bottom      { vertical-align: bottom !important; }\n.align-text-bottom { vertical-align: text-bottom !important; }\n.align-text-top    { vertical-align: text-top !important; }\n", "// Contextual backgrounds\n\n@mixin bg-variant($parent, $color) {\n  #{$parent} {\n    background-color: $color !important;\n  }\n  a#{$parent} {\n    @include hover-focus {\n      background-color: darken($color, 10%) !important;\n    }\n  }\n}\n", "@each $color, $value in $theme-colors {\n  @include bg-variant('.bg-#{$color}', $value);\n}\n\n.bg-white { background-color: $white !important; }\n.bg-transparent { background-color: transparent !important; }\n", "//\n// Border\n//\n\n.border          { border: 1px solid $gray-200 !important; }\n.border-0        { border: 0 !important; }\n.border-top-0    { border-top: 0 !important; }\n.border-right-0  { border-right: 0 !important; }\n.border-bottom-0 { border-bottom: 0 !important; }\n.border-left-0   { border-left: 0 !important; }\n\n@each $color, $value in $theme-colors {\n  .border-#{$color} {\n    border-color: $value !important;\n  }\n}\n\n.border-white {\n  border-color: $white !important;\n}\n\n//\n// Border-radius\n//\n\n.rounded {\n  border-radius: $border-radius !important;\n}\n.rounded-top {\n  border-top-left-radius: $border-radius !important;\n  border-top-right-radius: $border-radius !important;\n}\n.rounded-right {\n  border-top-right-radius: $border-radius !important;\n  border-bottom-right-radius: $border-radius !important;\n}\n.rounded-bottom {\n  border-bottom-right-radius: $border-radius !important;\n  border-bottom-left-radius: $border-radius !important;\n}\n.rounded-left {\n  border-top-left-radius: $border-radius !important;\n  border-bottom-left-radius: $border-radius !important;\n}\n\n.rounded-circle {\n  border-radius: 50%;\n}\n\n.rounded-0 {\n  border-radius: 0;\n}\n", "//\n// Utilities for common `display` values\n//\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .d#{$infix}-none         { display: none !important; }\n    .d#{$infix}-inline       { display: inline !important; }\n    .d#{$infix}-inline-block { display: inline-block !important; }\n    .d#{$infix}-block        { display: block !important; }\n    .d#{$infix}-table        { display: table !important; }\n    .d#{$infix}-table-cell   { display: table-cell !important; }\n    .d#{$infix}-flex         { display: flex !important; }\n    .d#{$infix}-inline-flex  { display: inline-flex !important; }\n  }\n}\n\n\n//\n// Utilities for toggling `display` in print\n//\n\n.d-print-block {\n  display: none !important;\n\n  @media print {\n    display: block !important;\n  }\n}\n\n.d-print-inline {\n  display: none !important;\n\n  @media print {\n    display: inline !important;\n  }\n}\n\n.d-print-inline-block {\n  display: none !important;\n\n  @media print {\n    display: inline-block !important;\n  }\n}\n\n.d-print-none {\n  @media print {\n    display: none !important;\n  }\n}\n", "// Credit: <PERSON> and <PERSON><PERSON>T CSS.\n\n.embed-responsive {\n  position: relative;\n  display: block;\n  width: 100%;\n  padding: 0;\n  overflow: hidden;\n\n  &::before {\n    display: block;\n    content: \"\";\n  }\n\n  .embed-responsive-item,\n  iframe,\n  embed,\n  object,\n  video {\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    border: 0;\n  }\n}\n\n.embed-responsive-21by9 {\n  &::before {\n    padding-top: percentage(9 / 21);\n  }\n}\n\n.embed-responsive-16by9 {\n  &::before {\n    padding-top: percentage(9 / 16);\n  }\n}\n\n.embed-responsive-4by3 {\n  &::before {\n    padding-top: percentage(3 / 4);\n  }\n}\n\n.embed-responsive-1by1 {\n  &::before {\n    padding-top: percentage(1 / 1);\n  }\n}\n", "// Flex variation\n//\n// Custom styles for additional flex alignment options.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .flex#{$infix}-row            { flex-direction: row !important; }\n    .flex#{$infix}-column         { flex-direction: column !important; }\n    .flex#{$infix}-row-reverse    { flex-direction: row-reverse !important; }\n    .flex#{$infix}-column-reverse { flex-direction: column-reverse !important; }\n\n    .flex#{$infix}-wrap         { flex-wrap: wrap !important; }\n    .flex#{$infix}-nowrap       { flex-wrap: nowrap !important; }\n    .flex#{$infix}-wrap-reverse { flex-wrap: wrap-reverse !important; }\n\n    .justify-content#{$infix}-start   { justify-content: flex-start !important; }\n    .justify-content#{$infix}-end     { justify-content: flex-end !important; }\n    .justify-content#{$infix}-center  { justify-content: center !important; }\n    .justify-content#{$infix}-between { justify-content: space-between !important; }\n    .justify-content#{$infix}-around  { justify-content: space-around !important; }\n\n    .align-items#{$infix}-start    { align-items: flex-start !important; }\n    .align-items#{$infix}-end      { align-items: flex-end !important; }\n    .align-items#{$infix}-center   { align-items: center !important; }\n    .align-items#{$infix}-baseline { align-items: baseline !important; }\n    .align-items#{$infix}-stretch  { align-items: stretch !important; }\n\n    .align-content#{$infix}-start   { align-content: flex-start !important; }\n    .align-content#{$infix}-end     { align-content: flex-end !important; }\n    .align-content#{$infix}-center  { align-content: center !important; }\n    .align-content#{$infix}-between { align-content: space-between !important; }\n    .align-content#{$infix}-around  { align-content: space-around !important; }\n    .align-content#{$infix}-stretch { align-content: stretch !important; }\n\n    .align-self#{$infix}-auto     { align-self: auto !important; }\n    .align-self#{$infix}-start    { align-self: flex-start !important; }\n    .align-self#{$infix}-end      { align-self: flex-end !important; }\n    .align-self#{$infix}-center   { align-self: center !important; }\n    .align-self#{$infix}-baseline { align-self: baseline !important; }\n    .align-self#{$infix}-stretch  { align-self: stretch !important; }\n  }\n}\n", "@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .float#{$infix}-left  { @include float-left; }\n    .float#{$infix}-right { @include float-right; }\n    .float#{$infix}-none  { @include float-none; }\n  }\n}\n", "@mixin float-left {\n  float: left !important;\n}\n@mixin float-right {\n  float: right !important;\n}\n@mixin float-none {\n  float: none !important;\n}\n", "// Positioning\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.sticky-top {\n  @supports (position: sticky) {\n    position: sticky;\n    top: 0;\n    z-index: $zindex-sticky;\n  }\n}\n", "//\n// Screenreaders\n//\n\n.sr-only {\n  @include sr-only();\n}\n\n.sr-only-focusable {\n  @include sr-only-focusable();\n}\n", "// Only display content to screen readers\n//\n// See: http://a11yproject.com/posts/how-to-hide-content\n// See: http://hugogiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  overflow: hidden;\n  clip: rect(0,0,0,0);\n  white-space: nowrap;\n  clip-path: inset(50%);\n  border: 0;\n}\n\n// Use in conjunction with .sr-only to only display content when it's focused.\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n//\n// Credit: HTML5 Boilerplate\n\n@mixin sr-only-focusable {\n  &:active,\n  &:focus {\n    position: static;\n    width: auto;\n    height: auto;\n    overflow: visible;\n    clip: auto;\n    white-space: normal;\n    clip-path: none;\n  }\n}\n", "// Width and height\n\n@each $prop, $abbrev in (width: w, height: h) {\n  @each $size, $length in $sizes {\n    .#{$abbrev}-#{$size} { #{$prop}: $length !important; }\n  }\n}\n\n.mw-100 { max-width: 100% !important; }\n.mh-100 { max-height: 100% !important; }\n", "// Mar<PERSON> and Padding\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $prop, $abbrev in (margin: m, padding: p) {\n      @each $size, $length in $spacers {\n\n        .#{$abbrev}#{$infix}-#{$size}  { #{$prop}:        $length !important; }\n        .#{$abbrev}t#{$infix}-#{$size} { #{$prop}-top:    $length !important; }\n        .#{$abbrev}r#{$infix}-#{$size} { #{$prop}-right:  $length !important; }\n        .#{$abbrev}b#{$infix}-#{$size} { #{$prop}-bottom: $length !important; }\n        .#{$abbrev}l#{$infix}-#{$size} { #{$prop}-left:   $length !important; }\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-right: $length !important;\n          #{$prop}-left:  $length !important;\n        }\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-top:    $length !important;\n          #{$prop}-bottom: $length !important;\n        }\n      }\n    }\n\n    // Some special margin utils\n    .m#{$infix}-auto  { margin:        auto !important; }\n    .mt#{$infix}-auto { margin-top:    auto !important; }\n    .mr#{$infix}-auto { margin-right:  auto !important; }\n    .mb#{$infix}-auto { margin-bottom: auto !important; }\n    .ml#{$infix}-auto { margin-left:   auto !important; }\n    .mx#{$infix}-auto {\n      margin-right: auto !important;\n      margin-left:  auto !important;\n    }\n    .my#{$infix}-auto {\n      margin-top:    auto !important;\n      margin-bottom: auto !important;\n    }\n  }\n}\n", "//\n// Text\n//\n\n// Alignment\n\n.text-justify  { text-align: justify !important; }\n.text-nowrap   { white-space: nowrap !important; }\n.text-truncate { @include text-truncate; }\n\n// Responsive alignment\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .text#{$infix}-left   { text-align: left !important; }\n    .text#{$infix}-right  { text-align: right !important; }\n    .text#{$infix}-center { text-align: center !important; }\n  }\n}\n\n// Transformation\n\n.text-lowercase  { text-transform: lowercase !important; }\n.text-uppercase  { text-transform: uppercase !important; }\n.text-capitalize { text-transform: capitalize !important; }\n\n// Weight and italics\n\n.font-weight-normal { font-weight: $font-weight-normal; }\n.font-weight-bold   { font-weight: $font-weight-bold; }\n.font-italic        { font-style: italic; }\n\n// Contextual colors\n\n.text-white { color: #fff !important; }\n\n@each $color, $value in $theme-colors {\n  @include text-emphasis-variant('.text-#{$color}', $value);\n}\n\n.text-muted { color: $text-muted !important; }\n\n// Misc\n\n.text-hide {\n  @include text-hide();\n}\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "// Typography\n\n@mixin text-emphasis-variant($parent, $color) {\n  #{$parent} {\n    color: $color !important;\n  }\n  a#{$parent} {\n    @include hover-focus {\n      color: darken($color, 10%) !important;\n    }\n  }\n}\n", "// CSS image replacement\n@mixin text-hide() {\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n}\n", "//\n// Visibility utilities\n//\n\n.visible {\n  @include invisible(visible);\n}\n\n.invisible {\n  @include invisible(hidden);\n}\n", "// Visibility\n\n@mixin invisible($visibility) {\n  visibility: $visibility !important;\n}\n"]}
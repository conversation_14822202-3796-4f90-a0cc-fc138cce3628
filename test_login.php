<?php
/**
 * Login Test Script
 * 
 * This script helps test login functionality and debug password issues
 */

// Include CodeIgniter bootstrap
require_once 'index.php';

echo "=== KONJAS Login Test ===\n\n";

// Get CI instance
$CI =& get_instance();
$CI->load->database();

// Test database connection
echo "1. Testing Database Connection...\n";
try {
    $tables = $CI->db->list_tables();
    echo "   ✓ Database connected successfully\n";
    echo "   ✓ Found " . count($tables) . " tables\n";
} catch (Exception $e) {
    echo "   ✗ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Check for user tables
echo "\n2. Checking User Tables...\n";
$user_tables = ['aset_users', 'v_user', 'users'];
foreach ($user_tables as $table) {
    if (in_array($table, $tables)) {
        echo "   ✓ Table '$table' exists\n";
        
        // Count users in this table
        $count = $CI->db->count_all($table);
        echo "     - Contains $count records\n";
        
        // Show sample user data (without password)
        $CI->db->select('id_user, userlogin, username, email, id_user_group');
        $CI->db->limit(3);
        $users = $CI->db->get($table)->result_array();
        
        if (!empty($users)) {
            echo "     - Sample users:\n";
            foreach ($users as $user) {
                $id = $user['id_user'] ?? 'N/A';
                $login = $user['userlogin'] ?? $user['username'] ?? 'N/A';
                $email = $user['email'] ?? 'N/A';
                $group = $user['id_user_group'] ?? 'N/A';
                echo "       * ID: $id, Login: $login, Email: $email, Group: $group\n";
            }
        }
    } else {
        echo "   - Table '$table' not found\n";
    }
}

// Test password hashing
echo "\n3. Testing Password Functions...\n";
$test_password = "test123";
$md5_hash = md5($test_password);
$modern_hash = password_hash($test_password, PASSWORD_DEFAULT);

echo "   ✓ MD5 hash: $md5_hash\n";
echo "   ✓ Modern hash: $modern_hash\n";
echo "   ✓ MD5 verification: " . (md5($test_password) === $md5_hash ? "PASS" : "FAIL") . "\n";
echo "   ✓ Modern verification: " . (password_verify($test_password, $modern_hash) ? "PASS" : "FAIL") . "\n";

// Check for specific user (if provided via command line)
if (isset($argv[1])) {
    $username = $argv[1];
    echo "\n4. Testing Login for User: $username\n";
    
    // Search for user in all tables
    foreach ($user_tables as $table) {
        if (in_array($table, $tables)) {
            $CI->db->where('userlogin', $username);
            $query = $CI->db->get($table);
            
            if ($query->num_rows() > 0) {
                $user = $query->row();
                echo "   ✓ User found in table '$table'\n";
                echo "     - ID: " . ($user->id_user ?? $user->id ?? 'N/A') . "\n";
                echo "     - Email: " . ($user->email ?? 'N/A') . "\n";
                echo "     - Group: " . ($user->id_user_group ?? 'N/A') . "\n";
                
                if (isset($user->password)) {
                    $password_length = strlen($user->password);
                    $is_md5 = ($password_length == 32 && ctype_xdigit($user->password));
                    echo "     - Password length: $password_length\n";
                    echo "     - Password type: " . ($is_md5 ? "MD5 (legacy)" : "Modern hash") . "\n";
                    
                    if (isset($argv[2])) {
                        $test_pass = $argv[2];
                        if ($is_md5) {
                            $valid = (md5($test_pass) === $user->password);
                        } else {
                            $valid = password_verify($test_pass, $user->password);
                        }
                        echo "     - Password test: " . ($valid ? "VALID" : "INVALID") . "\n";
                    }
                } else {
                    echo "     - Password: NOT FOUND\n";
                }
                break;
            }
        }
    }
}

echo "\n=== Test Complete ===\n";
echo "Usage: php test_login.php [username] [password]\n";
echo "Example: php test_login.php admin admin123\n";
?>

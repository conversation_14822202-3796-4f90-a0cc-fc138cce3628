name: PHPUnit

on: [push, pull_request]

permissions:
  contents: read

jobs:
  tests:
    runs-on: ubuntu-22.04
    if: "!contains(github.event.head_commit.message, '[ci skip]')"
    env:
      PHP_INI_VALUES: assert.exception=1, zend.assertions=1

    strategy:
      fail-fast: false
      matrix:
        php: [ '8.2', '8.1', '8.0', '7.4', '7.3', '7.2', '7.1', '7.0', '5.6', '5.5', '5.4']
        DB: [ 'pdo/mysql', 'pdo/pgsql', 'pdo/sqlite', 'mysqli', 'pgsql', 'sqlite' ]
        compiler: [ default ]
        include:
          - php: '8.2'
            DB: 'pdo/mysql'
            compiler: jit
          - php: '8.2'
            DB: 'pdo/pgsql'
            compiler: jit
          - php: '8.2'
            DB: 'pdo/sqlite'
            compiler: jit
          - php: '8.2'
            DB: 'mysqli'
            compiler: jit
          - php: '8.2'
            DB: 'pgsql'
            compiler: jit
          - php: '8.2'
            DB: 'sqlite'
            compiler: jit
          - php: '8.1'
            DB: 'pdo/mysql'
            compiler: jit
          - php: '8.1'
            DB: 'pdo/pgsql'
            compiler: jit
          - php: '8.1'
            DB: 'pdo/sqlite'
            compiler: jit
          - php: '8.1'
            DB: 'mysqli'
            compiler: jit
          - php: '8.1'
            DB: 'pgsql'
            compiler: jit
          - php: '8.1'
            DB: 'sqlite'
            compiler: jit
          - php: '8.0'
            DB: 'pdo/mysql'
            compiler: jit
          - php: '8.0'
            DB: 'pdo/pgsql'
            compiler: jit
          - php: '8.0'
            DB: 'pdo/sqlite'
            compiler: jit
          - php: '8.0'
            DB: 'mysqli'
            compiler: jit
          - php: '8.0'
            DB: 'pgsql'
            compiler: jit
          - php: '8.0'
            DB: 'sqlite'
            compiler: jit
          - php: '5.6'
            DB: 'mysql'
            compiler: default
          - php: '5.5'
            DB: 'mysql'
            compiler: default
          - php: '5.4'
            DB: 'mysql'
            compiler: default

    services:
      postgres:
        image: postgres:12
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ci_test
        ports:
          - 5432:5432
        options: --health-cmd=pg_isready --health-interval=10s --health-timeout=5s --health-retries=3

      mysql:
        image: mysql:5.7
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: true
          MYSQL_USER: travis
          MYSQL_PASSWORD: travis
          MYSQL_DATABASE: ci_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Override PHP ini values for JIT compiler
        if: matrix.compiler == 'jit'
        run: echo "PHP_INI_VALUES::assert.exception=1, zend.assertions=1, opcache.enable=1, opcache.enable_cli=1, opcache.optimization_level=-1, opcache.jit=1255, opcache.jit_buffer_size=64M" >> $GITHUB_ENV 

      - name: Install PHP${{ matrix.php }} - DB ${{ matrix.DB }}
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          tools: composer, pecl
          extensions: imagick, sqlite3, pgsql, mysqli, pdo, pdo_mysql, pdo_pgsql, pdo_sqlite, mbstring
          ini-values: ${{ env.PHP_INI_VALUES }}
          coverage: xdebug

      - name: Get composer cache directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"
      - name: Cache composer dependencies
        uses: actions/cache@v2
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-   
      - name: Install composer dependencies
        run: composer install --no-progress --prefer-dist --optimize-autoloader

      - name: PHPUnit Test
        run: |
          php -d error_reporting=E_ALL -d zend.enable_gc=0 -d date.timezone=UTC -d mbstring.func_overload=7 -d mbstring.internal_encoding=UTF-8 vendor/bin/phpunit --coverage-text --configuration tests/travis/${{ matrix.DB }}.phpunit.xml
        env:
          XDEBUG_MODE: coverage

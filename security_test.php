<?php
/**
 * Security Test Script for KONJAS Application
 * 
 * This script tests various security implementations
 */

echo "=== KONJAS Security Test Report ===\n\n";

// Test 1: Check Security Headers
echo "1. Testing Security Headers...\n";
$headers = get_headers('http://localhost/konjas/login', 1);

$security_headers = [
    'X-Content-Type-Options' => 'nosniff',
    'X-Frame-Options' => 'DENY',
    'X-XSS-Protection' => '1; mode=block',
    'Content-Security-Policy' => true,
    'Referrer-Policy' => 'strict-origin-when-cross-origin'
];

foreach ($security_headers as $header => $expected) {
    if (isset($headers[$header])) {
        echo "   ✓ $header: " . $headers[$header] . "\n";
    } else {
        echo "   ✗ $header: MISSING\n";
    }
}

// Test 2: Check CSRF Protection
echo "\n2. Testing CSRF Protection...\n";
$csrf_config = file_get_contents('application/config/config.php');
if (strpos($csrf_config, "\$config['csrf_protection'] = TRUE") !== false) {
    echo "   ✓ CSRF Protection: ENABLED\n";
} else {
    echo "   ✗ CSRF Protection: DISABLED\n";
}

// Test 3: Check Session Security
echo "\n3. Testing Session Security...\n";
if (strpos($csrf_config, "cookie_secure']\t= TRUE") !== false) {
    echo "   ✓ Secure Cookies: ENABLED\n";
} else {
    echo "   ✗ Secure Cookies: DISABLED\n";
}

if (strpos($csrf_config, "cookie_httponly'] \t= TRUE") !== false) {
    echo "   ✓ HttpOnly Cookies: ENABLED\n";
} else {
    echo "   ✗ HttpOnly Cookies: DISABLED\n";
}

// Test 4: Check Password Hashing
echo "\n4. Testing Password Security...\n";
$user_controller = file_get_contents('application/controllers/data_users/Data_users.php');
if (strpos($user_controller, 'password_hash') !== false) {
    echo "   ✓ Secure Password Hashing: IMPLEMENTED\n";
} else {
    echo "   ✗ Secure Password Hashing: NOT IMPLEMENTED\n";
}

// Test 5: Check File Upload Security
echo "\n5. Testing File Upload Security...\n";
$upload_handler = file_get_contents('application/libraries/UploadHandler.php');
if (strpos($upload_handler, "'accept_file_types' => '/\\.(pdf|doc|docx|xls|xlsx|jpg|jpeg|png|gif)$/i'") !== false) {
    echo "   ✓ File Type Restrictions: IMPLEMENTED\n";
} else {
    echo "   ✗ File Type Restrictions: NOT IMPLEMENTED\n";
}

// Test 6: Check Security Libraries
echo "\n6. Testing Security Libraries...\n";
if (file_exists('application/libraries/Security_validator.php')) {
    echo "   ✓ Security Validator: AVAILABLE\n";
} else {
    echo "   ✗ Security Validator: MISSING\n";
}

if (file_exists('application/hooks/Security_headers.php')) {
    echo "   ✓ Security Headers Hook: AVAILABLE\n";
} else {
    echo "   ✗ Security Headers Hook: MISSING\n";
}

// Test 7: Check Database Security
echo "\n7. Testing Database Security...\n";
$db_config = file_get_contents('application/config/database.php');
if (strpos($db_config, "'stricton' => TRUE") !== false) {
    echo "   ✓ Database Strict Mode: ENABLED\n";
} else {
    echo "   ✗ Database Strict Mode: DISABLED\n";
}

// Test 8: Check Logging
echo "\n8. Testing Security Logging...\n";
if (strpos($csrf_config, "\$config['log_threshold'] = (ENVIRONMENT === 'production') ? 1 : 4") !== false) {
    echo "   ✓ Security Logging: CONFIGURED\n";
} else {
    echo "   ✗ Security Logging: NOT CONFIGURED\n";
}

// Test 9: Check Environment Configuration
echo "\n9. Testing Environment Security...\n";
if (file_exists('env.php')) {
    echo "   ⚠ Environment File: EXPOSED (should be moved outside web root)\n";
} else {
    echo "   ✓ Environment File: SECURE\n";
}

// Test 10: Check for Common Vulnerabilities
echo "\n10. Testing for Common Vulnerabilities...\n";

// Check for SQL injection patterns
$controllers = glob('application/controllers/*/*.php');
$sql_injection_found = false;
foreach ($controllers as $controller) {
    $content = file_get_contents($controller);
    if (preg_match('/\$this->db->query\s*\(\s*["\'].*\$/', $content)) {
        $sql_injection_found = true;
        break;
    }
}

if (!$sql_injection_found) {
    echo "   ✓ SQL Injection: NO OBVIOUS VULNERABILITIES\n";
} else {
    echo "   ⚠ SQL Injection: POTENTIAL VULNERABILITIES FOUND\n";
}

echo "\n=== Security Test Complete ===\n";
echo "Review the results above and address any issues marked with ✗ or ⚠\n";
echo "For detailed remediation steps, see SECURITY-ASSESSMENT.md\n";
?>
